import type {
	ActivateSmtpConfiguration,
	CreateSmtpConfiguration,
	SmtpTestResult,
	TestSmtpConfiguration,
	TestSmtpConnection,
	UpdateSmtpConfiguration,
} from "@repo/api/src/routes/organizations/smtp-configuration/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface GetSmtpConfigurationParams {
	organizationId: string;
}

// Query keys for React Query
export const smtpConfigurationKeys = {
	all: ["smtp-configuration"] as const,
	details: () => [...smtpConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...smtpConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchSmtpConfiguration = async (
	params: GetSmtpConfigurationParams,
) => {
	const response = await apiClient.organizations["smtp-configuration"].$get({
		query: {
			organizationId: params.organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch SMTP configuration");
	}

	return response.json();
};

export const createSmtpConfiguration = async (
	data: CreateSmtpConfiguration,
) => {
	const response = await apiClient.organizations["smtp-configuration"].$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to create SMTP configuration");
	}

	return response.json();
};

export const updateSmtpConfiguration = async (
	data: UpdateSmtpConfiguration,
) => {
	const response = await apiClient.organizations["smtp-configuration"].$put({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to update SMTP configuration");
	}

	return response.json();
};

export const deleteSmtpConfiguration = async (organizationId: string) => {
	const response = await apiClient.organizations[
		"smtp-configuration"
	].$delete({
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to delete SMTP configuration");
	}

	return response.json();
};

export const testSmtpConfiguration = async (data: TestSmtpConfiguration) => {
	const response = await apiClient.organizations[
		"smtp-configuration"
	].test.$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to test SMTP configuration");
	}

	return response.json();
};

export const testSmtpConnection = async (data: TestSmtpConnection) => {
	const response = await apiClient.organizations["smtp-configuration"][
		"test-connection"
	].$post({
		json: data,
	});

	if (!response.ok) {
		// Parse the error response and throw it
		const errorData = await response.json();
		throw errorData;
	}

	return response.json();
};

export const activateSmtpConfiguration = async (
	data: ActivateSmtpConfiguration,
) => {
	const response = await apiClient.organizations[
		"smtp-configuration"
	].activate.$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to activate SMTP configuration");
	}

	return response.json();
};

// OAuth2 API functions
export const initiateOAuth2Flow = async (
	organizationId: string,
	provider: "microsoft",
) => {
	// Use fetch directly since the OAuth routes are dynamic in the auth router
	const baseUrl = process.env.NEXT_PUBLIC_SITE_URL;
	const response = await fetch(
		`${baseUrl}/api/oauth/smtp/${provider}/initiate`,
		{
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			credentials: "include",
			body: JSON.stringify({ organizationId }),
		},
	);

	if (!response.ok) {
		throw new Error("Failed to initiate OAuth2 flow");
	}

	return response.json();
};

// React Query Hooks
export const useSmtpConfigurationQuery = (
	params: GetSmtpConfigurationParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: smtpConfigurationKeys.detail(params.organizationId),
		queryFn: () => fetchSmtpConfiguration(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

export const useCreateSmtpConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<CreateSmtpConfiguration, "organizationId">,
		) => {
			return createSmtpConfiguration({ ...data, organizationId });
		},
		onSuccess: () => {
			toast.success("SMTP configuration created successfully");
			queryClient.invalidateQueries({
				queryKey: smtpConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create SMTP configuration",
			);
		},
	});
};

export const useUpdateSmtpConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateSmtpConfiguration, "organizationId">,
		) => {
			return updateSmtpConfiguration({ ...data, organizationId });
		},
		onSuccess: () => {
			toast.success("SMTP configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: smtpConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update SMTP configuration",
			);
		},
	});
};

export const useDeleteSmtpConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async () => {
			return deleteSmtpConfiguration(organizationId);
		},
		onSuccess: () => {
			toast.success("SMTP configuration deleted successfully");
			queryClient.invalidateQueries({
				queryKey: smtpConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete SMTP configuration",
			);
		},
	});
};

export const useTestSmtpConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<TestSmtpConfiguration, "organizationId">,
		) => {
			const result = await testSmtpConfiguration({
				...data,
				organizationId,
			});

			// Check if the SMTP test actually succeeded
			if (!result.success) {
				// Use the detailed error message if available
				const errorMessage =
					result.error ||
					result.message ||
					"SMTP configuration test failed";
				throw new Error(errorMessage);
			}

			return result;
		},
		onSuccess: () => {
			toast.success("SMTP configuration tested successfully");
			queryClient.invalidateQueries({
				queryKey: smtpConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to test SMTP configuration",
				{
					duration: 10000, // Longer duration for errors
				},
			);
		},
	});
};

export const useTestSmtpConnectionMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (
			data: Omit<TestSmtpConnection, "organizationId">,
		) => {
			const result = (await testSmtpConnection({
				...data,
				organizationId,
			})) as SmtpTestResult;

			// Check if the SMTP test actually succeeded
			if (!result.success) {
				throw new Error(
					result.message || "SMTP connection test failed",
				);
			}

			return result;
		},
		onSuccess: () => {
			toast.success("SMTP connection tested successfully");
		},
		onError: (error: any) => {
			// Just display whatever error message we get from the backend
			const message = error.message || "Failed to test SMTP connection";
			toast.error(message, {
				duration: 10000, // Longer duration for errors
			});
		},
	});
};

export const useActivateSmtpConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (isActive: boolean) => {
			return activateSmtpConfiguration({ organizationId, isActive });
		},
		onSuccess: (_, isActive) => {
			toast.success(
				`SMTP configuration ${isActive ? "activated" : "deactivated"} successfully`,
			);
			queryClient.invalidateQueries({
				queryKey: smtpConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to activate SMTP configuration",
			);
		},
	});
};

export const useOAuth2FlowMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (provider: "microsoft") => {
			const result = await initiateOAuth2Flow(organizationId, provider);

			// Open popup window for OAuth2 flow
			const popup = window.open(
				result.authUrl,
				"oauth2-popup",
				"width=600,height=700,scrollbars=yes,resizable=yes",
			);

			if (!popup) {
				throw new Error(
					"Popup window blocked. Please allow popups for this site.",
				);
			}

			// Wait for OAuth2 callback message
			return new Promise<{
				refreshToken: string;
				accessToken: string;
				tokenExpires?: string;
				userEmail?: string;
			}>((resolve, reject) => {
				const messageHandler = (event: MessageEvent) => {
					if (event.origin !== window.location.origin) {
						return;
					}

					if (event.data.type === "oauth2-success") {
						window.removeEventListener("message", messageHandler);
						popup.close();

						// Extract user email from access token if available
						let userEmail: string | undefined;
						try {
							if (event.data.tokens.accessToken) {
								const tokenParts =
									event.data.tokens.accessToken.split(".");
								if (tokenParts.length === 3) {
									// Decode JWT payload
									const payload = JSON.parse(
										atob(
											tokenParts[1]
												.replace(/-/g, "+")
												.replace(/_/g, "/"),
										),
									);
									userEmail =
										payload.preferred_username ||
										payload.upn ||
										payload.unique_name ||
										payload.email;
								}
							}
						} catch (error) {
							console.warn(
								"Failed to extract user email from access token:",
								error,
							);
						}

						resolve({
							...event.data.tokens,
							userEmail,
						});
					}
				};

				window.addEventListener("message", messageHandler);

				// Check if popup was closed manually
				const checkClosed = setInterval(() => {
					if (popup.closed) {
						clearInterval(checkClosed);
						window.removeEventListener("message", messageHandler);
						reject(new Error("OAuth2 flow was cancelled"));
					}
				}, 1000);
			});
		},
		onSuccess: () => {
			toast.success(
				"OAuth2 authentication successful! You can now save your SMTP configuration.",
			);
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to complete OAuth2 authentication",
			);
		},
	});
};

// Export types for use in components
export type {
	CreateSmtpConfiguration,
	UpdateSmtpConfiguration,
	TestSmtpConfiguration,
	TestSmtpConnection,
	ActivateSmtpConfiguration,
	SmtpTestResult,
};
