"use client";

import { DataTablePagination } from "@saas/shared/components/data-table/pagination";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import type {
	ColumnDef,
	OnChangeFn,
	SortingState,
	VisibilityState,
} from "@tanstack/react-table";
import {
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	useReactTable,
} from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Skeleton } from "@ui/components/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { DownloadIcon, Settings2 } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { DataTableToolbar } from "./toolbar";

export interface DataTableProps<TData, TValue> {
	columns: ColumnDef<TData, TValue>[];
	data: TData[];
	defaultColumnVisibility?: VisibilityState;
	filterColumn?: string;
	filterOptions?: {
		value: string;
		label: string;
	}[];
	onSearch?: (value: string) => void;
	searchValue?: string;
	searchPlaceholder?: string;
	pagination?: {
		page: number;
		setPage: (page: number) => void;
		pageSize: number;
		setPageSize: (size: number) => void;
		totalPages: number;
		total: number;
	};
	isLoading?: boolean;
	sorting?: SortingState;
	onSortingChange?: OnChangeFn<SortingState>;
	manualSorting?: boolean;
	shortcutsScope?: string;
	showToolbar?: boolean;
	// Date range filter props
	dateRange?: { from?: Date; to?: Date };
	onDateRangeChange?: (
		dateRange: { from?: Date; to?: Date } | undefined,
	) => void;
	dateRangeLabel?: string;
}

export function DataTable<TData, TValue>({
	columns,
	data,
	defaultColumnVisibility,
	filterColumn,
	filterOptions,
	onSearch,
	searchValue,
	searchPlaceholder,
	pagination,
	isLoading,
	sorting,
	onSortingChange,
	manualSorting = false,
	shortcutsScope = "table",
	showToolbar = true,
	// Date range filter props
	dateRange,
	onDateRangeChange,
	dateRangeLabel,
}: DataTableProps<TData, TValue>) {
	const { registerScopeWithShortcuts, activeScopes, registerCapability } =
		useShortcuts();
	const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
		defaultColumnVisibility ?? {},
	);
	const searchRef = useRef<HTMLInputElement>(null);
	const [highlightedRowIndex, setHighlightedRowIndex] = useState<
		number | null
	>(null);
	const tableRef = useRef<HTMLDivElement>(null);
	const [isSearchFocused, setIsSearchFocused] = useState(false);

	// Register shortcuts and scope for this table
	useEffect(() => {
		// Register the scope and get the cleanup function
		const cleanupShortcuts = registerScopeWithShortcuts(shortcutsScope);

		// Return cleanup function
		return cleanupShortcuts;
	}, [registerScopeWithShortcuts, shortcutsScope]);

	// Focus search field on Ctrl+S
	useHotkeys(
		"ctrl+s",
		(e) => {
			e.preventDefault();
			e.stopPropagation();
			searchRef.current?.focus();
		},
		{
			enableOnFormTags: true,
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
		},
	);

	// Clear highlight on Escape
	useHotkeys(
		"esc",
		(e) => {
			e.preventDefault();
			if (isSearchFocused) {
				searchRef.current?.blur();
			}
			setHighlightedRowIndex(null);
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
			enableOnFormTags: true,
		},
	);

	const table = useReactTable<TData>({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getSortedRowModel: getSortedRowModel(),
		onSortingChange,
		onColumnVisibilityChange: setColumnVisibility,
		state: {
			sorting: sorting ?? [],
			columnVisibility,
			columnFilters: [],
			pagination: {
				pageIndex: (pagination?.page ?? 1) - 1,
				pageSize: pagination?.pageSize ?? 10,
			},
		},
		manualPagination: true,
		manualSorting,
		pageCount: pagination?.totalPages ?? 1,
	});

	// Keep the click outside handler
	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (
				tableRef.current &&
				!tableRef.current.contains(event.target as Node)
			) {
				setHighlightedRowIndex(null);
			}
		}

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	useHotkeys(
		"arrowdown",
		(e) => {
			if (isSearchFocused) {
				return;
			}

			e.preventDefault();
			const rows = table.getRowModel().rows;
			if (!rows.length) {
				return;
			}

			setHighlightedRowIndex((current) => {
				if (current === null) {
					return 0;
				}
				return current < rows.length - 1 ? current + 1 : current;
			});
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
		},
	);

	useHotkeys(
		"arrowup",
		(e) => {
			if (isSearchFocused) {
				return;
			}

			e.preventDefault();
			const rows = table.getRowModel().rows;
			if (!rows.length) {
				return;
			}

			setHighlightedRowIndex((current) => {
				if (current === null) {
					return rows.length - 1;
				}
				return current > 0 ? current - 1 : current;
			});
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
		},
	);

	// Check if table has various action capabilities
	const { hasViewCapability, hasEditCapability, hasDeleteCapability } =
		React.useMemo(() => {
			if (data.length === 0) {
				return {
					hasViewCapability: false,
					hasEditCapability: false,
					hasDeleteCapability: false,
				};
			}

			// We only need to check the first row since all rows in a table will have the same actions
			const firstRow = table.getRowModel().rows[0];
			if (!firstRow) {
				return {
					hasViewCapability: false,
					hasEditCapability: false,
					hasDeleteCapability: false,
				};
			}

			const actions = firstRow
				.getVisibleCells()
				.slice(-1)[0]
				?.column.columnDef.meta?.getRowActions?.(firstRow);

			// Check if various actions exist and are functions
			return {
				hasViewCapability: typeof actions?.openView === "function",
				hasEditCapability: typeof actions?.openEdit === "function",
				hasDeleteCapability: typeof actions?.openDelete === "function",
			};
		}, [table, data]);

	// Register capabilities for various actions
	React.useEffect(() => {
		// Register capabilities for each action type
		registerCapability("table-view-row", hasViewCapability);
		registerCapability("table-edit-row", hasEditCapability);
		registerCapability("table-delete-row", hasDeleteCapability);

		return () => {
			// Reset capabilities when component unmounts
			registerCapability("table-view-row", false);
			registerCapability("table-edit-row", false);
			registerCapability("table-delete-row", false);
		};
	}, [
		registerCapability,
		hasViewCapability,
		hasEditCapability,
		hasDeleteCapability,
	]);

	useHotkeys(
		"enter",
		(e) => {
			if (isSearchFocused || highlightedRowIndex === null) {
				return;
			}
			e.preventDefault();

			const row = table.getRowModel().rows[highlightedRowIndex];
			if (!row) {
				return;
			}

			const actions = row
				.getVisibleCells()
				.slice(-1)[0]
				.column.columnDef.meta?.getRowActions?.(row);

			if (actions?.openView) {
				actions.openView();
			}
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
		},
	);

	// Only add the ctrl+e hotkey handler if edit capability exists
	useHotkeys(
		"ctrl+e",
		(e) => {
			if (isSearchFocused || highlightedRowIndex === null) {
				return;
			}
			e.preventDefault();

			const row = table.getRowModel().rows[highlightedRowIndex];
			if (!row) {
				return;
			}

			const actions = row
				.getVisibleCells()
				.slice(-1)[0]
				.column.columnDef.meta?.getRowActions?.(row);

			// Call openEdit (we know it exists because the shortcut is only enabled if it does)
			actions?.openEdit?.();
		},
		{
			preventDefault: true,
			// Keep enablement check for immediate UX response - the capability registry
			// handles visibility in the help documentation
			enabled: activeScopes.includes(shortcutsScope) && hasEditCapability,
		},
	);

	useHotkeys(
		"ctrl+d",
		(e) => {
			if (isSearchFocused || highlightedRowIndex === null) {
				return;
			}
			e.preventDefault();

			const row = table.getRowModel().rows[highlightedRowIndex];
			if (!row) {
				return;
			}

			const actions = row
				.getVisibleCells()
				.slice(-1)[0]
				.column.columnDef.meta?.getRowActions?.(row);
			actions?.openDelete?.();
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope),
		},
	);

	// Handle Page Down for next page
	useHotkeys(
		"pagedown",
		(e) => {
			if (isSearchFocused) {
				return;
			}
			e.preventDefault();

			if (pagination && pagination.page < pagination.totalPages) {
				pagination.setPage(pagination.page + 1);
				setHighlightedRowIndex(null);
			}
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope) && !!pagination,
		},
	);

	// Handle Page Up for previous page
	useHotkeys(
		"pageup",
		(e) => {
			if (isSearchFocused) {
				return;
			}
			e.preventDefault();

			if (pagination && pagination.page > 1) {
				pagination.setPage(pagination.page - 1);
				setHighlightedRowIndex(null);
			}
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes(shortcutsScope) && !!pagination,
		},
	);

	return (
		<div className="space-y-4">
			{showToolbar && (
				<div className="flex items-center justify-between">
					<DataTableToolbar<TData>
						table={table}
						filterColumn={filterColumn}
						filterOptions={filterOptions}
						onSearch={onSearch}
						searchValue={searchValue}
						searchPlaceholder={searchPlaceholder}
						searchRef={searchRef}
						onSearchFocus={() => setIsSearchFocused(true)}
						onSearchBlur={() => setIsSearchFocused(false)}
						dateRange={dateRange}
						onDateRangeChange={onDateRangeChange}
						dateRangeLabel={dateRangeLabel}
					/>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							className="border-cyan-500 text-foreground hover:bg-primary hover:text-primary-foreground"
							onClick={() => console.log("Exporting data...")}
						>
							<DownloadIcon className="mr-2 h-4 w-4" />
							Export
						</Button>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="outline"
									className="ml-auto border-cyan-500 text-foreground hover:bg-primary hover:text-primary-foreground"
								>
									<Settings2 className="mr-2 h-4 w-4" />
									Columns
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent
								align="end"
								className="w-[150px] bg-popover border-border"
							>
								{table
									.getAllColumns()
									.filter(
										(column) =>
											column.getCanHide() &&
											column.id !== "actions",
									)
									.map((column) => (
										<DropdownMenuCheckboxItem
											key={column.id}
											className="capitalize text-popover-foreground"
											checked={column.getIsVisible()}
											onCheckedChange={(value) =>
												column.toggleVisibility(!!value)
											}
											onSelect={(e) => e.preventDefault()}
										>
											{column.id}
										</DropdownMenuCheckboxItem>
									))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			)}
			<div
				className={cn(
					"rounded-md border border-border shadow-sm bg-card",
					isLoading && "overflow-hidden",
				)}
				ref={tableRef}
			>
				<Table className="w-full">
					<TableHeader className="bg-muted/30">
						{table.getHeaderGroups().map((headerGroup) => (
							<TableRow
								key={headerGroup.id}
								className="hover:bg-transparent border-b border-border"
							>
								{headerGroup.headers.map((header) => (
									<TableHead
										key={header.id}
										className={cn(
											"hover:bg-transparent transition-none text-card-foreground font-medium",
											header.column.columnDef.meta
												?.className,
										)}
									>
										{header.isPlaceholder
											? null
											: flexRender(
													header.column.columnDef
														.header,
													header.getContext(),
												)}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{isLoading ? (
							// Skeleton rows
							Array.from({ length: 5 }).map((_, i) => (
								<TableRow
									key={i}
									className="border-b border-border last:border-b-0"
								>
									{table.getAllColumns().map((column) => (
										<TableCell
											key={column.id}
											className={cn(
												"text-card-foreground",
												column.columnDef.meta
													?.className,
											)}
										>
											<Skeleton
												className={cn(
													"h-6 bg-muted/50",
													// Make month columns narrower but not too narrow
													column.id.length === 3 &&
														[
															"Jan",
															"Feb",
															"Mar",
															"Apr",
															"May",
															"Jun",
															"Jul",
															"Aug",
															"Sep",
															"Oct",
															"Nov",
															"Dec",
														].includes(column.id)
														? "w-10" // Wider for month columns
														: "w-[100px]",
												)}
											/>
										</TableCell>
									))}
								</TableRow>
							))
						) : table.getRowModel().rows?.length ? (
							table.getRowModel().rows.map((row, index) => (
								<TableRow
									key={row.id}
									data-row-id={row.id}
									className={cn(
										"border-b border-border last:border-b-0 hover:bg-muted/10 text-card-foreground",
										highlightedRowIndex === index &&
											"bg-primary/5",
										"transition-colors",
									)}
								>
									{row.getVisibleCells().map((cell) => (
										<TableCell
											key={cell.id}
											className={
												cell.column.columnDef.meta
													?.className
											}
										>
											{flexRender(
												cell.column.columnDef.cell,
												cell.getContext(),
											)}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell
									colSpan={columns.length}
									className="h-24 text-center text-muted-foreground"
								>
									No results.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			<DataTablePagination table={table} pagination={pagination} />
		</div>
	);
}
