"use client";

import { CountrySelect } from "@saas/shared/components/CountrySelect";
import { Alert, AlertDescription } from "@ui/components/alert";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { AlertTriangle, Clock, Info } from "lucide-react";
import type { Control } from "react-hook-form";
import { useWatch } from "react-hook-form";
import { AddressSelector } from "../ui-components/address-selector";

interface AddressInformationSectionProps {
	control: Control<any>;
	index: number;
}

export function AddressInformationSection({
	control,
	index,
}: AddressInformationSectionProps) {
	// Watch the stop type to show appropriate hint text
	const stopType = useWatch({
		control,
		name: `stops.${index}.stopType`,
	});

	// Watch the selected address to display operating hours and instructions
	const selectedAddress = useWatch({
		control,
		name: `stops.${index}.selectedAddress`,
	});

	// Also watch street to handle cases where address may be partially selected
	const street = useWatch({
		control,
		name: `stops.${index}.street`,
	});

	// Get the appropriate config based on stop type
	const getAddressConfig = () => {
		if (!selectedAddress) {
			return null;
		}

		// Try to get the config from the selectedAddress
		if (stopType?.toLowerCase() === "loading") {
			return selectedAddress.loadingConfig;
		}
		if (stopType?.toLowerCase() === "unloading") {
			return selectedAddress.unloadingConfig;
		}
		return null;
	};

	// Create a hint text based on stop type
	const getAddressHint = () => {
		if (!stopType) {
			return "Select from saved addresses";
		}

		return stopType.toLowerCase() === "loading"
			? "Select from loading addresses"
			: "Select from unloading addresses";
	};

	// Get config information
	const addressConfig = getAddressConfig();

	// Format operating hours for display
	const formatOperatingHours = (
		operatingHours: Record<string, string[]> | undefined,
	) => {
		if (!operatingHours) {
			return null;
		}

		const days = Object.keys(operatingHours);
		if (days.length === 0) {
			return null;
		}

		return (
			<div className="mt-4 space-y-2">
				<div className="flex items-center gap-2 text-sm font-medium">
					<Clock className="h-4 w-4 text-muted-foreground" />
					<h4>Operating Hours</h4>
				</div>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-xs">
					{days.map((day) => (
						<div key={day} className="flex justify-between">
							<span className="capitalize">{day}:</span>
							<span>{operatingHours[day].join(", ")}</span>
						</div>
					))}
				</div>
			</div>
		);
	};

	return (
		<div className="border rounded-md p-4 space-y-4">
			<h3 className="text-sm font-medium">Address Information</h3>

			{/* Address Selector Combobox */}
			<div className="mb-4">
				<p className="text-xs mb-2 font-medium">{getAddressHint()}</p>
				<AddressSelector index={index} />
			</div>

			<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
				{/* Entrance Number */}
				<FormField
					control={control}
					name={`stops.${index}.entrance_number`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Entrance Number</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter entrance number"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Street */}
				<FormField
					control={control}
					name={`stops.${index}.street`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Street</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter street"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Address Supplement */}
				<FormField
					control={control}
					name={`stops.${index}.addressSupplement`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Address Supplement</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter additional address info"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Zip Code */}
				<FormField
					control={control}
					name={`stops.${index}.zipCode`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Zip Code</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter zip code"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* City */}
				<FormField
					control={control}
					name={`stops.${index}.city`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>City</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter city"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Country */}
				<FormField
					control={control}
					name={`stops.${index}.country`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Country</FormLabel>
							<FormControl>
								<CountrySelect
									name={field.name}
									value={field.value || ""}
									onValueChange={field.onChange}
									placeholder="Select country"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Display Operating Hours and Instructions if available */}
			{selectedAddress && street && (
				<>
					<Separator className="my-2" />

					{/* Slot Booking Warning Section */}
					{addressConfig?.slotBooking && (
						<Alert variant="error" className="mt-4">
							<AlertTriangle className="h-4 w-4" />
							<AlertDescription>
								Slot booking is required for this{" "}
								{stopType?.toLowerCase()} address. Please ensure
								you have booked a time slot.
							</AlertDescription>
						</Alert>
					)}

					{/* Operating Hours Section */}
					{addressConfig?.operatingHours &&
						formatOperatingHours(addressConfig.operatingHours)}

					{/* Instructions Section */}
					{addressConfig?.instructions && (
						<div className="mt-4 space-y-2">
							<div className="flex items-center gap-2 text-sm font-medium">
								<Info className="h-4 w-4 text-muted-foreground" />
								<h4>
									{stopType?.toLowerCase() === "loading"
										? "Loading"
										: "Unloading"}{" "}
									Instructions
								</h4>
							</div>
							<p className="text-sm text-muted-foreground">
								{addressConfig.instructions}
							</p>
						</div>
					)}
				</>
			)}
		</div>
	);
}
