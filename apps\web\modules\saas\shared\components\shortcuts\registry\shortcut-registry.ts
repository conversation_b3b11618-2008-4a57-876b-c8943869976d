"use client";

import type { Messages } from "@repo/i18n";
import {
	Calendar,
	CommandIcon,
	ContactIcon,
	FileText,
	PackageIcon,
	Pencil,
	PlusCircle,
	TableIcon,
	Trash2Icon,
	UserPlusIcon,
} from "lucide-react";
import type React from "react";

/**
 * Shortcut Registry - Central system for managing keyboard shortcuts
 */

// Type for valid translation keys
export type ShortcutTranslationKey =
	keyof Messages["common"]["keyboard"]["actions"];

export interface ShortcutDefinition {
	id: string;
	combo: string;
	description: string;
	scope: string;
	translationKey?: ShortcutTranslationKey; // Optional translation key for i18n
}

/**
 * Capability Registry - System for tracking which shortcuts have functional implementations
 */
export interface CapabilityRegistry {
	registerCapability(
		shortcutId: string,
		hasCapability: boolean | (() => boolean),
	): void;
	hasCapability(shortcutId: string): boolean;
}

// Implementation of the capability registry
class CapabilityRegistryImpl implements CapabilityRegistry {
	private capabilities = new Map<string, boolean | (() => boolean)>();

	registerCapability(
		shortcutId: string,
		hasCapability: boolean | (() => boolean),
	) {
		this.capabilities.set(shortcutId, hasCapability);
	}

	hasCapability(shortcutId: string): boolean {
		// If no capability is registered, assume it's available
		if (!this.capabilities.has(shortcutId)) {
			return true;
		}

		const capability = this.capabilities.get(shortcutId);
		if (typeof capability === "function") {
			return capability();
		}
		return !!capability;
	}
}

// Create and export the singleton instance
export const capabilityRegistry = new CapabilityRegistryImpl();

// Type definitions for shortcut scopes
export interface ScopeDefinition {
	id: string;
	displayName: string;
	translationKey?: string; // Direct translation key
	priority: number;
	description: string;
	icon?: React.ComponentType<{ className?: string }>; // Icon component to use for this scope
}

// Registry of all shortcut scopes
export const SHORTCUT_SCOPES: Record<string, ScopeDefinition> = {
	"*": {
		id: "*",
		displayName: "global",
		translationKey: "common.keyboard.categories.global",
		priority: 0,
		description: "Available throughout the application",
		icon: CommandIcon,
	},
	table: {
		id: "table",
		displayName: "table",
		translationKey: "common.keyboard.categories.table",
		priority: 10,
		description: "Available when interacting with any data table",
		icon: TableIcon,
	},
	"contact-shortcuts": {
		id: "contact-shortcuts",
		displayName: "contact",
		translationKey: "common.keyboard.categories.contact",
		priority: 20,
		description: "Available when working with contacts",
		icon: ContactIcon,
	},
	"contact-view": {
		id: "contact-view",
		displayName: "contact view",
		translationKey: "common.keyboard.categories.contact-view",
		priority: 25,
		description: "Available when viewing a contact",
		icon: ContactIcon,
	},
	"contact-delete-dialog": {
		id: "contact-delete-dialog",
		displayName: "deleteContactDialog",
		translationKey: "common.keyboard.categories.deleteContactDialog",
		priority: 100,
		description: "Available when confirming contact deletion",
		icon: Trash2Icon,
	},
	"delete-dialog": {
		id: "delete-dialog",
		displayName: "deleteDialog",
		translationKey: "common.keyboard.categories.deleteDialog",
		priority: 90,
		description: "Available in any deletion confirmation dialog",
		icon: Trash2Icon,
	},
	"create-contact-shortcuts": {
		id: "create-contact-shortcuts",
		displayName: "contact-create",
		translationKey: "common.keyboard.categories.contact-create",
		priority: 20,
		description: "Available when creating a new contact",
		icon: UserPlusIcon,
	},
	"personnel-shortcuts": {
		id: "personnel-shortcuts",
		displayName: "personnel",
		translationKey: "common.keyboard.categories.personnel",
		priority: 20,
		description: "Available when working with personnel",
		icon: UserPlusIcon,
	},
	"personnel-view": {
		id: "personnel-view",
		displayName: "personnel view",
		translationKey: "common.keyboard.categories.personnel-view",
		priority: 25,
		description: "Available when viewing personnel details",
		icon: UserPlusIcon,
	},
	"personnel-delete-dialog": {
		id: "personnel-delete-dialog",
		displayName: "deletePersonnelDialog",
		translationKey: "common.keyboard.categories.deletePersonnelDialog",
		priority: 100,
		description: "Available when confirming personnel deletion",
		icon: Trash2Icon,
	},
	"license-form-dialog": {
		id: "license-form-dialog",
		displayName: "licenseFormDialog",
		translationKey: "common.keyboard.categories.licenseFormDialog",
		priority: 110,
		description: "Available when editing a license",
		icon: Pencil,
	},
	"license-delete-dialog": {
		id: "license-delete-dialog",
		displayName: "licenseDeleteDialog",
		translationKey: "common.keyboard.categories.licenseDeleteDialog",
		priority: 110,
		description: "Available when confirming license deletion",
		icon: Trash2Icon,
	},
	"language-form-dialog": {
		id: "language-form-dialog",
		displayName: "languageFormDialog",
		translationKey: "common.keyboard.categories.languageFormDialog",
		priority: 110,
		description: "Available when editing a language",
		icon: Pencil,
	},
	"language-delete-dialog": {
		id: "language-delete-dialog",
		displayName: "languageDeleteDialog",
		translationKey: "common.keyboard.categories.languageDeleteDialog",
		priority: 110,
		description: "Available when confirming language deletion",
		icon: Trash2Icon,
	},
	"absence-create-dialog": {
		id: "absence-create-dialog",
		displayName: "absenceCreateDialog",
		translationKey: "common.keyboard.categories.absenceCreateDialog",
		priority: 110,
		description: "Available when creating a new absence",
		icon: PlusCircle,
	},
	"absence-history-sheet": {
		id: "absence-history-sheet",
		displayName: "absenceHistorySheet",
		translationKey: "common.keyboard.categories.absenceHistorySheet",
		priority: 110,
		description: "Available when viewing absence history",
		icon: FileText,
	},
	"personnel-tabs": {
		id: "personnel-tabs",
		displayName: "personnel tabs",
		translationKey: "common.keyboard.categories.personnel-tabs",
		priority: 25,
		description: "Available when viewing the personnel tabs",
		icon: UserPlusIcon,
	},
	"calendar-view": {
		id: "calendar-view",
		displayName: "calendar view",
		translationKey: "common.keyboard.categories.calendar-view",
		priority: 25,
		description: "Available when viewing calendar",
		icon: Calendar,
	},
	"calendar-date-focus": {
		id: "calendar-date-focus",
		displayName: "Calendar Date Focus",
		translationKey: "common.keyboard.categories.calendar-date-focus",
		priority: 45,
		description:
			"Keyboard shortcuts for navigating between calendar dates with keyboard focus",
		icon: Calendar,
	},
	"calendar-event-focus": {
		id: "calendar-event-focus",
		displayName: "calendar event focus",
		translationKey: "common.keyboard.categories.calendar-event-focus",
		priority: 50,
		description:
			"Keyboard shortcuts for navigating between events on a selected date",
		icon: Calendar,
	},
	"orders-shortcuts": {
		id: "orders-shortcuts",
		displayName: "orders",
		translationKey: "common.keyboard.categories.orders",
		priority: 20,
		description: "Available when working with orders",
		icon: PackageIcon,
	},
	"orders-view": {
		id: "orders-view",
		displayName: "orders view",
		translationKey: "common.keyboard.categories.orders-view",
		priority: 25,
		description: "Available when viewing order details",
		icon: PackageIcon,
	},
	"orders-delete-dialog": {
		id: "orders-delete-dialog",
		displayName: "deleteOrderDialog",
		translationKey: "common.keyboard.categories.deleteOrderDialog",
		priority: 100,
		description: "Available when confirming order deletion",
		icon: Trash2Icon,
	},
};

// Function to get scope display name
export function getScopeDisplayName(scope: string): string {
	return SHORTCUT_SCOPES[scope]?.displayName || scope;
}

// Core shortcut definitions - these are registered by default
export const CORE_SHORTCUTS: ShortcutDefinition[] = [
	// {
	// 	id: "command",
	// 	combo: "ctrl+k",
	// 	description: "Open command palette",
	// 	scope: "*",
	// 	translationKey: "openCommand",
	// },
	{
		id: "help",
		combo: "?",
		description: "Show keyboard shortcuts",
		scope: "*",
		translationKey: "showShortcuts",
	},
];

// Component-specific shortcut definitions that will be registered by components
export const TABLE_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "table-search",
		combo: "ctrl+s",
		description: "Focus search field",
		scope: "table",
		translationKey: "focusSearch",
	},
	{
		id: "table-next-row",
		combo: "arrowdown",
		description: "Highlight next row",
		scope: "table",
		translationKey: "nextRow",
	},
	{
		id: "table-previous-row",
		combo: "arrowup",
		description: "Highlight previous row",
		scope: "table",
		translationKey: "previousRow",
	},
	{
		id: "table-clear-highlight",
		combo: "esc",
		description: "Clear row highlight",
		scope: "table",
		translationKey: "clearHighlight",
	},
	{
		id: "table-view-row",
		combo: "enter",
		description: "View highlighted row",
		scope: "table",
		translationKey: "viewRow",
	},
	{
		id: "table-edit-row",
		combo: "ctrl+e",
		description: "Edit highlighted row",
		scope: "table",
		translationKey: "editRow",
	},
	{
		id: "table-delete-row",
		combo: "ctrl+d",
		description: "Delete highlighted row",
		scope: "table",
		translationKey: "deleteRow",
	},
	{
		id: "table-next-page",
		combo: "pagedown",
		description: "Go to next page",
		scope: "table",
		translationKey: "nextPage",
	},
	{
		id: "table-previous-page",
		combo: "pageup",
		description: "Go to previous page",
		scope: "table",
		translationKey: "previousPage",
	},
];

// Contact-specific shortcuts
export const CONTACT_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "contacts-create",
		combo: "ctrl+n",
		description: "Create new contact",
		scope: "contact-shortcuts",
		translationKey: "createContact",
	},
];

// Delete dialog shortcuts
export const DELETE_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "delete-dialog-confirm",
		combo: "enter",
		description: "Confirm delete",
		scope: "contact-delete-dialog",
		translationKey: "confirmDelete",
	},
	{
		id: "delete-dialog-cancel",
		combo: "esc",
		description: "Cancel delete",
		scope: "contact-delete-dialog",
		translationKey: "cancelDelete",
	},
	{
		id: "delete-dialog-left",
		combo: "left",
		description: "Focus cancel button",
		scope: "contact-delete-dialog",
		translationKey: "focusCancelButton",
	},
	{
		id: "delete-dialog-right",
		combo: "right",
		description: "Focus confirm button",
		scope: "contact-delete-dialog",
		translationKey: "focusConfirmButton",
	},
];

// Create Contact wizard shortcuts
export const CREATE_CONTACT_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "create-contact-next",
		combo: "right",
		description: "Next step",
		scope: "create-contact-shortcuts",
		translationKey: "contactNextStep",
	},
	{
		id: "create-contact-previous",
		combo: "left",
		description: "Previous step",
		scope: "create-contact-shortcuts",
		translationKey: "contactPreviousStep",
	},
	{
		id: "create-contact-jump",
		combo: "1-9",
		description: "Jump to step",
		scope: "create-contact-shortcuts",
		translationKey: "contactJumpToStep",
	},
	{
		id: "create-contact-validate-vat",
		combo: "alt+v",
		description: "Validate VAT",
		scope: "create-contact-shortcuts",
		translationKey: "contactValidateVat",
	},
];

// Define contact view shortcuts
export const CONTACT_VIEW_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "contact.next-tab",
		combo: "ArrowRight",
		description: "Navigate to next tab",
		translationKey: "nextTab",
		scope: "contact-view",
	},
	{
		id: "contact.prev-tab",
		combo: "ArrowLeft",
		description: "Navigate to previous tab",
		translationKey: "prevTab",
		scope: "contact-view",
	},
	{
		id: "contact.back",
		combo: "Escape",
		description: "Back to contacts list",
		translationKey: "backToList",
		scope: "contact-view",
	},
];

// Define personnel view shortcuts
export const PERSONNEL_VIEW_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "personnel.next-tab",
		combo: "ArrowRight",
		description: "Navigate to next tab",
		translationKey: "nextTab",
		scope: "personnel-view",
	},
	{
		id: "personnel.prev-tab",
		combo: "ArrowLeft",
		description: "Navigate to previous tab",
		translationKey: "prevTab",
		scope: "personnel-view",
	},
	{
		id: "personnel.edit-mode",
		combo: "ctrl+e",
		description: "Toggle edit mode",
		scope: "personnel-view",
	},
	{
		id: "personnel.save-changes",
		combo: "ctrl+s",
		description: "Save changes",
		scope: "personnel-view",
	},
	{
		id: "personnel.cancel-edit",
		combo: "Escape",
		description: "Cancel edit",
		scope: "personnel-view",
	},
	// License management shortcuts
	{
		id: "personnel.add-license",
		combo: "ctrl+n",
		description: "Add new license",
		scope: "personnel-view",
	},
	{
		id: "personnel.next-license",
		combo: "arrowdown",
		description: "Navigate to next license",
		scope: "personnel-view",
	},
	{
		id: "personnel.prev-license",
		combo: "arrowup",
		description: "Navigate to previous license",
		scope: "personnel-view",
	},
	{
		id: "personnel.edit-license",
		combo: "ctrl+e",
		description: "Edit selected license",
		scope: "personnel-view",
	},
	{
		id: "personnel.delete-license",
		combo: "ctrl+d",
		description: "Delete selected license",
		scope: "personnel-view",
	},
];

// Add Personnel-specific shortcuts after CONTACT_SHORTCUTS
export const PERSONNEL_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "personnel-create",
		combo: "ctrl+n",
		description: "Create new personnel",
		scope: "personnel-shortcuts",
		translationKey: "createPersonnel",
	},
];

// Define shortcuts for license dialogs
export const LICENSE_FORM_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "license-form.submit",
		combo: "ctrl+enter",
		description: "Save license",
		scope: "license-form-dialog",
	},
	{
		id: "license-form.cancel",
		combo: "escape",
		description: "Cancel editing",
		scope: "license-form-dialog",
	},
];

export const LICENSE_DELETE_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "license-delete.confirm",
		combo: "enter",
		description: "Confirm delete",
		scope: "license-delete-dialog",
	},
	{
		id: "license-delete.cancel",
		combo: "escape",
		description: "Cancel delete",
		scope: "license-delete-dialog",
	},
	{
		id: "license-delete.left",
		combo: "left",
		description: "Focus cancel button",
		scope: "license-delete-dialog",
	},
	{
		id: "license-delete.right",
		combo: "right",
		description: "Focus confirm button",
		scope: "license-delete-dialog",
	},
];

// Define shortcuts for language dialogs
export const LANGUAGE_FORM_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "language-form.submit",
		combo: "ctrl+enter",
		description: "Save language",
		scope: "language-form-dialog",
	},
	{
		id: "language-form.cancel",
		combo: "escape",
		description: "Cancel editing",
		scope: "language-form-dialog",
	},
];

export const LANGUAGE_DELETE_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "language-delete.confirm",
		combo: "enter",
		description: "Confirm delete",
		scope: "language-delete-dialog",
	},
	{
		id: "language-delete.cancel",
		combo: "escape",
		description: "Cancel delete",
		scope: "language-delete-dialog",
	},
	{
		id: "language-delete.left",
		combo: "left",
		description: "Focus cancel button",
		scope: "language-delete-dialog",
	},
	{
		id: "language-delete.right",
		combo: "right",
		description: "Focus confirm button",
		scope: "language-delete-dialog",
	},
];

// Define shortcuts for absence create dialog
export const ABSENCE_CREATE_DIALOG_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "absence-create.submit",
		combo: "ctrl+enter",
		description: "Create absence",
		scope: "absence-create-dialog",
	},
	{
		id: "absence-create.cancel",
		combo: "escape",
		description: "Cancel creation",
		scope: "absence-create-dialog",
	},
	{
		id: "absence-create.add-range",
		combo: "ctrl+n",
		description: "Add new date range",
		scope: "absence-create-dialog",
	},
];

// Define absence history sheet shortcuts
export const ABSENCE_HISTORY_SHEET_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "absence-history.close",
		combo: "escape",
		description: "Close history sheet",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.focus-filters",
		combo: "ctrl+f",
		description: "Focus year/type filters",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.next-row",
		combo: "ArrowDown",
		description: "Select next absence",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.prev-row",
		combo: "ArrowUp",
		description: "Select previous absence",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.view-document",
		combo: "enter",
		description: "View document of selected absence",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.download-document",
		combo: "ctrl+d",
		description: "Download document of selected absence",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.upload-document",
		combo: "ctrl+u",
		description: "Upload document for selected absence",
		scope: "absence-history-sheet",
	},
	{
		id: "absence-history.delete-absence",
		combo: "Delete",
		description: "Delete selected absence",
		scope: "absence-history-sheet",
	},
];

// Define personnel tabs shortcuts
export const PERSONNEL_TABS_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "personnel-tabs.next-tab",
		combo: "ArrowRight",
		description: "Navigate to next tab",
		translationKey: "nextTab",
		scope: "personnel-tabs",
	},
	{
		id: "personnel-tabs.prev-tab",
		combo: "ArrowLeft",
		description: "Navigate to previous tab",
		translationKey: "prevTab",
		scope: "personnel-tabs",
	},
	{
		id: "personnel-tabs.create-absence",
		combo: "ctrl+n",
		description: "Create new absence",
		scope: "personnel-tabs",
		translationKey: "createAbsence",
	},
];

// Define calendar view shortcuts
export const CALENDAR_VIEW_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "calendar.next-month",
		combo: "pagedown",
		description: "Go to next month",
		scope: "calendar-view",
		translationKey: "nextMonth",
	},
	{
		id: "calendar.prev-month",
		combo: "pageup",
		description: "Go to previous month",
		scope: "calendar-view",
		translationKey: "prevMonth",
	},
	{
		id: "calendar.today",
		combo: "home",
		description: "Go to today",
		scope: "calendar-view",
		translationKey: "goToToday",
	},
	{
		id: "calendar.focus-dates",
		combo: "f",
		description: "Focus on dates",
		scope: "calendar-view",
		translationKey: "focusDates",
	},
];

export const CALENDAR_DATE_FOCUS_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "calendar.move-right",
		combo: "arrowright",
		description: "Move to next day",
		scope: "calendar-date-focus",
		translationKey: "nextDay",
	},
	{
		id: "calendar.move-left",
		combo: "arrowleft",
		description: "Move to previous day",
		scope: "calendar-date-focus",
		translationKey: "prevDay",
	},
	{
		id: "calendar.move-up",
		combo: "arrowup",
		description: "Move to previous week",
		scope: "calendar-date-focus",
		translationKey: "prevWeek",
	},
	{
		id: "calendar.move-down",
		combo: "arrowdown",
		description: "Move to next week",
		scope: "calendar-date-focus",
		translationKey: "nextWeek",
	},
	{
		id: "calendar.select-date",
		combo: "space",
		description: "Select/view highlighted date",
		scope: "calendar-date-focus",
		translationKey: "selectDate",
	},
	{
		id: "calendar.exit-focus",
		combo: "escape",
		description: "Exit date focus mode",
		scope: "calendar-date-focus",
		translationKey: "exitFocus",
	},
];

// Define calendar event focus shortcuts
export const CALENDAR_EVENT_FOCUS_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "calendar-event-focus-next",
		combo: "arrowdown",
		description: "Navigate to next event",
		scope: "calendar-event-focus",
		translationKey: "nextEvent",
	},
	{
		id: "calendar-event-focus-previous",
		combo: "arrowup",
		description: "Navigate to previous event",
		scope: "calendar-event-focus",
		translationKey: "prevEvent",
	},
	{
		id: "calendar-event-focus-view",
		combo: "space",
		description: "View event details",
		scope: "calendar-event-focus",
		translationKey: "viewEvent",
	},
	{
		id: "calendar-event-focus-view-alt",
		combo: "enter",
		description: "View event details",
		scope: "calendar-event-focus",
		translationKey: "viewEvent",
	},
	{
		id: "calendar-event-focus-exit",
		combo: "escape",
		description: "Return to date focus",
		scope: "calendar-event-focus",
		translationKey: "exitEventFocus",
	},
];

// Add Orders-specific shortcuts
export const ORDER_SHORTCUTS: ShortcutDefinition[] = [
	{
		id: "orders-create",
		combo: "ctrl+n",
		description: "Create new order",
		scope: "orders-shortcuts",
	},
];

// Helper function to get all shortcuts for a scope
export function getShortcutsByScope(scope: string): ShortcutDefinition[] {
	// For contact-shortcuts, we want to include both table shortcuts and contact-specific shortcuts
	if (scope === "contact-shortcuts") {
		return [
			...TABLE_SHORTCUTS.map((shortcut) => ({
				...shortcut,
				scope: "contact-shortcuts",
			})),
			...CONTACT_SHORTCUTS,
		];
	}

	// For personnel-shortcuts, include both table shortcuts and personnel-specific shortcuts
	if (scope === "personnel-shortcuts") {
		return [
			...TABLE_SHORTCUTS.map((shortcut) => ({
				...shortcut,
				scope: "personnel-shortcuts",
			})),
			...PERSONNEL_SHORTCUTS,
		];
	}

	// For orders-shortcuts, include both table shortcuts and orders-specific shortcuts
	if (scope === "orders-shortcuts") {
		return [
			...TABLE_SHORTCUTS.map((shortcut) => ({
				...shortcut,
				scope: "orders-shortcuts",
			})),
			...ORDER_SHORTCUTS,
		];
	}

	// For other scopes, return the appropriate predefined shortcuts
	switch (scope) {
		case "*":
			return CORE_SHORTCUTS;
		case "table":
			return TABLE_SHORTCUTS;
		case "contact-delete-dialog":
			return DELETE_DIALOG_SHORTCUTS;
		case "personnel-delete-dialog":
			return DELETE_DIALOG_SHORTCUTS.map((shortcut) => ({
				...shortcut,
				scope: "personnel-delete-dialog",
			}));
		case "create-contact-shortcuts":
			return CREATE_CONTACT_SHORTCUTS;
		case "contact-view":
			return CONTACT_VIEW_SHORTCUTS;
		case "personnel-view":
			return PERSONNEL_VIEW_SHORTCUTS;
		case "personnel-tabs":
			return PERSONNEL_TABS_SHORTCUTS;
		case "license-form-dialog":
			return LICENSE_FORM_DIALOG_SHORTCUTS;
		case "license-delete-dialog":
			return LICENSE_DELETE_DIALOG_SHORTCUTS;
		case "language-form-dialog":
			return LANGUAGE_FORM_DIALOG_SHORTCUTS;
		case "language-delete-dialog":
			return LANGUAGE_DELETE_DIALOG_SHORTCUTS;
		case "absence-create-dialog":
			return ABSENCE_CREATE_DIALOG_SHORTCUTS;
		case "absence-history-sheet":
			return ABSENCE_HISTORY_SHEET_SHORTCUTS;
		case "calendar-view":
			return CALENDAR_VIEW_SHORTCUTS;
		case "calendar-date-focus":
			return CALENDAR_DATE_FOCUS_SHORTCUTS;
		case "calendar-event-focus":
			return CALENDAR_EVENT_FOCUS_SHORTCUTS;
		case "orders-delete-dialog":
			return DELETE_DIALOG_SHORTCUTS.map((shortcut) => ({
				...shortcut,
				scope: "orders-delete-dialog",
			}));
		case "absence-table-shortcuts":
			return [];
		default:
			return [];
	}
}

// Helper function to get scope priority
export function getScopePriority(scope: string): number {
	return SHORTCUT_SCOPES[scope]?.priority || 50;
}
