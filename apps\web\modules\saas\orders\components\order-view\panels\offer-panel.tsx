"use client";

import type { OfferStatus } from "@repo/api/src/routes/offers/types";
import { OfferEmailPreview } from "@saas/offers/components/offer-email-preview";
import { useOfferMutations, useOffers } from "@saas/offers/hooks/use-offer";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ScrollArea } from "@ui/components/scroll-area";
import { format } from "date-fns";
import {
	CheckCircle,
	ExternalLink,
	FileText,
	Loader2,
	Mail,
	MoreHorizontal,
	SendHorizontal,
	XCircle,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

// Define the email log interface
interface EmailLog {
	id: string;
	status: string;
	sentAt: string;
	offerId?: string;
}

// Define an extended offer type that includes email logs
interface OfferWithEmailLogs {
	id: string;
	status: string;
	createdAt: string;
	valid_until?: string | null;
	internal_comment?: string | null;
	decline_reason?: string | null;
	declined_at?: string | null;
	accepted_at?: string | null;
	order?: {
		id: string;
		customer_order_number?: string | null;
		order_number?: string | null;
	} | null;
	contact?: {
		email: string | null;
	} | null;
	customer?: {
		email: string | null;
	} | null;
	prices?: Array<{
		id: string;
		totalPrice: number;
		currency: string;
		type: string;
	}>;
	emailLogs?: EmailLog[];
	// Other offer properties can be added as needed
}

interface OffersPanelProps {
	orderId: string;
	onCreateOffer?: () => void;
}

export function OffersPanel({ orderId, onCreateOffer }: OffersPanelProps) {
	const t = useTranslations();
	const [offerToCancel, setOfferToCancel] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [sendingEmailId, setSendingEmailId] = useState<string | null>(null);
	const [previewOfferId, setPreviewOfferId] = useState<string | null>(null);
	const [customRecipient, setCustomRecipient] = useState<string | undefined>(
		undefined,
	);

	// Fetch offers data for this specific order
	const { data, isLoading, error, refetch } = useOffers(orderId);

	const { acceptOffer, cancelOffer, sendOfferEmail } = useOfferMutations();

	const offers = data?.items || [];

	const handleAccept = async (offerId: string) => {
		setIsProcessing(true);
		try {
			await acceptOffer(offerId);
		} catch (error) {
			console.error(error);
		} finally {
			setIsProcessing(false);
		}
	};

	const handleCancel = async (offerId: string) => {
		setOfferToCancel(offerId);
	};

	const confirmCancel = async () => {
		if (!offerToCancel) {
			return;
		}

		setIsProcessing(true);
		try {
			await cancelOffer(offerToCancel);
		} catch (error) {
			console.error(error);
		} finally {
			setIsProcessing(false);
			setOfferToCancel(null);
		}
	};

	const cancelAction = () => {
		setOfferToCancel(null);
	};

	const handleSend = async (offerId: string, recipientEmail?: string) => {
		setPreviewOfferId(offerId);
		setCustomRecipient(recipientEmail);
	};

	const handleConfirmSend = async (offerId: string) => {
		setSendingEmailId(offerId);
		try {
			await sendOfferEmail({
				id: offerId,
				recipientEmail: customRecipient,
			});
			// Success is handled by the toast in the mutation
		} catch (error) {
			// Error is handled by the toast in the mutation
			console.error("Failed to send email:", error);
		} finally {
			setSendingEmailId(null);
			setPreviewOfferId(null);
			setCustomRecipient(undefined);
		}
	};

	const getStatusBadge = (status: OfferStatus) => {
		const statusConfig = {
			open: { label: "Open", status: "info" },
			accepted: { label: "Accepted", status: "success" },
			cancelled: { label: "Cancelled", status: "error" },
			expired: { label: "Expired", status: "warning" },
		} as const;

		const config = statusConfig[status] || {
			label: status,
			status: "default",
		};
		return (
			<Badge status={config.status as any} className="ml-2">
				{config.label}
			</Badge>
		);
	};

	const formatCurrency = (
		amount?: string | number | null,
		currency = "EUR",
	) => {
		if (!amount && amount !== 0) {
			return "-";
		}
		return new Intl.NumberFormat("de-DE", {
			style: "currency",
			currency,
		}).format(Number(amount));
	};

	const calculateOfferTotal = (offer: OfferWithEmailLogs) => {
		if (!offer.prices || offer.prices.length === 0) {
			return { total: 0, currency: "EUR" };
		}

		// Sum all prices (all line items are now revenue-only)
		const total = offer.prices.reduce(
			(sum, price) => sum + price.totalPrice,
			0,
		);
		const currency = offer.prices[0]?.currency || "EUR";

		return { total, currency };
	};

	const formatDate = (date?: string | Date | null) => {
		if (!date) {
			return "-";
		}
		return format(new Date(date), "PP");
	};

	const formatDateTime = (date?: string | Date | null) => {
		if (!date) {
			return "-";
		}
		return format(new Date(date), "PP p"); // Format with date and time
	};

	return (
		<div className="space-y-2">
			{/* <div className="flex items-center justify-between">
				<h3 className="font-medium">Offers</h3>
			</div> */}

			{isLoading ? (
				<div className="flex justify-center items-center py-6">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
				</div>
			) : error ? (
				<div className="text-center py-4 text-sm text-destructive">
					Error loading offers
				</div>
			) : offers.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No offers created for this order
				</div>
			) : (
				<ScrollArea className="h-[300px] pr-3 -mr-3">
					<div className="space-y-3">
						{offers.map((originalOffer) => {
							// Cast to our extended type
							const offer =
								originalOffer as unknown as OfferWithEmailLogs;
							return (
								<div
									key={offer.id}
									className="border rounded-lg p-4 bg-card"
								>
									{/* Header with Offer Number and Status */}
									<div className="flex items-center justify-between mb-3">
										<div className="flex items-center gap-2">
											<FileText className="h-4 w-4 text-muted-foreground" />
											<h4 className="font-semibold text-sm">
												Offer #
												{offer.order?.order_number}
											</h4>
										</div>
										<div className="flex items-center gap-2">
											{getStatusBadge(
												offer.status as OfferStatus,
											)}
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 p-0"
													>
														<MoreHorizontal className="h-4 w-4" />
														<span className="sr-only">
															Actions
														</span>
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													{offer.status ===
														"open" && (
														<>
															<DropdownMenuItem
																onClick={() =>
																	handleAccept(
																		offer.id,
																	)
																}
																disabled={
																	isProcessing
																}
															>
																<CheckCircle className="h-3.5 w-3.5 mr-2" />
																Accept
															</DropdownMenuItem>
															<DropdownMenuItem
																onClick={() =>
																	handleSend(
																		offer.id,
																		offer
																			.contact
																			?.email ||
																			undefined,
																	)
																}
																disabled={
																	isProcessing ||
																	sendingEmailId ===
																		offer.id
																}
															>
																{sendingEmailId ===
																offer.id ? (
																	<>
																		<Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
																		Sending...
																	</>
																) : (
																	<>
																		<SendHorizontal className="h-3.5 w-3.5 mr-2" />
																		Send
																	</>
																)}
															</DropdownMenuItem>
														</>
													)}
													<DropdownMenuItem>
														<ExternalLink className="h-3.5 w-3.5 mr-2" />
														View Details
													</DropdownMenuItem>
													{offer.status ===
														"open" && (
														<DropdownMenuItem
															className="text-destructive"
															onClick={() =>
																handleCancel(
																	offer.id,
																)
															}
															disabled={
																isProcessing
															}
														>
															<XCircle className="h-3.5 w-3.5 mr-2" />
															Cancel
														</DropdownMenuItem>
													)}
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</div>

									{/* Offer Details Grid */}
									<div className="grid grid-cols-2 gap-4 mb-3 text-sm">
										<div>
											<p className="text-muted-foreground text-xs">
												Created:
											</p>
											<p className="font-medium">
												{formatDate(offer.createdAt)}
											</p>
										</div>
										{offer.valid_until && (
											<div>
												<p className="text-muted-foreground text-xs">
													Valid until:
												</p>
												<p className="font-medium">
													{formatDate(
														offer.valid_until,
													)}
												</p>
											</div>
										)}
										{offer.accepted_at && (
											<div className="col-span-2">
												<p className="text-muted-foreground text-xs">
													Accepted at:
												</p>
												<p className="font-medium">
													{formatDate(
														offer.accepted_at,
													)}
												</p>
											</div>
										)}
										{offer.declined_at && (
											<div className="col-span-2">
												<p className="text-muted-foreground text-xs">
													Declined at:
												</p>
												<p className="font-medium">
													{formatDate(
														offer.declined_at,
													)}
												</p>
											</div>
										)}
									</div>

									{/* Amount - Prominent Display */}
									<div className="mb-3 p-3 bg-muted/50 rounded-md">
										<p className="text-muted-foreground text-xs">
											Amount:
										</p>
										<p className="font-bold text-lg">
											{(() => {
												const { total, currency } =
													calculateOfferTotal(offer);
												return formatCurrency(
													total,
													currency,
												);
											})()}
										</p>
									</div>

									{offer.decline_reason && (
										<div className="mb-3 p-3 bg-destructive/10 dark:bg-destructive/20 rounded-md border border-destructive/20 dark:border-destructive/30">
											<div className="flex items-center gap-2 mb-1">
												<XCircle className="h-4 w-4 text-destructive" />
												<span className="font-medium text-sm text-destructive">
													Decline Reason:
												</span>
											</div>
											<p className="text-sm text-destructive/80 dark:text-destructive/70">
												{offer.decline_reason}
											</p>
										</div>
									)}

									{offer.internal_comment && (
										<div className="mt-2 text-xs italic text-muted-foreground">
											{offer.internal_comment}
										</div>
									)}

									{/* Email History - Enhanced */}
									{offer.emailLogs &&
										offer.emailLogs.length > 0 && (
											<div className="mb-3 p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-md border border-blue-200/50 dark:border-blue-800/50">
												<div className="flex items-center gap-2 mb-2">
													<Mail className="h-4 w-4 text-blue-600 dark:text-blue-400" />
													<span className="font-medium text-sm text-blue-900 dark:text-blue-100">
														Email History:
													</span>
												</div>
												<div className="space-y-2">
													{offer.emailLogs
														.slice(0, 3)
														.map((log) => (
															<div
																key={log.id}
																className="flex items-center justify-between text-xs"
															>
																<div className="flex items-center gap-2">
																	<Badge
																		status={
																			log.status ===
																			"SENT"
																				? "success"
																				: "warning"
																		}
																		className="h-5 px-2 text-[10px] font-medium"
																	>
																		{
																			log.status
																		}
																	</Badge>
																	<span className="text-muted-foreground">
																		{formatDateTime(
																			log.sentAt,
																		)}
																	</span>
																</div>
															</div>
														))}
													{offer.emailLogs.length >
														3 && (
														<div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
															+
															{offer.emailLogs
																.length -
																3}{" "}
															more
														</div>
													)}
												</div>
											</div>
										)}

									{/* Action buttons for common actions */}
									{offer.status === "open" && (
										<div className="flex gap-2 mt-3">
											<Button
												variant="outline"
												size="sm"
												className="h-7 text-xs"
												onClick={() =>
													handleAccept(offer.id)
												}
												disabled={isProcessing}
											>
												<CheckCircle className="h-3 w-3 mr-1" />
												Accept
											</Button>
											<Button
												variant="outline"
												size="sm"
												className="h-7 text-xs"
												onClick={() =>
													handleSend(offer.id)
												}
												disabled={
													isProcessing ||
													sendingEmailId === offer.id
												}
											>
												{sendingEmailId === offer.id ? (
													<>
														<Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
														Sending...
													</>
												) : (
													<>
														<SendHorizontal className="h-3.5 w-3.5 mr-2" />
														Send
													</>
												)}
											</Button>
										</div>
									)}
								</div>
							);
						})}
					</div>
				</ScrollArea>
			)}

			<AlertDialog
				open={offerToCancel !== null}
				onOpenChange={cancelAction}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Cancel this offer?</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. The offer will be
							marked as cancelled and can no longer be accepted.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>No, keep it</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmCancel}
							disabled={isProcessing}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							{isProcessing
								? "Cancelling..."
								: "Yes, cancel offer"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			<OfferEmailPreview
				offerId={previewOfferId}
				isOpen={previewOfferId !== null}
				onClose={() => {
					setPreviewOfferId(null);
					setCustomRecipient(undefined);
				}}
				onSend={handleConfirmSend}
				isSending={!!sendingEmailId}
				recipientEmail={customRecipient}
			/>
		</div>
	);
}
