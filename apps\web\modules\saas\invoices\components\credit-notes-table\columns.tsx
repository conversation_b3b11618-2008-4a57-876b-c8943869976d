"use client";

import type { CreditNote } from "@repo/database";
import { useCreditNotesUI } from "@saas/invoices/context/credit-notes-ui-context";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { format } from "date-fns";
import {
	Calendar,
	CreditCard,
	FileText,
	MoreHorizontal,
	Pencil,
	Receipt,
	Tag,
	Trash,
	User,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

type ActionsCellProps = {
	row: Row<CreditNote>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const creditNote = row.original;
	const {
		handleDeleteCreditNote,
		handleViewCreditNote,
		handleEditCreditNote,
	} = useCreditNotesUI();

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>Actions</DropdownMenuLabel>
				<DropdownMenuItem
					onClick={() => handleEditCreditNote(creditNote.id)}
				>
					<Pencil className="mr-2 h-4 w-4" />
					Edit
				</DropdownMenuItem>

				<DropdownMenuSeparator />

				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteCreditNote(creditNote)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					Delete
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<CreditNote>[] {
	const {
		handleDeleteCreditNote,
		handleViewCreditNote,
		handleEditCreditNote,
	} = useCreditNotesUI();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "credit_note_number",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title="Credit Note Number"
				/>
			),
			cell: ({ row }) => {
				const creditNoteNumber = row.getValue("credit_note_number") as
					| string
					| null
					| undefined;
				return (
					<div className="flex items-center gap-2">
						<Receipt className="h-4 w-4 text-muted-foreground" />
						<span className="font-medium">
							{creditNoteNumber || "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "customer",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Customer" />
			),
			cell: ({ row }) => {
				const customer = row.getValue("customer") as {
					id: string;
					nameLine1: string;
					nameLine2: string | null;
				} | null;

				const customerName = customer
					? `${customer.nameLine1}${customer.nameLine2 ? ` ${customer.nameLine2}` : ""}`
					: "-";

				return (
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-muted-foreground" />
						{customer?.id ? (
							<Link
								href={`/app/${params.organizationSlug}/contacts/${customer.id}`}
								className="hover:underline cursor-pointer"
							>
								<span>{customerName}</span>
							</Link>
						) : (
							<span>{customerName}</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "order_allocations",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Related Orders" />
			),
			cell: ({ row }) => {
				const orderCreditNotes =
					(row.original as any).orderCreditNotes || [];

				return (
					<div className="flex items-center gap-2">
						<Tag className="h-4 w-4 text-muted-foreground" />
						{orderCreditNotes.length > 0 ? (
							<div className="flex flex-col gap-1">
								{orderCreditNotes.map(
									(ocn: any, index: number) => (
										<div key={index}>
											{ocn.order?.id ? (
												<Link
													href={`/app/${params.organizationSlug}/orders/${ocn.order.id}`}
													className="hover:underline cursor-pointer"
												>
													<span>
														{ocn.order
															?.order_number ||
															"-"}
													</span>
												</Link>
											) : (
												<span>
													{ocn.order?.order_number ||
														"-"}
												</span>
											)}
										</div>
									),
								)}
							</div>
						) : (
							<span>-</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "credit_note_date",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Date" />
			),
			cell: ({ row }) => {
				const creditNoteDate = row.getValue("credit_note_date") as
					| Date
					| string
					| null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{creditNoteDate
								? format(new Date(creditNoteDate), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "net_amount",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Amount" />
			),
			cell: ({ row }) => {
				const netAmount = row.getValue("net_amount") as number | null;

				if (netAmount === null) {
					return "-";
				}

				return (
					<div className="flex items-center gap-2">
						<CreditCard className="h-4 w-4 text-muted-foreground" />
						<span>
							{new Intl.NumberFormat("de-DE", {
								style: "currency",
								currency: "EUR",
							}).format(netAmount)}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "description",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Description" />
			),
			cell: ({ row }) => {
				const description = row.getValue("description") as
					| string
					| null;
				return (
					<div className="flex items-center gap-2">
						<FileText className="h-4 w-4 text-muted-foreground" />
						<span className="truncate max-w-[200px]">
							{description || "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Created At" />
			),
			cell: ({ row }) => {
				const date = row.getValue("createdAt") as Date | string;
				return format(new Date(date), "PPP");
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<CreditNote>) => {
					const creditNote = row.original;
					return {
						openView: () => handleViewCreditNote(creditNote),
						openDelete: () => handleDeleteCreditNote(creditNote),
					};
				},
			},
		},
	];
}
