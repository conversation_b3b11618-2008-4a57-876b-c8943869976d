"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Clock, Plus, Trash } from "lucide-react";
import { useCallback, useState } from "react";

const DAYS_OF_WEEK = [
	"monday",
	"tuesday",
	"wednesday",
	"thursday",
	"friday",
	"saturday",
	"sunday",
];

interface OperatingHoursFieldProps {
	value: Record<string, string[]>;
	onChange: (value: Record<string, string[]>) => void;
}

// Component for a single time range input using native time inputs
function TimeRangeInput({
	startTime,
	endTime,
	onChange,
	onRemove,
	hasError,
	errorMessage,
}: {
	startTime: string;
	endTime: string;
	onChange: (startTime: string, endTime: string) => void;
	onRemove: () => void;
	hasError?: boolean;
	errorMessage?: string;
}) {
	// Convert HH:MM to HH:MM format for time input
	const formatForTimeInput = (time: string): string => {
		if (!time) {
			return "";
		}
		return time;
	};

	// Convert from time input to our format
	const handleTimeChange = (value: string, isStart: boolean) => {
		onChange(isStart ? value : startTime, isStart ? endTime : value);
	};

	return (
		<div className="space-y-1">
			<div className="flex items-center gap-2">
				<div
					className={cn(
						"flex-1 grid grid-cols-2 gap-2",
						hasError ? "text-destructive" : "",
					)}
				>
					<Input
						type="time"
						value={formatForTimeInput(startTime)}
						onChange={(e) => handleTimeChange(e.target.value, true)}
						className={cn(hasError ? "border-destructive" : "")}
					/>
					<div className="flex items-center gap-2">
						<span>-</span>
						<Input
							type="time"
							value={formatForTimeInput(endTime)}
							onChange={(e) =>
								handleTimeChange(e.target.value, false)
							}
							className={cn(hasError ? "border-destructive" : "")}
						/>
					</div>
				</div>
				<Button
					type="button"
					variant="ghost"
					size="icon"
					onClick={onRemove}
					className="h-8 w-8"
				>
					<Trash className="h-4 w-4" />
				</Button>
			</div>
			{hasError && errorMessage && (
				<p className="text-xs text-destructive">{errorMessage}</p>
			)}
		</div>
	);
}

export function OperatingHoursField({
	value = {},
	onChange,
}: OperatingHoursFieldProps) {
	// Ensure value is always an object, not undefined or null
	const safeValue = value || {};

	// Validate a time range and return an error message if invalid
	const validateTimeRange = (startTime: string, endTime: string): string => {
		if (!startTime || !endTime) {
			return "";
		}

		// Parse times as 24h format
		const [startHours, startMinutes] = startTime.split(":").map(Number);
		const [endHours, endMinutes] = endTime.split(":").map(Number);

		const startTotalMinutes = startHours * 60 + startMinutes;
		const endTotalMinutes = endHours * 60 + endMinutes;

		if (endTotalMinutes <= startTotalMinutes) {
			return "End time must be after start time";
		}

		return "";
	};

	// Add a new time range to a day
	const addTimeRange = useCallback(
		(day: string) => {
			const currentRanges = safeValue[day] || [];
			const newRanges = [...currentRanges, "08:00-17:00"];

			onChange({
				...safeValue,
				[day]: newRanges,
			});
		},
		[safeValue, onChange],
	);

	// Remove a time range from a day
	const removeTimeRange = useCallback(
		(day: string, index: number) => {
			const currentRanges = safeValue[day] || [];
			const newRanges = [...currentRanges];
			newRanges.splice(index, 1);

			const newValue = { ...safeValue };
			if (newRanges.length === 0) {
				delete newValue[day];
			} else {
				newValue[day] = newRanges;
			}

			onChange(newValue);
		},
		[safeValue, onChange],
	);

	// Update a time range for a specific day
	const updateTimeRange = useCallback(
		(day: string, index: number, startTime: string, endTime: string) => {
			const error = validateTimeRange(startTime, endTime);

			// Only update if we have both times (native time input ensures valid format)
			if (!error && startTime && endTime) {
				const currentRanges = safeValue[day] || [];
				const newRanges = [...currentRanges];

				newRanges[index] = `${startTime}-${endTime}`;

				onChange({
					...safeValue,
					[day]: newRanges,
				});
			}

			return error;
		},
		[safeValue, onChange],
	);

	// Parse a time range string into separate start and end times
	const parseTimeRange = (timeRange: string): [string, string] => {
		const [startTime, endTime] = timeRange.split("-");
		return [startTime || "", endTime || ""];
	};

	// State to track validation errors
	const [errors, setErrors] = useState<Record<string, string>>({});

	// Handle time range changes and validation
	const handleTimeRangeChange = useCallback(
		(
			day: string,
			index: number,
			newStartTime: string,
			newEndTime: string,
		) => {
			const error = updateTimeRange(day, index, newStartTime, newEndTime);

			// Update error state
			setErrors((prev) => {
				const errorKey = `${day}-${index}`;
				const newErrors = { ...prev };

				if (error) {
					newErrors[errorKey] = error;
				} else {
					delete newErrors[errorKey];
				}

				return newErrors;
			});
		},
		[updateTimeRange],
	);

	return (
		<div className="space-y-4 border rounded-md p-4">
			<div className="text-sm text-muted-foreground mb-2">
				Enter operating hours for each day
			</div>

			{DAYS_OF_WEEK.map((day) => (
				<div key={day} className="space-y-2">
					<div className="flex items-center justify-between">
						<h4 className="text-sm font-medium capitalize">
							{day}
						</h4>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => addTimeRange(day)}
							className="h-7 px-2"
						>
							<Plus className="h-3.5 w-3.5 mr-1" />
							Add hours
						</Button>
					</div>

					{/* Show empty state when no time ranges */}
					{(!safeValue[day] || safeValue[day].length === 0) && (
						<div className="text-sm text-muted-foreground italic flex items-center">
							<Clock className="h-3.5 w-3.5 mr-1" />
							No operating hours set
						</div>
					)}

					{/* List time ranges for this day */}
					{safeValue[day]?.map((timeRange, index) => {
						const [startTime, endTime] = parseTimeRange(timeRange);
						const errorKey = `${day}-${index}`;

						return (
							<TimeRangeInput
								key={`${day}-${index}`}
								startTime={startTime}
								endTime={endTime}
								onChange={(newStart, newEnd) =>
									handleTimeRangeChange(
										day,
										index,
										newStart,
										newEnd,
									)
								}
								onRemove={() => removeTimeRange(day, index)}
								hasError={Boolean(errors[errorKey])}
								errorMessage={errors[errorKey]}
							/>
						);
					})}
				</div>
			))}
		</div>
	);
}
