"use client";

import dynamic from "next/dynamic";
import * as React from "react";
import { type JSX, useCallback, useMemo, useState } from "react";
import { createPortal } from "react-dom";

import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
	MenuOption,
	type MenuTextMatch,
	useBasicTypeaheadTriggerMatch,
} from "@lexical/react/LexicalTypeaheadMenuPlugin";
import type { TextNode } from "lexical";
import { CircleUserRoundIcon } from "lucide-react";

import {
	Command,
	CommandGroup,
	CommandItem,
	CommandList,
} from "@ui/components/command";

import { useMembersAutocomplete } from "@shared/components/thread/hooks/use-threads";
import { useDebounce } from "@shared/hooks/use-debounce";
import { $createMentionNode } from "@ui/components/editor/nodes/mention-node";

const LexicalTypeaheadMenuPlugin = dynamic(
	() => import("@ui/components/editor/plugins/lexical-typeahead-menu-plugin"),
	{ ssr: false },
);

const PUNCTUATION =
	"\\.,\\+\\*\\?\\$\\@\\|#{}\\(\\)\\^\\-\\[\\]\\\\/!%'\"~=<>_:;";
const NAME = `\\b[A-Z][^\\s${PUNCTUATION}]`;

const DocumentMentionsRegex = {
	NAME,
	PUNCTUATION,
};

const PUNC = DocumentMentionsRegex.PUNCTUATION;
const TRIGGERS = ["@"].join("");

// Chars we expect to see in a mention (non-space, non-punctuation).
const VALID_CHARS = `[^${TRIGGERS}${PUNC}\\s]`;

// Non-standard series of chars. Each series must be preceded and followed by
// a valid char.
const VALID_JOINS = `(?:\\.[ |$]| |[${PUNC}]|)`;

const LENGTH_LIMIT = 75;

const AtSignMentionsRegex = new RegExp(
	`(^|\\s|\\()([${TRIGGERS}]((?:${VALID_CHARS}${VALID_JOINS}){0,${LENGTH_LIMIT}}))$`,
);

// 50 is the longest alias length limit.
const ALIAS_LENGTH_LIMIT = 50;

// Regex used to match alias.
const AtSignMentionsRegexAliasRegex = new RegExp(
	`(^|\\s|\\()([${TRIGGERS}]((?:${VALID_CHARS}){0,${ALIAS_LENGTH_LIMIT}}))$`,
);

// At most, 5 suggestions are shown in the popup.
const SUGGESTION_LIST_LENGTH_LIMIT = 5;

function checkForAtSignMentions(
	text: string,
	minMatchLength: number,
): MenuTextMatch | null {
	let match = AtSignMentionsRegex.exec(text);

	if (match === null) {
		match = AtSignMentionsRegexAliasRegex.exec(text);
	}
	if (match !== null) {
		// The strategy ignores leading whitespace but we need to know it's
		// length to add it to the leadOffset
		const maybeLeadingWhitespace = match[1];
		const matchingString = match[3];
		if (matchingString.length >= minMatchLength) {
			return {
				leadOffset: match.index + maybeLeadingWhitespace.length,
				matchingString,
				replaceableString: match[2],
			};
		}
	}
	return null;
}

function getPossibleQueryMatch(text: string): MenuTextMatch | null {
	return checkForAtSignMentions(text, 1);
}

class MentionTypeaheadOption extends MenuOption {
	name: string;
	email: string;
	picture: JSX.Element;
	userId: string;

	constructor(
		name: string,
		email: string,
		picture: JSX.Element,
		userId: string,
	) {
		super(name);
		this.name = name;
		this.email = email;
		this.picture = picture;
		this.userId = userId;
	}
}

export function CommentMentionsPlugin(): JSX.Element | null {
	const [editor] = useLexicalComposerContext();
	const [queryString, setQueryString] = useState<string | null>(null);

	// Debounce the query string to reduce API calls
	const debouncedQueryString = useDebounce(queryString, 300);

	// Use the real members autocomplete hook with debounced query
	const { data: membersData } = useMembersAutocomplete(
		debouncedQueryString || "",
		{
			enabled:
				!!debouncedQueryString &&
				debouncedQueryString.trim().length >= 2, // Minimum 2 characters
		},
	);

	const members = membersData || [];

	const checkForSlashTriggerMatch = useBasicTypeaheadTriggerMatch("/", {
		minLength: 0,
	});

	const options = useMemo(
		() =>
			members
				.map(
					(member: any) =>
						new MentionTypeaheadOption(
							member.name,
							member.email,
							<CircleUserRoundIcon className="size-4" />,
							member.userId,
						),
				)
				.slice(0, SUGGESTION_LIST_LENGTH_LIMIT),
		[members],
	);

	const onSelectOption = useCallback(
		(
			selectedOption: MentionTypeaheadOption,
			nodeToReplace: TextNode | null,
			closeMenu: () => void,
		) => {
			editor.update(() => {
				const mentionNode = $createMentionNode(
					selectedOption.name,
					selectedOption.userId,
				);
				if (nodeToReplace) {
					nodeToReplace.replace(mentionNode);
				}
				mentionNode.select();
				closeMenu();
			});
		},
		[editor],
	);

	const checkForMentionMatch = useCallback(
		(text: string) => {
			const slashMatch = checkForSlashTriggerMatch(text, editor);
			if (slashMatch !== null) {
				return null;
			}
			return getPossibleQueryMatch(text);
		},
		[checkForSlashTriggerMatch, editor],
	);

	return (
		// @ts-ignore
		<LexicalTypeaheadMenuPlugin<MentionTypeaheadOption>
			onQueryChange={setQueryString}
			onSelectOption={onSelectOption}
			triggerFn={checkForMentionMatch}
			options={options}
			menuRenderFn={(
				anchorElementRef,
				{ selectedIndex, selectOptionAndCleanUp, setHighlightedIndex },
			) => {
				return anchorElementRef.current && members.length
					? createPortal(
							<div className="fixed w-[250px] rounded-md shadow-lg bg-background border border-border z-[9999] pointer-events-auto">
								<Command
									className="bg-background text-foreground"
									onKeyDown={(e) => {
										if (e.key === "ArrowUp") {
											e.preventDefault();
											setHighlightedIndex(
												selectedIndex !== null
													? (selectedIndex -
															1 +
															options.length) %
															options.length
													: options.length - 1,
											);
										} else if (e.key === "ArrowDown") {
											e.preventDefault();
											setHighlightedIndex(
												selectedIndex !== null
													? (selectedIndex + 1) %
															options.length
													: 0,
											);
										}
									}}
								>
									<CommandList className="max-h-40">
										<CommandGroup>
											{options.map((option, index) => (
												<CommandItem
													key={option.key}
													onSelect={() => {
														selectOptionAndCleanUp(
															option,
														);
													}}
													onMouseEnter={(e) => {
														if (
															selectedIndex !==
															index
														) {
															e.currentTarget.style.backgroundColor =
																"rgba(71, 85, 105, 0.3)";
														}
													}}
													onMouseLeave={(e) => {
														if (
															selectedIndex !==
															index
														) {
															e.currentTarget.style.backgroundColor =
																"transparent";
														}
													}}
													style={{
														backgroundColor:
															selectedIndex ===
															index
																? "rgba(71, 85, 105, 0.5)"
																: "transparent",
														color: "#ffffff",
													}}
													className={`flex items-center gap-2 px-3 py-2 cursor-pointer text-foreground ${
														selectedIndex === index
															? "!bg-slate-700/50 !text-foreground"
															: "hover:!bg-slate-600/30"
													}`}
												>
													{option.picture}
													<div className="flex flex-col">
														<span className="font-medium text-foreground">
															{option.name}
														</span>
														<span className="text-xs text-muted-foreground">
															{option.email}
														</span>
													</div>
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</div>,
							anchorElementRef.current,
						)
					: null;
			}}
		/>
	);
}
