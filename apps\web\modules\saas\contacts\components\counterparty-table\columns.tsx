"use client";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import { useTranslations } from "next-intl";

import { useRouter } from "@shared/hooks/router";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Building2, Eye, MoreHorizontal, Trash } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useContactUI } from "../../context/counterparty-ui-context";
import type { useCounterparties } from "../../hooks/use-counterparty";

// Add this type declaration
declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

// Use the inferred type from the API response
export type Counterparty = NonNullable<
	ReturnType<typeof useCounterparties>["data"]
>["items"][number];

type ActionsCellProps = {
	row: Row<Counterparty>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const t = useTranslations();
	const counterparty = row.original;
	const { handleDeleteContact } = useContactUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	// Navigate to counterparty view page
	const navigateToCounterpartyView = () => {
		router.push(
			`/app/${params.organizationSlug}/contacts/${counterparty.id}`,
		);
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{t("app.contacts.table.actions.title")}
				</DropdownMenuLabel>
				<DropdownMenuItem onClick={navigateToCounterpartyView}>
					<Eye className="mr-2 h-4 w-4" />
					{t("app.contacts.table.actions.view")}
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteContact(counterparty)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					{t("app.contacts.table.actions.delete")}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

// Helper function to get the default address or first address
function getDisplayAddress(counterparty: Counterparty) {
	// Handle case where addresses is an array
	if (Array.isArray(counterparty.addresses)) {
		const defaultUsage = counterparty.addresses.find((a) => a.isDefault);
		return defaultUsage || counterparty.addresses[0];
	}

	// Handle case where addresses is an object with categorized arrays
	if (counterparty.addresses && typeof counterparty.addresses === "object") {
		// Always prioritize primary addresses
		if (
			"primary" in counterparty.addresses &&
			Array.isArray(counterparty.addresses.primary)
		) {
			const primaryAddresses = counterparty.addresses.primary;
			const defaultPrimary = primaryAddresses.find((a) => a.isDefault);
			if (defaultPrimary) {
				return defaultPrimary;
			}
			if (primaryAddresses.length > 0) {
				return primaryAddresses[0];
			}
		}

		// Fallback to other categories if no primary addresses
		const allAddresses = Object.values(counterparty.addresses).flat();
		const defaultUsage = allAddresses.find((a) => a.isDefault);
		return defaultUsage || allAddresses[0];
	}

	return undefined;
}

export function useColumns(): ColumnDef<Counterparty>[] {
	const t = useTranslations();
	const { handleDeleteContact } = useContactUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "nameLine1",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.contacts.table.columns.name")}
				/>
			),
			cell: ({ row }) => {
				const name = row.getValue("nameLine1") as string;
				const nameLine2 = row.original.nameLine2;
				const counterparty = row.original;
				const isBlocked = !!counterparty.blockedAt;
				const blockReason = counterparty.blockReason;

				const nameElement = (
					<div className="flex items-center gap-2">
						<Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
						<Link
							href={`/app/${params.organizationSlug}/contacts/${counterparty.id}`}
							className={cn(
								"hover:underline cursor-pointer",
								isBlocked && "text-red-600 dark:text-red-400",
							)}
						>
							<div className="font-medium">{name}</div>
						</Link>
					</div>
				);

				return (
					<div className="flex flex-col gap-1">
						{isBlocked && blockReason ? (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										{nameElement}
									</TooltipTrigger>
									<TooltipContent>
										<p>Blocked: {blockReason}</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						) : (
							nameElement
						)}

						{nameLine2 && (
							<div className="text-sm text-muted-foreground pl-6">
								{nameLine2}
							</div>
						)}

						<div className="flex flex-wrap gap-1 mt-1 pl-6">
							{/* Customer Number Badge */}
							{counterparty.customerNumber && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<span className="px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
												{counterparty.customerNumber}
											</span>
										</TooltipTrigger>
										<TooltipContent>
											<p>Customer Number</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}

							{/* Carrier Number Badge */}
							{counterparty.freightExchangeProfile
								?.carrierNumber && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<span className="px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
												{
													counterparty
														.freightExchangeProfile
														.carrierNumber
												}
											</span>
										</TooltipTrigger>
										<TooltipContent>
											<p>Carrier Number</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}

							{/* Show message if no numbers */}
							{!counterparty.customerNumber &&
								!counterparty.freightExchangeProfile
									?.carrierNumber && (
									<span className="text-muted-foreground text-xs">
										No numbers
									</span>
								)}
						</div>
					</div>
				);
			},
			enableResizing: true,
			size: 300,
		},
		{
			accessorKey: "street",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.contacts.table.columns.street")}
				/>
			),
			cell: ({ row }) => {
				const counterparty = row.original;
				const addressUsage = getDisplayAddress(counterparty);
				return addressUsage?.address?.street || "-";
			},
			enableResizing: true,
			size: 250,
		},
		{
			accessorKey: "zipCode",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.contacts.table.columns.zipCode")}
				/>
			),
			cell: ({ row }) => {
				const counterparty = row.original;
				const addressUsage = getDisplayAddress(counterparty);
				return addressUsage?.address?.zipCode || "-";
			},
			enableResizing: true,
			size: 100,
		},
		{
			accessorKey: "city",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.contacts.table.columns.city")}
				/>
			),
			cell: ({ row }) => {
				const counterparty = row.original;
				const addressUsage = getDisplayAddress(counterparty);
				return addressUsage?.address?.city || "-";
			},
			enableResizing: true,
			size: 150,
		},
		{
			accessorKey: "country",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.contacts.table.columns.country")}
				/>
			),
			cell: ({ row }) => {
				const counterparty = row.original;
				const addressUsage = getDisplayAddress(counterparty);
				return addressUsage?.address?.country || "-";
			},
			enableResizing: true,
			size: 120,
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<Counterparty>) => {
					const counterparty = row.original;
					return {
						openView: () => {
							router.push(
								`/app/${params.organizationSlug}/contacts/${counterparty.id}`,
							);
						},
						openDelete: () => handleDeleteContact(row.original),
					};
				},
			},
		},
	];
}
