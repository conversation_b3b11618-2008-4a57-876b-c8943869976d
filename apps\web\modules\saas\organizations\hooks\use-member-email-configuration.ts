import type {
	CreateMemberEmailConfiguration,
	TestMemberConnection,
	TestMemberEmailConfiguration,
	UpdateMemberEmailConfiguration,
} from "@repo/api/src/routes/organizations/member-email-configuration/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useState } from "react";

import {
	useActivateMemberEmailConfigurationMutation,
	useCreateMemberEmailConfigurationMutation,
	useDeleteMemberEmailConfigurationMutation,
	useMemberEmailConfigurationQuery,
	useMemberOAuth2FlowMutation,
	useTestMemberConnectionMutation,
	useTestMemberEmailConfigurationMutation,
	useUpdateMemberEmailConfigurationMutation,
} from "../lib/api-member-email-configuration";

// Main hook for member email configuration
export function useMemberEmailConfiguration() {
	const { activeOrganization, loaded } = useActiveOrganization();

	const query = useMemberEmailConfigurationQuery({
		organizationId: activeOrganization?.id ?? "",
	});

	const testMutation = useTestMemberEmailConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	const activateMutation = useActivateMemberEmailConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	const deleteMutation = useDeleteMemberEmailConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the test function
	const testMemberEmailConfiguration = (
		data: Omit<TestMemberEmailConfiguration, "organizationId">,
	) => {
		if (activeOrganization?.id) {
			testMutation.mutate(data);
		}
	};

	// Wrap the activate function
	const activateMemberEmailConfiguration = (isActive: boolean) => {
		if (activeOrganization?.id) {
			activateMutation.mutate(isActive);
		}
	};

	// Wrap the delete function
	const deleteMemberEmailConfiguration = () => {
		if (activeOrganization?.id) {
			deleteMutation.mutate();
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading || !loaded,
		error: query.error,
		refetch: query.refetch,
		testMemberEmailConfiguration,
		activateMemberEmailConfiguration,
		deleteMemberEmailConfiguration,
		isTestingConfiguration: testMutation.isPending,
		isActivating: activateMutation.isPending,
		isDeleting: deleteMutation.isPending,
	};
}

// Hook for member email configuration creation
export function useMemberEmailConfigurationCreation(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const [isSubmitting, setIsSubmitting] = useState(false);

	const createMutation = useCreateMemberEmailConfigurationMutation(orgId);

	const createMemberEmailConfiguration = async (
		data: Omit<CreateMemberEmailConfiguration, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await createMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} finally {
			setIsSubmitting(false);
		}
	};

	return {
		createMemberEmailConfiguration,
		isSubmitting,
	};
}

// Hook for member email configuration updates
export function useMemberEmailConfigurationUpdate(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const [isSubmitting, setIsSubmitting] = useState(false);

	const updateMutation = useUpdateMemberEmailConfigurationMutation(orgId);

	const updateMemberEmailConfiguration = async (
		data: Omit<UpdateMemberEmailConfiguration, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} finally {
			setIsSubmitting(false);
		}
	};

	return {
		updateMemberEmailConfiguration,
		isSubmitting,
	};
}

// Hook for testing member email connection with new settings
export function useMemberConnectionTest() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const testMutation = useTestMemberConnectionMutation(orgId);

	const testMemberConnection = (
		data: Omit<TestMemberConnection, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		testMutation.mutate(data);
	};

	return {
		testMemberConnection,
		isTestingConnection: testMutation.isPending,
	};
}

// Hook for member OAuth2 authentication flow
export function useMemberOAuth2Authentication() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const oauthMutation = useMemberOAuth2FlowMutation(orgId);

	const initiateMemberOAuth2Flow = (provider: "microsoft") => {
		if (!orgId) {
			return Promise.reject(new Error("No active organization"));
		}

		return oauthMutation.mutateAsync(provider);
	};

	return {
		initiateMemberOAuth2Flow,
		isAuthenticating: oauthMutation.isPending,
	};
}
