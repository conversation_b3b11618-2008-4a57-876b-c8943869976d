import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";

/**
 * Filter parameters for statistics data
 */
export interface StatisticsFilterParams {
	// Date range filters
	startDate?: Date;
	endDate?: Date;
	// Other filters
	departmentIds?: string[];
	absenceTypes?: AbsenceType[];
	personnelId?: string; // For individual employee stats
}

/**
 * KPI metrics for the dashboard
 */
export interface StatisticsKpiData {
	// Overall metrics
	absenceRate: {
		value: number; // Percentage
		change: number; // Percentage point change from previous period
		total: {
			absenceDays: number;
		};
	};
	// Total absence days
	totalAbsenceDays: {
		value: number;
		change: number; // Percentage change from previous period
	};
	// Average absence duration
	avgAbsenceDuration: {
		value: number; // Days
		change: number; // Percentage change from previous period
	};
	// Number of absence instances
	absenceInstances: {
		value: number;
		change: number; // Percentage change from previous period
	};
}

/**
 * Data point for time series charts
 */
export interface TimeSeriesDataPoint {
	date: string; // ISO date string or formatted date
	value: number;
	label?: string; // Optional label
}

/**
 * Data for absence trend chart
 */
export interface AbsenceTrendData {
	series: TimeSeriesDataPoint[];
	// Optional comparison data (e.g., previous period)
	comparisonSeries?: TimeSeriesDataPoint[];
}

/**
 * Data for absence type breakdown chart
 */
export interface AbsenceTypeData {
	type: AbsenceType;
	count: number;
	percentage: number;
	color?: string; // Optional color code
}

/**
 * Data for department comparison chart
 */
export interface DepartmentData {
	departmentId: string;
	departmentName: string;
	absenceDays: number;
	absenceRate: number; // Percentage
	employeeCount: number;
}

/**
 * Data for weekday distribution chart
 */
export interface WeekdayDistributionData {
	dayOfWeek: number; // 0-6 (Sunday-Saturday)
	dayName: string; // "Sunday", "Monday", etc.
	count: number;
	percentage: number;
}

/**
 * Bradford Factor data point
 */
export interface BradfordFactorData {
	score: number;
	employeeCount: number;
	percentage: number;
}

/**
 * Data for personnel with highest absence count
 */
export interface TopPersonnelAbsenceData {
	personnelId: string;
	name: string;
	departmentId: string;
	departmentName: string;
	absenceDays: number;
	absenceCount: number;
	absenceRate: number; // Percentage
}

/**
 * Complete statistics data object
 */
export interface StatisticsData {
	kpi: StatisticsKpiData;
	absenceTrend: AbsenceTrendData;
	absenceByType: AbsenceTypeData[];
	absenceByDepartment: DepartmentData[];
	absenceByWeekday: WeekdayDistributionData[];
	bradfordFactor?: BradfordFactorData[];
	topPersonnelAbsences: TopPersonnelAbsenceData[];
	// Metadata
	filters: StatisticsFilterParams;
	lastUpdated: string; // ISO date string
}

/**
 * Status of statistics data fetching
 */
export interface StatisticsDataStatus {
	isLoading: boolean;
	isError: boolean;
	error?: string;
}
