import type { Notification, Preference } from "@novu/react";
import type { ReactNode } from "react";

export interface NotificationConfig {
	subscriberId: string;
	applicationIdentifier: string;
	socketUrl?: string;
	apiUrl?: string;
	subscriberHash?: string;
}

export interface NotificationBellProps {
	onClick?: () => void;
	className?: string;
	showBadge?: boolean;
	badgeVariant?: "default" | "destructive";
}

export interface NotificationDropdownProps {
	isOpen: boolean;
	onClose: () => void;
	triggerRef: HTMLElement | null;
	children: ReactNode;
	className?: string;
	position?: "left" | "right" | "center";
}

export interface NotificationListProps {
	limit?: number;
	archived?: boolean;
	read?: boolean;
	tags?: string[];
	showActions?: boolean;
	onNotificationClick?: (notification: Notification) => void;
}

export interface NotificationItemProps {
	notification: Notification;
	onClick?: (notification: Notification) => void;
	showActions?: boolean;
	variant?: "default" | "compact";
}

export interface NotificationPreferencesProps {
	filter?: { tags?: string[] };
	onPreferencesChange?: (preferences: Preference[]) => void;
	showInModal?: boolean;
	className?: string;
	groupByChannel?: boolean;
}

export interface NotificationSystemProps {
	subscriberId: string;
	applicationIdentifier: string;
	className?: string;
	position?: "left" | "right" | "center";
}
