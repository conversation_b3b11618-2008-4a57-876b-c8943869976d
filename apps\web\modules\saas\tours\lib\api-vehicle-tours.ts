import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const vehicleToursKeys = {
	all: ["vehicleTours"] as const,
	list: (params: FetchVehicleToursParams) =>
		[...vehicleToursKeys.all, "list", params] as const,
	detail: (organizationId?: string, vehicleId?: string) =>
		[...vehicleToursKeys.all, "detail", organizationId, vehicleId] as const,
};

// Type for the fetch vehicles with tours params
export type FetchVehicleToursParams = {
	organizationId: string;
	startDate?: Date;
	endDate?: Date;
	vehicleId?: string;
	vehicleType?: "TRUCK" | "TRAILER";
};

/**
 * Fetch vehicles with their assigned tours for a given timeframe
 */
export const fetchVehiclesWithTours = async (
	params: FetchVehicleToursParams,
) => {
	const response = await apiClient["vehicle-tours"].$get({
		query: {
			organizationId: params.organizationId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
			vehicleId: params.vehicleId,
			vehicleType: params.vehicleType,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch vehicles with tours");
	}

	return response.json();
};

/**
 * React query hook for fetching vehicles with their assigned tours
 */
export const useVehiclesWithToursQuery = (params: FetchVehicleToursParams) => {
	return useQuery({
		queryKey: vehicleToursKeys.list(params),
		queryFn: () => fetchVehiclesWithTours(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

/**
 * Fetch tours for a specific vehicle in a given timeframe
 */
export const fetchVehicleTours = async (
	vehicleId: string,
	organizationId: string,
	startDate?: Date,
	endDate?: Date,
) => {
	const response = await apiClient["vehicle-tours"][":vehicleId"].$get({
		param: { vehicleId },
		query: {
			organizationId,
			startDate: startDate?.toISOString(),
			endDate: endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch vehicle tours");
	}

	return response.json();
};

/**
 * React query hook for fetching tours for a specific vehicle
 */
export const useVehicleToursQuery = (
	vehicleId?: string,
	organizationId?: string,
	startDate?: Date,
	endDate?: Date,
) => {
	return useQuery({
		queryKey: [
			...vehicleToursKeys.detail(organizationId, vehicleId),
			startDate,
			endDate,
		],
		queryFn: () => {
			if (!vehicleId || !organizationId) {
				return [];
			}
			return fetchVehicleTours(
				vehicleId,
				organizationId,
				startDate,
				endDate,
			);
		},
		enabled: !!vehicleId && !!organizationId,
	});
};
