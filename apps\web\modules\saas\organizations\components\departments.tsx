"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { DotsHorizontalIcon, PlusIcon, TrashIcon } from "@radix-ui/react-icons";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useDepartmentMutations, useDepartments } from "../hooks/use-config";

// Form schema for creating/editing departments
const formSchema = z.object({
	name: z.string().min(1, "Name is required"),
});

type FormValues = z.infer<typeof formSchema>;

export function Departments() {
	const [isOpen, setIsOpen] = useState(false);
	const [editingDepartment, setEditingDepartment] = useState<any | null>(
		null,
	);

	const { data, isLoading, refetch } = useDepartments();

	const {
		createDepartment,
		updateDepartment,
		deleteDepartment,
		isLoading: isMutating,
	} = useDepartmentMutations({
		onSuccess: () => {
			refetch();
			setIsOpen(false);
			setEditingDepartment(null);
		},
	});

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
		},
	});

	function onOpenChange(open: boolean) {
		if (!open) {
			setEditingDepartment(null);
			form.reset();
		}
		setIsOpen(open);
	}

	function onEdit(department: any) {
		setEditingDepartment(department);
		form.reset({
			name: department.name,
		});
		setIsOpen(true);
	}

	async function onSubmit(values: FormValues) {
		try {
			if (editingDepartment) {
				await updateDepartment(editingDepartment.id, values);
			} else {
				await createDepartment({
					name: values.name,
				});
			}
		} catch (error) {
			console.error("Error managing department:", error);
		}
	}

	async function onDelete(id: string) {
		if (confirm("Are you sure you want to delete this department?")) {
			try {
				await deleteDepartment(id);
			} catch (error) {
				console.error("Error deleting department:", error);
			}
		}
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<div>
					<CardTitle>Departments</CardTitle>
					<CardDescription>
						Manage departments for your organization
					</CardDescription>
				</div>
				<Dialog open={isOpen} onOpenChange={onOpenChange}>
					<DialogTrigger asChild>
						<Button variant="primary" size="sm">
							<PlusIcon className="mr-2 h-4 w-4" />
							Add Department
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>
								{editingDepartment
									? "Edit Department"
									: "Create Department"}
							</DialogTitle>
							<DialogDescription>
								{editingDepartment
									? "Update the details of this department."
									: "Add a new department to your organization."}
							</DialogDescription>
						</DialogHeader>
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-4"
							>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter name"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<DialogFooter>
									<Button
										type="submit"
										variant="primary"
										disabled={isMutating}
									>
										{editingDepartment
											? "Update"
											: "Create"}
									</Button>
								</DialogFooter>
							</form>
						</Form>
					</DialogContent>
				</Dialog>
			</CardHeader>
			<CardContent>
				{isLoading ? (
					<div className="space-y-2">
						<Skeleton className="h-12 w-full" />
						<Skeleton className="h-12 w-full" />
						<Skeleton className="h-12 w-full" />
					</div>
				) : data?.items?.length === 0 ? (
					<div className="flex flex-col items-center justify-center py-8 text-center">
						<PlusIcon className="mb-2 h-12 w-12 text-muted-foreground" />
						<h3 className="mb-2 text-lg font-medium">
							No departments
						</h3>
						<p className="mb-4 text-sm text-muted-foreground">
							Get started by creating a new department.
						</p>
						<Dialog open={isOpen} onOpenChange={onOpenChange}>
							<DialogTrigger asChild>
								<Button variant="primary">
									<PlusIcon className="mr-2 h-4 w-4" />
									Add Department
								</Button>
							</DialogTrigger>
						</Dialog>
					</div>
				) : (
					<div className="space-y-2">
						{data?.items?.map((department) => (
							<div
								key={department.id}
								className="flex items-center justify-between rounded-md border p-3"
							>
								<div className="flex flex-col">
									<div className="flex items-center gap-2">
										<span className="font-medium">
											{department.name}
										</span>
									</div>
								</div>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											className="h-8 w-8 p-0"
										>
											<DotsHorizontalIcon className="h-4 w-4" />
											<span className="sr-only">
												Open menu
											</span>
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent align="end">
										<DropdownMenuItem
											onClick={() => onEdit(department)}
										>
											Edit
										</DropdownMenuItem>
										<DropdownMenuItem
											className="text-destructive focus:text-destructive"
											onClick={() =>
												onDelete(department.id)
											}
										>
											<TrashIcon className="mr-2 h-4 w-4" />
											Delete
										</DropdownMenuItem>
									</DropdownMenuContent>
								</DropdownMenu>
							</div>
						))}
					</div>
				)}
			</CardContent>
		</Card>
	);
}
