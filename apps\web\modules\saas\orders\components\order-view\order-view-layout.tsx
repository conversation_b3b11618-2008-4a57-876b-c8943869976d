"use client";

import { useOrderView } from "@saas/orders/context/order-view-context";
import { Card, CardContent } from "@ui/components/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { useTranslations } from "next-intl";

import { OrderViewHeader } from "@saas/orders/components/order-view/order-view-header";
import { OrderViewSkeleton } from "@saas/orders/components/order-view/order-view-skeleton";
import { ActivityPanel } from "@saas/orders/components/order-view/panels/activity-panel";
import { DamagesPanel } from "@saas/orders/components/order-view/panels/damages-panel";
import { DetailsPanel } from "@saas/orders/components/order-view/panels/details-panel";
import { ItemsPanel } from "@saas/orders/components/order-view/panels/lineitems-panel";
import { OrderTabs } from "@saas/orders/components/order-view/panels/order-tabs";
import { QuickInfoPanel } from "@saas/orders/components/order-view/panels/quick-info-panel";
import { StopsPanel } from "@saas/orders/components/order-view/panels/stops-panel";

export function OrderViewLayout() {
	const { activeTab, setActiveTab, isLoading, order } = useOrderView();
	const t = useTranslations();

	// handleOfferCreated is now just a pass-through function since refetching is
	// handled inside useOfferMutations in use-offer.ts
	const handleOfferCreated = () => {
		// Intentionally empty - refetching is now handled in use-offer.ts
	};

	// Handler for invoice creation
	const handleInvoiceCreated = () => {
		// Intentionally empty - refetching is handled in the mutation
	};

	// Handler for credit note creation
	const handleCreditNoteCreated = () => {
		// Intentionally empty - refetching is handled in the mutation
	};

	if (isLoading || !order) {
		return <OrderViewSkeleton />;
	}

	return (
		<div className="">
			<OrderViewHeader order={order} />

			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="w-full"
			>
				<TabsList className="grid grid-cols-5 mb-6">
					<TabsTrigger value="details">
						{t("app.orders.tabs.details")}
					</TabsTrigger>
					<TabsTrigger value="items">
						{t("app.orders.tabs.items")}
					</TabsTrigger>
					<TabsTrigger value="stops">
						{t("app.orders.tabs.stops")}
					</TabsTrigger>
					<TabsTrigger value="damages">
						{t("app.orders.tabs.damages")}
					</TabsTrigger>
					<TabsTrigger value="activity">
						{t("app.orders.tabs.activity")}
					</TabsTrigger>
				</TabsList>

				<div className="flex gap-6">
					{/* Left sidebar */}
					<div className="w-1/4 space-y-4">
						<Card className="h-fit">
							<CardContent className="p-4">
								<QuickInfoPanel
									order={order}
									onCreateOffer={handleOfferCreated}
								/>
							</CardContent>
						</Card>

						{/* Offers & Invoices Tabs Panel */}
						{order.id && (
							<Card className="h-fit">
								<CardContent className="p-4">
									<OrderTabs
										orderId={order.id}
										onCreateOffer={handleOfferCreated}
										onCreateInvoice={handleInvoiceCreated}
										onCreateCreditNote={
											handleCreditNoteCreated
										}
									/>
								</CardContent>
							</Card>
						)}
					</div>

					{/* Main content area */}
					<div className="flex-1">
						<TabsContent value="details" className="mt-0">
							<DetailsPanel order={order} />
						</TabsContent>

						<TabsContent value="items" className="mt-0">
							<ItemsPanel orderId={order.id} />
						</TabsContent>

						<TabsContent value="stops" className="mt-0">
							<StopsPanel orderId={order.id} />
						</TabsContent>

						<TabsContent value="damages" className="mt-0">
							<DamagesPanel orderId={order.id} />
						</TabsContent>

						<TabsContent value="activity" className="mt-0">
							<ActivityPanel orderId={order.id} />
						</TabsContent>
					</div>
				</div>
			</Tabs>
		</div>
	);
}
