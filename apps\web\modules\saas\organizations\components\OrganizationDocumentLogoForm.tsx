"use client";

import { config } from "@repo/config";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useCompanyDetails } from "@saas/organizations/hooks/use-config";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { useSignedUploadUrlMutation } from "@saas/shared/lib/api";
import { Spinner } from "@shared/components/Spinner";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { v4 as uuid } from "uuid";
import { OrganizationDocumentLogo } from "./OrganizationDocumentLogo";

export function OrganizationDocumentLogoForm() {
	const t = useTranslations();
	const [uploading, setUploading] = useState(false);
	const { activeOrganization } = useActiveOrganization();
	const {
		details,
		updateCompanyDetails,
		refetch: refetchCompanyDetails,
	} = useCompanyDetails();
	const getSignedUploadUrlMutation = useSignedUploadUrlMutation();

	const { getRootProps, getInputProps } = useDropzone({
		onDrop: async (acceptedFiles) => {
			const file = acceptedFiles[0];
			await handleFileUpload(file);
		},
		accept: {
			"image/png": [".png"],
			"image/jpeg": [".jpg", ".jpeg"],
		},
		multiple: false,
	});

	if (!activeOrganization) {
		return null;
	}

	const handleFileUpload = async (file: File) => {
		setUploading(true);
		try {
			// Generate a filename with original extension
			const extension = file.name.split(".").pop();
			const path = `doc-${activeOrganization.id}-${uuid()}.${extension}`;

			const { signedUrl } = await getSignedUploadUrlMutation.mutateAsync({
				path,
				bucket: config.storage.bucketNames.avatars,
				contentType: file.type,
			});

			const response = await fetch(signedUrl, {
				method: "PUT",
				body: file,
				headers: {
					"Content-Type": file.type,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to upload image");
			}

			// Update the company details with the new document logo using the hook from use-config.ts
			await updateCompanyDetails({
				documentLogo: path,
			});

			toast.success(t("organizations.settings.documentLogo.success"));

			// Refresh the company details data
			refetchCompanyDetails();
		} catch (e) {
			toast.error(t("organizations.settings.documentLogo.error"));
		} finally {
			setUploading(false);
		}
	};

	return (
		<SettingsItem
			title={t("organizations.settings.documentLogo.title")}
			description={t("organizations.settings.documentLogo.description")}
		>
			<div className="relative w-full max-w-xs" {...getRootProps()}>
				<input {...getInputProps()} />
				<OrganizationDocumentLogo
					className="cursor-pointer border border-dashed border-muted-foreground/25 p-2 rounded-md"
					logoUrl={details?.documentLogo}
					name={activeOrganization.name ?? ""}
				/>

				{uploading && (
					<div className="absolute inset-0 z-20 flex items-center justify-center bg-card/90">
						<Spinner className="size-6" />
					</div>
				)}
			</div>
		</SettingsItem>
	);
}
