"use client";

import type { CreateCounterpartyInput } from "@repo/api/src/routes/counterparties/types";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { PhoneInput } from "@ui/components/phone-input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { Building2, HelpCircle, Tags } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import type { Country } from "react-phone-number-input";
import { isSupportedCountry } from "react-phone-number-input";

type CounterpartyType = "customer" | "supplier" | "carrier" | "other";

export function BusinessSection() {
	const t = useTranslations("app.contacts");
	const form = useFormContext<CreateCounterpartyInput>();
	// State to track the current country from the address section
	const [selectedCountry, setSelectedCountry] = useState<Country | undefined>(
		undefined,
	);

	// Initialize field array for types
	const typesArray = useFieldArray({
		control: form.control,
		name: "types",
	});

	// Types available for selection
	const availableTypes = [
		{ value: "customer" as CounterpartyType, label: "Customer" },
		{ value: "supplier" as CounterpartyType, label: "Supplier" },
		{ value: "carrier" as CounterpartyType, label: "Carrier" },
		{ value: "other" as CounterpartyType, label: "Other" },
	];

	// Add a type to the types array
	const addType = (type: CounterpartyType) => {
		// Check if type already exists
		const typeExists = typesArray.fields.some(
			(field) => field.type === type,
		);
		if (!typeExists) {
			typesArray.append({ type });
		}
	};

	// Remove a type from the types array
	const removeType = (type: CounterpartyType) => {
		const index = typesArray.fields.findIndex(
			(field) => field.type === type,
		);
		if (index > -1) {
			typesArray.remove(index);
		}
	};

	// Toggle a type selection
	const toggleType = (type: CounterpartyType, checked: boolean) => {
		if (checked) {
			addType(type);
		} else {
			removeType(type);
		}
	};

	// Check if a type is selected
	const isTypeSelected = (type: CounterpartyType) => {
		return typesArray.fields.some((field) => field.type === type);
	};

	// Set default value for legalEntityType when component mounts
	useEffect(() => {
		const currentValue = form.getValues("legalEntityType");
		if (!currentValue) {
			form.setValue("legalEntityType", "company", {
				shouldValidate: true,
			});
		}

		// Ensure at least one type is selected
		const currentTypes = typesArray.fields;
		if (currentTypes.length === 0) {
			addType("customer");
		}
	}, [form]);

	// Watch for changes to the country field and update the phone country
	useEffect(() => {
		const subscription = form.watch((value, { name }) => {
			// If the addressUsages array changes, update our selected country
			if (name?.startsWith("addressUsages") || name === undefined) {
				const addressUsages = value.addressUsages || [];
				// Find the default/primary address
				const primaryAddressUsage =
					addressUsages.find((addr) => addr?.isDefault) ||
					addressUsages[0];

				if (
					primaryAddressUsage?.address?.country &&
					primaryAddressUsage.address.country !== selectedCountry
				) {
					// Country codes should be uppercase for PhoneInput
					const countryCode =
						primaryAddressUsage.address.country.toUpperCase();

					// Only set if it's a valid country code
					if (isSupportedCountry(countryCode as Country)) {
						setSelectedCountry(countryCode as Country);
					}
				}
			}
		});

		// Initialize with current value from the default address
		const addressUsages = form.getValues("addressUsages") || [];
		const primaryAddressUsage =
			addressUsages.find((addr) => addr?.isDefault) || addressUsages[0];
		if (primaryAddressUsage?.address?.country) {
			const countryCode =
				primaryAddressUsage.address.country.toUpperCase();
			if (isSupportedCountry(countryCode as Country)) {
				setSelectedCountry(countryCode as Country);
			}
		}

		return () => subscription.unsubscribe();
	}, [form, selectedCountry]);

	// Watch for changes to legalEntityType and clear VAT if not company
	useEffect(() => {
		const subscription = form.watch((value, { name }) => {
			if (
				name === "legalEntityType" &&
				value.legalEntityType !== "company"
			) {
				form.setValue("uidNumber", "", { shouldValidate: true });
			}
		});

		return () => subscription.unsubscribe();
	}, [form]);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Business Details</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				{/* Company Details */}
				<div className="space-y-4">
					<div className="flex items-center gap-2 mb-2">
						<Building2 className="h-4 w-4 text-primary" />
						<h3 className="text-sm font-medium text-foreground">
							Company Details
						</h3>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="legalEntityType"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Legal Entity Type
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>Type of legal entity</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value || "company"}
									>
										<FormControl>
											<SelectTrigger className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30">
												<SelectValue placeholder="Select legal entity type" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="company">
												Company
											</SelectItem>
											<SelectItem value="individual">
												Individual
											</SelectItem>
											<SelectItem value="other">
												Other
											</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="nameLine1"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Name
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>Primary name</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="Enter name"
											className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="nameLine2"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Name (Line 2)
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>
														Additional name
														information
													</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											placeholder="Additional company name"
											className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				<Separator className="my-5" />

				{/* Counterparty Types */}
				<div className="space-y-4">
					<div className="flex items-center gap-2 mb-2">
						<Tags className="h-4 w-4 text-primary" />
						<h3 className="text-sm font-medium text-foreground">
							Counterparty Types
						</h3>
					</div>

					<div className="space-y-4">
						<FormLabel className="flex items-center gap-1">
							Types
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
									</TooltipTrigger>
									<TooltipContent>
										<p>
											Select all types that apply to this
											counterparty
										</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						</FormLabel>
						<div className="flex flex-wrap gap-3">
							{availableTypes.map((type) => (
								<div
									className="flex items-center space-x-2"
									key={type.value}
								>
									<Checkbox
										id={`type-${type.value}`}
										checked={isTypeSelected(
											type.value as CounterpartyType,
										)}
										onCheckedChange={(checked) =>
											toggleType(
												type.value as CounterpartyType,
												!!checked,
											)
										}
									/>
									<label
										htmlFor={`type-${type.value}`}
										className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
									>
										{type.label}
									</label>
								</div>
							))}
						</div>

						{/* Display selected types as badges */}
						<div className="flex flex-wrap gap-2 mt-2">
							{typesArray.fields.map((field, index) => (
								<Badge
									key={field.id || index}
									className="bg-primary/10 text-primary hover:bg-primary/20"
									onClick={() =>
										removeType(
											field.type as CounterpartyType,
										)
									}
								>
									{availableTypes.find(
										(t) => t.value === field.type,
									)?.label || field.type}
									<span
										className="ml-1 cursor-pointer"
										aria-hidden="true"
									>
										×
									</span>
								</Badge>
							))}
						</div>
					</div>
				</div>

				<Separator className="my-5" />

				{/* Contact Information */}
				<div className="space-y-4">
					<div className="flex items-center gap-2 mb-2">
						<Building2 className="h-4 w-4 text-primary" />
						<h3 className="text-sm font-medium text-foreground">
							Contact Information
						</h3>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Email
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>Primary email address</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="email"
											placeholder="Enter email address"
											className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="telephone"
							render={({ field }) => (
								<FormItem className="phone-input-wrapper">
									<FormLabel>Phone</FormLabel>
									<FormControl>
										<PhoneInput
											{...field}
											placeholder="Enter phone number"
											className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											defaultCountry={selectedCountry}
											international
											key={selectedCountry || "default"}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				<Separator className="my-5" />

				{/* Business Information */}
				<div className="space-y-4">
					<div className="flex items-center gap-2 mb-2">
						<Building2 className="h-4 w-4 text-primary" />
						<h3 className="text-sm font-medium text-foreground">
							Business Information
						</h3>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-1 gap-4">
						{(form.watch("legalEntityType") === "company" ||
							form.getValues("legalEntityType") ===
								"company") && (
							<FormField
								control={form.control}
								name="uidNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											VAT Number
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															VAT number for this
															customer
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Enter VAT number"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

export default BusinessSection;
