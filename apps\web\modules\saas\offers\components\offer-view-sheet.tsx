"use client";

import { useOffersUI } from "@saas/offers/context/offers-ui-context";
import { useOfferById, useOfferMutations } from "@saas/offers/hooks/use-offer";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetFooter,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { Calendar, Mail, Tag, User } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

type OfferStatus = "open" | "accepted" | "cancelled" | "expired";

function StatusBadge({ status }: { status: OfferStatus | string | null }) {
	let statusClass = "bg-gray-100 text-gray-800";

	if (status === "accepted") {
		statusClass = "bg-green-100 text-green-800";
	} else if (status === "cancelled") {
		statusClass = "bg-red-100 text-red-800";
	} else if (status === "expired") {
		statusClass = "bg-amber-100 text-amber-800";
	} else if (status === "open") {
		statusClass = "bg-blue-100 text-blue-800";
	}

	return (
		<span
			className={cn(
				"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
				statusClass,
			)}
		>
			{status || "Unknown"}
		</span>
	);
}

function InfoRow({
	label,
	children,
}: { label: string; children: React.ReactNode }) {
	return (
		<div className="flex flex-col space-y-1">
			<p className="text-sm font-medium text-muted-foreground">{label}</p>
			<div className="text-sm">{children}</div>
		</div>
	);
}

// Loading state component for sheet content
function OfferSheetSkeleton() {
	return (
		<div className="py-6 space-y-6">
			<div className="flex items-center gap-2">
				<Skeleton className="h-5 w-5 rounded-full" />
				<Skeleton className="h-5 w-40" />
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div className="space-y-4">
					<Skeleton className="h-4 w-24" />
					<Skeleton className="h-24 w-full" />
				</div>
				<div className="space-y-4">
					<Skeleton className="h-4 w-24" />
					<Skeleton className="h-24 w-full" />
				</div>
			</div>

			<Skeleton className="h-0.5 w-full" />

			<div className="space-y-4">
				<Skeleton className="h-4 w-32" />
				<div className="space-y-3">
					<Skeleton className="h-16 w-full" />
					<Skeleton className="h-16 w-full" />
				</div>
			</div>

			<Skeleton className="h-0.5 w-full" />

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div className="space-y-2">
					<Skeleton className="h-3 w-24" />
					<Skeleton className="h-4 w-32" />
				</div>
				<div className="space-y-2">
					<Skeleton className="h-3 w-24" />
					<Skeleton className="h-4 w-32" />
				</div>
			</div>

			<Skeleton className="h-0.5 w-full" />

			<div className="space-y-4">
				<Skeleton className="h-4 w-16" />
				<Skeleton className="h-32 w-full" />
			</div>
		</div>
	);
}

export function OfferViewSheet() {
	const t = useTranslations();
	const {
		selectedOffer,
		isViewSheetOpen,
		setViewSheetOpen,
		setSelectedOffer,
		handleSendOfferEmail,
	} = useOffersUI();

	// State to track if we should fetch detailed data
	const [detailedOfferId, setDetailedOfferId] = useState<string | null>(null);

	// Get detailed offer data when sheet is open
	const { data: detailedOffer, isLoading } = useOfferById(
		detailedOfferId || "",
	);

	// Get mutations for accepting and cancelling offers
	const { acceptOffer, cancelOffer } = useOfferMutations({
		onSuccess: () => {
			setViewSheetOpen(false);
		},
	});

	// Handle accept and cancel actions
	const handleAcceptOffer = async () => {
		if (displayOffer?.id) {
			try {
				await acceptOffer(displayOffer.id);
			} catch (error) {
				console.error("Error accepting offer:", error);
			}
		}
	};

	const handleCancelOffer = async () => {
		if (displayOffer?.id) {
			try {
				await cancelOffer(displayOffer.id);
			} catch (error) {
				console.error("Error cancelling offer:", error);
			}
		}
	};

	// When sheet opens and we have a selected offer, set the ID to fetch
	useEffect(() => {
		if (isViewSheetOpen && selectedOffer?.id) {
			setDetailedOfferId(selectedOffer.id);
		} else if (!isViewSheetOpen) {
			setDetailedOfferId(null);
		}
	}, [isViewSheetOpen, selectedOffer]);

	// Use the offer with the most detailed information
	const displayOffer = detailedOffer || selectedOffer;

	if (!displayOffer) {
		return null;
	}

	// Format dates
	const validUntil = displayOffer.valid_until
		? format(new Date(displayOffer.valid_until), "PPP")
		: "-";

	const createdAt = displayOffer.createdAt
		? format(new Date(displayOffer.createdAt), "PPP")
		: "-";

	// Calculate total amounts (all line items are now revenue-only)
	const prices = displayOffer.prices || [];

	const total = prices.reduce((sum, item) => sum + (item.totalPrice || 0), 0);

	const currency =
		prices.length > 0 && prices[0].currency ? prices[0].currency : "EUR";

	const formattedAmount = new Intl.NumberFormat("de-DE", {
		style: "currency",
		currency,
	}).format(total);

	// Get status classNames for status badge
	const getStatusClassName = (status: OfferStatus): string => {
		switch (status) {
			case "accepted":
				return "bg-green-100 text-green-800";
			case "cancelled":
				return "bg-red-100 text-red-800";
			case "expired":
				return "bg-amber-100 text-amber-800";
			default:
				return "bg-blue-100 text-blue-800";
		}
	};

	return (
		<Sheet open={isViewSheetOpen} onOpenChange={setViewSheetOpen}>
			<SheetContent side="right" className="sm:max-w-xl w-full">
				<SheetHeader className="pr-6">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-xl font-bold flex items-center gap-2">
							<Tag className="h-5 w-5" />
							{displayOffer.order?.order_number || "Offer"}
						</SheetTitle>
						<StatusBadge
							status={displayOffer.status as OfferStatus}
						/>
					</div>
					<SheetDescription>
						Created on {createdAt} • Valid until {validUntil}
						{displayOffer.accepted_at && (
							<>
								{" "}
								• Accepted on{" "}
								{format(
									new Date(displayOffer.accepted_at),
									"PPP",
								)}
							</>
						)}
						{displayOffer.declined_at && (
							<>
								{" "}
								• Declined on{" "}
								{format(
									new Date(displayOffer.declined_at),
									"PPP",
								)}
							</>
						)}
					</SheetDescription>
				</SheetHeader>

				{isLoading ? (
					<OfferSheetSkeleton />
				) : (
					<div className="py-6 space-y-6 overflow-y-auto max-h-[calc(100vh-180px)]">
						{/* Customer Information */}
						<div className="space-y-4">
							<h3 className="text-sm font-medium flex items-center gap-2">
								<User className="h-4 w-4 text-muted-foreground" />
								Customer
							</h3>
							<div className="bg-muted/40 rounded-md p-3 space-y-2">
								<p className="font-medium">
									{displayOffer.customer?.nameLine1 || "-"}
								</p>
								{displayOffer.customer?.nameLine2 && (
									<p>{displayOffer.customer.nameLine2}</p>
								)}
								{displayOffer.customer?.addresses?.[0]
									?.address && (
									<p className="text-sm text-muted-foreground">
										{[
											displayOffer.customer.addresses[0]
												.address.street,
											displayOffer.customer.addresses[0]
												.address.addressSupplement,
											displayOffer.customer.addresses[0]
												.address.zipCode &&
											displayOffer.customer.addresses[0]
												.address.city
												? `${displayOffer.customer.addresses[0].address.zipCode} ${displayOffer.customer.addresses[0].address.city}`
												: displayOffer.customer
														.addresses[0].address
														.city,
											displayOffer.customer.addresses[0]
												.address.country,
										]
											.filter(Boolean)
											.join(", ")}
									</p>
								)}
							</div>
						</div>

						<Separator />

						{/* Stops Information */}
						{displayOffer.stops &&
							displayOffer.stops.length > 0 && (
								<div className="space-y-4">
									<h3 className="text-sm font-medium">
										Transport Route
									</h3>
									<div className="space-y-3">
										{displayOffer.stops.map(
											(stop, index) => (
												<div
													key={stop.id}
													className="bg-muted/40 rounded-md p-3 space-y-2"
												>
													<div className="flex justify-between items-center">
														<p className="font-medium">
															{stop.stopType ===
															"loading"
																? "Loading"
																: "Unloading"}{" "}
															{index + 1}
														</p>
														{stop.datetime_start && (
															<div className="flex items-center gap-1 text-xs text-muted-foreground">
																<Calendar className="h-3 w-3" />
																{format(
																	new Date(
																		stop.datetime_start,
																	),
																	"PPP",
																)}
															</div>
														)}
													</div>
													<p className="text-sm">
														{[
															stop.street,
															stop.zipCode &&
															stop.city
																? `${stop.zipCode} ${stop.city}`
																: stop.city,
															stop.country,
														]
															.filter(Boolean)
															.join(", ")}
													</p>

													{/* Cargo details for this stop */}
													{(stop.goods_information ||
														stop.weight ||
														stop.dangerous_goods_nr ||
														stop.loading_meter ||
														stop.cubic_meters ||
														stop.special_transports_heavy ||
														stop.crane_loading ||
														stop.crane_unloading) && (
														<div className="mt-2 pt-2 border-t border-border/30">
															<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
																{stop.goods_information && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Goods:
																		</span>
																		<span>
																			{
																				stop.goods_information
																			}
																		</span>
																	</div>
																)}

																{stop.weight && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Weight:
																		</span>
																		<span>
																			{
																				stop.weight
																			}{" "}
																			kg
																		</span>
																	</div>
																)}

																{stop.loading_meter && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Loading
																			Meters:
																		</span>
																		<span>
																			{
																				stop.loading_meter
																			}
																		</span>
																	</div>
																)}

																{stop.cubic_meters && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Cubic
																			Meters:
																		</span>
																		<span>
																			{
																				stop.cubic_meters
																			}
																		</span>
																	</div>
																)}

																{stop.dangerous_goods_nr && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Dangerous
																			Goods:
																		</span>
																		<span>
																			{
																				stop.dangerous_goods_nr
																			}
																		</span>
																	</div>
																)}

																{(stop.special_transports_height ||
																	stop.special_transports_width ||
																	stop.special_transports_length) && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Special
																			Transport:
																		</span>
																		<span>
																			{stop.special_transports_length &&
																				`L: ${stop.special_transports_length}m`}{" "}
																			{stop.special_transports_width &&
																				`W: ${stop.special_transports_width}m`}{" "}
																			{stop.special_transports_height &&
																				`H: ${stop.special_transports_height}m`}
																		</span>
																	</div>
																)}

																{(stop.crane_loading ||
																	stop.crane_unloading) && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Crane
																			Required:
																		</span>
																		<span>
																			{stop.crane_loading &&
																				"Loading"}{" "}
																			{stop.crane_loading &&
																				stop.crane_unloading &&
																				"& "}{" "}
																			{stop.crane_unloading &&
																				"Unloading"}
																		</span>
																	</div>
																)}
															</div>
														</div>
													)}

													{/* Additional stop information */}
													{(stop.reference_number ||
														stop.driver_notes ||
														stop.information_text) && (
														<div className="mt-2 pt-2 border-t border-border/30">
															<div className="grid grid-cols-1 gap-2 text-xs">
																{stop.reference_number && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Reference
																			Number:
																		</span>
																		<span>
																			{
																				stop.reference_number
																			}
																		</span>
																	</div>
																)}

																{stop.driver_notes && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Driver
																			Notes:
																		</span>
																		<span>
																			{
																				stop.driver_notes
																			}
																		</span>
																	</div>
																)}

																{stop.information_text && (
																	<div className="flex flex-col">
																		<span className="text-muted-foreground">
																			Information:
																		</span>
																		<span>
																			{
																				stop.information_text
																			}
																		</span>
																	</div>
																)}
															</div>
														</div>
													)}
												</div>
											),
										)}
									</div>
								</div>
							)}

						<Separator />

						{/* Pricing Information */}
						<div className="space-y-4">
							<h3 className="text-sm font-medium">Pricing</h3>
							<div className="bg-muted/40 rounded-md p-3">
								<div className="space-y-3">
									<div className="grid grid-cols-4 items-center text-xs text-muted-foreground font-medium mb-2">
										<div>Description</div>
										<div>Quantity</div>
										<div className="text-right">
											Unit Price
										</div>
										<div className="text-right">Total</div>
									</div>

									{prices.map((item, idx) => (
										<div
											key={idx}
											className="grid grid-cols-4 items-center text-sm"
										>
											<div>
												{item.description || "Service"}
											</div>
											<div>
												{item.quantity !== null &&
												item.quantity !== undefined &&
												item.unit
													? `${item.quantity} ${item.unit}`
													: "1"}
											</div>
											<div className="text-right">
												{item.unitPrice !== null &&
												item.unitPrice !== undefined
													? new Intl.NumberFormat(
															"de-DE",
															{
																style: "currency",
																currency:
																	item.currency,
															},
														).format(item.unitPrice)
													: "-"}
											</div>
											<div className="text-right font-medium">
												{item.totalPrice !== null &&
												item.totalPrice !== undefined
													? new Intl.NumberFormat(
															"de-DE",
															{
																style: "currency",
																currency:
																	item.currency,
															},
														).format(
															item.totalPrice,
														)
													: "-"}
											</div>
										</div>
									))}

									<Separator />

									<div className="grid grid-cols-4 items-center text-sm font-bold pt-2 border-t border-border mt-2">
										<div>Total</div>
										<div />
										<div />
										<div className="text-right">
											{formattedAmount}
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Notes */}
						{displayOffer.internal_comment && (
							<>
								<Separator />
								<div className="space-y-2">
									<h3 className="text-sm font-medium">
										Internal Notes
									</h3>
									<div className="bg-muted/40 rounded-md p-3">
										<p className="text-sm whitespace-pre-wrap">
											{displayOffer.internal_comment}
										</p>
									</div>
								</div>
							</>
						)}

						{/* Decline Reason */}
						{displayOffer.decline_reason && (
							<>
								<Separator />
								<div className="space-y-2">
									<h3 className="text-sm font-medium text-destructive">
										Decline Reason
									</h3>
									<div className="bg-destructive/10 rounded-md p-3 border border-destructive/20">
										<p className="text-sm whitespace-pre-wrap text-destructive-foreground">
											{displayOffer.decline_reason}
										</p>
									</div>
								</div>
							</>
						)}
					</div>
				)}

				<SheetFooter className="flex justify-between sm:justify-between gap-2">
					<div className="flex gap-2">
						{displayOffer.status === "open" && (
							<>
								<Button
									variant="outline"
									size="sm"
									type="button"
									onClick={() =>
										displayOffer?.id &&
										handleSendOfferEmail({
											id: displayOffer.id,
										})
									}
								>
									<Mail className="h-4 w-4 mr-1" />
									Send Email
								</Button>
								<Button
									variant="error"
									size="sm"
									type="button"
									onClick={handleCancelOffer}
								>
									Cancel Offer
								</Button>
								<Button
									variant="primary"
									size="sm"
									type="button"
									onClick={handleAcceptOffer}
								>
									Accept Offer
								</Button>
							</>
						)}
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
