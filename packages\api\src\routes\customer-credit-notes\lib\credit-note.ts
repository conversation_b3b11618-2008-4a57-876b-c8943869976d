import type { Prisma } from "@prisma/client";
import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type {
	CreateCreditNoteInput,
	CreditNoteLineItemInput,
	UpdateCreditNoteInput,
} from "../types";
import {
	deleteCreditNoteDocument,
	getCreditNoteDocument,
	saveCreditNoteDocument,
} from "./credit-note-documents";

// Add type for the processed file
interface ProcessedFile {
	buffer: Buffer;
	filename: string;
	mimetype: string;
	size: number;
}

// List credit notes for an organization with basic pagination and filtering options
export async function listCreditNotes(
	organizationId: string,
	options: {
		search?: string;
		limit?: number;
		page?: number;
		sortBy?: string;
		sortDirection?: "asc" | "desc";
		customerId?: string;
		orderId?: string;
		startDate?: Date;
		endDate?: Date;
	} = {},
) {
	// Safely parse pagination parameters with defaults
	const take = Number(options.limit) || 10;
	const page = Number(options.page) || 1;
	const skip = (page - 1) * take;

	try {
		const whereClause: Prisma.CreditNoteWhereInput = {
			organizationId,
			deletedAt: null,
		};

		// Add filters based on options
		if (options.customerId) {
			whereClause.customerId = options.customerId;
		}

		// Filter by related order
		if (options.orderId) {
			whereClause.orderCreditNotes = {
				some: {
					orderId: options.orderId,
				},
			};
		}

		// Date range filtering
		if (options.startDate || options.endDate) {
			whereClause.createdAt = {};
			if (options.startDate) {
				whereClause.createdAt.gte = options.startDate;
			}
			if (options.endDate) {
				whereClause.createdAt.lte = options.endDate;
			}
		}

		// Search implementation
		if (options.search) {
			whereClause.OR = [
				{
					credit_note_number: {
						contains: options.search,
						mode: "insensitive" as Prisma.QueryMode,
					},
				},
				{
					description: {
						contains: options.search,
						mode: "insensitive" as Prisma.QueryMode,
					},
				},
				{
					customer: {
						nameLine1: {
							contains: options.search,
							mode: "insensitive" as Prisma.QueryMode,
						},
					},
				},
			];
		}

		// Define order by field
		const orderBy: Prisma.CreditNoteOrderByWithRelationInput =
			options.sortBy
				? { [options.sortBy]: options.sortDirection ?? "asc" }
				: { createdAt: "desc" };

		// Execute queries in parallel for efficiency
		const [total, items] = await Promise.all([
			db.creditNote.count({ where: whereClause }),
			db.creditNote.findMany({
				where: whereClause,
				take,
				skip,
				orderBy,
				include: {
					customer: {
						select: {
							id: true,
							nameLine1: true,
							nameLine2: true,
							email: true,
							uidNumber: true,
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					orderCreditNotes: {
						include: {
							order: {
								select: {
									id: true,
									order_number: true,
									customer_order_number: true,
								},
							},
						},
					},
				},
			}),
		]);

		return {
			items,
			total,
			page,
			totalPages: Math.ceil(total / take),
		};
	} catch (error) {
		console.error("Failed to list credit notes:", error);
		throw new HTTPException(500, {
			message: "Failed to list credit notes",
		});
	}
}

// Retrieve a specific credit note by ID ensuring it belongs to the organization
export async function getCreditNoteById(organizationId: string, id: string) {
	try {
		const creditNote = await db.creditNote.findFirst({
			where: {
				id,
				organizationId,
				deletedAt: null,
			},
			include: {
				customer: {
					select: {
						id: true,
						nameLine1: true,
						nameLine2: true,
						email: true,
						uidNumber: true,
						financialProfile: true,
					},
				},
				creator: {
					select: {
						id: true,
						name: true,
						email: true,
					},
				},
				orderCreditNotes: {
					include: {
						order: {
							select: {
								id: true,
								order_number: true,
								customer_order_number: true,
								lineItems: {
									// Removed type filter - all line items are now revenue-only
								},
							},
						},
						lineItems: {
							include: {
								orderLineItemConnections: true,
							},
						},
					},
				},
			},
		});

		if (!creditNote) {
			throw new HTTPException(404, { message: "Credit note not found" });
		}

		return creditNote;
	} catch (error) {
		console.error("Failed to fetch credit note:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to fetch credit note",
		});
	}
}

// Create a new credit note with order allocations
export async function createCreditNote(
	organizationId: string,
	creatorId: string,
	data: CreateCreditNoteInput,
) {
	try {
		const {
			orderIds,
			order_allocations: initial_order_allocations,
			documentFile,
			...creditNoteData
		} = data;

		// Define a structure for processed allocations, allowing amounts to be optional
		let processed_allocations:
			| Array<{
					orderId: string;
					gross_amount?: number;
					net_amount?: number;
					vat_amount?: number;
					vat_rate?: number;
					lineItems?: CreditNoteLineItemInput[];
			  }>
			| undefined;

		if (initial_order_allocations && initial_order_allocations.length > 0) {
			processed_allocations = initial_order_allocations;
		} else if (orderIds && orderIds.length > 0) {
			processed_allocations = orderIds.map((id) => ({
				orderId: id,
				// gross_amount, net_amount, etc., will be undefined here
			}));
		}

		// Validate that at least one order is provided
		if (!processed_allocations || processed_allocations.length === 0) {
			throw new HTTPException(400, {
				message:
					"At least one order must be associated with the credit note",
			});
		}

		// Get order IDs from the processed allocations
		const orderIdsToUse = processed_allocations.map((a) => a.orderId);

		// Verify all orders exist and belong to the organization and fetch their line items
		const orders = await db.order.findMany({
			where: {
				id: {
					in: orderIdsToUse,
				},
				organizationId,
				deletedAt: null,
			},
			include: {
				lineItems: {
					// Removed type filter - all line items are now revenue-only
					include: {
						invoiceConnections: true,
						creditNoteConnections: true,
					},
				},
			},
		});

		if (orders.length !== orderIdsToUse.length) {
			throw new HTTPException(404, {
				message:
					"One or more orders not found or don't belong to this organization",
			});
		}

		// Create a map of order IDs to their line items for easy access
		const orderLineItemsMap = orders.reduce(
			(map, order) => {
				map[order.id] = order.lineItems;
				return map;
			},
			{} as Record<string, (typeof orders)[0]["lineItems"]>,
		);

		// Process the document file if provided
		let processedFile: ProcessedFile | undefined;

		if (documentFile) {
			if (documentFile instanceof File) {
				// Browser-based File object
				const arrayBuffer = await documentFile.arrayBuffer();
				const buffer = Buffer.from(arrayBuffer);

				processedFile = {
					buffer,
					filename: documentFile.name,
					mimetype: documentFile.type,
					size: documentFile.size,
				};
			} else if (Buffer.isBuffer(documentFile)) {
				// Direct buffer
				processedFile = {
					buffer: documentFile,
					filename: `credit-note-${Date.now()}.pdf`,
					mimetype: "application/pdf",
					size: documentFile.length,
				};
			} else if (
				typeof documentFile === "object" &&
				documentFile.buffer
			) {
				// Object with buffer property
				processedFile = {
					buffer: documentFile.buffer,
					filename:
						documentFile.filename ||
						`credit-note-${Date.now()}.pdf`,
					mimetype: documentFile.mimetype || "application/pdf",
					size: documentFile.size || documentFile.buffer.length,
				};
			}
		}

		// Start a transaction to ensure all related data is created consistently
		return await db.$transaction(async (tx) => {
			// Create the credit note
			const creditNote = await tx.creditNote.create({
				data: {
					...creditNoteData,
					organizationId,
					creatorId,
				},
			});

			// Map to track created credit note connections
			const createdCreditNoteConnections: Record<string, number> = {};

			// Create order-credit note relationships and line items using processed_allocations
			for (const allocation of processed_allocations) {
				// Since we deleted all existing relationships, treat all as new allocations
				// Get the original order's line items
				const order = await tx.order.findUnique({
					where: { id: allocation.orderId },
					include: {
						lineItems: {
							// Removed type filter - all line items are now revenue-only
						},
					},
				});

				if (!order) {
					throw new HTTPException(404, {
						message: "Order not found",
					});
				}

				// Create the new OrderCreditNote
				const orderCreditNote = await tx.orderCreditNote.create({
					data: {
						creditNoteId: creditNote.id,
						orderId: allocation.orderId,
						gross_amount_applied_to_order: allocation.gross_amount,
						net_amount_applied_to_order: allocation.net_amount,
						// VAT tracked per line item, not at allocation level
					},
				});

				// Get all available line items for this order
				const orderLineItems =
					orderLineItemsMap[allocation.orderId] || [];

				// Determine which line items to include in the credit note
				let lineItemsToCreate = [];

				// If specific line items were selected
				if (allocation.lineItems && allocation.lineItems.length > 0) {
					// Map of order line item IDs for quick lookup
					const orderLineItemMap = Object.fromEntries(
						orderLineItems.map((item) => [item.id, item]),
					);

					// Process each selected line item
					for (const selectedItem of allocation.lineItems) {
						const originalItem =
							orderLineItemMap[selectedItem.orderLineItemId];

						if (!originalItem) {
							console.warn(
								`Line item ${selectedItem.orderLineItemId} not found in order ${allocation.orderId}`,
							);
							continue;
						}

						// Create a credit note line item based on original + selected overrides
						const finalVatRate =
							selectedItem.vatRate !== undefined
								? Number(selectedItem.vatRate)
								: originalItem.vatRate;

						lineItemsToCreate.push({
							orderCreditNoteId: orderCreditNote.id,
							description:
								selectedItem.description ||
								originalItem.description ||
								"",
							quantity:
								selectedItem.quantity !== undefined
									? selectedItem.quantity
									: originalItem.quantity,
							unit: originalItem.unit,
							unitPrice:
								selectedItem.unitPrice !== undefined
									? selectedItem.unitPrice
									: originalItem.unitPrice,
							totalPrice: selectedItem.totalPrice,
							vatRate: finalVatRate,
							currency: originalItem.currency || "EUR",
							// Create the connection to the original line item via the junction table
							orderLineItemConnections: {
								create: {
									orderLineItemId:
										selectedItem.orderLineItemId,
									originalQuantity: originalItem.quantity,
									creditedQuantity:
										selectedItem.quantity !== undefined
											? selectedItem.quantity
											: originalItem.quantity,
									originalUnitPrice: originalItem.unitPrice,
									creditedUnitPrice:
										selectedItem.unitPrice !== undefined
											? selectedItem.unitPrice
											: originalItem.unitPrice,
									originalTotalPrice: originalItem.totalPrice,
									creditedTotalPrice: selectedItem.totalPrice,
								},
							},
						});

						// Track this line item for invoice status calculation
						createdCreditNoteConnections[
							selectedItem.orderLineItemId
						] = selectedItem.totalPrice;
					}
				} else {
					// Backward compatibility: if no specific line items were selected, include all customer line items
					lineItemsToCreate = orderLineItems.map((lineItem) => ({
						orderCreditNoteId: orderCreditNote.id,
						description: lineItem.description || "",
						quantity: lineItem.quantity,
						unit: lineItem.unit,
						unitPrice: lineItem.unitPrice,
						totalPrice: lineItem.totalPrice,
						vatRate: lineItem.vatRate,
						currency: lineItem.currency || "EUR",
						// Create the connection to the original line item via the junction table
						orderLineItemConnections: {
							create: {
								orderLineItemId: lineItem.id,
								originalQuantity: lineItem.quantity,
								creditedQuantity: lineItem.quantity,
								originalUnitPrice: lineItem.unitPrice,
								creditedUnitPrice: lineItem.unitPrice,
								originalTotalPrice: lineItem.totalPrice,
								creditedTotalPrice: lineItem.totalPrice,
							},
						},
					}));

					// Track all line items for invoice status calculation
					for (const lineItem of orderLineItems) {
						createdCreditNoteConnections[lineItem.id] =
							lineItem.totalPrice;
					}
				}

				// Create the line items
				if (lineItemsToCreate.length > 0) {
					for (const item of lineItemsToCreate) {
						// Extract the junction table data
						const { orderLineItemConnections, ...lineItemData } =
							item;

						// Create the credit note line item
						const lineItem = await tx.creditNoteLineItem.create({
							data: lineItemData,
						});

						// Create the junction table entry
						if (orderLineItemConnections?.create) {
							await tx.orderLineItemCreditNote.create({
								data: {
									...orderLineItemConnections.create,
									creditNoteLineItemId: lineItem.id,
								},
							});
						}
					}
				}
			}

			// Upload document if provided
			let document = null;
			if (processedFile) {
				document = await saveCreditNoteDocument(
					creditNote.id,
					organizationId,
					processedFile,
				);

				// Update credit note with document URL
				await tx.creditNote.update({
					where: { id: creditNote.id },
					data: {
						customer_document_url: document.url,
					},
				});
			}

			// Now update the invoice status of each affected order
			for (const order of orders) {
				// Calculate invoicing status for each line item in this order
				const lineItemStatuses = order.lineItems.map((lineItem) => {
					// Sum of existing invoice connections
					const invoicedAmount = lineItem.invoiceConnections.reduce(
						(sum, conn: any) =>
							sum + (conn.invoicedTotalPrice || 0),
						0,
					);

					// Sum of existing credit note connections
					const existingCreditedAmount =
						lineItem.creditNoteConnections.reduce(
							(sum, conn: any) =>
								sum + (conn.creditedTotalPrice || 0),
							0,
						);

					// Amount just credited in this transaction
					const newCreditedAmount =
						createdCreditNoteConnections[lineItem.id] || 0;

					// Total credited amount (existing + new)
					const totalCreditedAmount =
						existingCreditedAmount + newCreditedAmount;

					// Total amount covered (invoiced + credited)
					const totalCoveredAmount =
						invoicedAmount + totalCreditedAmount;

					// Check if this line item is fully covered
					const isFullyCovered =
						totalCoveredAmount >= lineItem.totalPrice;

					return {
						id: lineItem.id,
						totalPrice: lineItem.totalPrice,
						invoicedAmount,
						creditedAmount: totalCreditedAmount,
						remainingAmount: Math.max(
							0,
							lineItem.totalPrice - totalCoveredAmount,
						),
						isFullyCovered,
					};
				});

				// Check if all line items are fully covered
				const allLineItemsFullyCovered = lineItemStatuses.every(
					(item) => item.isFullyCovered,
				);
				const anyLineItemsPartiallyInvoiced = lineItemStatuses.some(
					(item) =>
						!item.isFullyCovered &&
						(item.invoicedAmount > 0 || item.creditedAmount > 0),
				);

				// Determine order's invoice status
				// TODO: Use shared updateOrderInvoiceStatus function when refactored to support transactions
				let newInvoiceStatus: string;
				if (allLineItemsFullyCovered) {
					newInvoiceStatus = "invoiced";
				} else if (anyLineItemsPartiallyInvoiced) {
					newInvoiceStatus = "partially_invoiced";
				} else {
					// Keep existing status if no changes needed
					newInvoiceStatus = order.invoice_status || "open";
				}

				// Update the order's invoice status if it changed
				if (newInvoiceStatus !== order.invoice_status) {
					await tx.order.update({
						where: { id: order.id },
						data: { invoice_status: newInvoiceStatus },
					});
				}
			}

			// Return the complete credit note with all related data
			const result = await tx.creditNote.findUnique({
				where: { id: creditNote.id },
				include: {
					customer: true,
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					orderCreditNotes: {
						include: {
							order: {
								select: {
									id: true,
									order_number: true,
									customer_order_number: true,
								},
							},
							lineItems: true,
						},
					},
				},
			});

			// Add document info if available
			if (document) {
				return {
					...result,
					document: {
						id: document.id,
						fileName: document.fileName,
						fileType: document.fileType,
						fileSize: document.fileSize,
					},
				};
			}

			return result;
		});
	} catch (error) {
		console.error("Failed to create credit note:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to create credit note",
		});
	}
}

// Update an existing credit note
export async function updateCreditNote(
	organizationId: string,
	id: string,
	data: UpdateCreditNoteInput & { removeDocument?: boolean },
) {
	try {
		// First check if the credit note exists and belongs to the organization
		const existingCreditNote = await db.creditNote.findFirst({
			where: {
				id,
				organizationId,
				deletedAt: null,
			},
		});

		if (!existingCreditNote) {
			throw new HTTPException(404, { message: "Credit note not found" });
		}

		const {
			order_allocations,
			documentFile,
			removeDocument,
			...creditNoteData
		} = data;

		// Process document file if provided
		let processedFile: ProcessedFile | undefined;

		if (documentFile) {
			if (documentFile instanceof File) {
				// Browser-based File object
				const arrayBuffer = await documentFile.arrayBuffer();
				const buffer = Buffer.from(arrayBuffer);

				processedFile = {
					buffer,
					filename: documentFile.name,
					mimetype: documentFile.type,
					size: documentFile.size,
				};
			} else if (Buffer.isBuffer(documentFile)) {
				// Direct buffer
				processedFile = {
					buffer: documentFile,
					filename: `credit-note-${Date.now()}.pdf`,
					mimetype: "application/pdf",
					size: documentFile.length,
				};
			} else if (
				typeof documentFile === "object" &&
				documentFile.buffer
			) {
				// Object with buffer property
				processedFile = {
					buffer: documentFile.buffer,
					filename:
						documentFile.filename ||
						`credit-note-${Date.now()}.pdf`,
					mimetype: documentFile.mimetype || "application/pdf",
					size: documentFile.size || documentFile.buffer.length,
				};
			}
		}

		// Start a transaction for the update
		return await db.$transaction(async (tx) => {
			// Update the credit note
			const updatedCreditNote = await tx.creditNote.update({
				where: { id },
				data: creditNoteData,
			});

			// Handle document operations
			let document = null;

			// Remove document if requested
			if (removeDocument === true) {
				await deleteCreditNoteDocument(id);

				// Update credit note to remove document URL
				await tx.creditNote.update({
					where: { id },
					data: {
						customer_document_url: null,
					},
				});
			}
			// Upload new document if provided
			else if (processedFile) {
				document = await saveCreditNoteDocument(
					id,
					organizationId,
					processedFile,
				);

				// Update credit note with document URL
				await tx.creditNote.update({
					where: { id },
					data: {
						customer_document_url: document.url,
					},
				});
			}

			// Update order allocations if provided
			if (order_allocations && order_allocations.length > 0) {
				// First, delete all existing OrderCreditNote relationships for this credit note
				// This ensures we don't hit unique constraint violations when creating new ones
				const existingOrderCreditNotes =
					await tx.orderCreditNote.findMany({
						where: { creditNoteId: id },
						include: {
							lineItems: {
								include: {
									orderLineItemConnections: true,
								},
							},
						},
					});

				// Delete all existing relationships and their line items
				for (const existingOrderCreditNote of existingOrderCreditNotes) {
					// Delete line item connections first
					for (const lineItem of existingOrderCreditNote.lineItems) {
						await tx.orderLineItemCreditNote.deleteMany({
							where: {
								creditNoteLineItemId: lineItem.id,
							},
						});
					}

					// Delete credit note line items
					await tx.creditNoteLineItem.deleteMany({
						where: {
							orderCreditNoteId: existingOrderCreditNote.id,
						},
					});

					// Delete the order credit note
					await tx.orderCreditNote.delete({
						where: { id: existingOrderCreditNote.id },
					});
				}

				// Handle each allocation - create, update, or delete as needed
				for (const allocation of order_allocations) {
					// Skip creating OrderCreditNote if there are no line items to credit
					if (
						!allocation.lineItems ||
						allocation.lineItems.length === 0
					) {
						console.log(
							`Skipping order ${allocation.orderId} - no line items to credit`,
						);
						continue;
					}

					// Since we deleted all existing relationships, treat all as new allocations
					// Get the original order's line items
					const order = await tx.order.findUnique({
						where: { id: allocation.orderId },
						include: {
							lineItems: {
								// Removed type filter - all line items are now revenue-only
							},
						},
					});

					if (!order) {
						throw new HTTPException(404, {
							message: "Order not found",
						});
					}

					// Create the new OrderCreditNote
					const orderCreditNote = await tx.orderCreditNote.create({
						data: {
							creditNoteId: id,
							orderId: allocation.orderId,
							gross_amount_applied_to_order:
								allocation.gross_amount,
							net_amount_applied_to_order: allocation.net_amount,
							// VAT tracked per line item, not at allocation level
						},
					});

					// Create line items for this new order-credit note relationship
					// Map of order line items by ID for quick lookup
					const orderLineItemMap = Object.fromEntries(
						order.lineItems.map((item) => [item.id, item]),
					);

					// Process each selected line item
					for (const lineItem of allocation.lineItems) {
						const originalItem =
							orderLineItemMap[lineItem.orderLineItemId];

						if (!originalItem) {
							console.warn(
								`Line item ${lineItem.orderLineItemId} not found in order ${allocation.orderId}`,
							);
							continue;
						}

						// Create new credit note line item
						const newLineItem = await tx.creditNoteLineItem.create({
							data: {
								orderCreditNoteId: orderCreditNote.id,
								description:
									lineItem.description ||
									originalItem.description ||
									"",
								quantity:
									lineItem.quantity !== undefined
										? lineItem.quantity
										: originalItem.quantity,
								unit: originalItem.unit,
								unitPrice:
									lineItem.unitPrice !== undefined
										? lineItem.unitPrice
										: originalItem.unitPrice,
								totalPrice: lineItem.totalPrice,
								vatRate:
									lineItem.vatRate !== undefined
										? Number(lineItem.vatRate)
										: originalItem.vatRate,
								currency: originalItem.currency || "EUR",
							},
						});

						// Create junction table record
						await tx.orderLineItemCreditNote.create({
							data: {
								orderLineItemId: lineItem.orderLineItemId,
								creditNoteLineItemId: newLineItem.id,
								originalQuantity: originalItem.quantity,
								creditedQuantity:
									lineItem.quantity !== undefined
										? lineItem.quantity
										: originalItem.quantity,
								originalUnitPrice: originalItem.unitPrice,
								creditedUnitPrice:
									lineItem.unitPrice !== undefined
										? lineItem.unitPrice
										: originalItem.unitPrice,
								originalTotalPrice: originalItem.totalPrice,
								creditedTotalPrice: lineItem.totalPrice,
							},
						});
					}
				}

				// Now update the invoice status of each affected order (same logic as create)
				// Get all affected orders with their line items and connections
				const affectedOrders = await tx.order.findMany({
					where: {
						id: {
							in: order_allocations.map((alloc) => alloc.orderId),
						},
						organizationId,
					},
					include: {
						lineItems: {
							include: {
								invoiceConnections: true,
								creditNoteConnections: true,
							},
						},
					},
				});

				// Update invoice status for each affected order
				for (const order of affectedOrders) {
					// Calculate invoicing status for each line item in this order
					const lineItemStatuses = order.lineItems.map((lineItem) => {
						// Sum of existing invoice connections
						const invoicedAmount =
							lineItem.invoiceConnections.reduce(
								(sum, conn: any) =>
									sum + (conn.invoicedTotalPrice || 0),
								0,
							);

						// Sum of existing credit note connections
						const creditedAmount =
							lineItem.creditNoteConnections.reduce(
								(sum, conn: any) =>
									sum + (conn.creditedTotalPrice || 0),
								0,
							);

						// Total amount covered (invoiced + credited)
						const totalCoveredAmount =
							invoicedAmount + creditedAmount;

						// Check if this line item is fully covered
						const isFullyCovered =
							totalCoveredAmount >= lineItem.totalPrice;

						return {
							id: lineItem.id,
							totalPrice: lineItem.totalPrice,
							invoicedAmount,
							creditedAmount,
							remainingAmount: Math.max(
								0,
								lineItem.totalPrice - totalCoveredAmount,
							),
							isFullyCovered,
						};
					});

					// Check if all line items are fully covered
					const allLineItemsFullyCovered = lineItemStatuses.every(
						(item) => item.isFullyCovered,
					);
					const anyLineItemsPartiallyInvoiced = lineItemStatuses.some(
						(item) =>
							!item.isFullyCovered &&
							(item.invoicedAmount > 0 ||
								item.creditedAmount > 0),
					);

					// Determine order's invoice status
					let newInvoiceStatus: string;
					if (allLineItemsFullyCovered) {
						newInvoiceStatus = "invoiced";
					} else if (anyLineItemsPartiallyInvoiced) {
						newInvoiceStatus = "partially_invoiced";
					} else {
						// Keep existing status if no changes needed
						newInvoiceStatus = order.invoice_status || "open";
					}

					// Update the order's invoice status if it changed
					if (newInvoiceStatus !== order.invoice_status) {
						await tx.order.update({
							where: { id: order.id },
							data: { invoice_status: newInvoiceStatus },
						});
					}
				}
			}

			// Get the updated credit note with all related data
			const result = await tx.creditNote.findUnique({
				where: { id },
				include: {
					customer: true,
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					orderCreditNotes: {
						include: {
							order: {
								select: {
									id: true,
									order_number: true,
									customer_order_number: true,
								},
							},
							lineItems: {
								include: {
									orderLineItemConnections: true,
								},
							},
						},
					},
				},
			});

			// If we just updated the document, include its info
			if (document && result) {
				return {
					...result,
					document: {
						id: document.id,
						fileName: document.fileName,
						fileType: document.fileType,
						fileSize: document.fileSize,
					},
				};
			}

			// If the credit note has an existing document, fetch its info
			if (result?.customer_document_url && !removeDocument) {
				const existingDocument = await getCreditNoteDocument(id);
				if (existingDocument) {
					return {
						...result,
						document: existingDocument,
					};
				}
			}

			return result;
		});
	} catch (error) {
		console.error("Failed to update credit note:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to update credit note",
		});
	}
}

// Delete (soft-delete) a credit note
export async function deleteCreditNote(organizationId: string, id: string) {
	try {
		const creditNote = await db.creditNote.findFirst({
			where: {
				id,
				organizationId,
				deletedAt: null,
			},
		});

		if (!creditNote) {
			throw new HTTPException(404, { message: "Credit note not found" });
		}

		// Delete any associated document
		if (creditNote.customer_document_url) {
			await deleteCreditNoteDocument(id);
		}

		// Soft delete by setting deletedAt
		const updatedCreditNote = await db.creditNote.update({
			where: { id },
			data: {
				deletedAt: new Date(),
			},
		});

		return { success: true, id: updatedCreditNote.id };
	} catch (error) {
		console.error("Failed to delete credit note:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to delete credit note",
		});
	}
}

// Helper function to upload document file for credit note
// This integrates with your storage provider (e.g., S3)
export async function uploadCreditNoteDocument(
	organizationId: string,
	creditNoteId: string,
	file: any,
) {
	try {
		// First validate that the credit note exists and belongs to this organization
		const creditNote = await db.creditNote.findFirst({
			where: {
				id: creditNoteId,
				organizationId,
				deletedAt: null,
			},
		});

		if (!creditNote) {
			throw new HTTPException(404, { message: "Credit note not found" });
		}

		// Process file based on its type
		let processedFile: ProcessedFile;

		if (file instanceof File) {
			// Browser-based File object
			const arrayBuffer = await file.arrayBuffer();
			const buffer = Buffer.from(arrayBuffer);

			processedFile = {
				buffer,
				filename: file.name,
				mimetype: file.type,
				size: file.size,
			};
		} else if (Buffer.isBuffer(file)) {
			// Direct buffer
			processedFile = {
				buffer: file,
				filename: `credit-note-${Date.now()}.pdf`,
				mimetype: "application/pdf",
				size: file.length,
			};
		} else if (typeof file === "object" && file.buffer) {
			// Object with buffer property
			processedFile = {
				buffer: file.buffer,
				filename: file.filename || `credit-note-${Date.now()}.pdf`,
				mimetype: file.mimetype || "application/pdf",
				size: file.size || file.buffer.length,
			};
		} else {
			throw new HTTPException(400, { message: "Invalid file format" });
		}

		// Save the document to S3 and get the document record
		const document = await saveCreditNoteDocument(
			creditNoteId,
			organizationId,
			processedFile,
		);

		// Update the credit note with the document URL
		await db.creditNote.update({
			where: { id: creditNoteId },
			data: {
				customer_document_url: document.url,
			},
		});

		// Return success with document info
		return {
			success: true,
			document: {
				id: document.id,
				fileName: document.fileName,
				fileType: document.fileType,
				fileSize: document.fileSize,
			},
		};
	} catch (error) {
		console.error("Failed to upload document:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, { message: "Failed to upload document" });
	}
}

// Add a function to get a credit note with its document
export async function getCreditNoteWithDocument(
	organizationId: string,
	id: string,
) {
	try {
		const creditNote = await getCreditNoteById(organizationId, id);

		// Get the document with signed URL
		const document = await getCreditNoteDocument(id);

		// Return credit note with document info
		return {
			...creditNote,
			document,
		};
	} catch (error) {
		console.error("Failed to get credit note with document:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to get credit note with document",
		});
	}
}
