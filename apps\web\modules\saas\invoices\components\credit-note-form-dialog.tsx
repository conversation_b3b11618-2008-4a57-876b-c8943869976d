"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import {
	formatCreditNoteForApi,
	useCustomerCreditNoteById,
	useCustomerCreditNoteMutations,
} from "@saas/invoices/hooks/use-customer-credit-notes";
import { useOrdersWithUninvoicedItems } from "@saas/orders/hooks/use-orders";
import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { InputWithNumber } from "@ui/components/input-with-number";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import { File, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { OrderSelector } from "./order-selector";

// Define form schema with Zod
const creditNoteFormSchema = z.object({
	customerId: z.string().min(1, "Customer is required"),
	credit_note_number: z.string().optional(),
	credit_note_date: z.date(),
	description: z.string().optional().nullable(),
	gross_amount: z.number().min(0),
	net_amount: z.number().min(0),
	orderIds: z.array(z.string()).min(1, "At least one order must be selected"),
	amount_delta_reason: z.string().optional(),
});

type CreditNoteFormValues = z.infer<typeof creditNoteFormSchema>;

interface CreditNoteFormDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess?: () => void;
	editCreditNoteId?: string | null;
}

// Main component
export function CreditNoteFormDialog({
	open,
	onOpenChange,
	onSuccess,
	editCreditNoteId,
}: CreditNoteFormDialogProps) {
	const t = useTranslations();
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";
	const { createCreditNote, updateCreditNote, isLoading } =
		useCustomerCreditNoteMutations({
			onSuccess,
		});

	const isEditMode = !!editCreditNoteId;

	// Fetch full credit note data with document when editing
	const {
		creditNote: fullCreditNoteData,
		document: creditNoteDocument,
		isLoading: isLoadingCreditNote,
	} = useCustomerCreditNoteById(
		editCreditNoteId || undefined,
		true, // includeDocument
	);

	// Get orders data to access lineItemsStatus
	// In edit mode, exclude allocations from the current credit note
	const { data: ordersData, setCustomerId: setOrdersCustomerId } =
		useOrdersWithUninvoicedItems({
			excludeCreditNoteId: isEditMode
				? editCreditNoteId || undefined
				: undefined,
		});

	// State for selected customer, orders and line items
	const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
	const [selectedLineItems, setSelectedLineItems] = useState<
		Record<string, boolean>
	>({});
	const [orderLineItems, setOrderLineItems] = useState<Record<string, any[]>>(
		{},
	);
	const [lineItemProportions, setLineItemProportions] = useState<
		Record<string, number>
	>({});
	const [documentFile, setDocumentFile] = useState<File | null>(null);
	const [documentPreviewUrl, setDocumentPreviewUrl] = useState<string | null>(
		null,
	);
	const [isDeltaAmountVisible, setIsDeltaAmountVisible] = useState(false);
	const [calculatedTotals, setCalculatedTotals] = useState({
		gross: 0,
		net: 0,
	});

	// Store expanded order state
	const [expandedOrders, setExpandedOrders] = useState<
		Record<string, boolean>
	>({});

	// Track if we've corrected selections for this edit session
	const [hasCorrectedSelections, setHasCorrectedSelections] = useState(false);

	// Track which line items should be editable in edit mode
	const [editableLineItemIds, setEditableLineItemIds] = useState<Set<string>>(
		new Set(),
	);

	const formatNumberForDisplay = useCallback(
		(num: number | undefined | null): string => {
			if (num === undefined || num === null || Number.isNaN(num)) {
				return "0,00";
			}
			// Format with fixed two decimal places and use comma as decimal separator
			return num.toLocaleString("de-DE", {
				minimumFractionDigits: 2,
				maximumFractionDigits: 2,
			});
		},
		[],
	);

	// Create form with explicit default values for all fields
	const form = useForm<CreditNoteFormValues>({
		resolver: zodResolver(creditNoteFormSchema),
		defaultValues: {
			customerId: "",
			credit_note_number: "",
			credit_note_date: new Date(),
			description: "",
			gross_amount: 0,
			net_amount: 0,
			orderIds: [],
			amount_delta_reason: "",
		},
	});

	// Extract needed values from form to avoid re-renders on all form changes
	const customerId = form.watch("customerId");
	const orderIds = form.watch("orderIds");
	const netAmount = form.watch("net_amount");
	const grossAmount = form.watch("gross_amount");

	// Memoize these values to prevent unnecessary re-renders
	const customerIdValue = useMemo(() => customerId, [customerId]);
	const orderIdsValue = useMemo(() => orderIds, [orderIds]);

	// Reset form when dialog opens/closes
	useEffect(() => {
		if (open && !isEditMode) {
			// Only reset in create mode - edit mode will be populated by the edit useEffect
			form.setValue("customerId", "");
			form.setValue("credit_note_number", "");
			form.setValue("credit_note_date", new Date());
			form.setValue("description", "");
			form.setValue("gross_amount", 0);
			form.setValue("net_amount", 0);
			form.setValue("orderIds", []);
			form.setValue("amount_delta_reason", "");

			// Reset other state
			setDocumentFile(null);
			setDocumentPreviewUrl(null);
			setOrderLineItems({});
			setSelectedCustomer(null);
			setIsDeltaAmountVisible(false);
			setExpandedOrders({});
			setSelectedLineItems({});
			setHasCorrectedSelections(false); // Reset correction flag
		}
	}, [open, isEditMode, form]);

	// Populate form when in edit mode
	useEffect(() => {
		if (open && editCreditNoteId && fullCreditNoteData) {
			// Reset correction flag when starting edit mode
			setHasCorrectedSelections(false);

			console.log("🔍 DEBUG 1 - Credit Note Data:", {
				fullCreditNoteData,
				orderCreditNotes: (fullCreditNoteData as any).orderCreditNotes,
				lineItemsInCredit: (fullCreditNoteData as any)
					.orderCreditNotes?.[0]?.lineItems,
			});

			// Populate form with existing credit note data
			form.setValue("customerId", fullCreditNoteData.customerId || "");
			form.setValue(
				"credit_note_number",
				fullCreditNoteData.credit_note_number || "",
			);
			form.setValue(
				"credit_note_date",
				fullCreditNoteData.credit_note_date
					? new Date(fullCreditNoteData.credit_note_date)
					: new Date(),
			);
			form.setValue("description", fullCreditNoteData.description || "");
			form.setValue("gross_amount", fullCreditNoteData.gross_amount || 0);
			form.setValue("net_amount", fullCreditNoteData.net_amount || 0);

			// Get order IDs from orderCreditNotes relation
			const orderIds =
				(fullCreditNoteData as any).orderCreditNotes?.map(
					(ocn: any) => ocn.orderId,
				) || [];
			form.setValue("orderIds", orderIds);

			console.log("🔍 Setting orderIds for edit mode:", orderIds);

			// Don't try to set selections here - wait for the order data to load
			// The order line items data from the credit note query doesn't have creditNoteConnections
			// We need to wait for the uninvoiced orders API data which does have the connections

			// Set selected customer if available
			if ((fullCreditNoteData as any).customer) {
				setSelectedCustomer((fullCreditNoteData as any).customer);
				setOrdersCustomerId((fullCreditNoteData as any).customer.id);
			}

			// Handle document preview if available
			if (creditNoteDocument?.data) {
				// Create preview URL for existing document
				const pdfBlob = new Blob(
					[
						new Uint8Array(
							atob(creditNoteDocument.data)
								.split("")
								.map((char) => char.charCodeAt(0)),
						),
					],
					{ type: "application/pdf" },
				);
				const previewUrl = URL.createObjectURL(pdfBlob);
				setDocumentPreviewUrl(previewUrl);
			} else if (creditNoteDocument?.url) {
				setDocumentPreviewUrl(creditNoteDocument.url);
			}
		}
	}, [
		open,
		editCreditNoteId,
		fullCreditNoteData,
		creditNoteDocument,
		form,
		setOrdersCustomerId,
	]);

	// Separate effect to set selected line items after order data is loaded
	useEffect(() => {
		if (
			isEditMode &&
			editCreditNoteId &&
			fullCreditNoteData &&
			!hasCorrectedSelections // Only run once per edit session
		) {
			const existingSelectedLineItems: Record<string, boolean> = {};
			const newEditableLineItemIds = new Set<string>();

			// For edit mode, use the credit note line items to identify selected items and their VAT rates
			(fullCreditNoteData as any).orderCreditNotes?.forEach(
				(ocn: any) => {
					// First, set up the original order line items for display
					if (ocn.order?.lineItems) {
						// Update order line items with VAT rates from credit note line items
						const updatedOrderLineItems = ocn.order.lineItems.map(
							(orderItem: any) => {
								// Find corresponding credit note line item
								const creditNoteLineItem = ocn.lineItems?.find(
									(cnItem: any) =>
										cnItem.orderLineItemConnections?.some(
											(conn: any) =>
												conn.orderLineItemId ===
												orderItem.id,
										),
								);

								// If this line item was used in the credit note, use the credit note's VAT rate
								if (creditNoteLineItem) {
									return {
										...orderItem,
										vatRate:
											creditNoteLineItem.vatRate ||
											orderItem.vatRate,
									};
								}
								return orderItem;
							},
						);

						setOrderLineItems((prev) => ({
							...prev,
							[ocn.orderId]: updatedOrderLineItems,
						}));

						// Now identify selected line items from credit note line items
						ocn.lineItems?.forEach((creditNoteLineItem: any) => {
							// Find the connected order line item
							const connection =
								creditNoteLineItem
									.orderLineItemConnections?.[0];
							if (connection) {
								const orderLineItemId =
									connection.orderLineItemId;
								const itemKey = `${ocn.orderId}:${orderLineItemId}`;

								existingSelectedLineItems[itemKey] = true;
								newEditableLineItemIds.add(orderLineItemId);
							}
						});
					}
				},
			);

			setSelectedLineItems(existingSelectedLineItems);
			setEditableLineItemIds(newEditableLineItemIds);
			setHasCorrectedSelections(true);
		}
	}, [
		isEditMode,
		editCreditNoteId,
		hasCorrectedSelections,
		fullCreditNoteData,
	]);

	// Handle customer selection
	const handleSelectedCustomerChange = (customer: any) => {
		setSelectedCustomer(customer);

		// Only reset orders if we're not in edit mode or if this is a genuine user change
		// In edit mode, we want to preserve the existing orders when setting customer programmatically
		if (!isEditMode) {
			// Reset orderIds when customer changes (only in create mode)
			form.setValue("orderIds", []);
			setOrderLineItems({});
			setExpandedOrders({});
			setSelectedLineItems({});
		}

		// Set customerId for the orders hook
		if (customer) {
			setOrdersCustomerId(customer.id);
		} else {
			setOrdersCustomerId(undefined);
		}
	};

	// Handle totals change from OrderSelector
	const handleTotalsChange = useCallback(
		(totals: {
			gross: number;
			net: number;
			vat: number;
			rate: number;
		}) => {
			setCalculatedTotals({
				gross: totals.gross,
				net: totals.net,
			});

			// Update form values with rounded numbers to avoid floating point issues
			// Note: VAT is handled per allocation, not at credit note level
			form.setValue("net_amount", Number(totals.net.toFixed(2)));
			form.setValue("gross_amount", Number(totals.gross.toFixed(2)));
		},
		[form, setCalculatedTotals],
	);

	// Separate useEffect to check for delta between calculated and form values
	useEffect(() => {
		// Only check if we have items selected (calculatedTotals.net > 0)
		if (calculatedTotals.net > 0.005) {
			// Check if there's a meaningful difference between form values and calculated totals
			const netDifference = Math.abs(netAmount - calculatedTotals.net);
			const grossDifference = Math.abs(
				grossAmount - calculatedTotals.gross,
			);

			// Show reason field if either net or gross has been modified
			const threshold = 0.01; // 1 cent threshold to account for rounding
			if (netDifference > threshold || grossDifference > threshold) {
				setIsDeltaAmountVisible(true);
			} else {
				setIsDeltaAmountVisible(false);
			}
		} else {
			// No items selected, no need to show reason
			setIsDeltaAmountVisible(false);
		}
	}, [calculatedTotals, netAmount, grossAmount]);

	// useEffect for form field watching (manual edits to net_amount or gross_amount)
	useEffect(() => {
		const subscription = form.watch((values, { name }) => {
			// Only proceed if we have relevant fields changing
			if (name === "net_amount") {
				const formNetAmount = values.net_amount || 0;

				// Adjust proportions based on the new total if needed
				if (
					calculatedTotals.net > formNetAmount &&
					calculatedTotals.net > 0
				) {
					// Calculate the ratio needed to adjust all proportions
					const ratio = formNetAmount / calculatedTotals.net;

					// We need to adjust proportions to fit the new total
					const newProportions = { ...lineItemProportions };
					let totalAdjusted = false;

					// First check if we need to adjust proportions
					for (const orderId of form.getValues("orderIds")) {
						const items = orderLineItems[orderId] || [];

						for (let i = 0; i < items.length; i++) {
							const item = items[i];
							// All line items are now revenue-only (removed customer type filter)
							{
								const itemKey = `${orderId}:${item.id || i}`;
								if (selectedLineItems[itemKey]) {
									const currentProportion =
										newProportions[itemKey] || 100;

									// Calculate new proportion (can't be more than 100%)
									const newProportion = Math.min(
										100,
										Math.floor(currentProportion * ratio),
									);

									// Only update if it's different
									if (newProportion !== currentProportion) {
										newProportions[itemKey] = newProportion;
										totalAdjusted = true;
									}
								}
							}
						}
					}

					// Only update if we actually changed any proportions
					if (totalAdjusted) {
						setLineItemProportions(newProportions);
					}
				}

				// Calculate implied gross amount using the same ratio as calculated totals
				if (calculatedTotals.net > 0) {
					const ratio = calculatedTotals.gross / calculatedTotals.net;
					const impliedGross = formNetAmount * ratio;

					form.setValue(
						"gross_amount",
						Number.parseFloat(impliedGross.toFixed(2)),
					);
				}
			}
		});

		return () => subscription.unsubscribe();
	}, [
		form,
		calculatedTotals,
		lineItemProportions,
		selectedLineItems,
		orderLineItems,
	]);

	// Handle document file upload
	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0] || null;
		setDocumentFile(file);

		// Create preview URL for the document
		if (file) {
			// For PDFs, create an object URL
			if (file.type === "application/pdf") {
				const objectUrl = URL.createObjectURL(file);
				setDocumentPreviewUrl(objectUrl);
			}
			// For images, create an object URL
			else if (file.type.startsWith("image/")) {
				const objectUrl = URL.createObjectURL(file);
				setDocumentPreviewUrl(objectUrl);
			}
		} else {
			setDocumentPreviewUrl(null);
		}
	};

	// Memoized callbacks for OrderSelector to prevent unnecessary re-renders
	const handleOrderSelectionChange = useCallback(
		(orderIds: string[]) => {
			form.setValue("orderIds", orderIds);
		},
		[form],
	);

	const handleLineItemSelectionChange = useCallback(
		(newSelectedLineItems: Record<string, boolean>) => {
			setSelectedLineItems(newSelectedLineItems);
		},
		[],
	);

	const handleOrderLineItemsChange = useCallback(
		(newOrderLineItems: Record<string, any[]>) => {
			setOrderLineItems(newOrderLineItems);
		},
		[],
	);

	const handleExpandedOrdersChange = useCallback(
		(newExpandedOrders: Record<string, boolean>) => {
			setExpandedOrders(newExpandedOrders);
		},
		[],
	);

	// Submit handler
	const onSubmit = async (
		values: CreditNoteFormValues,
		event?: React.BaseSyntheticEvent,
		ordersData?: any,
	) => {
		try {
			console.log("🔍 Form values before submit:", form.getValues());
			console.log("🔍 Values parameter:", values);

			if (!organizationId) {
				toast.error("No active organization");
				return;
			}

			// Calculate total selected items amount to determine proportional distribution
			let totalSelectedAmount = 0;
			const selectedItems: Record<
				string,
				{
					orderId: string;
					lineItem: any;
					amount: number;
				}
			> = {};

			// First pass: identify all selected items and their total value
			for (const orderId of values.orderIds) {
				const items = orderLineItems[orderId] || [];
				const orderData = ordersData?.items?.find(
					(item: any) => item.id === orderId,
				);

				for (let index = 0; index < items.length; index++) {
					const item = items[index];
					// All line items are now revenue-only (removed customer type filter)
					{
						const itemKey = `${orderId}:${item.id || index}`;
						if (selectedLineItems[itemKey]) {
							// Find the status for this line item
							const itemStatus = orderData?.lineItemsStatus?.find(
								(status: any) => status.id === item.id,
							);

							// Use remaining amount if available, otherwise use total price
							const amount =
								itemStatus?.remainingAmount !== undefined &&
								itemStatus.remainingAmount < item.totalPrice
									? itemStatus.remainingAmount
									: item.totalPrice || 0;

							totalSelectedAmount += amount;
							selectedItems[itemKey] = {
								orderId,
								lineItem: item,
								amount, // This now stores the remaining amount for selected items
							};
						}
					}
				}
			}

			// Calculate proportions based on user-entered total amount
			const desiredNetAmount = values.net_amount;
			const scaleFactor =
				totalSelectedAmount > 0
					? desiredNetAmount / totalSelectedAmount
					: 0;

			// Create order allocations with proportionally adjusted amounts
			const orderAllocations = values.orderIds.map((orderId) => {
				const orderItems = orderLineItems[orderId] || [];
				const orderData = ordersData?.items?.find(
					(item: any) => item.id === orderId,
				);

				const selectedOrderItems = orderItems.filter((item, index) => {
					const itemKey = `${orderId}:${item.id || index}`;
					return selectedLineItems[itemKey] === true;
				});

				// No items selected for this order
				if (selectedOrderItems.length === 0) {
					return { orderId };
				}

				// Calculate totals per VAT rate for this order's selected items
				let orderNetTotal = 0;
				const vatGroups: Record<
					number,
					{ netAmount: number; vatAmount: number }
				> = {};

				// Format the line items with proportionally scaled amounts
				const lineItems = selectedOrderItems.map((item, index) => {
					const itemKey = `${orderId}:${item.id || index}`;

					// Find the status for this line item
					const itemStatus = orderData?.lineItemsStatus?.find(
						(status: any) => status.id === item.id,
					);

					// Use remaining amount if available as the base for proportion
					const baseAmount =
						itemStatus?.remainingAmount !== undefined &&
						itemStatus.remainingAmount < item.totalPrice
							? itemStatus.remainingAmount
							: item.totalPrice || 0;

					// Apply scaling factor to item amount
					const proportion = Math.min(1, scaleFactor); // Cap at 100%
					const adjustedTotalPrice = baseAmount * proportion;

					// Calculate VAT for this line item
					const itemVatRate = item.vatRate || 0;
					const itemVatAmount =
						adjustedTotalPrice * (itemVatRate / 100);

					// Group by VAT rate - each VAT rate gets its own totals
					if (!vatGroups[itemVatRate]) {
						vatGroups[itemVatRate] = { netAmount: 0, vatAmount: 0 };
					}
					vatGroups[itemVatRate].netAmount += adjustedTotalPrice;
					vatGroups[itemVatRate].vatAmount += itemVatAmount;

					// Add to order net total
					orderNetTotal += adjustedTotalPrice;

					return {
						orderLineItemId: item.id,
						quantity: (item.quantity || 0) * proportion, // Scale quantity too
						unitPrice: item.unitPrice,
						totalPrice: adjustedTotalPrice,
						description: item.description,
						vatRate: itemVatRate, // Include user-editable VAT rate per line item
					};
				});

				// Calculate total VAT amount across all VAT groups
				const orderVatTotal = Object.values(vatGroups).reduce(
					(sum, group) => sum + group.vatAmount,
					0,
				);

				// For allocation level, we store aggregated amounts but VAT details are tracked per line item
				// The allocation doesn't store VAT rates since they vary per line item
				return {
					orderId,
					gross_amount: orderNetTotal + orderVatTotal,
					net_amount: orderNetTotal,
					// VAT breakdown available in vatGroups if needed for reporting:
					// vatGroups: vatGroups (could be added for detailed VAT reporting)
					lineItems,
				};
			});

			// Format data for API with our calculated order allocations
			// Note: VAT is calculated per line item, not at document or allocation level
			const formattedData = formatCreditNoteForApi({
				organizationId,
				customerId: values.customerId,
				credit_note_number: values.credit_note_number,
				credit_note_date: values.credit_note_date,
				description: values.description || undefined,
				gross_amount: values.gross_amount,
				net_amount: values.net_amount,
				// VAT excluded - tracked per line item only
				orderIds: values.orderIds,
				documentFile: documentFile || undefined,
				order_allocations: orderAllocations,
			});

			// Submit to API
			if (editCreditNoteId) {
				await updateCreditNote(editCreditNoteId, formattedData);
			} else {
				await createCreditNote(formattedData);
			}

			// Call onSuccess callback if provided
			if (onSuccess) {
				onSuccess();
			}

			// Close dialog on success
			onOpenChange(false);
		} catch (error) {
			console.error(
				`Failed to ${editCreditNoteId ? "update" : "create"} credit note:`,
				error,
			);
			toast.error(
				`Failed to ${editCreditNoteId ? "update" : "create"} credit note`,
			);
		}
	};

	// Clean up object URLs when component unmounts
	useEffect(() => {
		return () => {
			if (documentPreviewUrl) {
				URL.revokeObjectURL(documentPreviewUrl);
			}
		};
	}, [documentPreviewUrl]);

	// Reset all state when dialog closes
	useEffect(() => {
		if (!open) {
			// Reset form to default values
			form.reset({
				customerId: "",
				credit_note_number: "",
				credit_note_date: new Date(),
				description: "",
				gross_amount: 0,
				net_amount: 0,
				orderIds: [],
				amount_delta_reason: "",
			});

			// Reset other state
			setDocumentFile(null);
			setDocumentPreviewUrl(null);
			setOrderLineItems({});
			setSelectedCustomer(null);
			setIsDeltaAmountVisible(false);
			setExpandedOrders({});
			setSelectedLineItems({});
			setLineItemProportions({});
			setEditableLineItemIds(new Set());
			setHasCorrectedSelections(false);
			setCalculatedTotals({ gross: 0, net: 0 });
		}
	}, [open, form]);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-[80%] h-[80vh] max-h-[80vh] overflow-hidden flex flex-col">
				<DialogHeader>
					<DialogTitle>
						{isEditMode ? "Edit Credit Note" : "Create Credit Note"}
					</DialogTitle>
					<DialogDescription>
						{isEditMode
							? "Edit the credit note details"
							: "Create a new credit note for a customer"}
					</DialogDescription>
				</DialogHeader>

				<div className="flex-1 overflow-hidden grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* Show loading when fetching credit note data for editing */}
					{isEditMode && isLoadingCreditNote ? (
						<div className="col-span-2 flex items-center justify-center py-8">
							<div className="flex items-center gap-2">
								<Loader2 className="h-6 w-6 animate-spin" />
								<span>Loading credit note data...</span>
							</div>
						</div>
					) : (
						<>
							{/* Left Side - Document Preview */}
							<div className="border rounded-md overflow-hidden flex flex-col">
								<div className="bg-muted p-3 font-medium flex justify-between items-center">
									<span>Document Preview</span>
									<Input
										type="file"
										onChange={handleFileChange}
										accept=".pdf,.jpg,.jpeg,.png"
										className="max-w-xs"
									/>
								</div>
								<div className="flex-1 overflow-hidden">
									{documentPreviewUrl ? (
										documentFile?.type ===
											"application/pdf" ||
										(!documentFile &&
											documentPreviewUrl.includes(
												"application/pdf",
											)) ||
										(!documentFile && isEditMode) ? (
											<iframe
												src={documentPreviewUrl}
												className="w-full h-full"
												title="Credit Note Document Preview"
											/>
										) : (
											<div className="flex items-center justify-center h-full">
												<img
													src={documentPreviewUrl}
													alt="Document Preview"
													className="max-w-full max-h-full object-contain"
												/>
											</div>
										)
									) : (
										<div className="flex flex-col items-center justify-center h-full text-muted-foreground p-6">
											<File className="h-16 w-16 mb-2 opacity-50" />
											<p className="text-center">
												Upload a credit note document to
												preview it here
											</p>
											<p className="text-center text-xs mt-2">
												Supports PDF and image formats
											</p>
										</div>
									)}
								</div>
							</div>

							{/* Right Side - Form Content */}
							<div className="flex flex-col overflow-hidden">
								<Form {...form}>
									<form
										onSubmit={form.handleSubmit(
											(data, event) =>
												onSubmit(
													data,
													event,
													ordersData,
												),
										)}
										className="h-full flex flex-col overflow-hidden"
									>
										{/* Top section - non-scrollable */}
										<div className="space-y-4 mb-4">
											{/* Customer Selection */}
											<FormField
												control={form.control}
												name="customerId"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Customer
														</FormLabel>
														<FormControl>
															<CounterpartySelector
																name="customerId"
																value={
																	field.value
																}
																onChange={
																	field.onChange
																}
																type="customer"
																placeholder="Select customer"
																onSelectedCounterpartyChange={
																	handleSelectedCustomerChange
																}
																allowClear={
																	true
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Basic Info Grid */}
											<div className="grid grid-cols-2 gap-4">
												{/* Credit Note Number */}
												<FormField
													control={form.control}
													name="credit_note_number"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Credit Note
																Number
															</FormLabel>
															<FormControl>
																<Input
																	name={
																		field.name
																	}
																	value={
																		field.value ??
																		""
																	}
																	onChange={
																		field.onChange
																	}
																	onBlur={
																		field.onBlur
																	}
																	disabled={
																		field.disabled
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												{/* Credit Note Date */}
												<FormField
													control={form.control}
													name="credit_note_date"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Credit Note Date
															</FormLabel>
															<FormControl>
																<InputWithDate
																	inputProps={{
																		value: field.value
																			? format(
																					field.value,
																					"dd.MM.yyyy",
																				)
																			: undefined,
																		name: field.name,
																	}}
																	onDateChange={(
																		date,
																	) => {
																		if (
																			date instanceof
																			Date
																		) {
																			field.onChange(
																				date,
																			);
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>

											{/* Description */}
											<FormField
												control={form.control}
												name="description"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Description
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Enter description of the credit note"
																className="min-h-[80px]"
																{...field}
																value={
																	field.value ||
																	""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>

										{/* Order Selector Component */}
										{customerIdValue && (
											<div className="flex-1 min-h-0 mb-4 overflow-auto">
												<OrderSelector
													key={`order-selector-${Object.keys(selectedLineItems).join(",")}`}
													customerId={customerIdValue}
													selectedOrderIds={
														orderIdsValue
													}
													onOrderSelectionChange={
														handleOrderSelectionChange
													}
													selectedLineItems={
														selectedLineItems
													}
													onLineItemSelectionChange={
														handleLineItemSelectionChange
													}
													orderLineItems={
														orderLineItems
													}
													onOrderLineItemsChange={
														handleOrderLineItemsChange
													}
													expandedOrders={
														expandedOrders
													}
													onExpandedOrdersChange={
														handleExpandedOrdersChange
													}
													onTotalsChange={
														handleTotalsChange
													}
													lineItemProportions={
														lineItemProportions
													}
													onLineItemProportionsChange={
														setLineItemProportions
													}
													defaultVat={
														selectedCustomer
															?.financialProfile
															?.vatTerm?.rate ??
														19
													}
													currentVatRate={
														selectedCustomer
															?.financialProfile
															?.vatTerm?.rate ??
														19
													}
													isEditMode={isEditMode}
													editCreditNoteId={
														editCreditNoteId
													}
													editableLineItemIds={
														editableLineItemIds
													}
												/>
											</div>
										)}

										{/* Bottom section - non-scrollable */}
										<div className="space-y-4 mt-auto">
											{/* Amounts */}
											<div className="grid grid-cols-2 gap-4 pt-4 border-t">
												<FormField
													control={form.control}
													name="net_amount"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Net Amount
															</FormLabel>
															<FormControl>
																<InputWithNumber
																	inputProps={{
																		name: field.name,
																		placeholder:
																			"0,00",
																		disabled:
																			field.disabled,
																		defaultValue:
																			field.value,
																	}}
																	onNumberChange={(
																		value,
																	) => {
																		field.onChange(
																			value ===
																				null
																				? 0
																				: value,
																		);
																	}}
																	precision={
																		2
																	}
																/>
															</FormControl>
															<p className="text-xs text-muted-foreground mt-1">
																Amounts will be
																distributed
																proportionally
																across selected
																line items
															</p>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="gross_amount"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Gross Amount
															</FormLabel>
															<FormControl>
																<InputWithNumber
																	inputProps={{
																		name: field.name,
																		placeholder:
																			"0,00",
																		disabled:
																			field.disabled,
																		defaultValue:
																			field.value,
																	}}
																	onNumberChange={(
																		value,
																	) => {
																		field.onChange(
																			value ===
																				null
																				? 0
																				: value,
																		);
																	}}
																	precision={
																		2
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>

											{/* Delta Amount Reason - only shown when there's a difference in net amount */}
											{isDeltaAmountVisible && (
												<FormField
													control={form.control}
													name="amount_delta_reason"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Reason for
																Amount
																Difference
															</FormLabel>
															<FormControl>
																<Textarea
																	placeholder="Please explain the reason for the difference between calculated and entered amounts"
																	className="min-h-[80px]"
																	{...field}
																	value={
																		field.value ||
																		""
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											)}

											<DialogFooter className="pt-4">
												<Button
													type="button"
													variant="outline"
													onClick={() =>
														onOpenChange(false)
													}
												>
													Cancel
												</Button>
												<Button
													type="submit"
													disabled={
														isLoading ||
														(isEditMode &&
															isLoadingCreditNote)
													}
												>
													{isLoading ? (
														<>
															<Loader2 className="mr-2 h-4 w-4 animate-spin" />
															{isEditMode
																? "Updating..."
																: "Creating..."}
														</>
													) : isEditMode ? (
														"Update Credit Note"
													) : (
														"Create Credit Note"
													)}
												</Button>
											</DialogFooter>
										</div>
									</form>
								</Form>
							</div>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
