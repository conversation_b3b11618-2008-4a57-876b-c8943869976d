import { ClickableLinkPlugin } from "@lexical/react/LexicalClickableLinkPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { Button } from "@ui/components/button";
import { ContentEditable } from "@ui/components/editor/editor-ui/content-editable";
import { AutoLinkPlugin } from "@ui/components/editor/plugins/auto-link-plugin";
import { EmojiPickerPlugin } from "@ui/components/editor/plugins/emoji-picker-plugin";
import { EmojisPlugin } from "@ui/components/editor/plugins/emojis-plugin";
import { FloatingLinkEditorPlugin } from "@ui/components/editor/plugins/floating-link-editor-plugin";
import { LinkPlugin } from "@ui/components/editor/plugins/link-plugin";
import { FontFormatToolbarPlugin } from "@ui/components/editor/plugins/toolbar/font-format-toolbar-plugin";
import { LinkToolbarPlugin } from "@ui/components/editor/plugins/toolbar/link-toolbar-plugin";
import { ToolbarPlugin } from "@ui/components/editor/plugins/toolbar/toolbar-plugin";
import { Send } from "lucide-react";
import { useState } from "react";
import { CommentMentionsPlugin } from "./plugins/CommentMentionsPlugin";

interface PluginsProps {
	placeholder: string;
	compact: boolean;
	onSubmit?: () => void;
	submitButtonText: string;
	onCancel?: () => void;
	isLoading: boolean;
	hasContent?: boolean;
	readOnly?: boolean;
}

export function Plugins({
	placeholder,
	compact,
	onSubmit,
	submitButtonText,
	onCancel,
	isLoading,
	hasContent = false,
	readOnly = false,
}: PluginsProps) {
	const [floatingAnchorElem, setFloatingAnchorElem] =
		useState<HTMLDivElement | null>(null);

	const onRef = (_floatingAnchorElem: HTMLDivElement) => {
		if (_floatingAnchorElem !== null) {
			setFloatingAnchorElem(_floatingAnchorElem);
		}
	};

	return (
		<div className="relative">
			{/* Only show toolbar in edit mode */}
			{!readOnly && (
				<>
					{/* Compact toolbar for comments */}
					{compact ? (
						<ToolbarPlugin>
							{() => (
								<div className="flex items-center gap-2 p-2 border-b">
									<div className="flex items-center gap-1">
										<FontFormatToolbarPlugin format="bold" />
										<FontFormatToolbarPlugin format="italic" />
										<LinkToolbarPlugin />
									</div>
									<div className="text-xs text-muted-foreground">
										Use @ to mention • Ctrl+Enter to submit
									</div>
								</div>
							)}
						</ToolbarPlugin>
					) : (
						// Full toolbar (inherited from editor-00)
						<ToolbarPlugin>
							{() => (
								<div className="sticky top-0 z-10 flex gap-2 overflow-auto border-b p-1">
									<FontFormatToolbarPlugin format="bold" />
									<FontFormatToolbarPlugin format="italic" />
									<FontFormatToolbarPlugin format="underline" />
									<LinkToolbarPlugin />
								</div>
							)}
						</ToolbarPlugin>
					)}
				</>
			)}

			<div className="relative">
				<RichTextPlugin
					contentEditable={
						<div className="">
							<div className="" ref={onRef}>
								<ContentEditable
									placeholder={placeholder}
									className={`${readOnly ? "min-h-auto" : "min-h-[80px]"} p-3 focus:outline-none`}
									placeholderClassName="pointer-events-none absolute left-0 top-0 select-none overflow-hidden text-ellipsis p-3 text-muted-foreground"
								/>
							</div>
						</div>
					}
					ErrorBoundary={LexicalErrorBoundary}
				/>

				{/* Core plugins */}
				<HistoryPlugin />
				<ListPlugin />
				<ClickableLinkPlugin />
				<AutoLinkPlugin />
				<LinkPlugin />

				{/* Comment-specific plugins - only in edit mode */}
				{!readOnly && (
					<>
						<CommentMentionsPlugin />
						<EmojisPlugin />
						<EmojiPickerPlugin />
					</>
				)}

				{!readOnly && (
					<FloatingLinkEditorPlugin anchorElem={floatingAnchorElem} />
				)}
			</div>

			{/* Submit controls - only in edit mode */}
			{!readOnly && onSubmit && (
				<div className="flex items-center justify-between p-3 border-t bg-muted/5">
					<div className="flex items-center gap-2">
						<span className="text-xs text-muted-foreground">
							Markdown supported
						</span>
					</div>

					<div className="flex gap-2">
						{onCancel && (
							<Button
								variant="ghost"
								size="sm"
								onClick={onCancel}
								disabled={isLoading}
							>
								Cancel
							</Button>
						)}
						<Button
							onClick={onSubmit}
							size="sm"
							disabled={!hasContent || isLoading}
						>
							{isLoading ? (
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
							) : (
								<>
									<Send className="w-4 h-4 mr-2" />
									{submitButtonText}
								</>
							)}
						</Button>
					</div>
				</div>
			)}
		</div>
	);
}
