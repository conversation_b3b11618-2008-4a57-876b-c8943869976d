import type { UpdateCarrierConfigurationInput } from "@repo/api/src/routes/settings/contact-settings/types";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Query keys for React Query
export const carrierConfigurationKeys = {
	all: ["carrier-configuration"] as const,
	details: () => [...carrierConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...carrierConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchCarrierConfiguration = async (organizationId: string) => {
	const response = await apiClient.settings["carrier-settings"].$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch carrier configuration");
	}

	return response.json();
};

// React Query Hooks
export const useCarrierConfigurationQuery = (
	organizationId: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined ? options.enabled : !!organizationId;

	return useQuery({
		queryKey: carrierConfigurationKeys.detail(organizationId),
		queryFn: () => fetchCarrierConfiguration(organizationId),
		enabled: isEnabled && !!organizationId,
	});
};

// Mutation Hooks
export const useUpdateCarrierConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateCarrierConfigurationInput, "organizationId">,
		) => {
			const response = await apiClient.settings["carrier-settings"].$put({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to update carrier configuration");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Carrier configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: carrierConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update carrier configuration",
			);
		},
	});
};
