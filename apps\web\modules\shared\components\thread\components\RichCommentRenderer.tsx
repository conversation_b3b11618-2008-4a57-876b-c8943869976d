"use client";

import { CommentEditor } from "@ui/components/blocks/editor-comment";
import type { SerializedEditorState } from "lexical";

interface RichCommentRendererProps {
	content: string;
	richContent?: string; // JSON string of SerializedEditorState
	className?: string;
}

export function RichCommentRenderer({
	content,
	richContent,
	className = "",
}: RichCommentRendererProps) {
	// Try to parse rich content if available
	let editorState: SerializedEditorState | undefined;

	if (richContent) {
		try {
			editorState = JSON.parse(richContent);
		} catch (error) {
			console.warn("Failed to parse rich content:", error);
		}
	}

	// If we have rich content, use the editor in read-only mode
	if (editorState) {
		return (
			<div
				className={`[&_p]:!mt-0 [&_p]:!mb-1 [&_p]:!leading-tight [&_p:last-child]:!mb-0 ${className}`}
			>
				<CommentEditor
					editorSerializedState={editorState}
					compact={false}
					readOnly={true}
					placeholder=""
				/>
			</div>
		);
	}

	// Fallback to plain text with simple formatting
	return (
		<div className={`prose prose-sm max-w-none ${className}`}>
			{content.split("\n").map((line, index) => (
				<p key={index} className="mb-2 last:mb-0">
					{line || "\u00A0"}{" "}
					{/* Non-breaking space for empty lines */}
				</p>
			))}
		</div>
	);
}
