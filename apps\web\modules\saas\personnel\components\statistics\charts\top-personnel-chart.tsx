"use client";

import { Badge } from "@ui/components/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import { semanticColors } from "../utils/chart-colors";
import { formatNumber, formatPercentage } from "../utils/chart-helpers";
import type { TopPersonnelAbsenceData } from "../utils/statistics-types";

interface TopPersonnelChartProps {
	data: TopPersonnelAbsenceData[];
	isLoading?: boolean;
	title?: string;
	height?: number;
}

/**
 * A ranked list showing personnel with highest absence rates
 */
export function TopPersonnelChart({
	data = [], // Default to empty array for safety
	isLoading = false,
	title = "Top Personnel Absences",
	height = 300,
}: TopPersonnelChartProps) {
	if (isLoading) {
		return <TopPersonnelChartSkeleton height={height} />;
	}

	// Sort data by absenceDays in descending order
	const sortedData = [...data]
		.sort((a, b) => b.absenceDays - a.absenceDays)
		.slice(0, 5);

	// Helper function to get color based on absence rate
	const getAbsenceRateColor = (rate: number) => {
		if (rate >= 12) {
			// Critical - red
			return "bg-red-500 text-white";
		}
		if (rate >= 8) {
			// High - amber
			return "bg-amber-500 text-amber-950";
		}
		if (rate >= 5) {
			// Medium - blue
			return "bg-blue-500 text-white";
		}
		// Low - green
		return "bg-green-500 text-white";
	};

	// Function to get badge color consistently with semantic colors
	const getBadgeStyle = (rate: number) => {
		if (rate >= 12) {
			return { backgroundColor: semanticColors.negative, color: "white" };
		}
		if (rate >= 8) {
			return {
				backgroundColor: semanticColors.caution,
				color: "#78350f",
			};
		}
		if (rate >= 5) {
			return { backgroundColor: semanticColors.neutral, color: "white" };
		}
		return { backgroundColor: semanticColors.positive, color: "white" };
	};

	return (
		<Card className="w-full">
			<CardHeader className="pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
			</CardHeader>
			<CardContent>
				<ScrollArea
					className="w-full"
					style={{ height: `${height}px` }}
				>
					<div className="space-y-4 pr-4">
						{sortedData.map((person, index) => (
							<div
								key={index}
								className="flex items-center border-b pb-3 last:border-0 last:pb-0"
								style={{
									transition: "all 0.3s ease-out",
									transitionDelay: `${index * 50}ms`,
									opacity: 1,
									transform: "translateY(0)",
								}}
							>
								<div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-muted font-medium text-muted-foreground mr-3">
									{index + 1}
								</div>
								<div className="flex-grow min-w-0">
									<div className="flex justify-between items-start">
										<div>
											<h4 className="font-medium text-sm truncate">
												{person.name}
											</h4>
											<p className="text-xs text-muted-foreground truncate">
												{person.departmentName}
											</p>
										</div>
										<Badge
											style={getBadgeStyle(
												person.absenceRate,
											)}
										>
											{formatPercentage(
												person.absenceRate,
											)}
										</Badge>
									</div>
									<div className="flex justify-between mt-1 text-xs">
										<div className="flex items-center">
											<span className="text-muted-foreground mr-1">
												Days:
											</span>
											<span className="font-medium">
												{formatNumber(
													person.absenceDays,
												)}
											</span>
										</div>
										<div className="flex items-center">
											<span className="text-muted-foreground mr-1">
												Instances:
											</span>
											<span className="font-medium">
												{formatNumber(
													person.absenceCount,
												)}
											</span>
										</div>
									</div>
								</div>
							</div>
						))}

						{sortedData.length === 0 && (
							<div className="h-full flex items-center justify-center text-muted-foreground text-sm">
								No personnel data available
							</div>
						)}
					</div>
				</ScrollArea>

				{/* Legend */}
				<div className="flex items-center justify-center mt-4 text-xs space-x-4">
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.positive }}
						/>
						<span>Low (&lt;5%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.neutral }}
						/>
						<span>Medium (5-8%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.caution }}
						/>
						<span>High (8-12%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.negative }}
						/>
						<span>Critical (≥12%)</span>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

/**
 * Skeleton loader for the top personnel chart
 */
function TopPersonnelChartSkeleton({ height }: { height: number }) {
	return (
		<Card className="w-full">
			<CardHeader className="pb-2">
				<Skeleton className="h-4 w-32" />
			</CardHeader>
			<CardContent>
				<div style={{ height: `${height}px` }} className="w-full">
					<div className="space-y-4">
						{Array.from({ length: 5 }).map((_, i) => (
							<div
								key={i}
								className="flex items-center border-b pb-3 last:border-0 last:pb-0"
							>
								<Skeleton className="w-8 h-8 rounded-full mr-3" />
								<div className="flex-grow">
									<Skeleton className="h-4 w-2/3 mb-2" />
									<Skeleton className="h-3 w-1/3" />
								</div>
								<Skeleton className="w-12 h-6 ml-2" />
							</div>
						))}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
