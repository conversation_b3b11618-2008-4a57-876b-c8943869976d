import type { EmailIdentityInput } from "@repo/api/src/routes/organizations/email-identities/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface ListEmailIdentitiesParams {
	organizationId: string;
}

// Type for email identity response
export interface EmailIdentity {
	id: string;
	email: string;
	verificationStatus: string;
	createdAt: string;
	verifiedAt: string | null;
	lastStatusCheckAt: string | null;
	isPrimary: boolean;
	source: string; // "SES" or "SMTP"
	smtpConfigId: string | null;
}

export interface EmailIdentitiesResponse {
	items: EmailIdentity[];
}

// Query keys for React Query
export const emailIdentityKeys = {
	all: ["email-identities"] as const,
	lists: () => [...emailIdentityKeys.all, "list"] as const,
	list: (params: ListEmailIdentitiesParams) =>
		[...emailIdentityKeys.lists(), params] as const,
	details: () => [...emailIdentityKeys.all, "detail"] as const,
	detail: (organizationId: string, id: string) =>
		[...emailIdentityKeys.details(), organizationId, id] as const,
};

// API functions
export const fetchEmailIdentities = async (
	params: ListEmailIdentitiesParams,
) => {
	const response = await apiClient.organizations["email-identities"].$get({
		query: {
			organizationId: params.organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch email identities");
	}

	return response.json();
};

export const fetchEmailIdentity = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.organizations["email-identities"][
		":identityId"
	].$get({
		query: { organizationId },
		param: {
			identityId: id,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch email identity");
	}

	return response.json();
};

export const createEmailIdentity = async (
	organizationId: string,
	data: EmailIdentityInput,
) => {
	const response = await apiClient.organizations["email-identities"].$post({
		json: { ...data, organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to create email identity");
	}

	return response.json();
};

export const checkEmailIdentityStatus = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.organizations["email-identities"][
		":identityId"
	].status.$get({
		query: { organizationId },
		param: {
			identityId: id,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to check email identity status");
	}

	return response.json();
};

export const resendEmailVerification = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.organizations["email-identities"][
		":identityId"
	].resend.$post({
		query: { organizationId },
		param: {
			identityId: id,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to resend email verification");
	}

	return response.json();
};

export const setPrimaryEmailIdentity = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.organizations["email-identities"][
		":identityId"
	].primary.$post({
		query: { organizationId },
		param: {
			identityId: id,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to set email identity as primary");
	}

	return response.json();
};

export const deleteEmailIdentity = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.organizations["email-identities"][
		":identityId"
	].$delete({
		query: { organizationId },
		param: {
			identityId: id,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to delete email identity");
	}

	return response.json();
};

// React Query Hooks
export const useEmailIdentitiesQuery = (
	params: ListEmailIdentitiesParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: emailIdentityKeys.list(params),
		queryFn: () => fetchEmailIdentities(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

export const useEmailIdentityQuery = (
	organizationId: string,
	id: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!organizationId && !!id;

	return useQuery({
		queryKey: emailIdentityKeys.detail(organizationId, id),
		queryFn: () => fetchEmailIdentity(organizationId, id),
		enabled: isEnabled && !!organizationId && !!id,
	});
};

export const useCreateEmailIdentityMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: EmailIdentityInput) => {
			return createEmailIdentity(organizationId, data);
		},
		onSuccess: () => {
			toast.success("Email identity created successfully");
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create email identity",
			);
		},
	});
};

export const useCheckEmailIdentityStatusMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			return checkEmailIdentityStatus(organizationId, id);
		},
		onSuccess: (_, id) => {
			toast.success("Email identity status checked successfully");
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.detail(organizationId, id),
			});
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to check email identity status",
			);
		},
	});
};

export const useResendEmailVerificationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			return resendEmailVerification(organizationId, id);
		},
		onSuccess: (_, id) => {
			toast.success("Verification email resent successfully");
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.detail(organizationId, id),
			});
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to resend verification email",
			);
		},
	});
};

export const useSetPrimaryEmailIdentityMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			return setPrimaryEmailIdentity(organizationId, id);
		},
		onSuccess: (_, id) => {
			toast.success("Email identity set as primary");
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.detail(organizationId, id),
			});
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to set email identity as primary",
			);
		},
	});
};

export const useDeleteEmailIdentityMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			return deleteEmailIdentity(organizationId, id);
		},
		onSuccess: () => {
			toast.success("Email identity deleted successfully");
			queryClient.invalidateQueries({
				queryKey: emailIdentityKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete email identity",
			);
		},
	});
};

export type EmailIdentityStatus = "PENDING" | "VERIFIED" | "FAILED";

export interface CreateEmailIdentityRequest {
	email: string;
}

export interface SetPrimaryEmailIdentityRequest {
	id: string;
}

export interface ResendEmailIdentityVerificationRequest {
	id: string;
}

export interface DeleteEmailIdentityRequest {
	id: string;
}
