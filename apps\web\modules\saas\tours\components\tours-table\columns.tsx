"use client";

import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import { useToursUI } from "@saas/tours/context/tours-ui-context";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import {
	Building2,
	Edit,
	Flag,
	FlagTriangleRight,
	MapPin,
	MoreHorizontal,
	Package,
	Tag,
	Trash,
	Truck,
	User,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useMemo } from "react";
import type { Tour } from "../../context/tours-ui-context";

// Add this type declaration for row actions
declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

type ActionsCellProps = {
	row: Row<Tour>;
};

// Helper function to format stop time
function formatStopTime(stop: any) {
	if (!stop?.datetime_start && !stop?.datetime_end) {
		return "";
	}

	if (
		stop.time_type === "from-to" &&
		stop.datetime_start &&
		stop.datetime_end
	) {
		return `${format(new Date(stop.datetime_start), "HH:mm")} - ${format(new Date(stop.datetime_end), "HH:mm")}`;
	}

	if (stop.time_type === "from" && stop.datetime_start) {
		return `From ${format(new Date(stop.datetime_start), "HH:mm")}`;
	}

	if (stop.time_type === "to" && stop.datetime_end) {
		return `Until ${format(new Date(stop.datetime_end), "HH:mm")}`;
	}

	if (stop.datetime_start) {
		return format(new Date(stop.datetime_start), "dd.MM.yyyy, HH:mm");
	}

	if (stop.datetime_end) {
		return format(new Date(stop.datetime_end), "dd.MM.yyyy, HH:mm");
	}

	return "";
}

// Helper function to format shortened address for display
function formatShortAddress(stop: any) {
	if (!stop) {
		return "";
	}
	return `${stop.zipCode || ""} ${stop.city || ""}${stop.country ? `, ${stop.country}` : ""}`.trim();
}

function ActionsCell({ row }: ActionsCellProps) {
	const t = useTranslations();
	const tour = row.original;
	const { handleDeleteTour } = useToursUI();
	const params = useParams<{ organizationSlug: string }>();

	const navigateToTourEdit = () => {
		if (params.organizationSlug) {
			window.location.href = `/app/${params.organizationSlug}/tours/${tour.id}`;
		}
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{t("common.actions.title")}
				</DropdownMenuLabel>
				<DropdownMenuItem onClick={navigateToTourEdit}>
					<Edit className="mr-2 h-4 w-4" />
					{t("common.edit")}
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteTour(tour)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					{t("common.delete")}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<Tour>[] {
	const t = useTranslations();
	const { handleDeleteTour } = useToursUI();
	const params = useParams<{ organizationSlug: string }>();

	return useMemo(
		() => [
			// Tour Number Column
			{
				accessorKey: "tourNumber",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.tourNumber")}
					/>
				),
				cell: ({ row }) => {
					const tour = row.original;
					const tourNumber = row.getValue("tourNumber") as string;
					return (
						<div className="flex items-center gap-2">
							<Tag className="h-4 w-4 text-muted-foreground" />
							<Link
								href={`/app/${params.organizationSlug}/tours/${tour.id}`}
								className="hover:underline cursor-pointer"
							>
								<span>{tourNumber || "-"}</span>
							</Link>
						</div>
					);
				},
				enableSorting: true,
				size: 160,
			},

			// Order Numbers Column
			{
				accessorKey: "tourOrders",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.orderNumbers")}
					/>
				),
				cell: ({ row }) => {
					const tourOrders = row.original.tourOrders || [];

					if (tourOrders.length === 0) {
						return <div className="text-muted-foreground">-</div>;
					}

					return (
						<div className="flex flex-col gap-1">
							{tourOrders.map((tourOrder, index) => (
								<div
									key={tourOrder.id}
									className="flex items-center gap-2"
								>
									<Package className="h-4 w-4 text-muted-foreground" />
									{tourOrder.order?.id ? (
										<Link
											href={`/app/${params.organizationSlug}/orders/${tourOrder.order.id}`}
											className="hover:underline cursor-pointer"
										>
											<span>
												{tourOrder.order
													?.order_number || "-"}
											</span>
										</Link>
									) : (
										<span>
											{tourOrder.order?.order_number ||
												"-"}
										</span>
									)}
								</div>
							))}
						</div>
					);
				},
				enableSorting: false,
				size: 160,
			},

			// Customer Names Column
			{
				accessorKey: "customers",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.customers")}
					/>
				),
				cell: ({ row }) => {
					const tourOrders = row.original.tourOrders || [];

					if (tourOrders.length === 0) {
						return <div className="text-muted-foreground">-</div>;
					}

					// Create a Map to deduplicate customers by ID
					const customersMap = new Map();

					tourOrders.forEach((tourOrder) => {
						if (tourOrder.order?.customer) {
							const customer = tourOrder.order.customer;
							customersMap.set(customer.id, customer);
						}
					});

					const uniqueCustomers = Array.from(customersMap.values());

					return (
						<div className="flex flex-col gap-1">
							{uniqueCustomers.map((customer) => (
								<div
									key={customer.id}
									className="flex items-center gap-2"
								>
									<User className="h-4 w-4 text-muted-foreground" />
									{customer.id ? (
										<Link
											href={`/app/${params.organizationSlug}/contacts/${customer.id}`}
											className="hover:underline cursor-pointer"
										>
											<span>
												{customer.nameLine1 || "-"}
											</span>
										</Link>
									) : (
										<span>{customer.nameLine1 || "-"}</span>
									)}
								</div>
							))}
						</div>
					);
				},
				enableSorting: false,
				size: 180,
			},

			// Status Column
			{
				accessorKey: "status",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.status")}
					/>
				),
				cell: ({ row }) => {
					const status = row.getValue("status") as string;
					return (
						<div className="flex items-center">
							<span
								className={`px-2 py-1 rounded-md text-xs uppercase font-medium ${
									status === "open"
										? "bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-400"
										: status === "cancelled"
											? "bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400"
											: status === "completed"
												? "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400"
												: status === "confirmed"
													? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800/20 dark:text-yellow-400"
													: status === "draft"
														? "bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400"
														: "bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400"
								}`}
							>
								{status || "draft"}
							</span>
						</div>
					);
				},
				enableSorting: true,
				size: 120,
			},

			// First Stop (Start) Column
			{
				accessorKey: "stops",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.firstStop")}
					/>
				),
				cell: ({ row }) => {
					const stops = row.original.stops;
					const sortedStops = stops?.length
						? [...stops].sort(
								(a, b) =>
									(a.positionTour || 0) -
									(b.positionTour || 0),
							)
						: [];
					const firstStop =
						sortedStops.length > 0 ? sortedStops[0] : null;

					if (!firstStop) {
						return <div className="text-muted-foreground">-</div>;
					}

					return (
						<div className="flex flex-col">
							<div className="flex items-center">
								<FlagTriangleRight className="h-3.5 w-3.5 mr-1 text-emerald-500" />
								<span>{formatShortAddress(firstStop)}</span>
							</div>
							<div className="text-xs text-muted-foreground">
								{firstStop.datetime_start
									? format(
											new Date(firstStop.datetime_start),
											"dd.MM.yyyy, HH:mm",
										)
									: "No date"}
							</div>
						</div>
					);
				},
				enableSorting: false,
				size: 200,
			},

			// Last Stop (End) Column
			{
				accessorKey: "lastStop",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.lastStop")}
					/>
				),
				cell: ({ row }) => {
					const stops = row.original.stops;
					const sortedStops = stops?.length
						? [...stops].sort(
								(a, b) =>
									(a.positionTour || 0) -
									(b.positionTour || 0),
							)
						: [];
					const lastStop =
						sortedStops.length > 1
							? sortedStops[sortedStops.length - 1]
							: null;

					if (!lastStop || lastStop === sortedStops[0]) {
						return <div className="text-muted-foreground">-</div>;
					}

					return (
						<div className="flex flex-col">
							<div className="flex items-center">
								<Flag className="h-3.5 w-3.5 mr-1 text-red-500" />
								<span>{formatShortAddress(lastStop)}</span>
							</div>
							<div className="text-xs text-muted-foreground">
								{lastStop.datetime_start
									? format(
											new Date(lastStop.datetime_start),
											"dd.MM.yyyy, HH:mm",
										)
									: lastStop.datetime_end
										? format(
												new Date(lastStop.datetime_end),
												"dd.MM.yyyy, HH:mm",
											)
										: "No date"}
							</div>
						</div>
					);
				},
				enableSorting: false,
				size: 200,
			},

			// Vehicle Column
			{
				accessorKey: "vehicle",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.vehicle")}
					/>
				),
				cell: ({ row }) => {
					const vehicle = row.original.vehicle;
					return (
						<div className="flex items-center gap-2">
							<Truck className="h-4 w-4 text-muted-foreground" />
							{vehicle?.id ? (
								<Link
									href={`/app/${params.organizationSlug}/vehicles/${vehicle.id}`}
									className="hover:underline cursor-pointer"
								>
									<span>{vehicle?.licensePlate || "-"}</span>
								</Link>
							) : (
								<span>{vehicle?.licensePlate || "-"}</span>
							)}
						</div>
					);
				},
				enableSorting: true,
				size: 140,
			},

			// Carrier Column
			{
				accessorKey: "carrier",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.carrier")}
					/>
				),
				cell: ({ row }) => {
					const carrier = row.original.carrier;
					return (
						<div className="flex items-center gap-2">
							<Building2 className="h-4 w-4 text-muted-foreground" />
							{carrier?.id ? (
								<Link
									href={`/app/${params.organizationSlug}/contacts/${carrier.id}`}
									className="hover:underline cursor-pointer"
								>
									<span>{carrier?.nameLine1 || "-"}</span>
								</Link>
							) : (
								<span>{carrier?.nameLine1 || "-"}</span>
							)}
						</div>
					);
				},
				enableSorting: true,
				size: 140,
			},

			// Stops Column
			{
				accessorKey: "stopsCount",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title={t("app.tours.stops")}
					/>
				),
				cell: ({ row }) => {
					const stops = row.original.stops || [];
					const stopsCount = stops.length;

					if (stopsCount === 0) {
						return <Badge>No stops</Badge>;
					}

					return (
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<div className="flex items-center gap-2 cursor-help">
										<Badge>{stopsCount} stops</Badge>
									</div>
								</TooltipTrigger>
								<TooltipContent
									side="bottom"
									className="w-[280px]"
								>
									<div className="space-y-3 max-w-sm">
										<h4 className="font-medium">
											All Stops ({stopsCount})
										</h4>
										<div className="space-y-2">
											{stops.map((stop, index) => (
												<div
													key={stop?.id || index}
													className="border-b border-border pb-2 last:border-0"
												>
													<div className="flex items-center gap-1 font-medium">
														<MapPin className="h-3 w-3" />
														Stop {index + 1} (
														{stop.stopType})
													</div>
													<div className="text-xs">
														{stop.street
															? `${stop.street}, `
															: ""}
														{stop.zipCode || ""}{" "}
														{stop.city || ""}
														{stop.country
															? `, ${stop.country}`
															: ""}
													</div>
													<div className="text-xs text-muted-foreground">
														{stop.datetime_start
															? format(
																	new Date(
																		stop.datetime_start,
																	),
																	"dd.MM.yyyy, HH:mm",
																)
															: ""}
														{stop.datetime_end &&
															stop.datetime_start &&
															` - ${format(new Date(stop.datetime_end), "HH:mm")}`}
														{!stop.datetime_start &&
															stop.datetime_end &&
															format(
																new Date(
																	stop.datetime_end,
																),
																"dd.MM.yyyy, HH:mm",
															)}
													</div>
													{stop.goods_information && (
														<div className="text-xs">
															{
																stop.goods_information
															}
														</div>
													)}
												</div>
											))}
										</div>
									</div>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					);
				},
				enableSorting: false,
				size: 100,
			},

			// Actions Column
			{
				id: "actions",
				header: () => <div className="text-right">Actions</div>,
				cell: ({ row }) => <ActionsCell row={row} />,
				size: 60,
				meta: {
					getRowActions: (row: Row<Tour>) => {
						const tour = row.original;
						return {
							openView: () => {
								if (params.organizationSlug) {
									window.location.href = `/app/${params.organizationSlug}/tours/${tour.id}`;
								}
							},
							openEdit: () => {
								if (params.organizationSlug) {
									window.location.href = `/app/${params.organizationSlug}/tours/${tour.id}`;
								}
							},
							openDelete: () => handleDeleteTour(row.original),
						};
					},
				},
			},
		],
		[t, handleDeleteTour, params.organizationSlug],
	);
}
