"use client";
import { EmojiPicker as FeruccEmojiPicker } from "@ferrucc-io/emoji-picker";
import { Button } from "@ui/components/button";
import { SmileIcon } from "lucide-react";
import { useEffect, useRef } from "react";

interface EmojiPickerProps {
	onEmojiSelect: (emoji: string) => void;
	disabled?: boolean;
	size?: "sm" | "md" | "lg";
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function EmojiPicker({
	onEmojiSelect,
	disabled = false,
	size = "sm",
	open,
	onOpenChange,
}: EmojiPickerProps) {
	const containerRef = useRef<HTMLDivElement>(null);

	// <PERSON>le clicks outside to close the picker
	useEffect(() => {
		if (!open) {
			return;
		}

		const handleClickOutside = (event: MouseEvent) => {
			if (
				containerRef.current &&
				!containerRef.current.contains(event.target as Node)
			) {
				onOpenChange(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [open, onOpenChange]);

	const handleEmojiSelect = (emoji: string) => {
		onEmojiSelect(emoji);
		onOpenChange(false);
	};

	// Match the styling of other action buttons in CommentItem
	const buttonClasses =
		size === "sm"
			? "h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
			: size === "md"
				? "h-8 w-8 p-0"
				: "h-10 w-10 p-0";

	const iconSize =
		size === "sm" ? "w-3 h-3" : size === "md" ? "w-4 h-4" : "w-5 h-5";

	return (
		<div className="relative" ref={containerRef}>
			<Button
				variant="ghost"
				size="sm"
				className={buttonClasses}
				disabled={disabled}
				onClick={() => onOpenChange(!open)}
			>
				<SmileIcon className={iconSize} />
			</Button>

			{open && (
				<div className="absolute z-50 mt-2 right-0">
					<FeruccEmojiPicker
						className="w-[380px] border border-zinc-200 dark:border-zinc-800 rounded-lg bg-background shadow-lg"
						emojisPerRow={9}
						emojiSize={36}
						onEmojiSelect={handleEmojiSelect}
					>
						<FeruccEmojiPicker.Header className="p-2 pb-0">
							<FeruccEmojiPicker.Input
								placeholder="Search emoji"
								autoFocus={true}
								className="h-[36px] bg-background border border-zinc-300 dark:border-zinc-600 w-full rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent focus:outline-none"
							/>
						</FeruccEmojiPicker.Header>
						<FeruccEmojiPicker.Group>
							<FeruccEmojiPicker.List
								hideStickyHeader={true}
								containerHeight={320}
							/>
						</FeruccEmojiPicker.Group>
					</FeruccEmojiPicker>
				</div>
			)}
		</div>
	);
}
