import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	getAvailableOrders,
	getAvailableVehicles,
	getVehiclesWithTours,
} from "./dashboard";
import { getFinanceData } from "./dashboard-finance";
import { dashboardParamsSchema, financeDataParamsSchema } from "./types";

export const dashboardRouter = new Hono()
	.basePath("/dashboard")
	.get(
		"/vehicles",
		authMiddleware,
		validator("query", dashboardParamsSchema),
		describeRoute({
			tags: ["Dashboard"],
			summary: "Get vehicles with today's and tomorrow's tours",
			description:
				"Returns all vehicles with their scheduled tours for today and tomorrow, showing first and last stops",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "integer", default: 1, minimum: 1 },
					description: "Page number for pagination",
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: {
						type: "integer",
						default: 50,
						minimum: 1,
						maximum: 100,
					},
					description: "Number of items per page",
				},
			],
			responses: {
				200: {
					description: "List of vehicles with their tours",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									items: {
										type: "array",
										items: {
											type: "object",
											properties: {
												id: { type: "string" },
												licensePlate: {
													type: "string",
													nullable: true,
												},
												owner: {
													type: "object",
													nullable: true,
													properties: {
														id: { type: "string" },
														nameLine1: {
															type: "string",
														},
													},
												},
												workHours: {
													type: "object",
													properties: {
														start: {
															type: "string",
														},
														end: { type: "string" },
													},
												},
												todayTours: {
													type: "array",
													items: {
														type: "object",
														properties: {
															id: {
																type: "string",
															},
															tourNumber: {
																type: "string",
																nullable: true,
															},
															firstStop: {
																type: "object",
																nullable: true,
																properties: {
																	time_type: {
																		type: "string",
																		nullable: true,
																	},
																	datetime_start:
																		{
																			type: "string",
																			format: "date-time",
																			nullable: true,
																		},
																	datetime_end:
																		{
																			type: "string",
																			format: "date-time",
																			nullable: true,
																		},
																	city: {
																		type: "string",
																		nullable: true,
																	},
																	street: {
																		type: "string",
																		nullable: true,
																	},
																	zipCode: {
																		type: "string",
																		nullable: true,
																	},
																},
															},
															lastStop: {
																type: "object",
																nullable: true,
																properties: {
																	time_type: {
																		type: "string",
																		nullable: true,
																	},
																	datetime_start:
																		{
																			type: "string",
																			format: "date-time",
																			nullable: true,
																		},
																	datetime_end:
																		{
																			type: "string",
																			format: "date-time",
																			nullable: true,
																		},
																	city: {
																		type: "string",
																		nullable: true,
																	},
																	street: {
																		type: "string",
																		nullable: true,
																	},
																	zipCode: {
																		type: "string",
																		nullable: true,
																	},
																},
															},
														},
													},
												},
											},
										},
									},
									total: { type: "integer" },
									page: { type: "integer" },
									totalPages: { type: "integer" },
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				500: {
					description: "Server error",
				},
			},
		}),
		async (c) => {
			try {
				const queryParams = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					queryParams.organizationId,
					user.id,
				);

				// Get vehicles with their tours
				const result = await getVehiclesWithTours(queryParams);
				return c.json(result);
			} catch (error) {
				console.error("Error fetching dashboard data:", error);

				if (error instanceof Error) {
					if (error.message.includes("not found")) {
						return c.json({ message: "Resource not found" }, 404);
					}
					if (error.message.includes("access")) {
						return c.json({ message: "Access denied" }, 403);
					}
				}

				return c.json(
					{ message: "Failed to fetch dashboard data" },
					500,
				);
			}
		},
	)
	.get(
		"/available-vehicles",
		authMiddleware,
		validator("query", dashboardParamsSchema),
		describeRoute({
			tags: ["Dashboard"],
			summary: "Get available truck vehicles",
			description:
				"Returns all truck vehicles that have no future tours, prioritized by availability. Vehicles with no tours appear first, followed by those becoming available soonest.",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "integer", default: 1, minimum: 1 },
					description: "Page number for pagination",
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: {
						type: "integer",
						default: 50,
						minimum: 1,
						maximum: 100,
					},
					description: "Number of items per page",
				},
			],
			responses: {
				200: {
					description:
						"List of available truck vehicles, ordered by priority",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									items: {
										type: "array",
										items: {
											type: "object",
											properties: {
												id: { type: "string" },
												licensePlate: {
													type: "string",
													nullable: true,
												},
												owner: {
													type: "object",
													nullable: true,
													properties: {
														id: { type: "string" },
														nameLine1: {
															type: "string",
														},
													},
												},
												lastStop: {
													type: "object",
													nullable: true,
													properties: {
														time_type: {
															type: "string",
															nullable: true,
														},
														datetime_start: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														datetime_end: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														city: {
															type: "string",
															nullable: true,
														},
														street: {
															type: "string",
															nullable: true,
														},
														zipCode: {
															type: "string",
															nullable: true,
														},
													},
												},
											},
										},
									},
									total: { type: "integer" },
									page: { type: "integer" },
									totalPages: { type: "integer" },
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				500: {
					description: "Server error",
				},
			},
		}),
		async (c) => {
			try {
				const queryParams = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					queryParams.organizationId,
					user.id,
				);

				// Get available vehicles
				const result = await getAvailableVehicles(queryParams);
				return c.json(result);
			} catch (error) {
				console.error("Error fetching available vehicles:", error);

				if (error instanceof Error) {
					if (error.message.includes("not found")) {
						return c.json({ message: "Resource not found" }, 404);
					}
					if (error.message.includes("access")) {
						return c.json({ message: "Access denied" }, 403);
					}
				}

				return c.json(
					{ message: "Failed to fetch available vehicles" },
					500,
				);
			}
		},
	)
	.get(
		"/available-orders",
		authMiddleware,
		validator("query", dashboardParamsSchema),
		describeRoute({
			tags: ["Dashboard"],
			summary: "Get available orders",
			description:
				"Returns all open orders that need to be assigned, prioritized by the earliest first stop. Orders with overdue first stops appear first, followed by upcoming orders sorted by earliest stops.",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "integer", default: 1, minimum: 1 },
					description: "Page number for pagination",
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: {
						type: "integer",
						default: 50,
						minimum: 1,
						maximum: 100,
					},
					description: "Number of items per page",
				},
			],
			responses: {
				200: {
					description:
						"List of available orders, ordered by priority",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									items: {
										type: "array",
										items: {
											type: "object",
											properties: {
												id: { type: "string" },
												order_number: {
													type: "string",
													nullable: true,
												},
												customer: {
													type: "object",
													nullable: true,
													properties: {
														id: { type: "string" },
														nameLine1: {
															type: "string",
															nullable: true,
														},
													},
												},
												firstStop: {
													type: "object",
													nullable: true,
													properties: {
														time_type: {
															type: "string",
															nullable: true,
														},
														datetime_start: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														datetime_end: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														city: {
															type: "string",
															nullable: true,
														},
														street: {
															type: "string",
															nullable: true,
														},
														zipCode: {
															type: "string",
															nullable: true,
														},
													},
												},
												lastStop: {
													type: "object",
													nullable: true,
													properties: {
														time_type: {
															type: "string",
															nullable: true,
														},
														datetime_start: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														datetime_end: {
															type: "string",
															format: "date-time",
															nullable: true,
														},
														city: {
															type: "string",
															nullable: true,
														},
														street: {
															type: "string",
															nullable: true,
														},
														zipCode: {
															type: "string",
															nullable: true,
														},
													},
												},
												createdAt: {
													type: "string",
													format: "date-time",
												},
											},
										},
									},
									total: { type: "integer" },
									page: { type: "integer" },
									totalPages: { type: "integer" },
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				500: {
					description: "Server error",
				},
			},
		}),
		async (c) => {
			try {
				const queryParams = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					queryParams.organizationId,
					user.id,
				);

				// Get available orders
				const result = await getAvailableOrders(queryParams);
				return c.json(result);
			} catch (error) {
				console.error("Error fetching available orders:", error);

				if (error instanceof Error) {
					if (error.message.includes("not found")) {
						return c.json({ message: "Resource not found" }, 404);
					}
					if (error.message.includes("access")) {
						return c.json({ message: "Access denied" }, 403);
					}
				}

				return c.json(
					{ message: "Failed to fetch available orders" },
					500,
				);
			}
		},
	)
	.get(
		"/finance",
		authMiddleware,
		validator("query", financeDataParamsSchema),
		describeRoute({
			tags: ["Dashboard"],
			summary: "Get financial data for revenue chart",
			description:
				"Returns projected and confirmed revenue data for a time period",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "timeRange",
					in: "query",
					required: false,
					schema: {
						type: "string",
						enum: ["week", "month", "year"],
						default: "month",
					},
					description: "Time range for data aggregation",
				},
				{
					name: "startDate",
					in: "query",
					required: false,
					schema: {
						type: "string",
						format: "date-time",
					},
					description: "Custom start date (optional)",
				},
				{
					name: "endDate",
					in: "query",
					required: false,
					schema: {
						type: "string",
						format: "date-time",
					},
					description: "Custom end date (optional)",
				},
			],
			responses: {
				200: {
					description: "Financial data for chart visualization",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									data: {
										type: "array",
										items: {
											type: "object",
											properties: {
												date: { type: "string" },
												projected: { type: "number" },
												confirmed: { type: "number" },
											},
										},
									},
									totalProjected: { type: "number" },
									totalConfirmed: { type: "number" },
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				500: {
					description: "Server error",
				},
			},
		}),
		async (c) => {
			try {
				const queryParams = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					queryParams.organizationId,
					user.id,
				);

				// Get financial data
				const result = await getFinanceData({
					userId: user.id,
					organizationId: queryParams.organizationId,
					timeRange: queryParams.timeRange,
					startDate: queryParams.startDate,
					endDate: queryParams.endDate,
				});

				return c.json(result);
			} catch (error) {
				console.error("Error fetching financial data:", error);

				if (error instanceof Error) {
					if (error.message.includes("not found")) {
						return c.json({ message: "Resource not found" }, 404);
					}
					if (error.message.includes("access")) {
						return c.json({ message: "Access denied" }, 403);
					}
				}

				return c.json(
					{ message: "Failed to fetch financial data" },
					500,
				);
			}
		},
	);
