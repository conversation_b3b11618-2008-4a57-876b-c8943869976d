"use client";

import type { useCounterpartyById } from "@saas/contacts/hooks/use-counterparty";
import { InvoiceFormDialog } from "@saas/invoices/components/invoice-form-dialog";
import { Button } from "@ui/components/button";
import { Separator } from "@ui/components/separator";
import { FileText, Mail, MapPin, Phone } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

// Extract the return type from useCounterpartyById hook
type CounterpartyResponse = ReturnType<
	typeof useCounterpartyById
>["counterparty"];

interface QuickInfoPanelProps {
	counterparty: NonNullable<CounterpartyResponse>;
}

export function QuickInfoPanel({ counterparty }: QuickInfoPanelProps) {
	const t = useTranslations();
	const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false);

	// Format credit limit correctly
	const formatCurrency = (value: string | null | undefined): string => {
		if (!value) {
			return "€0.00";
		}
		try {
			// Try to format as a number if possible
			return `€${Number.parseFloat(value).toFixed(2)}`;
		} catch (e) {
			// Fallback to original value if not a valid number
			return `€${value}`;
		}
	};

	return (
		<div className="space-y-4">
			{/* Key contact metrics
			<div className="space-y-2">
				<h3 className="font-medium">Financial Overview</h3>
				<div className="grid grid-cols-2 gap-2 text-sm">
					<div>Open Invoices</div>
					<div className="text-right font-medium">0</div>

					<div>Total Outstanding</div>
					<div className="text-right font-medium">€0.00</div>

					<div>Credit Limit</div>
					<div className="text-right font-medium">
						{formatCurrency(
							counterparty.financialProfile?.creditLimit,
						)}
					</div>
				</div>
			</div>

			<Separator /> */}

			{/* Contact Information */}
			<div className="space-y-2">
				<h3 className="font-medium">Contact Information</h3>
				<div className="space-y-3 text-sm">
					{counterparty.addressUsage?.address && (
						<div className="flex items-start">
							<MapPin className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
							<div>
								{counterparty.addressUsage.address.nameLine && (
									<p>
										{
											counterparty.addressUsage.address
												.nameLine
										}
									</p>
								)}
								{counterparty.addressUsage.address.street && (
									<p>
										{
											counterparty.addressUsage.address
												.street
										}
									</p>
								)}
								{counterparty.addressUsage.address.zipCode &&
									counterparty.addressUsage.address.city && (
										<p>
											{
												counterparty.addressUsage
													.address.zipCode
											}{" "}
											{
												counterparty.addressUsage
													.address.city
											}
										</p>
									)}
								{counterparty.addressUsage.address.country && (
									<p>
										{
											counterparty.addressUsage.address
												.country
										}
									</p>
								)}
							</div>
						</div>
					)}

					{/* Show phone if available */}
					{counterparty.telephone && (
						<div className="flex items-center">
							<Phone className="h-4 w-4 mr-2 text-muted-foreground" />
							<p>{counterparty.telephone}</p>
						</div>
					)}

					{/* Show email if available */}
					{counterparty.email && (
						<div className="flex items-center">
							<Mail className="h-4 w-4 mr-2 text-muted-foreground" />
							<p>{counterparty.email}</p>
						</div>
					)}
				</div>
			</div>

			<Separator />

			{/* Quick Actions */}
			<div className="space-y-2">
				<h3 className="font-medium">Quick Actions</h3>
				<div className="grid grid-cols-1 gap-2">
					<Button
						variant="outline"
						size="sm"
						className="justify-start"
						onClick={() => setIsInvoiceDialogOpen(true)}
					>
						<FileText className="h-4 w-4 mr-2" />
						New Invoice
					</Button>
				</div>
			</div>

			{/* Invoice Dialog */}
			<InvoiceFormDialog
				open={isInvoiceDialogOpen}
				onOpenChange={setIsInvoiceDialogOpen}
				initialCustomerId={counterparty.id}
				defaultMode="order"
				onSuccess={() => {
					// You could add a refresh function here if needed
				}}
			/>
		</div>
	);
}
