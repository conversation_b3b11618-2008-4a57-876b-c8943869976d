"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { createOrderSchema } from "@repo/api/src/routes/orders/types";
import { useOrderMutations } from "@saas/orders/hooks/use-orders";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useState,
} from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";
import type { z } from "zod";

// Define the form values type from the schema
export type OrderFormValues = z.infer<typeof createOrderSchema>;

// Default form values
export const defaultOrderFormValues: Partial<OrderFormValues> = {
	// Primary identifiers and relations
	organizationId: "",
	customerId: "",
	contactId: undefined,

	// Optional relations
	vehicleOrderTypeId: undefined,

	// Order details
	appointment: null,
	order_type: undefined,
	loading_equipment: false,

	// Import/Export
	export_custom_clearance: false,
	export_custom_clearance_text: "",
	import_custom_clearance: false,
	import_custom_clearance_text: "",

	// Comments
	internal_comment: "",
	invoice_note: "",

	// Related items
	lineItems: [],
	stops: [],
};

// Context type
interface OrderFormContextValue {
	// Form state
	formState: OrderFormValues;
	isSubmitting: boolean;

	// Form actions
	updateFormValues: (values: Partial<OrderFormValues>) => void;
	resetForm: () => void;
	submitForm: () => Promise<boolean>;

	// Helpers
	isDirty: boolean;
	getValues: () => OrderFormValues;
}

const OrderFormContext = createContext<OrderFormContextValue | null>(null);

interface OrderFormProviderProps {
	children: ReactNode;
	orderId?: string;
	initialValues?: Partial<OrderFormValues>;
	onSuccess?: () => void;
	onSubmitStart?: () => void;
	onError?: () => void;
	onFormChange?: (hasChanges: boolean) => void;
}

export function OrderFormProvider({
	children,
	orderId,
	initialValues = {},
	onSuccess,
	onSubmitStart,
	onError,
	onFormChange,
}: OrderFormProviderProps) {
	const { activeOrganization } = useActiveOrganization();
	const { createOrder, updateOrder } = useOrderMutations();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Initialize the form with default values and any provided initial values
	const methods = useForm<OrderFormValues>({
		resolver: zodResolver(createOrderSchema as any), // Cast to any as a temporary workaround
		defaultValues: {
			...defaultOrderFormValues,
			organizationId: activeOrganization?.id || "",
			...initialValues,
		},
		mode: "onChange", // Validate on change for better UX
	});

	// Watch for form changes and notify parent component
	useEffect(() => {
		const subscription = methods.watch((value, { name, type }) => {
			// Only trigger on user-initiated changes, not on mount
			if (type === "change") {
				onFormChange?.(true);
			}
		});

		return () => subscription.unsubscribe();
	}, [methods, onFormChange]);

	// Update form values (useful for programmatically setting values)
	const updateFormValues = (values: Partial<OrderFormValues>) => {
		Object.entries(values).forEach(([key, value]) => {
			methods.setValue(key as any, value as any, {
				shouldValidate: true,
				shouldDirty: true,
			});
		});
	};

	// Reset form to initial values
	const resetForm = () => {
		methods.reset({
			...defaultOrderFormValues,
			organizationId: activeOrganization?.id || "",
			...initialValues,
		});
	};

	// Submit the form
	const submitForm = async (): Promise<boolean> => {
		try {
			const valid = await methods.trigger();
			if (!valid) {
				toast.error(
					"Please fix the errors in the form before submitting.",
				);
				return false;
			}

			if (!activeOrganization?.id) {
				toast.error("No active organization selected");
				return false;
			}

			setIsSubmitting(true);
			onSubmitStart?.(); // Notify parent of submission start

			const formData = methods.getValues();

			let success = false;

			if (orderId) {
				// Update existing order
				await updateOrder({
					id: orderId,
					...formData,
				});
				success = true;
			} else {
				// Create new order
				await createOrder(formData);
				success = true;
			}

			if (success) {
				// The toast is already shown in the API call
				onSuccess?.();
			}

			return success;
		} catch (error) {
			console.error(error);
			// Error toast is already shown in the API call
			onError?.();
			return false;
		} finally {
			setIsSubmitting(false);
		}
	};

	const contextValue: OrderFormContextValue = {
		formState: methods.getValues(),
		isSubmitting,
		updateFormValues,
		resetForm,
		submitForm,
		isDirty: methods.formState.isDirty,
		getValues: methods.getValues,
	};

	return (
		<OrderFormContext.Provider value={contextValue}>
			<FormProvider {...methods}>{children}</FormProvider>
		</OrderFormContext.Provider>
	);
}

export function useOrderForm() {
	const context = useContext(OrderFormContext);
	if (!context) {
		throw new Error(
			"useOrderForm must be used within an OrderFormProvider",
		);
	}
	return context;
}
