import { InvoiceType, OrderType } from "@repo/api/src/routes/orders/types";
import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { InputWithDate } from "@ui/components/input-with-date";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { FormVehicleOrderTypeSelector } from "@ui/components/vehicle-order-type-selector";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

export function BasicInfoSection() {
	const { control, setValue, watch } = useFormContext();
	const t = useTranslations("app.orders");
	const firstFormElementRef = useRef<HTMLButtonElement>(null);

	// Watch customer ID
	const customerId = watch("customerId");

	// State to store customer contacts for the dropdown
	const [customerContacts, setCustomerContacts] = useState<any[]>([]);

	// Handler for when the selected counterparty changes
	const handleSelectedCustomerChange = (counterparty: any) => {
		// Check if we have contacts and they're different from current state
		const activeContacts =
			counterparty?.contacts?.filter(
				(contact: any) => contact.isActive,
			) || [];

		// Only update state if there's a meaningful change
		if (
			JSON.stringify(activeContacts) !== JSON.stringify(customerContacts)
		) {
			setCustomerContacts(activeContacts);
		}
	};

	// Focus the first input when the section mounts
	useEffect(() => {
		firstFormElementRef.current?.focus();
	}, []);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Basic Information</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				{/* Hidden Organization ID field - populated automatically by the form context */}
				<FormField
					control={control}
					name="organizationId"
					render={({ field }) => <input type="hidden" {...field} />}
				/>

				{/* Client & Transportation */}
				<div className="space-y-2">
					<h3 className="text-sm font-medium">
						Client & Transportation
					</h3>
					<div className="space-y-4">
						{/* Customer */}
						<FormField
							control={control}
							name="customerId"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Customer *</FormLabel>
									<FormControl>
										<CounterpartySelector
											value={field.value}
											onChange={(value) => {
												field.onChange(value);
											}}
											name="customerId"
											type="customer"
											placeholder="Select customer..."
											allowClear={true}
											onSelectedCounterpartyChange={
												handleSelectedCustomerChange
											}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Customer Contact selector - only show if customer is selected and has contacts */}
						{customerId && customerContacts.length > 0 && (
							<FormField
								control={control}
								name="contactId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Contact Person</FormLabel>
										<FormControl>
											<Select
												value={field.value || ""}
												onValueChange={field.onChange}
											>
												<SelectTrigger>
													<SelectValue placeholder="Select contact person" />
												</SelectTrigger>
												<SelectContent>
													{customerContacts.map(
														(contact) => (
															<SelectItem
																key={contact.id}
																value={
																	contact.id
																}
															>
																{
																	contact.firstName
																}{" "}
																{
																	contact.lastName
																}{" "}
																{contact.position
																	? `(${contact.position})`
																	: ""}
															</SelectItem>
														),
													)}
												</SelectContent>
											</Select>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}
					</div>
				</div>

				{/* Order Details */}
				<div className="space-y-2">
					<h3 className="text-sm font-medium">Order Details</h3>

					{/* Order Type - Changed from grid to single field since there's only one item now */}
					<FormField
						control={control}
						name="order_type"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Order Type</FormLabel>
								<FormControl>
									<Select
										value={field.value || ""}
										onValueChange={field.onChange}
									>
										<SelectTrigger
											ref={firstFormElementRef}
										>
											<SelectValue placeholder="Select order type" />
										</SelectTrigger>
										<SelectContent>
											{Object.values(OrderType).map(
												(type) => (
													<SelectItem
														key={type}
														value={type}
													>
														{type}
													</SelectItem>
												),
											)}
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Vehicle Order Type */}
					<FormVehicleOrderTypeSelector
						name="vehicleOrderTypeId"
						label="Vehicle Order Type"
						placeholder="Select vehicle order type..."
						showCreateOption={true}
					/>

					<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
						{/* Appointment */}
						<FormField
							control={control}
							name="appointment"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Appointment Date</FormLabel>
									<FormControl>
										<InputWithDate
											inputProps={{
												...field,
												placeholder: "DD.MM.YYYY",
												value: undefined,
											}}
											onDateChange={(date) => {
												if (date instanceof Date) {
													field.onChange(date);
												} else if (date === null) {
													field.onChange(null);
												}
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Invoice Type */}
						<FormField
							control={control}
							name="invoice_type"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Invoice Type</FormLabel>
									<FormControl>
										<Select
											value={field.value || ""}
											onValueChange={field.onChange}
										>
											<SelectTrigger>
												<SelectValue placeholder="Select type" />
											</SelectTrigger>
											<SelectContent>
												{Object.values(InvoiceType).map(
													(type) => (
														<SelectItem
															key={type}
															value={type}
														>
															{type}
														</SelectItem>
													),
												)}
											</SelectContent>
										</Select>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Invoice Note */}
					<FormField
						control={control}
						name="invoice_note"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Invoice Note</FormLabel>
								<FormControl>
									<Textarea
										{...field}
										value={field.value || ""}
										placeholder="Enter invoice note..."
										rows={3}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</CardContent>
		</Card>
	);
}
