import { ContactSalutationSchema } from "@repo/database/src/zod";
import {
	type Country,
	CountrySelect,
} from "@saas/shared/components/CountrySelect";
import {
	AddressAutocomplete,
	type AddressType,
} from "@shared/components/address/autocomplete";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { FormDepartmentSelector } from "@ui/components/department-selector";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { PhoneInput } from "@ui/components/phone-input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";
import type { Country as PhoneCountry } from "react-phone-number-input";
import { isSupportedCountry } from "react-phone-number-input";

// Extract the valid salutation values from the schema
const SALUTATIONS = ContactSalutationSchema.options;

// Function to format salutation for display
function formatSalutation(salutation: string): string {
	const mapping: Record<string, string> = {
		MR: "Mr.",
		MS: "Ms.",
	};
	return mapping[salutation] || salutation;
}

export function BasicInfoSection() {
	const { control, setValue } = useFormContext();
	const t = useTranslations("app.personnel");
	const firstInputRef = useRef<HTMLButtonElement>(null);

	// State to track the current country from the address section
	const [selectedCountry, setSelectedCountry] = useState<PhoneCountry>("DE");

	// Handler for country selection
	const handleCountrySelect = (country: Country) => {
		// Update the phone input country
		if (isSupportedCountry(country.alpha2 as PhoneCountry)) {
			setSelectedCountry(country.alpha2 as PhoneCountry);
		}
	};

	// Handle address updates from autocomplete
	const handleAddressChange = (address: AddressType) => {
		// Clear all address fields if empty address is provided
		if (!address.address1 && !address.formattedAddress) {
			setValue("street", "");
			setValue("addressSupplement", "");
			setValue("city", "");
			setValue("zipCode", "");
			setValue("country", "");
			return;
		}

		// Update form with new address values
		setValue("street", address.address1);
		setValue("addressSupplement", address.address2);
		setValue("city", address.city);
		setValue("zipCode", address.postalCode);
		setValue("country", address.country);

		// Update phone country if address country is supported
		if (
			address.country &&
			isSupportedCountry(address.country as PhoneCountry)
		) {
			setSelectedCountry(address.country as PhoneCountry);
		}
	};

	// Focus the first input when the section mounts
	useEffect(() => {
		firstInputRef.current?.focus();
	}, []);

	return (
		<Card>
			<CardHeader>
				<CardTitle>{t("form.basicSection.title")}</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
					{/* Salutation */}
					<FormField
						control={control}
						name="salutation"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Salutation</FormLabel>
								<Select
									onValueChange={field.onChange}
									value={field.value || ""}
								>
									<FormControl>
										<SelectTrigger ref={firstInputRef}>
											<SelectValue placeholder="Select salutation" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{SALUTATIONS.map((salutation) => (
											<SelectItem
												key={salutation}
												value={salutation}
											>
												{formatSalutation(salutation)}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* First Name */}
					<FormField
						control={control}
						name="firstName"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									{t("form.firstName.label")}
								</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter first name"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Last Name */}
					<FormField
						control={control}
						name="lastName"
						render={({ field }) => (
							<FormItem>
								<FormLabel>
									{t("form.lastName.label")}
								</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter last name"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Email */}
					<FormField
						control={control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										type="email"
										placeholder="Enter email address"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Phone */}
					<FormField
						control={control}
						name="telephone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Telephone</FormLabel>
								<FormControl>
									<PhoneInput
										value={field.value || ""}
										onChange={field.onChange}
										onBlur={field.onBlur}
										name={field.name}
										placeholder="Enter telephone number"
										international
										defaultCountry={selectedCountry || "DE"}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Mobile */}
					<FormField
						control={control}
						name="mobile"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Mobile</FormLabel>
								<FormControl>
									<PhoneInput
										value={field.value || ""}
										onChange={field.onChange}
										onBlur={field.onBlur}
										name={field.name}
										placeholder="Enter mobile number"
										international
										defaultCountry={selectedCountry || "DE"}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Personal Information */}
				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
					{/* Date of Birth */}
					<FormField
						control={control}
						name="dateOfBirth"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Date of Birth</FormLabel>
								<FormControl>
									<InputWithDate
										inputProps={{
											value: field.value || "",
											placeholder: "Enter date of birth",
										}}
										onDateChange={field.onChange}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Place of Birth */}
					<FormField
						control={control}
						name="placeOfBirth"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Place of Birth</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter place of birth"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
					{/* Address Autocomplete */}
					<div className="sm:col-span-2 space-y-2">
						<FormLabel>Address Search</FormLabel>
						<AddressAutocomplete
							onAddressChange={handleAddressChange}
							placeholder="Search for an address"
						/>
						<p className="text-xs text-muted-foreground">
							Search for an address or enter details manually
							below
						</p>
					</div>

					{/* Street */}
					<FormField
						control={control}
						name="street"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Street</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter street address"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Address Supplement */}
					<FormField
						control={control}
						name="addressSupplement"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Address Supplement</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter address supplement"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
					{/* Zip Code */}
					<FormField
						control={control}
						name="zipCode"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Zip Code</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter zip code"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* City */}
					<FormField
						control={control}
						name="city"
						render={({ field }) => (
							<FormItem>
								<FormLabel>City</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter city name"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Country */}
					<FormField
						control={control}
						name="country"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Country</FormLabel>
								<FormControl>
									<CountrySelect
										name="country"
										value={field.value || ""}
										onValueChange={field.onChange}
										onCountrySelect={handleCountrySelect}
										placeholder="Select country"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Professional Information */}
				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
					{/* Department */}
					<FormDepartmentSelector
						name="departmentId"
						label="Department"
						placeholder="Select department"
						showCreateOption={true}
					/>
				</div>

				{/* Notes */}
				<div className="grid grid-cols-1 gap-4">
					<FormField
						control={control}
						name="notes"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Notes</FormLabel>
								<FormControl>
									<Textarea
										{...field}
										value={field.value || ""}
										placeholder="Enter additional notes"
										className="min-h-[100px]"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</CardContent>
		</Card>
	);
}
