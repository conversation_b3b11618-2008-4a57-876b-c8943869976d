"use client";
import { CreditNoteFormDialog } from "@saas/invoices/components/credit-note-form-dialog";
import { CreditNotesDataTable } from "@saas/invoices/components/credit-notes-table/data-table";
import { InvoiceEditDialog } from "@saas/invoices/components/invoice-edit-dialog";
import { InvoiceFormDialog } from "@saas/invoices/components/invoice-form-dialog";
import { InvoicesDataTable } from "@saas/invoices/components/invoices-table/data-table";
import { CreditNotesUIProvider } from "@saas/invoices/context/credit-notes-ui-context";
import {
	InvoicesUIProvider,
	useInvoicesUI,
} from "@saas/invoices/context/invoices-ui-context";
import { useCustomerCreditNotes } from "@saas/invoices/hooks/use-customer-credit-notes";
import { useInvoices } from "@saas/invoices/hooks/use-invoice";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { Button } from "@ui/components/button";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

function InvoicePageContent() {
	const { isCreateDialogOpen, setCreateDialogOpen } = useInvoicesUI();
	const { refetch: refetchInvoices } = useInvoices();
	const { refetch: refetchCreditNotes } = useCustomerCreditNotes();
	const t = useTranslations();
	const [activeTab, setActiveTab] = useState<"invoices" | "credit-notes">(
		"invoices",
	);
	const [isCreditNoteFormOpen, setIsCreditNoteFormOpen] = useState(false);

	return (
		<>
			<div className="flex items-center justify-between pb-3">
				<h1 className="text-3xl font-bold tracking-tight">
					{t("app.menu.invoices")}
				</h1>
				{activeTab === "invoices" && (
					<Button onClick={() => setCreateDialogOpen(true)}>
						<Plus className="mr-2 h-4 w-4" />
						{t("app.invoices.createInvoice")}
					</Button>
				)}
				{activeTab === "credit-notes" && (
					<Button onClick={() => setIsCreditNoteFormOpen(true)}>
						<Plus className="mr-2 h-4 w-4" />
						Create Credit Note
					</Button>
				)}
			</div>

			<Tabs
				defaultValue="invoices"
				value={activeTab}
				onValueChange={(value) =>
					setActiveTab(value as "invoices" | "credit-notes")
				}
				className="mb-4"
			>
				<TabsList>
					<TabsTrigger value="invoices">Invoices</TabsTrigger>
					<TabsTrigger value="credit-notes">Credit Notes</TabsTrigger>
				</TabsList>
				<TabsContent value="invoices">
					<InvoicesDataTable />
				</TabsContent>
				<TabsContent value="credit-notes">
					<CreditNotesUIProvider>
						<CreditNotesDataTable />
					</CreditNotesUIProvider>
				</TabsContent>
			</Tabs>

			{/* Invoice Form Dialog */}
			<InvoiceFormDialog
				open={isCreateDialogOpen}
				onOpenChange={setCreateDialogOpen}
				onSuccess={refetchInvoices}
			/>

			{/* Credit Note Form Dialog */}
			<CreditNoteFormDialog
				open={isCreditNoteFormOpen}
				onOpenChange={setIsCreditNoteFormOpen}
				onSuccess={refetchCreditNotes}
			/>

			{/* Invoice Edit Dialog */}
			<InvoiceEditDialog />
		</>
	);
}

export default function InvoicesPage() {
	const { refetch } = useInvoices();
	const { registerScopeWithShortcuts } = useShortcuts();
	const t = useTranslations();

	// Register the invoices shortcuts scope
	useEffect(() => {
		const cleanupShortcuts =
			registerScopeWithShortcuts("invoices-shortcuts");
		return cleanupShortcuts;
	}, [registerScopeWithShortcuts]);

	return (
		<InvoicesUIProvider onInvoiceDeleted={refetch}>
			<InvoicePageContent />
		</InvoicesUIProvider>
	);
}
