"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { CalendarIcon, MapIcon } from "lucide-react";

type View = "map" | "calendar";

interface ViewSwitcherProps {
	activeView: View;
	onChange: (view: View) => void;
}

export function ViewSwitcher({ activeView, onChange }: ViewSwitcherProps) {
	return (
		<div className="flex items-center border rounded-md overflow-hidden">
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant={activeView === "map" ? "primary" : "ghost"}
							size="sm"
							className="rounded-none"
							onClick={() => onChange("map")}
							aria-label="Show map view"
						>
							<MapIcon className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Map view</TooltipContent>
				</Tooltip>
			</TooltipProvider>

			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							variant={
								activeView === "calendar" ? "primary" : "ghost"
							}
							size="sm"
							className="rounded-none"
							onClick={() => onChange("calendar")}
							aria-label="Show calendar view"
						>
							<CalendarIcon className="h-4 w-4" />
						</Button>
					</TooltipTrigger>
					<TooltipContent>Calendar view</TooltipContent>
				</Tooltip>
			</TooltipProvider>
		</div>
	);
}
