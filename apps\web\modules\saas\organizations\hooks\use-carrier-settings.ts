import type { UpdateCarrierConfigurationInput } from "@repo/api/src/routes/settings/contact-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	fetchCarrierConfiguration,
	useCarrierConfigurationQuery,
	useUpdateCarrierConfigurationMutation,
} from "@saas/organizations/lib/api-carrier-settings";

// Type for frontend form values
export type CarrierSettingsFormValues = Omit<
	UpdateCarrierConfigurationInput,
	"organizationId"
>;

// Main hook for carrier configuration
export function useCarrierSettings() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	// Query for carrier configuration
	const query = useCarrierConfigurationQuery(organizationId, {
		enabled: !!organizationId,
	});

	// Mutation for updating carrier configuration
	const updateMutation =
		useUpdateCarrierConfigurationMutation(organizationId);

	// Wrapped update function
	const updateCarrierSettings = async (data: CarrierSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		return await updateMutation.mutateAsync(data);
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
		refetch: query.refetch,
		updateCarrierSettings,
		isUpdating: updateMutation.isPending,
	};
}

// Hook for accessing raw fetch function
export function useCarrierSettingsRaw() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const updateMutation =
		useUpdateCarrierConfigurationMutation(organizationId);

	const fetchSettings = async () => {
		if (!organizationId) {
			throw new Error("No active organization");
		}
		return await fetchCarrierConfiguration(organizationId);
	};

	const updateSettings = async (data: CarrierSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		return await updateMutation.mutateAsync(data);
	};

	return {
		fetchSettings,
		updateSettings,
		isUpdating: updateMutation.isPending,
	};
}
