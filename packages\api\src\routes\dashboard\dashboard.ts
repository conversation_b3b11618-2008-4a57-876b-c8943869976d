import { db } from "@repo/database";
import { addDays, endOfDay, startOfDay, subDays } from "date-fns";
import { HTTPException } from "hono/http-exception";
import type {
	AvailableOrder,
	AvailableOrdersResponse,
	AvailableVehicle,
	AvailableVehiclesResponse,
	GetAvailableOrdersParams,
	GetAvailableVehiclesParams,
	GetVehiclesToursParams,
	SimpleTourInfo,
	StopInfo,
	VehicleWithTours,
	VehiclesToursResponse,
	WorkHours,
} from "./types";

/**
 * Get all vehicles with their tours for today and tomorrow
 */
export async function getVehiclesWithTours({
	organizationId,
	page = 1,
	limit = 50,
}: GetVehiclesToursParams): Promise<VehiclesToursResponse> {
	try {
		// Calculate today and tomorrow date ranges
		const today = new Date();
		const todayStart = startOfDay(today);
		const tomorrowEnd = endOfDay(addDays(today, 1));

		// Hardcoded work hours for all vehicles
		const defaultWorkHours: WorkHours = {
			start: "06:00",
			end: "22:00",
		};

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		// Get all vehicles with pagination
		const [total, rawVehicles] = await Promise.all([
			db.vehicle.count({
				where: { organizationId },
			}),
			db.vehicle.findMany({
				where: {
					organizationId,
					isActiveInDispatch: true,
				},
				include: {
					// Include owner information (Counterparty)
					owner: {
						select: {
							id: true,
							nameLine1: true,
						},
					},
					// Get confirmed tours that have ANY stops during today or tomorrow
					tours: {
						where: {
							status: "confirmed",
							// Find tours with ANY stop that falls within our date range
							stops: {
								some: {
									OR: [
										// Stop starts today/tomorrow
										{
											datetime_start: {
												gte: todayStart,
												lte: tomorrowEnd,
											},
										},
										// Stop ends today/tomorrow
										{
											datetime_end: {
												gte: todayStart,
												lte: tomorrowEnd,
											},
										},
										// Stop spans across today/tomorrow (starts before, ends after)
										{
											datetime_start: { lt: todayStart },
											datetime_end: { gt: tomorrowEnd },
										},
									],
								},
							},
						},
						select: {
							id: true,
							tourNumber: true,
							// Include all stops to determine which days this tour is active
							stops: {
								orderBy: [
									// Primary sort by positionTour to maintain stop sequence
									{ positionTour: "asc" },
									// Secondary sort by time
									{ datetime_start: "asc" },
								],
								select: {
									time_type: true,
									datetime_start: true,
									datetime_end: true,
									city: true,
									street: true,
									zipCode: true,
								},
							},
						},
					},
				},
				skip,
				take: limit,
				orderBy: {
					licensePlate: "asc",
				},
			}),
		]);

		// Calculate pagination metadata
		const totalPages = Math.ceil(total / limit);

		// Define internal type that includes all stops for processing
		type InternalTourInfo = {
			id: string;
			tourNumber: string | null;
			firstStop: StopInfo | null;
			lastStop: StopInfo | null;
			allStops: StopInfo[];
		};

		// Transform the response to match our expected type structure
		const vehiclesWithGroupedTours = rawVehicles.map((vehicle) => {
			// We need to explicitly type the tours to satisfy TypeScript
			type RawTourType = {
				id: string;
				tourNumber: string | null;
				stops: StopInfo[];
			};

			// Get all tours and explicitly cast to expected type
			const vehicleTours = (vehicle.tours ||
				[]) as unknown as RawTourType[];

			// Process tours with their stops
			const processedTours = vehicleTours.map((tour) => {
				// Extract first and last stops if they exist
				const firstStop = tour.stops.length > 0 ? tour.stops[0] : null;
				const lastStop =
					tour.stops.length > 1
						? tour.stops[tour.stops.length - 1]
						: null;

				// Return tour information with all stops for internal processing
				const internalTour: InternalTourInfo = {
					id: tour.id,
					tourNumber: tour.tourNumber,
					firstStop,
					lastStop,
					allStops: tour.stops,
				};

				return internalTour;
			});

			// Check if a tour is active in the 2-day window
			const isTourActive = (tour: InternalTourInfo) => {
				// Check all stops to see if any activity happens in our 2-day window
				return tour.allStops.some((stop) => {
					// Case 1: Stop starts within the 2-day range
					if (
						stop.datetime_start &&
						new Date(stop.datetime_start) >= todayStart &&
						new Date(stop.datetime_start) <= tomorrowEnd
					) {
						return true;
					}

					// Case 2: Stop ends within the 2-day range
					if (
						stop.datetime_end &&
						new Date(stop.datetime_end) >= todayStart &&
						new Date(stop.datetime_end) <= tomorrowEnd
					) {
						return true;
					}

					// Case 3: Stop spans across the 2-day range
					if (
						stop.datetime_start &&
						stop.datetime_end &&
						new Date(stop.datetime_start) < todayStart &&
						new Date(stop.datetime_end) > tomorrowEnd
					) {
						return true;
					}

					return false;
				});
			};

			// Filter tours active in our 2-day window and convert to SimpleTourInfo format
			const activeTours = processedTours
				.filter(isTourActive)
				.map(({ allStops, ...rest }) => rest as SimpleTourInfo);

			// Return the vehicle with tour information
			const result: VehicleWithTours = {
				id: vehicle.id,
				licensePlate: vehicle.licensePlate,
				owner: vehicle.owner,
				// Add hardcoded work hours
				workHours: defaultWorkHours,
				tours: activeTours,
			};

			return result;
		});

		return {
			items: vehiclesWithGroupedTours,
			total,
			page,
			totalPages,
		};
	} catch (error) {
		console.error("Failed to fetch vehicles with tours:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch vehicles with tours",
		});
	}
}

/**
 * Get vehicles that have no upcoming tours or their tour is finishing today with no tour for tomorrow
 */
export async function getAvailableVehicles({
	organizationId,
	page = 1,
	limit = 50,
}: GetAvailableVehiclesParams): Promise<AvailableVehiclesResponse> {
	try {
		// Calculate date ranges
		const now = new Date();

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		// Get all active vehicles with their tours
		const [total, vehicles] = await Promise.all([
			db.vehicle.count({
				where: {
					organizationId,
					isActiveInDispatch: true,
					// Filter for trucks only
					vehicleType: "TRUCK",
					deletedAt: null,
				},
			}),
			db.vehicle.findMany({
				where: {
					organizationId,
					isActiveInDispatch: true,
					// Filter for trucks only
					vehicleType: "TRUCK",
					deletedAt: null,
				},
				include: {
					owner: {
						select: {
							id: true,
							nameLine1: true,
						},
					},
					tours: {
						where: {
							status: "confirmed",
							// Filter to get only relevant tours
							OR: [
								// Future tours (to check availability)
								{
									stops: {
										some: {
											OR: [
												{
													datetime_start: {
														gte: now,
													},
												},
												{ datetime_end: { gte: now } },
											],
										},
									},
								},
								// Recent past tours (to calculate idle time)
								{
									endDate: {
										lt: now,
										gte: subDays(now, 30), // Last 30 days
									},
								},
							],
						},
						select: {
							id: true,
							tourNumber: true,
							endDate: true,
							stops: {
								orderBy: [
									{ positionTour: "asc" },
									{ datetime_start: "asc" },
								],
								select: {
									time_type: true,
									datetime_start: true,
									datetime_end: true,
									city: true,
									street: true,
									zipCode: true,
									country: true,
								},
							},
						},
					},
				},
				skip,
				take: limit,
				orderBy: {
					licensePlate: "asc",
				},
			}),
		]);

		// Process and prioritize vehicles
		const processedVehicles = vehicles.map((vehicle) => {
			// For each vehicle, determine if it has future tours
			let lastStop: StopInfo | null = null; // Initialize lastStop here
			let priorityScore: number;
			let nextAvailableTime: Date | null = null;
			let hasFutureTours = false;
			let idleTime = 0;
			let lastCompletedTime: Date | null = null;

			// If there are no tours, skip that processing
			if (!vehicle.tours?.length) {
				// No tours - highest priority (trucks that never had a stop)
				priorityScore = 2000000000000; // Use 2 trillion to ensure highest priority
			} else {
				// Process tour stops
				vehicle.tours.forEach((tour) => {
					if (!tour.stops?.length) {
						return;
					}

					// Check if tour has any future stops
					const tourHasFutureStops = tour.stops.some((stop) => {
						const startDate = stop.datetime_start
							? new Date(stop.datetime_start)
							: null;
						const endDate = stop.datetime_end
							? new Date(stop.datetime_end)
							: null;

						return (
							(startDate && startDate > now) ||
							(endDate && endDate > now)
						);
					});

					if (tourHasFutureStops) {
						hasFutureTours = true;

						// Find when this tour ends (for vehicles with future tours)
						const tourLastStop = tour.stops[tour.stops.length - 1];
						if (tourLastStop) {
							// Consider datetime_end first, then datetime_start as the point of interest for availability
							const effectiveLastActivityTimeValue =
								tourLastStop.datetime_end ||
								tourLastStop.datetime_start;

							if (effectiveLastActivityTimeValue) {
								const stopTime = new Date(
									effectiveLastActivityTimeValue,
								);

								// Update nextAvailableTime if this is the soonest ending/starting future activity
								if (
									stopTime > now && // Ensure this activity itself is in the future
									(!nextAvailableTime ||
										stopTime < nextAvailableTime)
								) {
									nextAvailableTime = stopTime;
									lastStop = tourLastStop; // Set lastStop to this specific stop object
								}
							}
						}
					} else {
						// Tour is in the past - find last completed stop
						tour.stops.forEach((stop) => {
							// Parse dates explicitly
							const stopStartDate = stop.datetime_start
								? new Date(stop.datetime_start)
								: null;
							const stopEndDate = stop.datetime_end
								? new Date(stop.datetime_end)
								: null;

							// Skip future stops
							if (
								(stopEndDate && stopEndDate > now) ||
								(stopStartDate && stopStartDate > now)
							) {
								return;
							}

							// For past stops, use end time if available, otherwise start time
							const stopCompletionTime =
								stopEndDate || stopStartDate;

							if (
								stopCompletionTime &&
								(!lastCompletedTime ||
									stopCompletionTime > lastCompletedTime)
							) {
								lastCompletedTime = stopCompletionTime;

								// Only update lastStop if we don't have a future one determined yet
								if (!nextAvailableTime) {
									lastStop = stop; // Correctly assign the specific past stop
								}
							}
						});
					}
				});

				// Calculate the priority score based on future tours
				if (hasFutureTours && nextAvailableTime) {
					// Vehicle has future tours - lower priority, ordered by soonest available
					// Use explicit type assertion to help TypeScript
					const timeUntilAvailableMs =
						(nextAvailableTime as Date).getTime() - now.getTime();
					priorityScore = -timeUntilAvailableMs; // Negative score, higher (less negative) = higher priority
				} else {
					// Vehicle has past tours but no future ones - secondary priority based on idle time
					const nowTimestamp = now.getTime();
					// Use a default date (0) if no last completed time to avoid null reference
					const lastTimestamp = lastCompletedTime
						? (lastCompletedTime as Date).getTime()
						: 0;
					idleTime = nowTimestamp - lastTimestamp;
					priorityScore = 1000000000000 + idleTime; // Lower than no-tour vehicles but higher based on idle time
				}
			}

			// Return vehicle with priority info
			return {
				id: vehicle.id,
				licensePlate: vehicle.licensePlate,
				owner: vehicle.owner,
				lastStop, // Ensure lastStop is returned
				priorityScore,
				hasFutureTours,
				nextAvailableTime,
				idleTime,
			};
		});

		// Sort by priority score (descending)
		const sortedVehicles = processedVehicles
			.sort((a, b) => b.priorityScore - a.priorityScore)
			// Remove the metadata fields from the final response but keep lastStop
			.map(
				({
					priorityScore,
					hasFutureTours,
					nextAvailableTime,
					idleTime,
					lastStop, // Keep lastStop
					...vehicleBase
				}) =>
					({
						...vehicleBase,
						lastStop, // Ensure lastStop is in the final mapped object
					}) as AvailableVehicle,
			);

		console.log(`Found ${sortedVehicles.length} available vehicles`);
		console.log(
			`Vehicles with stops: ${sortedVehicles.filter((v) => v.lastStop !== null).length}`,
		);

		return {
			items: sortedVehicles,
			total: total,
			page,
			totalPages: Math.ceil(total / limit),
		};
	} catch (error) {
		console.error("Failed to fetch available vehicles:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch available vehicles",
		});
	}
}

/**
 * Get open orders that need to be assigned, prioritized by the earliest first stop
 */
export async function getAvailableOrders({
	organizationId,
	page = 1,
	limit = 50,
}: GetAvailableOrdersParams): Promise<AvailableOrdersResponse> {
	try {
		// Calculate current date
		const now = new Date();

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		// Get all open orders with their stops
		const [total, orders] = await Promise.all([
			db.order.count({
				where: {
					organizationId,
					order_status: "open", // Only get orders with open status
				},
			}),
			db.order.findMany({
				where: {
					organizationId,
					order_status: "open",
				},
				include: {
					// Include customer information
					customer: {
						select: {
							id: true,
							nameLine1: true,
						},
					},
					// Include stops for prioritization
					stops: {
						orderBy: [
							// Primary sort by position to maintain stop sequence
							{ positionTour: "asc" },
							// Secondary sort by time
							{ datetime_start: "asc" },
						],
						select: {
							time_type: true,
							datetime_start: true,
							datetime_end: true,
							city: true,
							street: true,
							zipCode: true,
							country: true,
						},
					},
				},
				skip,
				take: limit,
				orderBy: {
					createdAt: "desc", // Most recent orders first as initial sorting
				},
			}),
		]);

		// Filter and process orders
		const processedOrders = orders
			.filter((order) => order.stops && order.stops.length > 0) // Filter out orders without stops
			.map((order) => {
				// Calculate priority score based on the first stop's datetime_start
				let priorityScore: number;
				const firstStop =
					order.stops.length > 0 ? order.stops[0] : null;

				if (firstStop?.datetime_start) {
					const firstStopDate = new Date(firstStop.datetime_start);

					if (firstStopDate < now) {
						// Past/overdue stops have highest priority - the more overdue, the higher the priority
						priorityScore =
							2000000000000 +
							(now.getTime() - firstStopDate.getTime());
					} else {
						// Future stops have priority based on soonest deadline
						priorityScore =
							1000000000000 -
							(firstStopDate.getTime() - now.getTime());
					}
				} else {
					// Orders without a datetime_start get lower priority, ordered by creation date
					priorityScore = order.createdAt.getTime();
				}

				return {
					id: order.id,
					order_number: order.order_number,
					customer: order.customer,
					stops: order.stops, // Return all stops rather than just first/last
					createdAt: order.createdAt,
					priorityScore,
				};
			});

		// Sort by priority score (descending)
		const sortedOrders = processedOrders
			.sort((a, b) => b.priorityScore - a.priorityScore)
			// Remove the metadata fields from the final response
			.map(
				({ priorityScore, ...orderBase }) =>
					orderBase as AvailableOrder,
			);

		console.log(`Found ${sortedOrders.length} available orders`);

		return {
			items: sortedOrders,
			total: total,
			page,
			totalPages: Math.ceil(total / limit),
		};
	} catch (error) {
		console.error("Failed to fetch available orders:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch available orders",
		});
	}
}
