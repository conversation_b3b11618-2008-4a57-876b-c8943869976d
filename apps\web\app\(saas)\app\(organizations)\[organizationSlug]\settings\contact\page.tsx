import { CarrierSettingsForm } from "@saas/organizations/contact-settings/components/carrier-settings-form";
import { CustomerSettingsForm } from "@saas/organizations/contact-settings/components/customer-settings-form";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";

import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import React from "react";

export const metadata: Metadata = {
	title: "Contact Settings | Settings",
	description:
		"Configure customer and carrier numbering for your organization",
};

export default async function ContactSettingsPage() {
	const t = await getTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium">Contact Settings</h3>
				<p className="text-sm text-muted-foreground">
					Configure how customer and carrier numbers are generated
				</p>
			</div>
			<Separator />

			<Tabs defaultValue="customers" className="w-full">
				<TabsList className="mb-4">
					<TabsTrigger value="customers">
						Customer Numbers
					</TabsTrigger>
					<TabsTrigger value="carriers">Carrier Numbers</TabsTrigger>
				</TabsList>

				<TabsContent value="customers" className="space-y-6">
					<CustomerSettingsForm />

					<Card>
						<CardHeader>
							<CardTitle>About Customer Numbering</CardTitle>
							<CardDescription>
								How the customer numbering system works
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<p>
								Customer numbers can be customized using a
								prefix and a sequential counter. This allows you
								to generate customer numbers that match your
								business requirements.
							</p>

							<div className="space-y-2">
								<h4 className="font-medium">Example Format:</h4>
								<ul className="list-disc pl-5 space-y-1">
									<li>
										<strong>
											Prefix + Sequential Number:
										</strong>{" "}
										For example, with prefix "2" and 4
										leading zeros: 20001, 20002, etc.
									</li>
								</ul>
							</div>

							<div className="space-y-2">
								<h4 className="font-medium">Tips:</h4>
								<ul className="list-disc pl-5 space-y-1">
									<li>
										Use distinctive prefixes for different
										types of contacts to easily identify
										them
									</li>
									<li>
										The sequential number increases
										automatically with each new customer
									</li>
								</ul>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="carriers" className="space-y-6">
					<CarrierSettingsForm />

					<Card>
						<CardHeader>
							<CardTitle>About Carrier Numbering</CardTitle>
							<CardDescription>
								How the carrier numbering system works
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<p>
								Carrier numbers can be customized using a prefix
								and a sequential counter. This allows you to
								generate carrier numbers that match your
								business requirements.
							</p>

							<div className="space-y-2">
								<h4 className="font-medium">Example Format:</h4>
								<ul className="list-disc pl-5 space-y-1">
									<li>
										<strong>
											Prefix + Sequential Number:
										</strong>{" "}
										For example, with prefix "6" and 4
										leading zeros: 60001, 60002, etc.
									</li>
								</ul>
							</div>

							<div className="space-y-2">
								<h4 className="font-medium">Tips:</h4>
								<ul className="list-disc pl-5 space-y-1">
									<li>
										Use distinctive prefixes for different
										types of contacts to easily identify
										them
									</li>
									<li>
										The sequential number increases
										automatically with each new carrier
									</li>
								</ul>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
