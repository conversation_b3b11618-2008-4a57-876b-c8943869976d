"use client";
import {
	Dnd<PERSON>ontext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	restrictToParentElement,
	restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
	SortableContext,
	arrayMove,
	sortableKeyboardCoordinates,
	useSortable,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { StopDialog } from "@saas/shared/components/stops/StopDialog";
import { StopType } from "@saas/shared/lib/stops/stop-types";
import type { StopFormValues } from "@saas/shared/lib/stops/stop-types";
import {
	type NewTourStop,
	type Stop,
	useTourBuilder,
} from "@saas/tours/context/tours-planner-context";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import {
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	Clock,
	GripVertical,
	Info,
	Package,
	Plus,
	Trash,
	TruckIcon,
	Weight,
} from "lucide-react";
import type { CSSProperties } from "react";
import { useCallback, useEffect, useState } from "react";
import React from "react";
import { v4 as uuidv4 } from "uuid";
import { CraneIcon } from "./icons/crane-icon";

interface SortableItemProps {
	id: string;
	index: number;
	stop: any;
	moveUp: (index: number) => void;
	moveDown: (index: number) => void;
	removeStop: (id: string) => void;
	isSaving: boolean;
	stopsLength: number;
}

function SortableItem({
	id,
	index,
	stop,
	moveUp,
	moveDown,
	removeStop,
	isSaving,
	stopsLength,
}: SortableItemProps) {
	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id });

	const style: CSSProperties = {
		transform: CSS.Transform.toString(transform),
		transition,
		width: "100%",
		maxWidth: "100%",
		position: transform ? "relative" : undefined,
		zIndex: transform ? 10 : undefined,
	};

	// Determine badge status based on stop type
	const getBadgeStatus = (
		stopType: string,
	): "info" | "success" | "warning" | "error" | null | undefined => {
		switch (stopType) {
			case "loading":
				return "info";
			case "unloading":
				return "success";
			case "stopover":
				return "warning";
			default:
				return null;
		}
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className="border rounded-md bg-card p-3 mb-2"
		>
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-2">
					<div {...attributes} {...listeners} className="cursor-grab">
						<GripVertical className="h-5 w-5 text-muted-foreground" />
					</div>
					<Badge status={getBadgeStatus(stop.stopType)}>
						{stop.stopType}
					</Badge>

					<span className="font-medium">
						{stop.nameLine && stop.street && stop.city
							? `${stop.nameLine}, ${stop.street}, ${stop.city}`
							: stop.street && stop.city
								? `${stop.street}, ${stop.city}`
								: stop.nameLine
									? stop.nameLine
									: "Address not available"}
					</span>
				</div>
				<div className="flex items-center space-x-1">
					<Button
						variant="outline"
						size="icon"
						onClick={() => moveUp(index)}
						disabled={index === 0 || isSaving}
					>
						<ChevronUp className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={() => moveDown(index)}
						disabled={index === stopsLength - 1 || isSaving}
					>
						<ChevronDown className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						onClick={() => removeStop(stop.id)}
						disabled={isSaving}
					>
						<Trash className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{stop.order?.customer?.nameLine1 && (
				<div className="mt-2 text-sm font-medium text-primary">
					{stop.order.customer.nameLine1}
					{stop.order?.order_number && (
						<span className="ml-2 text-muted-foreground">
							(Order: {stop.order.order_number})
						</span>
					)}
				</div>
			)}

			{(stop.datetime_start || stop.datetime_end) && (
				<div className="flex items-center gap-1 mt-2 text-sm text-muted-foreground">
					<Clock className="h-3.5 w-3.5 flex-shrink-0" />
					<span>
						{stop.datetime_start && (
							<span>
								From:{" "}
								{format(new Date(stop.datetime_start), "PPp")}
							</span>
						)}
						{stop.datetime_start && stop.datetime_end && (
							<span> - </span>
						)}
						{stop.datetime_end && (
							<span>
								To: {format(new Date(stop.datetime_end), "PPp")}
							</span>
						)}
					</span>
				</div>
			)}

			{/* Display goods information always */}
			{stop.goods_information && (
				<div className="mt-2 text-sm border-t border-dashed border-gray-200 pt-2">
					<span className="font-medium">Goods:</span>{" "}
					{stop.goods_information}
				</div>
			)}

			{/* Key indicators with icons */}
			<div className="flex flex-wrap gap-2 mt-2 mb-2">
				<TooltipProvider>
					{stop.dangerous_goods_nr && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-red-100">
									<AlertTriangle className="h-3.5 w-3.5 text-red-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Dangerous goods: {stop.dangerous_goods_nr}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.special_transports_height > 0 ||
						stop.special_transports_width > 0 ||
						stop.special_transports_length > 0) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-amber-100">
									<TruckIcon className="h-3.5 w-3.5 text-amber-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Special transport:{" "}
									{stop.special_transports_length}x
									{stop.special_transports_width}x
									{stop.special_transports_height}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.crane_loading || stop.crane_unloading) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-blue-100">
									<CraneIcon className="h-3.5 w-3.5 text-blue-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Crane needed for{" "}
									{stop.crane_loading && stop.crane_unloading
										? "loading & unloading"
										: stop.crane_loading
											? "loading"
											: "unloading"}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{stop.special_transports_heavy && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-purple-100">
									<Weight className="h-3.5 w-3.5 text-purple-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>Heavy transport required</p>
							</TooltipContent>
						</Tooltip>
					)}

					{stop.pallet_exchange_count > 0 && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-green-100">
									<Package className="h-3.5 w-3.5 text-green-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Pallet exchange:{" "}
									{stop.pallet_exchange_count}{" "}
									{stop.pallet_exchange_type || "units"}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.driver_notes || stop.information_text) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-6 h-6 flex items-center justify-center rounded-full bg-gray-100">
									<Info className="h-3.5 w-3.5 text-gray-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									{stop.driver_notes || stop.information_text}
								</p>
							</TooltipContent>
						</Tooltip>
					)}
				</TooltipProvider>
			</div>

			{/* All details - replacing collapsible */}
			<div className="mt-2 pt-2 border-t border-dashed border-gray-200">
				<div className="space-y-2 text-sm overflow-hidden break-words">
					{(stop.length || stop.width || stop.height) && (
						<div>
							<span className="font-medium">Dimensions:</span>{" "}
							{stop.length}x{stop.width}x{stop.height}{" "}
							{stop.units || "m"}
						</div>
					)}

					{stop.weight && (
						<div>
							<span className="font-medium">Weight:</span>{" "}
							{stop.weight} kg
						</div>
					)}

					{stop.cubic_meters && (
						<div>
							<span className="font-medium">Volume:</span>{" "}
							{stop.cubic_meters} m³
						</div>
					)}

					{stop.loading_meter && (
						<div>
							<span className="font-medium">Loading Meters:</span>{" "}
							{stop.loading_meter}
						</div>
					)}

					{stop.measurement_text && (
						<div>
							<span className="font-medium">
								Measurement Notes:
							</span>{" "}
							{stop.measurement_text}
						</div>
					)}

					{stop.reference_number && (
						<div>
							<span className="font-medium">Reference:</span>{" "}
							{stop.reference_number}
						</div>
					)}

					{stop.neutrality_text && (
						<div>
							<span className="font-medium">Neutrality:</span>{" "}
							{stop.neutrality_text}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

// Create a static copy of the SortableItem for the overlay
function DragItem({ stop, stopsLength }: { stop: any; stopsLength: number }) {
	// Determine badge status based on stop type
	const getBadgeStatus = (
		stopType: string,
	): "info" | "success" | "warning" | "error" | null | undefined => {
		switch (stopType) {
			case "loading":
				return "info";
			case "unloading":
				return "success";
			case "stopover":
				return "warning";
			default:
				return null;
		}
	};

	return (
		<div className="border rounded-md p-3 bg-card mb-2 shadow-lg w-full max-w-full overflow-hidden">
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-2">
					<div className="cursor-grab">
						<GripVertical className="h-5 w-5 text-muted-foreground" />
					</div>
					<Badge status={getBadgeStatus(stop.stopType)}>
						{stop.stopType}
					</Badge>

					<span className="font-medium">
						{stop.nameLine && stop.street && stop.city
							? `${stop.nameLine}, ${stop.street}, ${stop.city}`
							: stop.street && stop.city
								? `${stop.street}, ${stop.city}`
								: stop.nameLine
									? stop.nameLine
									: "Address not available"}
					</span>
				</div>
				<div className="flex items-center space-x-1 invisible">
					{/* These buttons are invisible in drag overlay */}
					<Button variant="outline" size="icon">
						<ChevronUp className="h-4 w-4" />
					</Button>
					<Button variant="outline" size="icon">
						<ChevronDown className="h-4 w-4" />
					</Button>
					<Button variant="outline" size="icon">
						<Trash className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{stop.order?.customer?.nameLine1 && (
				<div className="mt-2 text-sm font-medium text-primary">
					{stop.order.customer.nameLine1}
					{stop.order?.order_number && (
						<span className="ml-2 text-muted-foreground">
							(Order: {stop.order.order_number})
						</span>
					)}
				</div>
			)}

			{(stop.datetime_start || stop.datetime_end) && (
				<div className="flex items-center gap-1 mt-2 text-sm text-muted-foreground">
					<Clock className="h-3.5 w-3.5 flex-shrink-0" />
					<span>
						{stop.datetime_start && (
							<span>
								From:{" "}
								{format(new Date(stop.datetime_start), "PPp")}
							</span>
						)}
						{stop.datetime_start && stop.datetime_end && (
							<span> - </span>
						)}
						{stop.datetime_end && (
							<span>
								To: {format(new Date(stop.datetime_end), "PPp")}
							</span>
						)}
					</span>
				</div>
			)}

			{/* Simplified goods info for overlay */}
			{stop.goods_information && (
				<div className="mt-2 text-sm border-t border-dashed border-gray-200 pt-2">
					<span className="font-medium">Goods:</span>{" "}
					{stop.goods_information}
				</div>
			)}

			{/* Just display indicators without tooltips */}
			<div className="flex flex-wrap gap-2 mt-2 mb-2">
				{stop.dangerous_goods_nr && (
					<div className="w-6 h-6 flex items-center justify-center rounded-full bg-red-100">
						<AlertTriangle className="h-3.5 w-3.5 text-red-600" />
					</div>
				)}
				{/* ... other indicators simplified ... */}
			</div>
		</div>
	);
}

// Travel info component that goes between stops
function TravelInfo({
	fromStop,
	toStop,
	index,
}: {
	fromStop: any;
	toStop: any;
	index: number;
}) {
	// Always initialize useState with the same structure
	const [travelInfo, setTravelInfo] = useState<{
		duration: number;
		distance: number;
		loading: boolean;
		error: boolean;
	}>({
		duration: 0,
		distance: 0,
		loading: true,
		error: false,
	});

	// Always use one useEffect with the same structure
	useEffect(() => {
		let isMounted = true;

		// Centralized function for setting travel info state
		const updateTravelInfo = (data: Partial<typeof travelInfo>) => {
			if (isMounted) {
				setTravelInfo((prev) => ({ ...prev, ...data }));
			}
		};

		async function fetchTravelInfo() {
			// Early return with consistent state update
			if (!fromStop || !toStop) {
				updateTravelInfo({ loading: false });
				return;
			}

			try {
				let fromCoords: { longitude: number; latitude: number } | null =
					null;
				let toCoords: { longitude: number; latitude: number } | null =
					null;

				// Use existing coordinates if available
				if (fromStop.coordinates) {
					fromCoords = fromStop.coordinates;
				} else if (fromStop.street && fromStop.city) {
					// Or geocode the address
					fromCoords = await geocodeAddress(
						fromStop.street,
						fromStop.city,
					);
				}

				if (toStop.coordinates) {
					toCoords = toStop.coordinates;
				} else if (toStop.street && toStop.city) {
					toCoords = await geocodeAddress(toStop.street, toStop.city);
				}

				// If we have both coordinates, fetch directions
				if (fromCoords && toCoords) {
					try {
						const coordString = `${fromCoords.longitude},${fromCoords.latitude};${toCoords.longitude},${toCoords.latitude}`;

						const response = await fetch(
							`https://api.mapbox.com/directions/v5/mapbox/driving/${coordString}?alternatives=false&geometries=geojson&overview=full&steps=false&access_token=${process.env.NEXT_PUBLIC_MAPBOX_API_KEY}`,
						);

						if (!response.ok) {
							throw new Error("Directions request failed");
						}

						const data = await response.json();

						if (data.routes && data.routes.length > 0) {
							// Duration is in seconds, distance in meters
							updateTravelInfo({
								duration: data.routes[0].duration,
								distance: data.routes[0].distance,
								loading: false,
							});
						} else {
							updateTravelInfo({ loading: false });
						}
					} catch (error) {
						console.error("Error fetching travel info:", error);
						updateTravelInfo({ loading: false, error: true });
					}
				} else {
					updateTravelInfo({ loading: false });
				}
			} catch (error) {
				console.error("Error in travel info:", error);
				updateTravelInfo({ loading: false, error: true });
			}
		}

		// Function to geocode addresses
		async function geocodeAddress(street: string, city: string) {
			try {
				const query = encodeURIComponent(`${street}, ${city}`);
				const response = await fetch(
					`https://api.mapbox.com/geocoding/v5/mapbox.places/${query}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_API_KEY}`,
				);

				if (!response.ok) {
					throw new Error("Geocoding request failed");
				}

				const data = await response.json();

				if (data.features && data.features.length > 0) {
					// Mapbox returns coordinates as [longitude, latitude]
					const [longitude, latitude] = data.features[0].center;
					return { longitude, latitude };
				}

				return null;
			} catch (error) {
				console.error("Geocoding error:", error);
				return null;
			}
		}

		fetchTravelInfo();

		// Clean up function to handle component unmounting
		return () => {
			isMounted = false;
		};
	}, [fromStop, toStop]);

	// Format duration nicely (converting seconds to hours/minutes)
	const formatDuration = (seconds: number) => {
		const hours = Math.floor(seconds / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);

		if (hours > 0) {
			return `${hours}h ${minutes}m`;
		}
		return `${minutes} min`;
	};

	// Format distance nicely (converting meters to km)
	const formatDistance = (meters: number) => {
		const km = (meters / 1000).toFixed(1);
		return `${km} km`;
	};

	// Use a single return with conditional rendering inside
	return (
		<div className="flex justify-center py-1 relative">
			<div className="absolute left-[1.3rem] top-0 bottom-0 w-0.5 bg-muted-foreground/20" />
			{!travelInfo.loading &&
				travelInfo.duration > 0 &&
				travelInfo.distance > 0 && (
					<div className="bg-muted/40 rounded-full py-1 px-3 text-xs text-muted-foreground flex items-center z-10">
						<TruckIcon className="h-3 w-3 mr-1.5" />
						<span>{formatDuration(travelInfo.duration)}</span>
						<span className="mx-1.5">•</span>
						<span>{formatDistance(travelInfo.distance)}</span>
					</div>
				)}
		</div>
	);
}

export function TourStopsPanel({
	onRemoveStop,
}: { onRemoveStop?: (stopId: string) => void }) {
	const {
		stops,
		reorderStops,
		removeStop: contextRemoveStop,
		isSaving,
		addTourStop,
		newTourStops,
	} = useTourBuilder();
	const [activeId, setActiveId] = useState<string | null>(null);
	const [isDialogOpen, setIsDialogOpen] = useState(false);

	// Use the provided removeStop function or fall back to the context one
	const removeStop = useCallback(
		(stopId: string) => {
			// Execute the removal in the next event loop cycle
			// This ensures React can complete its current render pass
			// before the state changes that might affect hooks execution
			setTimeout(() => {
				if (onRemoveStop) {
					onRemoveStop(stopId);
				} else {
					contextRemoveStop(stopId);
				}
			}, 0);
		},
		[onRemoveStop, contextRemoveStop],
	);

	// Combine regular stops and new tour stops for display
	const allStops = [...stops, ...newTourStops].sort(
		(a, b) => (a.position || 0) - (b.position || 0),
	);

	const activeStop = activeId
		? allStops.find((stop) => stop.id === activeId)
		: null;

	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8, // Add a small distance threshold to prevent accidental drags
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	const handleDragEnd = (event: DragEndEvent) => {
		setActiveId(null);
		const { active, over } = event;

		if (!over || active.id === over.id) {
			return;
		}

		const oldIndex = allStops.findIndex((stop) => stop.id === active.id);
		const newIndex = allStops.findIndex((stop) => stop.id === over.id);

		const updatedItems = arrayMove(allStops, oldIndex, newIndex).map(
			(item, index) => ({
				...item,
				position: index,
			}),
		);

		// Since we're now working with a combined array, we need to split it back
		// into regular stops and new tour stops for the state update
		const updatedStops = updatedItems.filter((stop) => !("isNew" in stop));
		const updatedNewTourStops = updatedItems.filter(
			(stop) => "isNew" in stop,
		) as NewTourStop[];

		// Update the stop positions in the tour builder context
		reorderStops(updatedStops as Stop[]);

		// For new tour stops, we'll need to update them individually since
		// we don't have a reorderNewTourStops function
		updatedNewTourStops.forEach((stop) => {
			// Remove and re-add each stop to update its position
			removeStop(stop.id);
			addTourStop(stop);
		});
	};

	// Move a stop up in the list
	const moveUp = (index: number) => {
		if (index === 0) {
			return;
		}

		const items = Array.from(allStops);
		[items[index - 1], items[index]] = [items[index], items[index - 1]];

		// Update positions based on new order
		const updatedItems = items.map((item, idx) => ({
			...item,
			position: idx,
		}));

		// Split and update as above
		const updatedStops = updatedItems.filter((stop) => !("isNew" in stop));
		const updatedNewTourStops = updatedItems.filter(
			(stop) => "isNew" in stop,
		) as NewTourStop[];

		reorderStops(updatedStops as Stop[]);
		updatedNewTourStops.forEach((stop) => {
			removeStop(stop.id);
			addTourStop(stop);
		});
	};

	// Move a stop down in the list
	const moveDown = (index: number) => {
		if (index === allStops.length - 1) {
			return;
		}

		const items = Array.from(allStops);
		[items[index], items[index + 1]] = [items[index + 1], items[index]];

		// Update positions based on new order
		const updatedItems = items.map((item, idx) => ({
			...item,
			position: idx,
		}));

		// Split and update as above
		const updatedStops = updatedItems.filter((stop) => !("isNew" in stop));
		const updatedNewTourStops = updatedItems.filter(
			(stop) => "isNew" in stop,
		) as NewTourStop[];

		reorderStops(updatedStops as Stop[]);
		updatedNewTourStops.forEach((stop) => {
			removeStop(stop.id);
			addTourStop(stop);
		});
	};

	// Handle form submission from the StopDialog
	const handleSubmitTourStop = async (values: StopFormValues) => {
		let coordinates = null;

		// Geocode the address if street and city are provided
		if (values.street && values.city) {
			try {
				const query = encodeURIComponent(
					`${values.street}, ${values.city}`,
				);
				const response = await fetch(
					`https://api.mapbox.com/geocoding/v5/mapbox.places/${query}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_API_KEY}`,
				);

				if (response.ok) {
					const data = await response.json();
					if (data.features && data.features.length > 0) {
						// Mapbox returns coordinates as [longitude, latitude]
						const [longitude, latitude] = data.features[0].center;
						coordinates = { longitude, latitude };
					}
				}
			} catch (error) {
				console.error("Error geocoding address:", error);
			}
		}

		// Convert the separate date/time fields to datetime fields
		let datetime_start = null;
		let datetime_end = null;

		// Combine start_date and start_time into datetime_start
		if (values.start_date) {
			try {
				// The date might be in format "YYYY-MM-DD" or "DD.MM.YYYY"
				// Try to parse it properly
				let dateStr = values.start_date;

				// If date is in DD.MM.YYYY format, convert to YYYY-MM-DD
				if (dateStr.includes(".")) {
					const [day, month, year] = dateStr.split(".");
					dateStr = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
				}

				if (values.start_time) {
					// Ensure time is in HH:MM format
					const timeStr = values.start_time.padStart(5, "0");
					datetime_start = new Date(
						`${dateStr}T${timeStr}:00`,
					).toISOString();
				} else {
					datetime_start = new Date(
						`${dateStr}T00:00:00`,
					).toISOString();
				}
			} catch (error) {
				console.error("Error parsing start datetime:", error);
				datetime_start = null;
			}
		}

		// Combine end_date and end_time into datetime_end
		if (values.end_date) {
			try {
				// The date might be in format "YYYY-MM-DD" or "DD.MM.YYYY"
				let dateStr = values.end_date;

				// If date is in DD.MM.YYYY format, convert to YYYY-MM-DD
				if (dateStr.includes(".")) {
					const [day, month, year] = dateStr.split(".");
					dateStr = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
				}

				if (values.end_time) {
					// Ensure time is in HH:MM format
					const timeStr = values.end_time.padStart(5, "0");
					datetime_end = new Date(
						`${dateStr}T${timeStr}:00`,
					).toISOString();
				} else {
					datetime_end = new Date(
						`${dateStr}T23:59:59`,
					).toISOString();
				}
			} catch (error) {
				console.error("Error parsing end datetime:", error);
				datetime_end = null;
			}
		}

		// Create a new tour stop from the form values
		const newStop: NewTourStop = {
			// Generate a temporary ID for client-side management
			id: uuidv4(),
			// Spread all form values to include everything
			...values,
			// Override datetime fields with the combined values
			datetime_start,
			datetime_end,
			// Add/override specific fields
			coordinates: coordinates,
			organizationId: "will-be-set-on-server", // This will be set server-side
			isNew: true,
		};

		// Add the stop to the tour builder context
		addTourStop(newStop);
	};

	if (allStops.length === 0) {
		return (
			<Card className="h-full flex flex-col min-h-0">
				<CardHeader className="shrink-0 flex flex-row items-center justify-between">
					<CardTitle>Tour Stops</CardTitle>
					<Button
						size="sm"
						onClick={() => setIsDialogOpen(true)}
						className="flex items-center"
					>
						<Plus className="h-4 w-4 mr-1" />
						Add Tour Stop
					</Button>
				</CardHeader>
				<CardContent className="flex-1 flex items-center justify-center overflow-hidden">
					<div className="text-center text-muted-foreground">
						<p>No stops added to this tour yet.</p>
						<p className="text-sm mt-2">
							Use the panel on the right to search and add stops,
							or add a tour-specific stop with the button above.
						</p>
					</div>
				</CardContent>

				{/* Tour Stop Dialog */}
				<StopDialog
					isOpen={isDialogOpen}
					onClose={() => setIsDialogOpen(false)}
					onSubmit={handleSubmitTourStop}
					initialValues={{
						stopType: "loading",
						time_type: "latest",
					}}
					title="Add Tour Stop"
					description="Create a stop directly assigned to this tour"
					availableStopTypes={[
						StopType.LOADING,
						StopType.UNLOADING,
						StopType.STOPOVER,
					]}
				/>
			</Card>
		);
	}

	return (
		<Card className="h-full flex flex-col min-h-0">
			<CardHeader className="shrink-0 flex flex-row items-center justify-between">
				<CardTitle>Tour Stops ({allStops.length})</CardTitle>
				<Button
					size="sm"
					onClick={() => setIsDialogOpen(true)}
					className="flex items-center"
				>
					<Plus className="h-4 w-4 mr-1" />
					Add Tour Stop
				</Button>
			</CardHeader>
			<CardContent className="p-0 flex-1 min-h-0">
				<ScrollArea className="h-full px-6 overflow-hidden">
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragStart={handleDragStart}
						onDragEnd={handleDragEnd}
						modifiers={[
							restrictToVerticalAxis,
							restrictToParentElement,
						]}
					>
						<SortableContext
							items={allStops.map((stop) => stop.id)}
							strategy={verticalListSortingStrategy}
						>
							<div className="overflow-x-hidden pb-4">
								{allStops.map((stop, index) => (
									<React.Fragment key={stop.id}>
										<SortableItem
											id={stop.id}
											index={index}
											stop={stop}
											moveUp={moveUp}
											moveDown={moveDown}
											removeStop={removeStop}
											isSaving={isSaving}
											stopsLength={allStops.length}
										/>
										{/* Add travel info after each stop except the last one */}
										{index < allStops.length - 1 && (
											<TravelInfo
												key={`travel-${index}`}
												fromStop={stop}
												toStop={allStops[index + 1]}
												index={index}
											/>
										)}
									</React.Fragment>
								))}
							</div>
						</SortableContext>

						{/* Drag overlay for consistent appearance */}
						<DragOverlay adjustScale={false}>
							{activeStop && (
								<DragItem
									stop={activeStop}
									stopsLength={allStops.length}
								/>
							)}
						</DragOverlay>
					</DndContext>
				</ScrollArea>
			</CardContent>

			{/* Tour Stop Dialog */}
			<StopDialog
				isOpen={isDialogOpen}
				onClose={() => setIsDialogOpen(false)}
				onSubmit={handleSubmitTourStop}
				initialValues={{
					stopType: "loading",
					time_type: "latest",
				}}
				title="Add Tour Stop"
				description="Create a stop directly assigned to this tour"
				availableStopTypes={[
					StopType.LOADING,
					StopType.UNLOADING,
					StopType.STOPOVER,
				]}
			/>
		</Card>
	);
}
