"use client";

import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	ChevronLeft,
	ChevronRight,
	ClipboardList,
	Flag,
	FlagTriangleRight,
	HelpCircle,
	MapPin,
} from "lucide-react";
import { useDashboard } from "../hooks/use-dashboard";

export function AvailableOrders() {
	const {
		availableOrders,
		availableOrdersTotal,
		availableOrdersPage,
		setAvailableOrdersPage,
		availableOrdersPageSize,
		setAvailableOrdersPageSize,
		isAvailableOrdersLoading,
		availableOrdersTotalPages,
		utils,
	} = useDashboard();

	const getRowAnimationClass = (order: any) => {
		// Orders with no stops should have normal priority
		if (!order.stops || order.stops.length === 0) {
			return "";
		}

		const firstStop = order.stops[0];
		const now = new Date();
		const startTime = firstStop.datetime_start
			? new Date(firstStop.datetime_start)
			: null;

		// If no start time available, no animation
		if (!startTime) {
			return "";
		}

		// If start time is in the past (overdue), pulsate red - highest priority
		if (startTime < now) {
			return "animate-pulse-red";
		}

		// If start time is within the next 24 hours, pulsate yellow
		const oneDayFromNow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
		if (startTime > now && startTime <= oneDayFromNow) {
			return "animate-pulse-yellow";
		}

		// If start time is in the next 2-3 days, pulsate blue
		const threeDaysFromNow = new Date(
			now.getTime() + 3 * 24 * 60 * 60 * 1000,
		);
		if (startTime > oneDayFromNow && startTime <= threeDaysFromNow) {
			return "animate-pulse-blue";
		}

		return "";
	};

	// Function to render stop details for tooltip
	const renderStopDetails = (stops: any[]) => {
		if (!stops || stops.length === 0) {
			return "No stops";
		}

		return (
			<div className="space-y-3 max-w-sm">
				<h4 className="font-medium">All Stops ({stops.length})</h4>
				<div className="space-y-2">
					{stops.map((stop, index) => (
						<div
							key={index}
							className="border-b border-border pb-2 last:border-0"
						>
							<div className="flex items-center gap-1 font-medium">
								<MapPin className="h-3 w-3" />
								Stop {index + 1}
							</div>
							<div className="text-xs">
								{utils.formatStopAddress(stop)}
							</div>
							<div className="text-xs text-muted-foreground">
								{stop.datetime_start
									? format(
											new Date(stop.datetime_start),
											"dd.MM.yyyy, HH:mm",
										)
									: "No date"}
								{stop.datetime_end &&
									stop.datetime_start &&
									` - ${format(new Date(stop.datetime_end), "HH:mm")}`}
							</div>
						</div>
					))}
				</div>
			</div>
		);
	};

	return (
		<Card className="w-full">
			<CardHeader className="p-4 sm:p-6">
				<div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
					<CardTitle className="text-lg sm:text-xl">
						Available Orders
					</CardTitle>
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger className="mt-1 self-start sm:mt-0">
								<HelpCircle className="h-4 w-4 text-muted-foreground" />
							</TooltipTrigger>
							<TooltipContent className="max-w-sm">
								<p>Orders are prioritized as follows:</p>
								<ul className="mt-2 list-disc pl-4">
									<li>
										Orders with overdue first stops have
										highest priority (red)
									</li>
									<li>
										Orders starting within 24 hours are
										medium priority (yellow)
									</li>
									<li>
										Orders starting within 2-3 days are
										lower priority (blue)
									</li>
									<li>
										Orders without start dates or with dates
										further in the future are shown last
									</li>
								</ul>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<CardDescription className="mt-1">
					Orders that need to be assigned, prioritized by the earliest
					first stop
				</CardDescription>
			</CardHeader>

			<CardContent className="p-3 sm:p-6">
				<style jsx global>{`
					@keyframes pulse-red {
						0%, 100% { background-color: transparent; }
						50% { background-color: rgba(239, 68, 68, 0.15); }
					}
					
					@keyframes pulse-yellow {
						0%, 100% { background-color: transparent; }
						50% { background-color: rgba(234, 179, 8, 0.15); }
					}
					
					@keyframes pulse-blue {
						0%, 100% { background-color: transparent; }
						50% { background-color: rgba(59, 130, 246, 0.15); }
					}
					
					.animate-pulse-red {
						animation: pulse-red 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
					}
					
					.animate-pulse-yellow {
						animation: pulse-yellow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
					}
					
					.animate-pulse-blue {
						animation: pulse-blue 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
					}
				`}</style>

				{isAvailableOrdersLoading ? (
					<div className="space-y-3">
						<Skeleton className="h-20 w-full" />
						<Skeleton className="h-20 w-full" />
						<Skeleton className="h-20 w-full" />
					</div>
				) : availableOrders.length === 0 ? (
					<div className="py-6 text-center text-muted-foreground">
						No available orders found
					</div>
				) : (
					<>
						<ScrollArea className="h-[300px] sm:h-[400px] md:h-[500px] pr-2 sm:pr-4">
							<div className="space-y-2 sm:space-y-3">
								{availableOrders.map((order) => {
									const firstStop =
										order.stops && order.stops.length > 0
											? order.stops[0]
											: null;
									const lastStop =
										order.stops && order.stops.length > 1
											? order.stops[
													order.stops.length - 1
												]
											: null;

									return (
										<div
											key={order.id}
											className={cn(
												"flex flex-col sm:flex-row sm:items-center sm:justify-between rounded-lg border p-3 sm:p-4",
												getRowAnimationClass(order),
											)}
										>
											<div className="flex items-center gap-3 mb-2 sm:mb-0">
												<ClipboardList className="h-5 w-5 sm:h-6 sm:w-6 text-muted-foreground flex-shrink-0" />
												<div>
													<div className="font-medium truncate max-w-[240px] sm:max-w-none">
														Order:{" "}
														{order.order_number ||
															order.id.substring(
																0,
																8,
															)}
														{order.stops.length >
															1 && (
															<Badge
																status="info"
																className="ml-2"
															>
																{
																	order.stops
																		.length
																}{" "}
																stops
															</Badge>
														)}
													</div>
													{order.customer && (
														<div className="text-xs sm:text-sm text-muted-foreground truncate max-w-[240px] sm:max-w-none">
															{order.customer
																.nameLine1 ||
																"Unnamed customer"}
														</div>
													)}
												</div>
											</div>
											<div className="text-xs sm:text-sm">
												{order.stops &&
												order.stops.length > 0 ? (
													<TooltipProvider>
														<Tooltip>
															<TooltipTrigger
																asChild
															>
																<div className="sm:text-right cursor-help">
																	<div className="flex flex-col gap-1">
																		{/* First stop address */}
																		{firstStop && (
																			<div>
																				<div className="flex items-center sm:justify-end">
																					<FlagTriangleRight className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 text-emerald-500 flex-shrink-0" />
																					<span className="truncate max-w-[240px]">
																						{firstStop.zipCode ||
																							""}
																						{firstStop.city
																							? `, ${firstStop.city}`
																							: ""}
																					</span>
																				</div>
																				<div className="text-muted-foreground text-xs">
																					{firstStop.datetime_start
																						? format(
																								new Date(
																									firstStop.datetime_start,
																								),
																								"dd.MM.yyyy, HH:mm",
																							)
																						: "No date specified"}
																				</div>
																			</div>
																		)}

																		{/* Last stop address if it exists and is different from first */}
																		{lastStop &&
																			lastStop !==
																				firstStop && (
																				<div className="mt-1 sm:mt-2">
																					<div className="flex items-center sm:justify-end">
																						<Flag className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 text-red-500 flex-shrink-0" />
																						<span className="truncate max-w-[240px]">
																							{lastStop.zipCode ||
																								""}
																							{lastStop.city
																								? `, ${lastStop.city}`
																								: ""}
																						</span>
																					</div>
																					<div className="text-muted-foreground text-xs">
																						{lastStop.datetime_start
																							? format(
																									new Date(
																										lastStop.datetime_start,
																									),
																									"dd.MM.yyyy, HH:mm",
																								)
																							: "No date specified"}
																					</div>
																				</div>
																			)}
																	</div>
																</div>
															</TooltipTrigger>
															<TooltipContent
																side="top"
																sideOffset={5}
																className="w-[280px] sm:w-auto"
															>
																{renderStopDetails(
																	order.stops,
																)}
															</TooltipContent>
														</Tooltip>
													</TooltipProvider>
												) : (
													<Badge>
														No stops defined
													</Badge>
												)}
											</div>
										</div>
									);
								})}
							</div>
						</ScrollArea>

						{availableOrdersTotalPages &&
							availableOrdersTotalPages > 1 && (
								<div className="mt-4 flex flex-wrap items-center justify-between sm:justify-end sm:space-x-2">
									<div className="text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-0">
										Page {availableOrdersPage} of{" "}
										{availableOrdersTotalPages}
									</div>
									<div className="space-x-2">
										<Button
											variant="outline"
											size="sm"
											onClick={() =>
												setAvailableOrdersPage(
													availableOrdersPage - 1,
												)
											}
											disabled={availableOrdersPage <= 1}
											className="h-8 w-8 p-0"
										>
											<ChevronLeft className="h-4 w-4" />
										</Button>
										<Button
											variant="outline"
											size="sm"
											onClick={() =>
												setAvailableOrdersPage(
													availableOrdersPage + 1,
												)
											}
											disabled={
												availableOrdersPage >=
												(availableOrdersTotalPages || 1)
											}
											className="h-8 w-8 p-0"
										>
											<ChevronRight className="h-4 w-4" />
										</Button>
									</div>
								</div>
							)}
					</>
				)}
			</CardContent>
		</Card>
	);
}
