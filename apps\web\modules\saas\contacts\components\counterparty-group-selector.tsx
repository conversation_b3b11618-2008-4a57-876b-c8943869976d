"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";

import {
	useCounterpartyGroupsQuery,
	useCreateCounterpartyGroupMutation,
} from "@saas/contacts/lib/api-counterparty-groups";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import {
	Toolt<PERSON>,
	Toolt<PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>rovider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Check, ChevronDown, HelpCircle, Plus } from "lucide-react";
import { useRef, useState } from "react";
import { useForm, useFormContext } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

export interface CounterpartyGroupType {
	id: string;
	name: string;
	description?: string;
}

export interface CounterpartyGroupSelectorProps {
	counterpartyGroups?: CounterpartyGroupType[];
	onChange?: (value: string | undefined) => void;
	value?: string;
	name: string;
	label?: string;
	tooltip?: string;
	isLoading?: boolean;
	placeholder?: string;
	className?: string;
	error?: string;
	showCreateOption?: boolean;
	isModal?: boolean;
	allowClear?: boolean;
}

// Form schema for creating counterparty groups
const createCounterpartyGroupSchema = z.object({
	name: z.string().min(1, "Name is required"),
	description: z.string().optional(),
});

type CreateCounterpartyGroupValues = z.infer<
	typeof createCounterpartyGroupSchema
>;

/**
 * A reusable Counterparty Group selector component that displays group details
 */
export function CounterpartyGroupSelector({
	counterpartyGroups = [],
	onChange,
	value,
	name,
	label,
	tooltip,
	isLoading = false,
	placeholder = "Select counterparty group",
	className,
	error,
	showCreateOption = true,
	isModal = false,
	allowClear = true,
}: CounterpartyGroupSelectorProps) {
	const { activeOrganization } = useActiveOrganization();
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [open, setOpen] = useState(false);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const createButtonRef = useRef<HTMLDivElement>(null);

	const createForm = useForm<CreateCounterpartyGroupValues>({
		resolver: zodResolver(createCounterpartyGroupSchema),
		defaultValues: {
			name: "",
			description: "",
		},
	});

	const createMutation = useCreateCounterpartyGroupMutation(
		activeOrganization?.id || "",
	);

	// Get counterparty groups from API if not provided
	const { data: apiCounterpartyGroups, isLoading: isLoadingApi } =
		useCounterpartyGroupsQuery(
			{ organizationId: activeOrganization?.id || "" },
			{
				enabled:
					!counterpartyGroups?.length && !!activeOrganization?.id,
			},
		);

	// Combine provided counterparty groups with those from API
	const allCounterpartyGroups = counterpartyGroups?.length
		? counterpartyGroups
		: apiCounterpartyGroups?.items || [];

	// Handle creating a new counterparty group
	const handleCreateCounterpartyGroup = async (
		values: CreateCounterpartyGroupValues,
	) => {
		try {
			if (!activeOrganization?.id) {
				toast.error("No active organization");
				return;
			}

			const result = await createMutation.mutateAsync({
				...values,
				organizationId: activeOrganization?.id,
			});

			// Select the newly created counterparty group
			if (result && onChange) {
				onChange(result.id);
			}

			setIsDialogOpen(false);
			createForm.reset();
		} catch (error) {
			console.error("Error creating counterparty group:", error);
		}
	};

	// Find the selected counterparty group
	const selectedGroup = allCounterpartyGroups.find(
		(group) => group.id === value,
	);

	// Handle keyboard navigation
	const handleKeyDown = (e: React.KeyboardEvent) => {
		// Focus on the "Create counterparty group" button when pressing Tab
		if (e.key === "Tab" && createButtonRef.current) {
			e.preventDefault();
			createButtonRef.current.focus();
		}

		// Close the dropdown menu when pressing Escape
		if (e.key === "Escape") {
			e.preventDefault();
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle clearing the selection
	const handleClear = () => {
		if (onChange) {
			onChange(undefined);
		}
		setOpen(false);
	};

	return (
		<div className={cn("space-y-2", className)}>
			<DropdownMenu open={open} onOpenChange={setOpen} modal={isModal}>
				<DropdownMenuTrigger
					asChild
					disabled={isLoading || isLoadingApi}
				>
					<Button
						ref={buttonRef}
						variant="outline"
						className={cn(
							"w-full justify-between p-0 overflow-hidden h-auto text-left",
							!selectedGroup && "text-muted-foreground",
						)}
					>
						{isLoading || isLoadingApi ? (
							<div className="p-4">Loading...</div>
						) : selectedGroup ? (
							<Card className="w-full border-0 shadow-none">
								<CardContent className="p-4">
									<div className="flex flex-col items-start">
										<div className="flex justify-between w-full items-center">
											<div className="font-medium text-base">
												{selectedGroup.name}
											</div>
										</div>
										{selectedGroup.description && (
											<div className="text-sm text-muted-foreground mt-1">
												{selectedGroup.description}
											</div>
										)}
									</div>
								</CardContent>
							</Card>
						) : (
							<div className="p-4 text-muted-foreground">
								{placeholder}
							</div>
						)}
						<ChevronDown className="h-4 w-4 shrink-0 opacity-50 mr-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					className="w-[--radix-dropdown-menu-trigger-width] max-h-[300px] overflow-y-auto"
					onKeyDown={handleKeyDown}
				>
					{/* Clear option */}
					{allowClear && selectedGroup && (
						<DropdownMenuItem
							onClick={handleClear}
							className="text-muted-foreground"
						>
							<div className="flex items-center justify-between w-full">
								<span>Clear selection</span>
							</div>
						</DropdownMenuItem>
					)}

					{/* Existing counterparty groups */}
					{allCounterpartyGroups.map((group) => (
						<DropdownMenuItem
							key={group.id}
							onClick={() => {
								if (onChange) {
									onChange(group.id);
								}
								setOpen(false);
							}}
							className="p-0"
						>
							<Card className="w-full border-0 shadow-none hover:bg-accent">
								<CardContent className="p-4">
									<div className="flex items-center justify-between">
										<div className="flex flex-col items-start">
											<div className="font-medium">
												{group.name}
											</div>
											{group.description && (
												<div className="text-sm text-muted-foreground">
													{group.description}
												</div>
											)}
										</div>
										<div className="flex items-center gap-2">
											{value === group.id && (
												<Check className="h-4 w-4 text-primary" />
											)}
										</div>
									</div>
								</CardContent>
							</Card>
						</DropdownMenuItem>
					))}

					{/* Create new counterparty group option */}
					{showCreateOption && (
						<Dialog
							open={isDialogOpen}
							onOpenChange={setIsDialogOpen}
						>
							<DialogTrigger asChild>
								<DropdownMenuItem
									ref={createButtonRef}
									onSelect={(e) => {
										e.preventDefault();
										setIsDialogOpen(true);
									}}
									className="bg-muted/50"
								>
									<Plus className="mr-2 h-4 w-4" />
									Create counterparty group
								</DropdownMenuItem>
							</DialogTrigger>
							<DialogContent className="sm:max-w-md">
								<DialogHeader>
									<DialogTitle>
										Create Counterparty Group
									</DialogTitle>
									<DialogDescription>
										Create a new counterparty group to
										organize your contacts.
									</DialogDescription>
								</DialogHeader>
								<Form {...createForm}>
									<form
										onSubmit={(e) => {
											e.preventDefault();
											e.stopPropagation();
											createForm.handleSubmit(
												handleCreateCounterpartyGroup,
											)(e);
										}}
										className="space-y-4"
										onClick={(e) => e.stopPropagation()}
										onKeyDown={(e) => e.stopPropagation()}
									>
										<FormField
											control={createForm.control}
											name="name"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Name</FormLabel>
													<FormControl>
														<Input
															placeholder="Group name"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={createForm.control}
											name="description"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Description (Optional)
													</FormLabel>
													<FormControl>
														<Textarea
															placeholder="Group description"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<DialogFooter>
											<Button
												type="button"
												variant="outline"
												onClick={() => {
													setIsDialogOpen(false);
													createForm.reset();
												}}
											>
												Cancel
											</Button>
											<Button
												type="submit"
												disabled={
													createMutation.isPending
												}
											>
												{createMutation.isPending
													? "Creating..."
													: "Create Group"}
											</Button>
										</DialogFooter>
									</form>
								</Form>
							</DialogContent>
						</Dialog>
					)}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Error message */}
			{error && <p className="text-sm text-destructive">{error}</p>}

			{/* Label and tooltip */}
			{(label || tooltip) && (
				<div className="flex items-center gap-2">
					{label && (
						<span className="text-sm font-medium">{label}</span>
					)}
					{tooltip && (
						<TooltipProvider>
							<Tooltip>
								<TooltipTrigger asChild>
									<HelpCircle className="h-4 w-4 text-muted-foreground" />
								</TooltipTrigger>
								<TooltipContent>
									<p>{tooltip}</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					)}
				</div>
			)}
		</div>
	);
}

/**
 * Form-integrated version of CounterpartyGroupSelector using react-hook-form
 */
export function FormCounterpartyGroupSelector({
	name,
	label,
	tooltip,
	counterpartyGroups = [],
	isLoading = false,
	placeholder = "Select counterparty group",
	className,
	showCreateOption = true,
	isModal = false,
	allowClear = true,
}: Omit<CounterpartyGroupSelectorProps, "onChange" | "value" | "error">) {
	const form = useFormContext();

	return (
		<FormField
			control={form.control}
			name={name}
			render={({ field, fieldState }) => (
				<FormItem className={className}>
					<CounterpartyGroupSelector
						{...field}
						name={name}
						label={label}
						tooltip={tooltip}
						counterpartyGroups={counterpartyGroups}
						isLoading={isLoading}
						placeholder={placeholder}
						error={fieldState.error?.message}
						showCreateOption={showCreateOption}
						isModal={isModal}
						allowClear={allowClear}
						onChange={(value) => field.onChange(value)}
						value={field.value}
					/>
				</FormItem>
			)}
		/>
	);
}
