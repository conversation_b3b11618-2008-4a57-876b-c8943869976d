"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	ChevronLeft,
	ChevronRight,
	Flag,
	HelpCircle,
	Truck,
} from "lucide-react";
import { useDashboard } from "../hooks/use-dashboard";

export function AvailableVehicles() {
	const {
		availableVehicles,
		availableTotal,
		availablePage,
		setAvailablePage,
		availablePageSize,
		setAvailablePageSize,
		isAvailableLoading,
		availableTotalPages,
		utils,
	} = useDashboard();

	const getRowAnimationClass = (vehicle: any) => {
		// Trucks with no lastStop should pulsate red (highest priority)
		if (!vehicle.lastStop) {
			return "animate-pulse-red";
		}

		const now = new Date();
		// Always prioritize datetime_end over datetime_start
		const endTime = vehicle.lastStop.datetime_end
			? new Date(vehicle.lastStop.datetime_end)
			: null;
		const startTime = vehicle.lastStop.datetime_start
			? new Date(vehicle.lastStop.datetime_start)
			: null;

		// Check the time to use for comparison (prioritize endTime)
		const timeToCheck = endTime || startTime;

		// If no times available, pulsate red
		if (!timeToCheck) {
			return "animate-pulse-red";
		}

		// If time is in the past
		if (timeToCheck < now) {
			return "animate-pulse-red";
		}

		// If time is within the next 2 hours
		const twoHoursFromNow = new Date(now.getTime() + 2 * 60 * 60 * 1000);
		if (timeToCheck > now && timeToCheck <= twoHoursFromNow) {
			return "animate-pulse-yellow";
		}

		return "";
	};

	return (
		<Card className="w-full">
			<CardHeader className="p-4 sm:p-6">
				<div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
					<CardTitle className="text-lg sm:text-xl">
						Available Trucks
					</CardTitle>
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger className="mt-1 self-start sm:mt-0">
								<HelpCircle className="h-4 w-4 text-muted-foreground" />
							</TooltipTrigger>
							<TooltipContent className="max-w-sm">
								<p>Trucks are prioritized as follows:</p>
								<ul className="mt-2 list-disc pl-4">
									<li>
										Trucks with no tours have highest
										priority
									</li>
									<li>
										Trucks with past tours but no future
										ones are ranked by idle time (longest
										idle first)
									</li>
									<li>
										Trucks with future tours are shown last,
										ordered by soonest availability
									</li>
								</ul>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
				<CardDescription className="mt-1">
					Trucks with no upcoming tours, ordered by availability
					priority
				</CardDescription>
			</CardHeader>

			<CardContent className="p-3 sm:p-6">
				<style jsx global>{`
					@keyframes pulse-red {
						0%, 100% { background-color: transparent; }
						50% { background-color: rgba(239, 68, 68, 0.15); }
					}
					
					@keyframes pulse-yellow {
						0%, 100% { background-color: transparent; }
						50% { background-color: rgba(234, 179, 8, 0.15); }
					}
					
					.animate-pulse-red {
						animation: pulse-red 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
					}
					
					.animate-pulse-yellow {
						animation: pulse-yellow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
					}
				`}</style>

				{isAvailableLoading ? (
					<div className="space-y-3">
						<Skeleton className="h-20 w-full" />
						<Skeleton className="h-20 w-full" />
						<Skeleton className="h-20 w-full" />
					</div>
				) : availableVehicles.length === 0 ? (
					<div className="py-6 text-center text-muted-foreground">
						No available vehicles found
					</div>
				) : (
					<>
						<ScrollArea className="h-[300px] sm:h-[400px] md:h-[500px] pr-2 sm:pr-4">
							<div className="space-y-2 sm:space-y-3">
								{availableVehicles.map((vehicle) => (
									<div
										key={vehicle.id}
										className={cn(
											"flex flex-col sm:flex-row sm:items-center sm:justify-between rounded-lg border p-3 sm:p-4",
											getRowAnimationClass(vehicle),
										)}
									>
										<div className="flex items-center gap-3 mb-2 sm:mb-0">
											<Truck className="h-5 w-5 sm:h-6 sm:w-6 text-muted-foreground flex-shrink-0" />
											<div>
												<div className="font-medium truncate max-w-[180px] sm:max-w-none">
													{vehicle.licensePlate ||
														"N/A"}
												</div>
												{vehicle.owner && (
													<div className="text-xs sm:text-sm text-muted-foreground truncate max-w-[180px] sm:max-w-none">
														{
															vehicle.owner
																.nameLine1
														}
													</div>
												)}
											</div>
										</div>
										<div className="text-xs sm:text-sm">
											{vehicle.lastStop ? (
												<div className="sm:text-right">
													<div className="flex items-center sm:justify-end">
														<Flag className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 text-red-500 flex-shrink-0" />
														<span className="truncate max-w-[240px]">
															{utils.formatStopAddress(
																vehicle.lastStop,
															)}
														</span>
													</div>
													<div className="text-muted-foreground mt-1">
														{vehicle.lastStop
															?.datetime_end
															? format(
																	new Date(
																		vehicle
																			.lastStop
																			.datetime_end,
																	),
																	"dd.MM.yyyy, HH:mm",
																)
															: vehicle.lastStop
																		?.datetime_start
																? format(
																		new Date(
																			vehicle
																				.lastStop
																				.datetime_start,
																		),
																		"dd.MM.yyyy, HH:mm",
																	)
																: "N/A"}
													</div>
												</div>
											) : (
												<Badge status="warning">
													No previous stops
												</Badge>
											)}
										</div>
									</div>
								))}
							</div>
						</ScrollArea>

						{availableTotalPages && availableTotalPages > 1 && (
							<div className="mt-4 flex flex-wrap items-center justify-between sm:justify-end sm:space-x-2">
								<div className="text-xs sm:text-sm text-muted-foreground mb-2 sm:mb-0">
									Page {availablePage} of{" "}
									{availableTotalPages}
								</div>
								<div className="space-x-2">
									<Button
										variant="outline"
										size="sm"
										onClick={() =>
											setAvailablePage(availablePage - 1)
										}
										disabled={availablePage <= 1}
										className="h-8 w-8 p-0"
									>
										<ChevronLeft className="h-4 w-4" />
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={() =>
											setAvailablePage(availablePage + 1)
										}
										disabled={
											availablePage >=
											(availableTotalPages || 1)
										}
										className="h-8 w-8 p-0"
									>
										<ChevronRight className="h-4 w-4" />
									</Button>
								</div>
							</div>
						)}
					</>
				)}
			</CardContent>
		</Card>
	);
}
