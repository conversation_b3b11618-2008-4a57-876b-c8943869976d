"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import type {
	UpdateCounterpartyInput,
	UpdateFinancialProfileInput,
	UpdateFreightExchangeInput,
	UpdateOrderProfileInput,
} from "@repo/api/src/routes/counterparties/types";
import { updateCounterpartySchema } from "@repo/api/src/routes/counterparties/types";
import { CounterpartyGroupBadges } from "@saas/contacts/components/counterparty-group-badges";
import { useCounterpartyView } from "@saas/contacts/context/counterparty-view-context";
import type { useCounterpartyById } from "@saas/contacts/hooks/use-counterparty";
import { useCounterpartyMutations } from "@saas/contacts/hooks/use-counterparty";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { PaymentTermSelector } from "@ui/components/payment-term-selector";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { ToggleGroup, ToggleGroupItem } from "@ui/components/toggle-group";
import { VatTermSelector } from "@ui/components/vat-term-selector";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useHotkeys } from "react-hotkeys-hook";

// Type for counterparty types
type CounterpartyType = "customer" | "supplier" | "carrier" | "other";

// Use the schema from the API but extend it to include profiles
type CounterpartyFormValues = Omit<UpdateCounterpartyInput, "addressUsages"> & {
	counterpartyGroupIds?: string[];
	financialProfile?: UpdateFinancialProfileInput & {
		requiredDocuments?:
			| "no"
			| "with_invoice"
			| "with_invoice_separate_file"
			| "separate"
			| "mail";
	};
	orderProfile?: UpdateOrderProfileInput;
	freightExchangeProfile?: Omit<
		UpdateFreightExchangeInput,
		"counterpartyFreightExchangeProfileId"
	> & {
		id?: string;
		counterpartyId?: string;
		carrierNumber?: string;
		timocomNumber?: string;
		teleroute?: string;
		transEu?: string;
		otherExchanges?: string;
	};
};

type Counterparty = ReturnType<typeof useCounterpartyById>["counterparty"];

interface DetailsPanelProps {
	counterparty: Counterparty;
	onUpdate?: () => void;
}

export function DetailsPanel({ counterparty, onUpdate }: DetailsPanelProps) {
	const t = useTranslations();
	const { updateCounterparty } = useCounterpartyMutations();
	const { isEditing, stopEditing, setSubmitForm } = useCounterpartyView();
	const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] =
		useState(false);
	const [isSaving, setIsSaving] = useState(false);
	const [selectedTypes, setSelectedTypes] = useState<CounterpartyType[]>([]);

	// Create form with counterparty data as default values and validation using the API schema
	const form = useForm<CounterpartyFormValues>({
		resolver: zodResolver(updateCounterpartySchema),
		defaultValues: {
			id: counterparty?.id || "",
			// General info
			nameLine1: counterparty?.nameLine1 || "",
			nameLine2: counterparty?.nameLine2 || "",
			email: counterparty?.email || undefined,
			telephone: counterparty?.telephone || "",
			uidNumber: counterparty?.uidNumber || "",
			notes: counterparty?.notes || "",
			legalEntityType: counterparty?.legalEntityType || undefined,
			insuranceExpiryDate: counterparty?.insuranceExpiryDate
				? new Date(counterparty.insuranceExpiryDate)
				: undefined,
			// Counterparty groups
			counterpartyGroupIds:
				counterparty?.groupMemberships?.map((m) => m.groupId) || [],
			// Types
			types: counterparty?.types
				? counterparty.types.map((typeAssignment) => ({
						id: typeAssignment.id,
						type: typeAssignment.type,
					}))
				: [],
			// Financial profile
			financialProfile: counterparty?.financialProfile
				? {
						id: counterparty.financialProfile.id,
						counterpartyId: counterparty.id,
						invoiceType:
							counterparty.financialProfile.invoiceType ||
							undefined,
						currency: counterparty.financialProfile.currency || "",
						creditLimit:
							counterparty.financialProfile.creditLimit || "",
						balance: counterparty.financialProfile.balance || "",
						invoiceEmail:
							counterparty.financialProfile.invoiceEmail ||
							undefined,
						creditNotesEmail:
							counterparty.financialProfile.creditNotesEmail ||
							undefined,
						taxNotes: counterparty.financialProfile.taxNotes || "",
						requiredDocuments:
							(counterparty.financialProfile.requiredDocuments as
								| "no"
								| "with_invoice"
								| "with_invoice_separate_file"
								| "separate"
								| "mail") || undefined,
						// Only set payment term and VAT term IDs if they actually exist
						paymentTermId:
							counterparty.financialProfile.paymentTerm?.id ||
							undefined,
						vatTermId:
							counterparty.financialProfile.vatTerm?.id ||
							undefined,
					}
				: undefined,
			// Order profile
			orderProfile: counterparty?.orderProfile
				? {
						id: counterparty.orderProfile.id,
						counterpartyId: counterparty.id,
						loadingMaterialExchange:
							counterparty.orderProfile.loadingMaterialExchange ||
							false,
						loadingMaterialInfo:
							counterparty.orderProfile.loadingMaterialInfo || "",
						orderWarningText:
							counterparty.orderProfile.orderWarningText || "",
						accountingOrderText:
							counterparty.orderProfile.accountingOrderText || "",
						neutralityText:
							counterparty.orderProfile.neutralityText || "",
					}
				: undefined,
			// Freight exchange profile
			freightExchangeProfile: counterparty?.freightExchangeProfile
				? {
						id: counterparty.freightExchangeProfile.id,
						counterpartyId: counterparty.id,
						carrierNumber:
							counterparty.freightExchangeProfile.carrierNumber ||
							"",
						timocomNumber:
							counterparty.freightExchangeProfile.timocomNumber ||
							"",
						teleroute:
							counterparty.freightExchangeProfile.teleroute || "",
						transEu:
							counterparty.freightExchangeProfile.transEu || "",
						otherExchanges:
							counterparty.freightExchangeProfile
								.otherExchanges || "",
					}
				: undefined,
		},
	});

	// Add a reference to the first input field
	const firstInputRef = useRef<HTMLInputElement>(null);

	// When entering edit mode, focus on the first input field
	useEffect(() => {
		if (isEditing && firstInputRef.current) {
			// Delay focus slightly to ensure the ref is properly set after render
			setTimeout(() => {
				firstInputRef.current?.focus();
			}, 100);
		}
	}, [isEditing]);

	// Add keyboard shortcuts for edit mode
	useHotkeys(
		"esc",
		() => {
			if (isEditing) {
				handleCancelEdit();
			}
		},
		{
			enabled: isEditing,
			preventDefault: true,
		},
	);

	useHotkeys(
		"ctrl+enter",
		() => {
			if (isEditing) {
				void form.handleSubmit(handleSave)();
			}
		},
		{
			enabled: isEditing,
			preventDefault: true,
		},
	);

	// Update form when counterparty changes
	useEffect(() => {
		if (counterparty) {
			form.reset({
				id: counterparty.id,
				nameLine1: counterparty.nameLine1 || "",
				nameLine2: counterparty.nameLine2 || "",
				email: counterparty.email || undefined,
				telephone: counterparty.telephone || "",
				uidNumber: counterparty.uidNumber || "",
				notes: counterparty.notes || "",
				legalEntityType: counterparty.legalEntityType || undefined,
				insuranceExpiryDate: counterparty.insuranceExpiryDate
					? new Date(counterparty.insuranceExpiryDate)
					: undefined,
				// Counterparty groups
				counterpartyGroupIds:
					counterparty?.groupMemberships?.map((m) => m.groupId) || [],
				// Types
				types: counterparty.types
					? counterparty.types.map((typeAssignment) => ({
							id: typeAssignment.id,
							type: typeAssignment.type,
						}))
					: [],
				financialProfile: counterparty.financialProfile
					? {
							id: counterparty.financialProfile.id,
							counterpartyId: counterparty.id,
							invoiceType:
								counterparty.financialProfile.invoiceType ||
								undefined,
							currency:
								counterparty.financialProfile.currency || "",
							creditLimit:
								counterparty.financialProfile.creditLimit || "",
							balance:
								counterparty.financialProfile.balance || "",
							invoiceEmail:
								counterparty.financialProfile.invoiceEmail ||
								undefined,
							creditNotesEmail:
								counterparty.financialProfile
									.creditNotesEmail || undefined,
							taxNotes:
								counterparty.financialProfile.taxNotes || "",
							requiredDocuments:
								(counterparty.financialProfile
									.requiredDocuments as
									| "no"
									| "with_invoice"
									| "with_invoice_separate_file"
									| "separate"
									| "mail") || undefined,
							paymentTermId:
								counterparty.financialProfile.paymentTerm?.id ||
								undefined,
							vatTermId:
								counterparty.financialProfile.vatTerm?.id ||
								undefined,
						}
					: undefined,
				orderProfile: counterparty.orderProfile
					? {
							id: counterparty.orderProfile.id,
							counterpartyId: counterparty.id,
							loadingMaterialExchange:
								counterparty.orderProfile
									.loadingMaterialExchange || false,
							loadingMaterialInfo:
								counterparty.orderProfile.loadingMaterialInfo ||
								"",
							orderWarningText:
								counterparty.orderProfile.orderWarningText ||
								"",
							accountingOrderText:
								counterparty.orderProfile.accountingOrderText ||
								"",
							neutralityText:
								counterparty.orderProfile.neutralityText || "",
						}
					: undefined,
				freightExchangeProfile: counterparty.freightExchangeProfile
					? {
							id: counterparty.freightExchangeProfile.id,
							counterpartyId: counterparty.id,
							carrierNumber:
								counterparty.freightExchangeProfile
									.carrierNumber || "",
							timocomNumber:
								counterparty.freightExchangeProfile
									.timocomNumber || "",
							teleroute:
								counterparty.freightExchangeProfile.teleroute ||
								"",
							transEu:
								counterparty.freightExchangeProfile.transEu ||
								"",
							otherExchanges:
								counterparty.freightExchangeProfile
									.otherExchanges || "",
						}
					: undefined,
			});
		}
	}, [counterparty, form]);

	// Initialize selected types when counterparty changes or edit mode changes
	useEffect(() => {
		if (counterparty?.types?.length) {
			const types = counterparty.types.map(
				(t) => t.type as CounterpartyType,
			);
			setSelectedTypes(types);
		} else {
			setSelectedTypes([]);
		}
	}, [counterparty]);

	// Update form when types are changed via toggle group
	useEffect(() => {
		if (isEditing) {
			const typeObjects = selectedTypes.map((type) => {
				// Find existing type if it exists to preserve ID
				const existingType = counterparty?.types?.find(
					(t) => t.type === type,
				);
				return existingType || { type };
			});
			form.setValue("types", typeObjects, { shouldDirty: true });
		}
	}, [selectedTypes, isEditing, form, counterparty?.types]);

	// Update selected types when form values change (for other form updates)
	useEffect(() => {
		const formTypes = form.watch("types");
		if (
			formTypes?.length &&
			JSON.stringify(formTypes.map((t) => t.type).sort()) !==
				JSON.stringify(selectedTypes.sort())
		) {
			setSelectedTypes(formTypes.map((t) => t.type as CounterpartyType));
		}
	}, [form.watch("types")]);

	// Handle save
	const handleSave = async (formData: CounterpartyFormValues) => {
		if (!counterparty) {
			return;
		}

		try {
			setIsSaving(true);

			// Prepare data for submission - we already exclude addressUsages by omitting in the type
			const updateData = {
				...formData,
				id: counterparty.id,
			};

			await updateCounterparty(updateData);
			stopEditing();
			// Trigger the parent component to refresh data if needed
			onUpdate?.();
		} catch (error) {
			console.error("Error updating counterparty:", error);
		} finally {
			setIsSaving(false);
		}
	};

	// Create a stable submit function with useCallback
	const submitFormCallback = useCallback(async (): Promise<boolean> => {
		try {
			// Validate form
			const isValid = await form.trigger();
			if (!isValid || !counterparty) {
				return false;
			}

			// Get form data
			const formData = form.getValues();

			// Call handleSave directly - this avoids setting state in this function itself
			await handleSave(formData);
			return true;
		} catch (error) {
			console.error("Form submission error:", error);
			return false;
		}
	}, [form, counterparty, handleSave]);

	// Register the submit function with the context only once
	useEffect(() => {
		setSubmitForm(submitFormCallback);
		return () => setSubmitForm(null);
	}, [submitFormCallback, setSubmitForm]);

	// Format currency with proper handling for string or number
	const formatCurrency = (value: string | number | null | undefined) => {
		if (value === null || value === undefined) {
			return "-";
		}
		const numValue =
			typeof value === "string" ? Number.parseFloat(value) : value;
		return Number.isNaN(numValue) ? "-" : `€${numValue.toFixed(2)}`;
	};

	// Format date with proper handling
	const formatDate = (date: string | Date | null | undefined) => {
		if (!date) {
			return "-";
		}
		return new Date(date).toLocaleDateString();
	};

	// Check for form changes and show confirmation dialog if needed
	const handleCancelEdit = () => {
		const isDirty = form.formState.isDirty;

		if (isDirty) {
			setShowUnsavedChangesDialog(true);
		} else {
			// No changes, just exit edit mode
			stopEditing();
		}
	};

	// Reset form and exit edit mode
	const discardChanges = () => {
		form.reset({
			id: counterparty?.id || "",
			nameLine1: counterparty?.nameLine1 || "",
			nameLine2: counterparty?.nameLine2 || "",
			email: counterparty?.email || undefined,
			telephone: counterparty?.telephone || "",
			uidNumber: counterparty?.uidNumber || "",
			notes: counterparty?.notes || "",
			legalEntityType: counterparty?.legalEntityType || undefined,
			insuranceExpiryDate: counterparty?.insuranceExpiryDate
				? new Date(counterparty.insuranceExpiryDate)
				: undefined,
			// Counterparty groups
			counterpartyGroupIds:
				counterparty?.groupMemberships?.map((m) => m.groupId) || [],
			// Types
			types: counterparty?.types
				? counterparty.types.map((typeAssignment) => ({
						id: typeAssignment.id,
						type: typeAssignment.type,
					}))
				: [],
			financialProfile: counterparty?.financialProfile
				? {
						id: counterparty.financialProfile.id,
						counterpartyId: counterparty.id,
						invoiceType:
							counterparty.financialProfile.invoiceType ||
							undefined,
						currency: counterparty.financialProfile.currency || "",
						creditLimit:
							counterparty.financialProfile.creditLimit || "",
						balance: counterparty.financialProfile.balance || "",
						invoiceEmail:
							counterparty.financialProfile.invoiceEmail ||
							undefined,
						creditNotesEmail:
							counterparty.financialProfile.creditNotesEmail ||
							undefined,
						taxNotes: counterparty.financialProfile.taxNotes || "",
						requiredDocuments:
							(counterparty.financialProfile.requiredDocuments as
								| "no"
								| "with_invoice"
								| "with_invoice_separate_file"
								| "separate"
								| "mail") || undefined,
						paymentTermId:
							counterparty.financialProfile.paymentTerm?.id ||
							undefined,
						vatTermId:
							counterparty.financialProfile.vatTerm?.id ||
							undefined,
					}
				: undefined,
			orderProfile: counterparty?.orderProfile
				? {
						id: counterparty.orderProfile.id,
						counterpartyId: counterparty.id,
						loadingMaterialExchange:
							counterparty.orderProfile.loadingMaterialExchange ||
							false,
						loadingMaterialInfo:
							counterparty.orderProfile.loadingMaterialInfo || "",
						orderWarningText:
							counterparty.orderProfile.orderWarningText || "",
						accountingOrderText:
							counterparty.orderProfile.accountingOrderText || "",
						neutralityText:
							counterparty.orderProfile.neutralityText || "",
					}
				: undefined,
			freightExchangeProfile: counterparty?.freightExchangeProfile
				? {
						id: counterparty.freightExchangeProfile.id,
						counterpartyId: counterparty.id,
						carrierNumber:
							counterparty.freightExchangeProfile.carrierNumber ||
							"",
						timocomNumber:
							counterparty.freightExchangeProfile.timocomNumber ||
							"",
						teleroute:
							counterparty.freightExchangeProfile.teleroute || "",
						transEu:
							counterparty.freightExchangeProfile.transEu || "",
						otherExchanges:
							counterparty.freightExchangeProfile
								.otherExchanges || "",
					}
				: undefined,
		});
		stopEditing();
		setShowUnsavedChangesDialog(false);
	};

	return (
		<div className="space-y-6">
			{/* Unsaved Changes Dialog */}
			<AlertDialog
				open={showUnsavedChangesDialog}
				onOpenChange={setShowUnsavedChangesDialog}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Unsaved Changes</AlertDialogTitle>
						<AlertDialogDescription>
							You have unsaved changes. Are you sure you want to
							exit edit mode? Your changes will be lost.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel
							onClick={() => setShowUnsavedChangesDialog(false)}
						>
							Continue Editing
						</AlertDialogCancel>
						<AlertDialogAction onClick={discardChanges}>
							Discard Changes
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			<Form {...form}>
				<form onSubmit={form.handleSubmit(handleSave)}>
					{/* General Information Card */}
					<Card>
						<CardHeader>
							<CardTitle>General Information</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-6">
								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Company Name
										</h4>
										{isEditing ? (
											<div className="space-y-2">
												<Input
													placeholder="Company name"
													{...form.register(
														"nameLine1",
														{
															// Combine the RHF ref with our focus ref
															shouldUnregister: false,
															value:
																counterparty?.nameLine1 ||
																"",
															onChange: (e) => {
																form.setValue(
																	"nameLine1",
																	e.target
																		.value,
																);
															},
														},
													)}
													ref={(el) => {
														// This is the key fix: properly handle multiple refs
														const { ref } =
															form.register(
																"nameLine1",
															);
														// Handle RHF's ref
														if (
															typeof ref ===
															"function"
														) {
															ref(el);
														}
														// Handle our custom ref
														firstInputRef.current =
															el;
													}}
													defaultValue={
														counterparty?.nameLine1 ||
														""
													}
												/>
												<Input
													placeholder="Company name line 2 (optional)"
													{...form.register(
														"nameLine2",
													)}
													defaultValue={
														counterparty?.nameLine2 ||
														""
													}
												/>
											</div>
										) : (
											<>
												<p>{counterparty?.nameLine1}</p>
												{counterparty?.nameLine2 && (
													<p>
														{
															counterparty?.nameLine2
														}
													</p>
												)}
											</>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Contact
										</h4>
										{isEditing ? (
											<div className="space-y-2">
												<FormField
													control={form.control}
													name="email"
													render={({ field }) => (
														<FormItem>
															<FormControl>
																<Input
																	placeholder="Email"
																	type="email"
																	{...field}
																	value={
																		field.value ||
																		""
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<Input
													placeholder="Telephone"
													{...form.register(
														"telephone",
													)}
												/>
											</div>
										) : (
											<>
												<p>
													{counterparty?.email || "-"}
												</p>
												<p>
													{counterparty?.telephone ||
														"-"}
												</p>
											</>
										)}
									</div>
								</div>

								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Customer Number
										</h4>
										<p>
											{counterparty?.customerNumber ||
												"-"}
										</p>
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											VAT ID
										</h4>
										{isEditing ? (
											<Input
												placeholder="VAT ID"
												{...form.register("uidNumber")}
											/>
										) : (
											<p>
												{counterparty?.uidNumber || "-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Insurance Expiry
										</h4>
										{isEditing ? (
											<InputWithDate
												inputProps={{
													placeholder:
														"Insurance expiry date",
													onChange: (date) =>
														form.setValue(
															"insuranceExpiryDate",
															date,
														),
												}}
												onDateChange={(date) => {
													if (date instanceof Date) {
														form.setValue(
															"insuranceExpiryDate",
															date,
														);
													}
												}}
											/>
										) : (
											<p>
												{formatDate(
													counterparty?.insuranceExpiryDate,
												)}
											</p>
										)}
									</div>
								</div>

								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Entity Type
										</h4>
										{isEditing ? (
											<Select
												onValueChange={(value) => {
													const legalEntityType =
														value as
															| "individual"
															| "company"
															| "other"
															| undefined;
													form.setValue(
														"legalEntityType",
														legalEntityType,
													);
												}}
												defaultValue={form.watch(
													"legalEntityType",
												)}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select entity type" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="individual">
														Individual
													</SelectItem>
													<SelectItem value="company">
														Company
													</SelectItem>
													<SelectItem value="partnership">
														Partnership
													</SelectItem>
													<SelectItem value="non-profit">
														Non-profit
													</SelectItem>
													<SelectItem value="government">
														Government
													</SelectItem>
												</SelectContent>
											</Select>
										) : (
											<p className="capitalize">
												{counterparty?.legalEntityType ||
													"-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Contact Groups
										</h4>
										{isEditing ? (
											<div className="mt-1">
												<CounterpartyGroupBadges
													value={
														form.watch(
															"counterpartyGroupIds",
														) || []
													}
													onChange={(
														value: string[],
													) =>
														form.setValue(
															"counterpartyGroupIds",
															value,
														)
													}
													placeholder="No groups assigned"
												/>
											</div>
										) : (
											<div className="mt-1">
												{counterparty?.groupMemberships
													?.length ? (
													<div className="flex flex-wrap gap-2">
														{counterparty.groupMemberships.map(
															(membership) => (
																<Badge
																	key={
																		membership.id
																	}
																	status="info"
																>
																	{
																		membership
																			.group
																			.name
																	}
																</Badge>
															),
														)}
													</div>
												) : (
													<p className="text-sm text-muted-foreground">
														No groups assigned
													</p>
												)}
											</div>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Contact Types
										</h4>
										{isEditing ? (
											<div className="mt-1">
												<ToggleGroup
													type="multiple"
													variant="outline"
													className="flex flex-col gap-2 sm:flex-row"
													value={selectedTypes}
													onValueChange={(value) =>
														setSelectedTypes(
															value as CounterpartyType[],
														)
													}
												>
													<ToggleGroupItem
														value="customer"
														className="justify-center"
													>
														Customer
													</ToggleGroupItem>
													<ToggleGroupItem
														value="supplier"
														className="justify-center"
													>
														Supplier
													</ToggleGroupItem>
													<ToggleGroupItem
														value="carrier"
														className="justify-center"
													>
														Carrier
													</ToggleGroupItem>
													<ToggleGroupItem
														value="other"
														className="justify-center"
													>
														Other
													</ToggleGroupItem>
												</ToggleGroup>
											</div>
										) : counterparty?.types?.length ? (
											<ul>
												{counterparty.types.map(
													(typeAssignment) => (
														<li
															key={
																typeAssignment.id
															}
															className="capitalize"
														>
															{
																typeAssignment.type
															}
														</li>
													),
												)}
											</ul>
										) : (
											<p>-</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Notes
										</h4>
										{isEditing ? (
											<Textarea
												placeholder="Notes"
												rows={3}
												{...form.register("notes")}
											/>
										) : (
											<p className="whitespace-pre-wrap">
												{counterparty?.notes || "-"}
											</p>
										)}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Financial Details Card */}
					<Card className="mt-6">
						<CardHeader>
							<CardTitle>Financial Details</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-3 gap-6">
								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Invoice Type
										</h4>
										{isEditing ? (
											<Input
												placeholder="Invoice type"
												{...form.register(
													"financialProfile.invoiceType",
												)}
											/>
										) : (
											<p>
												{counterparty?.financialProfile
													?.invoiceType || "-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Payment Terms
										</h4>
										{isEditing ? (
											<div className="mt-1">
												<PaymentTermSelector
													name="paymentTerm"
													value={
														form.watch(
															"financialProfile.paymentTermId",
														) || undefined
													}
													onChange={(value) =>
														form.setValue(
															"financialProfile.paymentTermId",
															value,
														)
													}
													disableAutoDefault={true}
												/>
											</div>
										) : (
											<>
												<p>
													{counterparty
														?.financialProfile
														?.paymentTerm?.name ||
														"-"}
												</p>
												{counterparty?.financialProfile
													?.paymentTerm
													?.daysToPayment && (
													<p className="text-sm text-muted-foreground">
														{
															counterparty
																.financialProfile
																.paymentTerm
																.daysToPayment
														}{" "}
														days
													</p>
												)}
											</>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											VAT Term
										</h4>
										{isEditing ? (
											<div className="mt-1">
												<VatTermSelector
													name="vatTerm"
													value={
														form.watch(
															"financialProfile.vatTermId",
														) || undefined
													}
													onChange={(value) =>
														form.setValue(
															"financialProfile.vatTermId",
															value,
														)
													}
													disableAutoDefault={true}
												/>
											</div>
										) : (
											<>
												<p>
													{counterparty
														?.financialProfile
														?.vatTerm?.name || "-"}
												</p>
												{counterparty?.financialProfile
													?.vatTerm?.rate && (
													<p className="text-sm text-muted-foreground">
														{
															counterparty
																.financialProfile
																.vatTerm.rate
														}
														%
													</p>
												)}
											</>
										)}
									</div>
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Tax Notes
										</h4>
										{isEditing ? (
											<Input
												placeholder="Tax notes"
												{...form.register(
													"financialProfile.taxNotes",
												)}
											/>
										) : (
											<p>
												{counterparty?.financialProfile
													?.taxNotes || "-"}
											</p>
										)}
									</div>
								</div>

								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Currency
										</h4>
										{isEditing ? (
											<Input
												placeholder="Currency"
												{...form.register(
													"financialProfile.currency",
												)}
											/>
										) : (
											<p>
												{counterparty?.financialProfile
													?.currency || "-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Credit Limit
										</h4>
										{isEditing ? (
											<Input
												placeholder="Credit limit"
												type="number"
												step="0.01"
												{...form.register(
													"financialProfile.creditLimit",
												)}
											/>
										) : (
											<p>
												{formatCurrency(
													counterparty
														?.financialProfile
														?.creditLimit,
												)}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Balance
										</h4>
										{isEditing ? (
											<Input
												placeholder="Balance"
												type="number"
												step="0.01"
												{...form.register(
													"financialProfile.balance",
												)}
											/>
										) : (
											<p>
												{formatCurrency(
													counterparty
														?.financialProfile
														?.balance,
												)}
											</p>
										)}
									</div>
								</div>

								<div className="space-y-4">
									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Invoice Email
										</h4>
										{isEditing ? (
											<FormField
												control={form.control}
												name="financialProfile.invoiceEmail"
												render={({ field }) => (
													<FormItem>
														<FormControl>
															<Input
																placeholder="Invoice email"
																type="email"
																{...field}
																value={
																	field.value ||
																	""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										) : (
											<p>
												{counterparty?.financialProfile
													?.invoiceEmail || "-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Credit Notes Email
										</h4>
										{isEditing ? (
											<FormField
												control={form.control}
												name="financialProfile.creditNotesEmail"
												render={({ field }) => (
													<FormItem>
														<FormControl>
															<Input
																placeholder="Credit notes email"
																type="email"
																{...field}
																value={
																	field.value ||
																	""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										) : (
											<p>
												{counterparty?.financialProfile
													?.creditNotesEmail || "-"}
											</p>
										)}
									</div>

									<div>
										<h4 className="text-sm font-medium text-muted-foreground">
											Required Documents
										</h4>
										{isEditing ? (
											<Select
												onValueChange={(
													value:
														| "no"
														| "with_invoice"
														| "with_invoice_separate_file"
														| "separate"
														| "mail",
												) =>
													form.setValue(
														"financialProfile.requiredDocuments",
														value,
													)
												}
												defaultValue={form.watch(
													"financialProfile.requiredDocuments",
												)}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select document requirements" />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="no">
														No Documents Required
													</SelectItem>
													<SelectItem value="with_invoice">
														With Invoice
													</SelectItem>
													<SelectItem value="with_invoice_separate_file">
														With Invoice (Separate
														File)
													</SelectItem>
													<SelectItem value="separate">
														Separate
													</SelectItem>
													<SelectItem value="mail">
														Mail
													</SelectItem>
												</SelectContent>
											</Select>
										) : (
											<p className="capitalize">
												{counterparty?.financialProfile
													?.requiredDocuments
													? counterparty.financialProfile.requiredDocuments.replace(
															/_/g,
															" ",
														)
													: "-"}
											</p>
										)}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>

					{counterparty?.orderProfile && (
						<Card className="mt-6">
							<CardHeader>
								<CardTitle>Order Information</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-3 gap-6">
									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Loading Material Exchange
											</h4>
											{isEditing ? (
												<div className="flex items-center h-10">
													<Switch
														checked={form.watch(
															"orderProfile.loadingMaterialExchange",
														)}
														onCheckedChange={(
															value,
														) =>
															form.setValue(
																"orderProfile.loadingMaterialExchange",
																value,
															)
														}
													/>
												</div>
											) : (
												<p>
													{counterparty.orderProfile
														.loadingMaterialExchange
														? "Yes"
														: "No"}
												</p>
											)}
										</div>

										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Loading Material Info
											</h4>
											{isEditing ? (
												<Input
													placeholder="Loading material info"
													{...form.register(
														"orderProfile.loadingMaterialInfo",
													)}
												/>
											) : (
												<p>
													{counterparty.orderProfile
														.loadingMaterialInfo ||
														"-"}
												</p>
											)}
										</div>
									</div>

									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Order Warning Text
											</h4>
											{isEditing ? (
												<Input
													placeholder="Order warning text"
													{...form.register(
														"orderProfile.orderWarningText",
													)}
												/>
											) : (
												<p>
													{counterparty.orderProfile
														.orderWarningText ||
														"-"}
												</p>
											)}
										</div>
									</div>

									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Accounting Order Text
											</h4>
											{isEditing ? (
												<Input
													placeholder="Accounting order text"
													{...form.register(
														"orderProfile.accountingOrderText",
													)}
												/>
											) : (
												<p>
													{counterparty.orderProfile
														.accountingOrderText ||
														"-"}
												</p>
											)}
										</div>

										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Neutrality Text
											</h4>
											{isEditing ? (
												<Input
													placeholder="Neutrality text"
													{...form.register(
														"orderProfile.neutralityText",
													)}
												/>
											) : (
												<p>
													{counterparty.orderProfile
														.neutralityText || "-"}
												</p>
											)}
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					)}

					{counterparty?.freightExchangeProfile && (
						<Card className="mt-6">
							<CardHeader>
								<CardTitle>Freight Exchange</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="grid grid-cols-3 gap-6">
									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Carrier Number
											</h4>
											<p>
												{counterparty
													.freightExchangeProfile
													.carrierNumber || "-"}
											</p>
										</div>

										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												TimoCom Number
											</h4>
											{isEditing ? (
												<Input
													placeholder="TimoCom number"
													{...form.register(
														"freightExchangeProfile.timocomNumber",
													)}
												/>
											) : (
												<p>
													{counterparty
														.freightExchangeProfile
														.timocomNumber || "-"}
												</p>
											)}
										</div>
									</div>

									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Teleroute
											</h4>
											{isEditing ? (
												<Input
													placeholder="Teleroute"
													{...form.register(
														"freightExchangeProfile.teleroute",
													)}
												/>
											) : (
												<p>
													{counterparty
														.freightExchangeProfile
														.teleroute || "-"}
												</p>
											)}
										</div>

										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												TransEU
											</h4>
											{isEditing ? (
												<Input
													placeholder="TransEU"
													{...form.register(
														"freightExchangeProfile.transEu",
													)}
												/>
											) : (
												<p>
													{counterparty
														.freightExchangeProfile
														.transEu || "-"}
												</p>
											)}
										</div>
									</div>

									<div className="space-y-4">
										<div>
											<h4 className="text-sm font-medium text-muted-foreground">
												Other Exchanges
											</h4>
											{isEditing ? (
												<Input
													placeholder="Other exchanges"
													{...form.register(
														"freightExchangeProfile.otherExchanges",
													)}
												/>
											) : (
												<p>
													{counterparty
														.freightExchangeProfile
														.otherExchanges || "-"}
												</p>
											)}
										</div>
									</div>
								</div>
							</CardContent>
						</Card>
					)}
				</form>
			</Form>
		</div>
	);
}
