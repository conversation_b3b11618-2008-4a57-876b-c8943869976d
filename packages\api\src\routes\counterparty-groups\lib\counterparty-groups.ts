import type { Prisma } from "@prisma/client";
import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type {
	AddCounterpartyToGroupInput,
	CreateCounterpartyGroupInput,
	GetGroupMembersOptions,
	ListCounterpartyGroupsOptions,
	RemoveCounterpartyFromGroupInput,
	UpdateCounterpartyGroupInput,
} from "../types";

export async function listCounterpartyGroups(
	options: ListCounterpartyGroupsOptions,
) {
	const take = options.limit ?? 10;
	const page = options.page ?? 1;
	const skip = (page - 1) * take;

	try {
		const where: Prisma.CounterpartyGroupWhereInput = {
			organizationId: options.organizationId,
			deletedAt: null,
			...(options.search
				? {
						OR: [
							{
								name: {
									contains: options.search,
									mode: "insensitive",
								},
							},
							{
								description: {
									contains: options.search,
									mode: "insensitive",
								},
							},
						],
					}
				: {}),
		};

		const orderBy: Prisma.CounterpartyGroupOrderByWithRelationInput =
			options.sortBy
				? { [options.sortBy]: options.sortDirection ?? "desc" }
				: { createdAt: "desc" };

		const [total, groups] = await Promise.all([
			db.counterpartyGroup.count({ where }),
			db.counterpartyGroup.findMany({
				where,
				take,
				skip,
				orderBy,
				include: {
					members: options.includeMembers
						? {
								include: {
									counterparty: {
										select: {
											id: true,
											nameLine1: true,
											nameLine2: true,
											customerNumber: true,
										},
									},
								},
							}
						: false,
				},
			}),
		]);

		return {
			items: groups,
			total,
			page,
			totalPages: Math.ceil(total / take),
		};
	} catch (error) {
		throw new HTTPException(500, {
			message: "Failed to fetch counterparty groups",
		});
	}
}

export async function getCounterpartyGroupById(
	groupId: string,
	organizationId: string,
) {
	try {
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id: groupId,
				organizationId,
				deletedAt: null,
			},
			include: {
				members: {
					include: {
						counterparty: {
							select: {
								id: true,
								nameLine1: true,
								nameLine2: true,
								customerNumber: true,
							},
						},
					},
				},
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message: "Counterparty group not found",
			});
		}

		return group;
	} catch (error) {
		console.error("Failed to fetch counterparty group:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch counterparty group",
		});
	}
}

export async function createCounterpartyGroup(
	data: CreateCounterpartyGroupInput,
) {
	try {
		// Check if group name already exists for this organization
		const existingGroup = await db.counterpartyGroup.findFirst({
			where: {
				organizationId: data.organizationId,
				name: data.name,
				deletedAt: null,
			},
		});

		if (existingGroup) {
			throw new HTTPException(409, {
				message: "A counterparty group with this name already exists",
			});
		}

		const group = await db.counterpartyGroup.create({
			data: {
				name: data.name,
				description: data.description,
				organizationId: data.organizationId,
			},
			include: {
				members: {
					include: {
						counterparty: {
							select: {
								id: true,
								nameLine1: true,
								nameLine2: true,
								customerNumber: true,
							},
						},
					},
				},
			},
		});

		return group;
	} catch (error) {
		console.error("Failed to create counterparty group:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to create counterparty group",
		});
	}
}

export async function updateCounterpartyGroup(
	data: UpdateCounterpartyGroupInput,
) {
	try {
		const { id, ...updateData } = data;

		// Check if group exists
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id,
				organizationId: updateData.organizationId,
				deletedAt: null,
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message: "Counterparty group not found",
			});
		}

		// Check if new name conflicts with existing group (if name is being updated)
		if (updateData.name && updateData.name !== group.name) {
			const existingGroup = await db.counterpartyGroup.findFirst({
				where: {
					organizationId: updateData.organizationId,
					name: updateData.name,
					deletedAt: null,
					id: { not: id }, // Exclude current group
				},
			});

			if (existingGroup) {
				throw new HTTPException(409, {
					message:
						"A counterparty group with this name already exists",
				});
			}
		}

		// Update the group
		const updated = await db.counterpartyGroup.update({
			where: { id },
			data: {
				...(updateData.name && { name: updateData.name }),
				...(updateData.description !== undefined && {
					description: updateData.description,
				}),
			},
			include: {
				members: {
					include: {
						counterparty: {
							select: {
								id: true,
								nameLine1: true,
								nameLine2: true,
								customerNumber: true,
							},
						},
					},
				},
			},
		});

		return updated;
	} catch (error) {
		console.error("Failed to update counterparty group:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to update counterparty group",
		});
	}
}

export async function deleteCounterpartyGroup(
	id: string,
	organizationId: string,
) {
	try {
		// Check if group exists
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id,
				organizationId,
				deletedAt: null,
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message: "Counterparty group not found",
			});
		}

		// Soft delete the group (this will cascade delete members due to onDelete: Cascade)
		await db.counterpartyGroup.update({
			where: { id },
			data: { deletedAt: new Date() },
		});

		return { success: true };
	} catch (error) {
		console.error("Failed to delete counterparty group:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to delete counterparty group",
		});
	}
}

export async function addCounterpartyToGroup(
	data: AddCounterpartyToGroupInput,
	organizationId: string,
) {
	try {
		// Verify group exists and belongs to organization
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id: data.groupId,
				organizationId,
				deletedAt: null,
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message:
					"Counterparty group not found or does not belong to this organization",
			});
		}

		// Verify counterparty exists and belongs to organization
		const counterparty = await db.counterparty.findFirst({
			where: {
				id: data.counterpartyId,
				organizationId,
				deletedAt: null,
			},
		});

		if (!counterparty) {
			throw new HTTPException(404, {
				message:
					"Counterparty not found or does not belong to this organization",
			});
		}

		// Check if counterparty is already in the group
		const existingMembership = await db.counterpartyGroupMember.findFirst({
			where: {
				groupId: data.groupId,
				counterpartyId: data.counterpartyId,
			},
		});

		if (existingMembership) {
			throw new HTTPException(409, {
				message: "Counterparty is already a member of this group",
			});
		}

		// Add counterparty to group
		const membership = await db.counterpartyGroupMember.create({
			data: {
				groupId: data.groupId,
				counterpartyId: data.counterpartyId,
			},
			include: {
				counterparty: {
					select: {
						id: true,
						nameLine1: true,
						nameLine2: true,
						customerNumber: true,
					},
				},
			},
		});

		return membership;
	} catch (error) {
		console.error("Failed to add counterparty to group:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to add counterparty to group",
		});
	}
}

export async function removeCounterpartyFromGroup(
	data: RemoveCounterpartyFromGroupInput,
	organizationId: string,
) {
	try {
		// Verify group exists and belongs to organization
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id: data.groupId,
				organizationId,
				deletedAt: null,
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message:
					"Counterparty group not found or does not belong to this organization",
			});
		}

		// Find existing membership
		const membership = await db.counterpartyGroupMember.findFirst({
			where: {
				groupId: data.groupId,
				counterpartyId: data.counterpartyId,
			},
		});

		if (!membership) {
			throw new HTTPException(404, {
				message: "Counterparty is not a member of this group",
			});
		}

		// Remove counterparty from group
		await db.counterpartyGroupMember.delete({
			where: { id: membership.id },
		});

		return { success: true };
	} catch (error) {
		console.error("Failed to remove counterparty from group:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to remove counterparty from group",
		});
	}
}

export async function getGroupMembers(options: GetGroupMembersOptions) {
	const take = options.limit ?? 10;
	const page = options.page ?? 1;
	const skip = (page - 1) * take;

	try {
		// Verify group exists and belongs to organization
		const group = await db.counterpartyGroup.findFirst({
			where: {
				id: options.groupId,
				organizationId: options.organizationId,
				deletedAt: null,
			},
		});

		if (!group) {
			throw new HTTPException(404, {
				message:
					"Counterparty group not found or does not belong to this organization",
			});
		}

		const where: Prisma.CounterpartyGroupMemberWhereInput = {
			groupId: options.groupId,
			...(options.search
				? {
						counterparty: {
							OR: [
								{
									nameLine1: {
										contains: options.search,
										mode: "insensitive",
									},
								},
								{
									nameLine2: {
										contains: options.search,
										mode: "insensitive",
									},
								},
								{
									customerNumber: {
										contains: options.search,
										mode: "insensitive",
									},
								},
							],
						},
					}
				: {}),
		};

		const [total, members] = await Promise.all([
			db.counterpartyGroupMember.count({ where }),
			db.counterpartyGroupMember.findMany({
				where,
				take,
				skip,
				orderBy: {
					counterparty: {
						nameLine1: "asc",
					},
				},
				include: {
					counterparty: {
						select: {
							id: true,
							nameLine1: true,
							nameLine2: true,
							customerNumber: true,
						},
					},
				},
			}),
		]);

		return {
			items: members,
			total,
			page,
			totalPages: Math.ceil(total / take),
		};
	} catch (error) {
		console.error("Failed to fetch group members:", error);
		if (error instanceof HTTPException) {
			throw error;
		}
		throw new HTTPException(500, {
			message: "Failed to fetch group members",
		});
	}
}
