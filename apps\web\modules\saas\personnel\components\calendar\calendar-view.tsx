"use client";

import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import {
	type ShortcutDefinition,
	getShortcutsByScope,
} from "@saas/shared/components/shortcuts/registry/shortcut-registry";
import { Button } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format, isSameDay, isSameMonth, isToday } from "date-fns";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { useAbsenceUI } from "../../context/absence-ui-context";
import { usePersonnelCalendar } from "../../hooks/use-personnel-calendar";

// Helper function to get color for an absence type
const getAbsenceTypeColor = (
	type: AbsenceType,
): { bg: string; text: string; emoji: string } => {
	switch (type) {
		case "HOLIDAY":
			return {
				bg: "bg-sky-100 dark:bg-sky-950/40",
				text: "text-sky-800 dark:text-sky-300",
				emoji: "🏖️",
			};
		case "SICK_LEAVE":
			return {
				bg: "bg-red-100 dark:bg-red-950/40",
				text: "text-red-800 dark:text-red-300",
				emoji: "💊",
			};
		case "PERSONAL_LEAVE":
			return {
				bg: "bg-indigo-100 dark:bg-indigo-950/40",
				text: "text-indigo-800 dark:text-indigo-300",
				emoji: "📅",
			};
		case "MATERNITY_LEAVE":
			return {
				bg: "bg-fuchsia-100 dark:bg-fuchsia-950/40",
				text: "text-fuchsia-800 dark:text-fuchsia-300",
				emoji: "👶",
			};
		case "PATERNITY_LEAVE":
			return {
				bg: "bg-violet-100 dark:bg-violet-950/40",
				text: "text-violet-800 dark:text-violet-300",
				emoji: "👨‍👩‍👦",
			};
		case "PARENTAL_LEAVE":
			return {
				bg: "bg-purple-100 dark:bg-purple-950/40",
				text: "text-purple-800 dark:text-purple-300",
				emoji: "👨‍👩‍👧",
			};
		case "UNPAID_LEAVE":
			return {
				bg: "bg-slate-100 dark:bg-slate-800/40",
				text: "text-slate-800 dark:text-slate-300",
				emoji: "💼",
			};
		case "OTHER":
			return {
				bg: "bg-amber-100 dark:bg-amber-950/40",
				text: "text-amber-800 dark:text-amber-300",
				emoji: "❓",
			};
		default:
			return {
				bg: "bg-gray-100 dark:bg-gray-800/40",
				text: "text-gray-800 dark:text-gray-300",
				emoji: "📆",
			};
	}
};

interface CalendarViewProps {
	searchQuery?: string;
	departmentId?: string;
}

export function CalendarView({ departmentId = "" }: CalendarViewProps) {
	// Get the create dialog state from context
	const { setCreateDialogOpen } = useAbsenceUI();
	const t = useTranslations();

	// Set up shortcuts system
	const {
		registerScopeWithShortcuts,
		enableScope,
		disableScope,
		removeShortcuts,
		addShortcuts,
		activeScopes,
	} = useShortcuts();
	const [focusedDate, setFocusedDate] = useState<Date | null>(null);
	const [isDateFocusMode, setIsDateFocusMode] = useState(false);
	const [calendarShortcutsRegistered, setCalendarShortcutsRegistered] =
		useState(false);

	// Add state for event focus mode
	const [isEventFocusMode, setIsEventFocusMode] = useState(false);
	const [focusedEventIndex, setFocusedEventIndex] = useState(0);
	const [selectedDate, setSelectedDate] = useState<Date | null>(null);

	// Get calendar data and actions from the hook
	const {
		events,
		isLoading, // True for initial load only
		isFetching, // True for any loading (including refetching)
		currentDate,
		setCurrentDate,
		activeFilters,
		availableAbsenceTypes,
		getEventsForDate,
		toggleFilter,
		clearFilters,
		goToNextMonth,
		goToPreviousMonth,
		refetch,
	} = usePersonnelCalendar({
		initialDate: new Date(),
		departmentId: departmentId || undefined,
	});

	// Register calendar shortcuts scope
	useEffect(() => {
		const cleanup = registerScopeWithShortcuts("calendar-view");
		setCalendarShortcutsRegistered(true);

		return () => {
			cleanup();
			setCalendarShortcutsRegistered(false);
		};
	}, [registerScopeWithShortcuts]);

	// Toggle date focus mode
	const enterDateFocusMode = useCallback(() => {
		if (!isDateFocusMode) {
			setIsDateFocusMode(true);
			setFocusedDate(new Date()); // Focus today initially

			// Disable calendar-view scope
			disableScope("calendar-view");

			// Disable personnel-tabs scope to prevent tab navigation with arrow keys
			disableScope("personnel-tabs");

			// Add and enable the date focus shortcuts
			const focusShortcuts = getShortcutsByScope("calendar-date-focus");
			addShortcuts(focusShortcuts);
			enableScope("calendar-date-focus");
		}
	}, [isDateFocusMode, disableScope, addShortcuts, enableScope]);

	// Enter event focus mode
	const enterEventFocusMode = useCallback(() => {
		if (focusedDate && !isEventFocusMode) {
			const events = getEventsForDate(focusedDate);

			if (events.length > 0) {
				// Set selected date and enter event focus mode
				setSelectedDate(focusedDate);
				setIsEventFocusMode(true);
				setFocusedEventIndex(0);

				// Disable date navigation shortcuts
				disableScope("calendar-date-focus");

				// Add and enable event navigation shortcuts
				const eventShortcuts = getShortcutsByScope(
					"calendar-event-focus",
				);
				addShortcuts(eventShortcuts);
				enableScope("calendar-event-focus");
			}
		}
	}, [
		focusedDate,
		isEventFocusMode,
		getEventsForDate,
		disableScope,
		addShortcuts,
		enableScope,
	]);

	// Return from event focus to date focus
	const exitEventFocusMode = useCallback(() => {
		if (isEventFocusMode) {
			setIsEventFocusMode(false);
			setFocusedEventIndex(0);

			// Disable event focus shortcuts
			disableScope("calendar-event-focus");
			const eventShortcuts = getShortcutsByScope("calendar-event-focus");
			removeShortcuts(
				eventShortcuts.map((s: ShortcutDefinition) => s.id),
			);

			// Re-enable date focus shortcuts
			enableScope("calendar-date-focus");

			// Clear the selected date when exiting event focus mode
			setSelectedDate(null);
		}
	}, [isEventFocusMode, disableScope, removeShortcuts, enableScope]);

	// Exit all focus modes
	const exitDateFocusMode = useCallback(() => {
		if (isEventFocusMode) {
			// First exit event focus mode
			exitEventFocusMode();
		}

		if (isDateFocusMode) {
			setIsDateFocusMode(false);
			setFocusedDate(null);
			setSelectedDate(null);

			// Disable calendar-date-focus scope
			disableScope("calendar-date-focus");

			// Remove date focus shortcuts
			const focusShortcuts = getShortcutsByScope("calendar-date-focus");
			removeShortcuts(
				focusShortcuts.map((s: ShortcutDefinition) => s.id),
			);

			// Re-enable calendar-view scope
			enableScope("calendar-view");

			// Re-enable personnel-tabs scope
			enableScope("personnel-tabs");
		}
	}, [
		isDateFocusMode,
		isEventFocusMode,
		disableScope,
		removeShortcuts,
		enableScope,
		exitEventFocusMode,
	]);

	// Clean up on unmount
	useEffect(() => {
		return () => {
			// Make sure to re-enable personnel-tabs scope when unmounting
			enableScope("personnel-tabs");

			// Clean up any active scopes
			disableScope("calendar-date-focus");

			if (calendarShortcutsRegistered) {
				const focusShortcuts = getShortcutsByScope(
					"calendar-date-focus",
				);
				removeShortcuts(
					focusShortcuts.map((s: ShortcutDefinition) => s.id),
				);
			}
		};
	}, [
		enableScope,
		disableScope,
		removeShortcuts,
		calendarShortcutsRegistered,
	]);

	// Basic calendar navigation with PageUp/PageDown
	useHotkeys(
		"pagedown",
		() => {
			goToNextMonth();
		},
		{
			enabled: activeScopes.includes("calendar-view"),
			preventDefault: true,
		},
		[goToNextMonth, activeScopes],
	);

	useHotkeys(
		"pageup",
		() => {
			goToPreviousMonth();
		},
		{
			enabled: activeScopes.includes("calendar-view"),
			preventDefault: true,
		},
		[goToPreviousMonth, activeScopes],
	);

	// Home key to jump to today
	useHotkeys(
		"home",
		() => {
			setCurrentDate(new Date());
			if (isDateFocusMode) {
				setFocusedDate(new Date());
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-view") ||
				activeScopes.includes("calendar-date-focus"),
			preventDefault: true,
		},
		[setCurrentDate, isDateFocusMode, setFocusedDate, activeScopes],
	);

	// Enter date focus mode with 'f' key
	useHotkeys(
		"f",
		() => {
			enterDateFocusMode();
		},
		{
			enabled: activeScopes.includes("calendar-view") && !isDateFocusMode,
			preventDefault: true,
		},
		[enterDateFocusMode, isDateFocusMode, activeScopes],
	);

	// Date navigation in focus mode
	useHotkeys(
		"arrowright",
		() => {
			if (focusedDate) {
				const nextDate = new Date(focusedDate);
				nextDate.setDate(nextDate.getDate() + 1);
				setFocusedDate(nextDate);

				// If navigating to next month, update the view
				if (nextDate.getMonth() !== focusedDate.getMonth()) {
					setCurrentDate(nextDate);
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-date-focus") && isDateFocusMode,
			preventDefault: true,
		},
		[
			focusedDate,
			setFocusedDate,
			setCurrentDate,
			isDateFocusMode,
			activeScopes,
		],
	);

	useHotkeys(
		"arrowleft",
		() => {
			if (focusedDate) {
				const prevDate = new Date(focusedDate);
				prevDate.setDate(prevDate.getDate() - 1);
				setFocusedDate(prevDate);

				// If navigating to previous month, update the view
				if (prevDate.getMonth() !== focusedDate.getMonth()) {
					setCurrentDate(prevDate);
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-date-focus") && isDateFocusMode,
			preventDefault: true,
		},
		[
			focusedDate,
			setFocusedDate,
			setCurrentDate,
			isDateFocusMode,
			activeScopes,
		],
	);

	useHotkeys(
		"arrowup",
		() => {
			if (focusedDate) {
				const prevWeekDate = new Date(focusedDate);
				prevWeekDate.setDate(prevWeekDate.getDate() - 7);
				setFocusedDate(prevWeekDate);

				// If navigating to previous month, update the view
				if (prevWeekDate.getMonth() !== focusedDate.getMonth()) {
					setCurrentDate(prevWeekDate);
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-date-focus") && isDateFocusMode,
			preventDefault: true,
		},
		[
			focusedDate,
			setFocusedDate,
			setCurrentDate,
			isDateFocusMode,
			activeScopes,
		],
	);

	useHotkeys(
		"arrowdown",
		() => {
			if (focusedDate) {
				const nextWeekDate = new Date(focusedDate);
				nextWeekDate.setDate(nextWeekDate.getDate() + 7);
				setFocusedDate(nextWeekDate);

				// If navigating to next month, update the view
				if (nextWeekDate.getMonth() !== focusedDate.getMonth()) {
					setCurrentDate(nextWeekDate);
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-date-focus") && isDateFocusMode,
			preventDefault: true,
		},
		[
			focusedDate,
			setFocusedDate,
			setCurrentDate,
			isDateFocusMode,
			activeScopes,
		],
	);

	// Select/view events for focused date with space
	useHotkeys(
		"space",
		() => {
			if (focusedDate && !isEventFocusMode) {
				const events = getEventsForDate(focusedDate);

				if (events.length > 0) {
					// Enter event focus mode
					enterEventFocusMode();
				} else {
					// Just set the selected date if no events to focus
					setSelectedDate(focusedDate);
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-date-focus") &&
				isDateFocusMode &&
				!isEventFocusMode,
			preventDefault: true,
		},
		[
			focusedDate,
			getEventsForDate,
			isDateFocusMode,
			isEventFocusMode,
			enterEventFocusMode,
			activeScopes,
		],
	);

	// Event navigation - up arrow for previous event
	useHotkeys(
		"arrowup",
		() => {
			if (selectedDate && isEventFocusMode) {
				setFocusedEventIndex((prev) => Math.max(0, prev - 1));
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-event-focus") &&
				isEventFocusMode,
			preventDefault: true,
		},
		[selectedDate, isEventFocusMode, activeScopes],
	);

	// Event navigation - down arrow for next event
	useHotkeys(
		"arrowdown",
		() => {
			if (selectedDate && isEventFocusMode) {
				const events = getEventsForDate(selectedDate);
				setFocusedEventIndex((prev) =>
					Math.min(events.length - 1, prev + 1),
				);
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-event-focus") &&
				isEventFocusMode,
			preventDefault: true,
		},
		[selectedDate, getEventsForDate, isEventFocusMode, activeScopes],
	);

	// View/open selected event
	useHotkeys(
		"space, enter",
		() => {
			if (selectedDate && isEventFocusMode) {
				const events = getEventsForDate(selectedDate);

				if (events.length > 0 && focusedEventIndex < events.length) {
					const event = events[focusedEventIndex];

					// Find the element for the selected event and trigger the tooltip
					const dateElements =
						document.querySelectorAll("[data-date]");
					let eventElement: HTMLElement | null = null;

					// Find the date cell containing the focused event
					dateElements.forEach((el) => {
						const dateAttr = el.getAttribute("data-date");
						if (
							dateAttr &&
							isSameDay(new Date(dateAttr), selectedDate)
						) {
							if (focusedEventIndex < 2) {
								// Event is visible in the main view
								const eventElements =
									el.querySelectorAll(".event-item");
								if (
									eventElements &&
									eventElements.length > focusedEventIndex
								) {
									const element =
										eventElements[focusedEventIndex];
									if (element instanceof HTMLElement) {
										eventElement = element;
									}
								}
							} else {
								// Event is in the "more" popover
								const popoverTrigger = el.querySelector(
									"[data-popover-trigger]",
								);
								if (
									popoverTrigger &&
									popoverTrigger instanceof HTMLElement
								) {
									popoverTrigger.click();

									// Give time for the popover to open
									setTimeout(() => {
										const popoverEventElements =
											document.querySelectorAll(
												".popover-event-item",
											);
										if (
											popoverEventElements &&
											popoverEventElements.length >
												focusedEventIndex
										) {
											const popoverElement =
												popoverEventElements[
													focusedEventIndex
												];
											if (
												popoverElement instanceof
												HTMLElement
											) {
												popoverElement.scrollIntoView({
													behavior: "smooth",
													block: "center",
												});
											}
										}
									}, 50);
								}
							}
						}
					});

					// If we found the element, trigger tooltip or scroll it into view
					if (eventElement) {
						(eventElement as HTMLElement).scrollIntoView({
							behavior: "smooth",
							block: "center",
						});

						// Try to trigger the tooltip programmatically
						const tooltipTrigger = (
							eventElement as HTMLElement
						).closest("[data-tooltip-trigger]");
						if (
							tooltipTrigger &&
							tooltipTrigger instanceof HTMLElement
						) {
							tooltipTrigger.focus();
						}
					}
				}
			}
		},
		{
			enabled:
				activeScopes.includes("calendar-event-focus") &&
				isEventFocusMode,
			preventDefault: true,
		},
		[
			selectedDate,
			getEventsForDate,
			focusedEventIndex,
			isEventFocusMode,
			activeScopes,
		],
	);

	// Updated Escape handler to handle both focus modes
	useHotkeys(
		"escape",
		() => {
			if (isEventFocusMode) {
				exitEventFocusMode();
			} else if (isDateFocusMode) {
				exitDateFocusMode();
			}
		},
		{
			enabled:
				(activeScopes.includes("calendar-date-focus") &&
					isDateFocusMode) ||
				(activeScopes.includes("calendar-event-focus") &&
					isEventFocusMode),
			preventDefault: true,
		},
		[
			isDateFocusMode,
			isEventFocusMode,
			exitDateFocusMode,
			exitEventFocusMode,
			activeScopes,
		],
	);

	// Refetch data when month changes
	useEffect(() => {
		refetch();
	}, [currentDate, refetch]);

	// Calculate calendar grid
	const calendarDays = useMemo(() => {
		const year = currentDate.getFullYear();
		const month = currentDate.getMonth();

		// First day of the month
		const firstDayOfMonth = new Date(year, month, 1);
		const startingDayIndex = firstDayOfMonth.getDay(); // 0 = Sunday, 1 = Monday, etc.

		// Last day of the month
		const lastDayOfMonth = new Date(year, month + 1, 0);
		const totalDays = lastDayOfMonth.getDate();

		// Generate array of days for the grid
		const days = [];

		// Add empty cells for days before the first day of the month
		for (let i = 0; i < startingDayIndex; i++) {
			days.push(null);
		}

		// Add days of the month
		for (let day = 1; day <= totalDays; day++) {
			days.push(new Date(year, month, day));
		}

		return days;
	}, [currentDate]);

	// Keyboard navigation
	useHotkeys(
		"arrowleft",
		(e) => {
			e.preventDefault();
			goToPreviousMonth();
		},
		{
			enabled: activeScopes.includes("personnel-tabs"),
			enableOnFormTags: false,
		},
		[goToPreviousMonth, activeScopes],
	);

	useHotkeys(
		"arrowright",
		(e) => {
			e.preventDefault();
			goToNextMonth();
		},
		{
			enabled: activeScopes.includes("personnel-tabs"),
			enableOnFormTags: false,
		},
		[goToNextMonth, activeScopes],
	);

	// Keyboard shortcut for creating a new absence
	useHotkeys(
		"ctrl+n",
		(e) => {
			e.preventDefault();
			setCreateDialogOpen(true);
		},
		{
			enabled: activeScopes.includes("personnel-tabs"),
			enableOnFormTags: false,
		},
		[setCreateDialogOpen, activeScopes],
	);

	// Skeleton loader component for calendar cells
	const SkeletonCalendarGrid = () => (
		<div className="grid grid-cols-7 h-[400px] sm:h-[calc(75vh-200px)] bg-card/30">
			{Array.from({ length: 35 }).map((_, index) => (
				<div
					key={`skeleton-${index}`}
					className="min-h-[50px] sm:min-h-[80px] border-b border-r border-border p-0.5 sm:p-1 relative"
				>
					<div className="text-right text-xs sm:text-sm font-medium opacity-0">
						00
					</div>
					<div className="mt-2 space-y-1">
						<Skeleton className="h-5 w-full bg-muted/50" />
						<Skeleton className="h-5 w-3/4 bg-muted/50" />
					</div>
				</div>
			))}
		</div>
	);

	// Render skeleton only for initial load (no data available yet)
	if (isLoading && !events.length) {
		return (
			<div className="space-y-4">
				<div className="flex items-center justify-between">
					<Skeleton className="h-8 w-40 bg-muted/50" />
					<Skeleton className="h-8 w-60 bg-muted/50" />
				</div>
				<div className="grid grid-cols-7 gap-1">
					{Array.from({ length: 7 }).map((_, index) => (
						<Skeleton
							key={index}
							className="h-8 w-full bg-muted/50"
						/>
					))}
				</div>
				<div className="grid grid-cols-7 gap-1">
					{Array.from({ length: 35 }).map((_, index) => (
						<Skeleton
							key={index}
							className="h-24 w-full rounded-md bg-muted/50"
						/>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4 relative mt-[-1px]">
			{/* Show loading indicator when refetching (not initial load) */}
			{isFetching && !isLoading && (
				<div className="absolute top-2 right-2 z-10 p-2 bg-background/80 rounded shadow-sm">
					<div
						className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
						aria-label="Loading"
					/>
				</div>
			)}

			<div className="flex flex-col sm:flex-row justify-between space-y-2 sm:space-y-0">
				{/* Calendar navigation controls */}
				<div className="flex items-center space-x-2">
					<Button
						variant="outline"
						size="icon"
						onClick={goToPreviousMonth}
						title="Previous Month (PageUp)"
						className="border-cyan-500 text-foreground hover:bg-primary hover:text-primary-foreground"
						tabIndex={0}
					>
						<ChevronLeft className="h-4 w-4" />
					</Button>
					<h2 className="text-xl font-semibold min-w-[180px] text-center text-foreground">
						{format(currentDate, "MMMM yyyy")}
					</h2>
					<Button
						variant="outline"
						size="icon"
						onClick={goToNextMonth}
						title="Next Month (PageDown)"
						className="border-cyan-500 text-foreground hover:bg-primary hover:text-primary-foreground"
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
				</div>
			</div>

			{/* Legend for absence types */}
			<div className="flex overflow-x-auto pb-2 -mx-1 px-1 scrollbar-none">
				<div className="flex flex-nowrap gap-2 pb-1">
					{availableAbsenceTypes.map((type) => {
						const { bg, text, emoji } = getAbsenceTypeColor(type);
						const isSelected = activeFilters.includes(type);

						return (
							<button
								key={type}
								type="button"
								onClick={() => toggleFilter(type)}
								className={`px-2 py-1 rounded text-xs flex items-center transition-all whitespace-nowrap
                  ${bg} ${text}
                  ${isSelected ? "ring-2 ring-primary" : ""}
                  hover:shadow-sm`}
								disabled={isFetching}
							>
								<span className="mr-1">{emoji}</span>
								<span className="hidden sm:inline">
									{type.replace(/_/g, " ")}
								</span>
							</button>
						);
					})}
					{activeFilters.length > 0 && (
						<button
							type="button"
							onClick={clearFilters}
							className="px-2 py-1 rounded text-xs bg-primary/10 text-primary hover:bg-primary/20 transition-all whitespace-nowrap"
							disabled={isFetching}
						>
							Clear
						</button>
					)}
				</div>
			</div>

			{/* Calendar grid */}
			<div className="rounded-md border border-border shadow-sm overflow-hidden bg-card">
				{/* Day headers */}
				<div className="grid grid-cols-7 bg-muted/30 text-center">
					{["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
						<div
							key={index}
							className="p-1 sm:p-2 font-medium text-xs sm:text-sm border-b border-border text-card-foreground"
						>
							<span className="hidden sm:inline">
								{
									[
										"Sun",
										"Mon",
										"Tue",
										"Wed",
										"Thu",
										"Fri",
										"Sat",
									][index]
								}
							</span>
							<span className="sm:hidden">{day}</span>
						</div>
					))}
				</div>

				{/* Calendar cells - now with event focus highlighting */}
				<div className="grid grid-cols-7 h-[400px] sm:h-[calc(75vh-200px)] bg-card/30">
					{calendarDays.map((day, index) => {
						if (!day) {
							// Empty cell for days outside the current month
							return (
								<div
									key={`empty-${index}`}
									className="min-h-[50px] sm:min-h-[80px] bg-muted/10 border-b border-r border-border p-0.5 sm:p-1"
								/>
							);
						}

						const dayEvents = getEventsForDate(day);
						const isCurrentDay = isToday(day);
						const isCurrentMonth = isSameMonth(day, currentDate);
						const isFocused =
							isDateFocusMode &&
							focusedDate &&
							isSameDay(day, focusedDate);
						const isSelected =
							selectedDate && isSameDay(day, selectedDate);

						return (
							<div
								key={day.toISOString()}
								data-date={day.toISOString()}
								className={`
                  min-h-[50px] 
                  sm:min-h-[80px] 
                  relative 
                  ${isFocused || isSelected ? "overflow-visible z-10" : "overflow-y-auto"}
                  ${isFocused || isSelected ? "shadow-sm" : "border-b border-r border-border"}
                  ${
						isSelected
							? "outline-[2px] outline-solid outline-success"
							: isFocused
								? "outline-[2px] outline-solid outline-ring"
								: ""
					}
                  ${isSelected ? "bg-success/5" : ""}
                  ${!isSelected && !isFocused && isCurrentDay ? "bg-primary/5" : ""}
                  ${!isSelected && !isFocused && !isCurrentDay && isCurrentMonth ? "bg-card" : ""}
                  ${!isSelected && !isFocused && !isCurrentMonth ? "bg-muted/5" : ""}
                  p-0.5 sm:p-1
                `}
							>
								{/* Day number */}
								<div
									className={`text-right text-xs sm:text-sm font-medium sticky top-0 z-10 rounded px-1 py-0.5 ${
										isCurrentDay
											? "bg-primary/10 text-primary-foreground"
											: "bg-transparent text-card-foreground"
									}`}
								>
									{format(day, "d")}
								</div>

								{/* Absences for this day with event focus highlighting */}
								<div className="mt-0.5 sm:mt-1 space-y-0.5 sm:space-y-1">
									{dayEvents
										.slice(0, 2)
										.map((event, eventIndex) => {
											const { bg, text, emoji } =
												getAbsenceTypeColor(event.type);

											// Display date range indicator
											const isFirst = isSameDay(
												day,
												new Date(event.startDate),
											);
											const isLast = isSameDay(
												day,
												new Date(event.endDate),
											);

											// Style for multi-day events
											let style = "rounded-none";
											if (isFirst && isLast) {
												style = "rounded";
											} else if (isFirst) {
												style = "rounded-l";
											} else if (isLast) {
												style = "rounded-r";
											}

											// Check if this event is focused in event focus mode
											const isEventFocused =
												isEventFocusMode &&
												isSelected &&
												isSameDay(
													day,
													selectedDate as Date,
												) &&
												eventIndex ===
													focusedEventIndex;

											return (
												<TooltipProvider key={event.id}>
													<Tooltip>
														<TooltipTrigger asChild>
															<div
																className={`
                                text-xs leading-tight p-0.5 truncate event-item
                                ${bg} ${text} ${style}
                                ${isEventFocused ? "ring-2 ring-highlight shadow-sm" : ""}
                                focus:outline-none focus:ring-0
                              `}
																data-tooltip-trigger="true"
																tabIndex={
																	isEventFocused
																		? 0
																		: -1
																}
															>
																<span className="mr-1">
																	{emoji}
																</span>
																<span>{`${event.personnel.firstName} ${event.personnel.lastName}`}</span>
															</div>
														</TooltipTrigger>
														<TooltipContent
															side="top"
															className="text-xs max-w-[200px] z-50"
														>
															<div>
																<div className="font-medium text-sm">{`${event.personnel.firstName} ${event.personnel.lastName}`}</div>
																<p>
																	{event.type.replace(
																		/_/g,
																		" ",
																	)}
																</p>
																<p className="text-muted-foreground">
																	{format(
																		new Date(
																			event.startDate,
																		),
																		"MMM d",
																	)}{" "}
																	-{" "}
																	{format(
																		new Date(
																			event.endDate,
																		),
																		"MMM d, yyyy",
																	)}
																</p>
															</div>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
											);
										})}

									{/* "More" button with event focus handling */}
									{dayEvents.length > 2 && (
										<Popover>
											<PopoverTrigger asChild>
												<div
													className={`
                            text-xs text-primary cursor-pointer hover:underline text-center
                            ${
								isEventFocusMode &&
								isSelected &&
								isSameDay(day, selectedDate as Date) &&
								focusedEventIndex >= 2
									? "ring-2 ring-highlight"
									: ""
							}
                          `}
													data-popover-trigger="true"
												>
													{dayEvents.length - 2}{" "}
													more...
												</div>
											</PopoverTrigger>
											<PopoverContent className="w-[90vw] max-w-[300px] sm:w-64 bg-popover border-border">
												<div className="space-y-2 p-1">
													<div className="font-medium text-popover-foreground">
														All absences for{" "}
														{format(
															day,
															"MMM d, yyyy",
														)}
													</div>
													<ScrollArea className="h-[40vh] max-h-[300px]">
														<div className="space-y-1 pr-3">
															{dayEvents.map(
																(
																	event,
																	eventIndex,
																) => {
																	const {
																		bg,
																		text,
																		emoji,
																	} =
																		getAbsenceTypeColor(
																			event.type,
																		);

																	// Check if this event is focused in the popover
																	const isPopoverEventFocused =
																		isEventFocusMode &&
																		isSelected &&
																		isSameDay(
																			day,
																			selectedDate as Date,
																		) &&
																		eventIndex ===
																			focusedEventIndex;

																	return (
																		<div
																			key={
																				event.id
																			}
																			className={`
                                      text-xs p-1 rounded flex items-center popover-event-item
                                      ${bg} ${text}
                                      ${isPopoverEventFocused ? "ring-2 ring-highlight shadow-sm" : ""}
                                      focus:outline-none focus:ring-0
                                    `}
																		>
																			<span className="mr-1">
																				{
																					emoji
																				}
																			</span>
																			<div className="flex-1">
																				<div className="font-medium text-sm">{`${event.personnel.firstName} ${event.personnel.lastName}`}</div>
																				<div className="text-[10px] text-muted-foreground">
																					{format(
																						new Date(
																							event.startDate,
																						),
																						"MMM d",
																					)}{" "}
																					-{" "}
																					{format(
																						new Date(
																							event.endDate,
																						),
																						"MMM d",
																					)}
																				</div>
																			</div>
																		</div>
																	);
																},
															)}
														</div>
													</ScrollArea>
												</div>
											</PopoverContent>
										</Popover>
									)}
								</div>
							</div>
						);
					})}
				</div>
			</div>

			{/* Focus mode indicator */}
			{(isDateFocusMode || isEventFocusMode) && (
				<div className="mt-4 text-center">
					<div className="inline-flex items-center gap-2 bg-secondary text-secondary-foreground px-3 py-1 rounded-md text-sm">
						{isEventFocusMode
							? "Event Focus Mode"
							: "Date Focus Mode"}
						<span className="px-1.5 py-0.5 text-xs rounded bg-secondary-foreground/10">
							ESC
						</span>
						to exit
					</div>
				</div>
			)}
		</div>
	);
}
