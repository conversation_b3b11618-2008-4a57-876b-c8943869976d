"use client";

import { useOrderView } from "@saas/orders/context/order-view-context";
import { useOrderLineItemMutations } from "@saas/orders/hooks/use-line-items";
import { LineItemsTable } from "@saas/shared/components/line-items/line-items-table";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Separator } from "@ui/components/separator";
import { useTranslations } from "next-intl";
import { AllocatedExpensesTable } from "./allocated-expenses-table";

interface ItemsPanelProps {
	orderId: string;
}

export function ItemsPanel({ orderId }: ItemsPanelProps) {
	const t = useTranslations();
	const { lineItems, lineItemsLoading } = useOrderView();
	const { createLineItem, updateLineItem, deleteLineItem } =
		useOrderLineItemMutations(orderId);

	return (
		<Card className="h-full flex flex-col min-h-0">
			<CardHeader className="flex flex-row items-center justify-between shrink-0">
				<div>
					<CardTitle>{t("app.orders.items.title")}</CardTitle>
					<CardDescription>
						{t("app.orders.items.description")}
					</CardDescription>
				</div>
			</CardHeader>
			<CardContent className="flex-1 flex flex-col min-h-0 p-0">
				{/* Vertical Split Layout */}
				<div className="flex flex-col h-full min-h-0">
					{/* Top Section: Order Line Items */}
					<div className="flex-1 min-h-0 p-6">
						<ScrollArea className="h-full">
							<LineItemsTable
								hideTypeTotals={true}
								hideVatColumn={true}
								items={lineItems || []}
								isLoading={lineItemsLoading}
								onCreateItem={async (item) => {
									try {
										await createLineItem({
											...item,
											orderId,
											position: lineItems?.length
												? lineItems.length + 1
												: 1,
										});
									} catch (error) {
										console.error(
											"Line item form error:",
											error,
										);
										throw error;
									}
								}}
								onUpdateItem={async (item) => {
									try {
										await updateLineItem({
											...item,
											orderId,
										});
									} catch (error) {
										console.error(
											"Line item form error:",
											error,
										);
										throw error;
									}
								}}
								onDeleteItem={async (id) => {
									try {
										await deleteLineItem(id);
									} catch (error) {
										console.error(
											"Delete line item error:",
											error,
										);
										throw error;
									}
								}}
							/>
						</ScrollArea>
					</div>

					{/* Separator */}
					<Separator />

					{/* Bottom Section: Allocated Expenses */}
					<div className="flex-1 min-h-0 p-6">
						<div className="mb-4">
							<h3 className="text-lg font-semibold">
								Allocated Expenses
							</h3>
							<p className="text-sm text-muted-foreground">
								Expenses allocated to this order
							</p>
						</div>
						<ScrollArea className="h-full">
							<AllocatedExpensesTable orderId={orderId} />
						</ScrollArea>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
