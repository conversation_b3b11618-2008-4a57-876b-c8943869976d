import type { ThreadStatus } from "@repo/api/src/routes/threads/types";
import type { BadgeProps } from "@ui/components/badge";
import { Badge } from "@ui/components/badge";
import { Archive, CheckCircle2, MessageCircle } from "lucide-react";

interface StatusBadgeProps {
	status: ThreadStatus;
	className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
	const config: Record<
		ThreadStatus,
		{ icon: React.ElementType; status: BadgeProps["status"]; label: string }
	> = {
		ACTIVE: {
			icon: MessageCircle,
			status: "info",
			label: "Active",
		},
		RESOLVED: {
			icon: CheckCircle2,
			status: "success",
			label: "Resolved",
		},
		ARCHIVED: {
			icon: Archive,
			status: "warning",
			label: "Archived",
		},
	};

	const {
		icon: Icon,
		status: badgeStatus,
		label,
	} = config[status] || config.ACTIVE;

	return (
		<Badge status={badgeStatus} className={className}>
			<Icon className="w-3 h-3" />
			{label}
		</Badge>
	);
}
