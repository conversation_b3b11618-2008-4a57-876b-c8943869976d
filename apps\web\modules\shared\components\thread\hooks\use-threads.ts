import type {
	CreateCommentInput,
	CreateThreadInput,
	ThreadStatus,
	UpdateCommentInput,
	UpdateThreadInput,
} from "@repo/api/src/routes/threads/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import { toast } from "sonner";
import {
	commentKeys,
	fetchComments,
	fetchMembersAutocomplete,
	fetchThreadById,
	fetchThreads,
	memberKeys,
	threadKeys,
} from "../lib/api";

export function useThreads() {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [status, setStatus] = useState<ThreadStatus | undefined>(undefined);
	const [entityType, setEntityType] = useState<string | undefined>(undefined);
	const [entityId, setEntityId] = useState<string | undefined>(undefined);
	const [isPinned, setIsPinned] = useState<boolean | undefined>(undefined);
	const [unread, setUnread] = useState<boolean | undefined>(undefined);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useQuery({
		queryKey: threadKeys.list({
			organizationId: activeOrganization?.id ?? "",
			search: debouncedSearch,
			page,
			limit: pageSize,
			sortBy: (sorting[0]?.id as any) || "lastActivity",
			sortDirection: sorting[0]?.desc ? "desc" : "asc",
			status,
			entityType,
			entityId,
			isPinned,
			unread,
		}),
		queryFn: () =>
			fetchThreads({
				organizationId: activeOrganization?.id ?? "",
				search: debouncedSearch,
				page,
				limit: pageSize,
				sortBy: (sorting[0]?.id as any) || "lastActivity",
				sortDirection: sorting[0]?.desc ? "desc" : "asc",
				status,
				entityType,
				entityId,
				isPinned,
				unread,
			}),
		placeholderData: keepPreviousData,
		enabled: !!activeOrganization?.id,
	});

	const deleteMutation = useDeleteThreadMutation();

	const deleteThread = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete thread error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		status,
		setStatus,
		entityType,
		setEntityType,
		entityId,
		setEntityId,
		isPinned,
		setIsPinned,
		unread,
		setUnread,
		refetch: query.refetch,
		deleteThread,
	};
}

export function useThreadById(threadId?: string) {
	const { activeOrganization } = useActiveOrganization();

	return useQuery({
		queryKey: threadKeys.detail(activeOrganization?.id, threadId),
		queryFn: () => {
			if (!activeOrganization?.id || !threadId) {
				return null;
			}
			return fetchThreadById(activeOrganization.id, threadId);
		},
		enabled: !!activeOrganization?.id && !!threadId,
	});
}

export function useComments(threadId?: string) {
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(50);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [parentId, setParentId] = useState<string | undefined>(undefined);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useQuery({
		queryKey: commentKeys.list({
			threadId: threadId ?? "",
			parentId,
			page,
			limit: pageSize,
			sortBy: (sorting[0]?.id as any) || "createdAt",
			sortDirection: sorting[0]?.desc ? "desc" : "asc",
		}),
		queryFn: () =>
			fetchComments({
				threadId: threadId ?? "",
				parentId,
				page,
				limit: pageSize,
				sortBy: (sorting[0]?.id as any) || "createdAt",
				sortDirection: sorting[0]?.desc ? "desc" : "asc",
			}),
		placeholderData: keepPreviousData,
		enabled: !!threadId,
	});

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		parentId,
		setParentId,
		refetch: query.refetch,
	};
}

export function useThreadMutations(options?: { onSuccess?: () => void }) {
	const createMutation = useCreateThreadMutation();
	const updateMutation = useUpdateThreadMutation();
	const deleteMutation = useDeleteThreadMutation();

	const createWithCallback = async (data: CreateThreadInput) => {
		try {
			const result = await createMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Create thread error:", error);
			throw error;
		}
	};

	const updateWithCallback = async (data: UpdateThreadInput) => {
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Update thread error:", error);
			throw error;
		}
	};

	const deleteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Delete thread error:", error);
			throw error;
		}
	};

	return {
		createThread: createWithCallback,
		updateThread: updateWithCallback,
		deleteThread: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

export function useCommentMutations(options?: { onSuccess?: () => void }) {
	const createMutation = useCreateCommentMutation();
	const updateMutation = useUpdateCommentMutation();
	const deleteMutation = useDeleteCommentMutation();

	const createWithCallback = async (data: CreateCommentInput) => {
		try {
			const result = await createMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Create comment error:", error);
			throw error;
		}
	};

	const updateWithCallback = async (data: UpdateCommentInput) => {
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Update comment error:", error);
			throw error;
		}
	};

	const deleteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Delete comment error:", error);
			throw error;
		}
	};

	return {
		createComment: createWithCallback,
		updateComment: updateWithCallback,
		deleteComment: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

export function useCommentReactionMutations(options?: {
	onSuccess?: () => void;
}) {
	const addMutation = useAddCommentReactionMutation();
	const removeMutation = useRemoveCommentReactionMutation();

	const addReaction = async (commentId: string, emoji: string) => {
		try {
			const result = await addMutation.mutateAsync({ commentId, emoji });
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Add reaction error:", error);
			throw error;
		}
	};

	const removeReaction = async (commentId: string, emoji: string) => {
		try {
			const result = await removeMutation.mutateAsync({
				commentId,
				emoji,
			});
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Remove reaction error:", error);
			throw error;
		}
	};

	const toggleReaction = async (
		commentId: string,
		emoji: string,
		hasReacted: boolean,
	) => {
		if (hasReacted) {
			return removeReaction(commentId, emoji);
		}
		return addReaction(commentId, emoji);
	};

	return {
		addReaction,
		removeReaction,
		toggleReaction,
		isLoading: addMutation.isPending || removeMutation.isPending,
	};
}

// Members autocomplete hook
export function useMembersAutocomplete(
	query: string,
	options?: { enabled?: boolean },
) {
	const { activeOrganization } = useActiveOrganization();
	const debouncedQuery = useDebounce(query, 200);

	return useQuery({
		queryKey: memberKeys.autocomplete({
			organizationId: activeOrganization?.id ?? "",
			q: debouncedQuery,
			limit: 10,
		}),
		queryFn: () =>
			fetchMembersAutocomplete({
				organizationId: activeOrganization?.id ?? "",
				q: debouncedQuery,
				limit: 10,
			}),
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!activeOrganization?.id &&
					!!debouncedQuery &&
					debouncedQuery.length > 0,
		staleTime: 30000, // 30 seconds
	});
}

// Individual mutation hooks
function useCreateThreadMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateThreadInput) => {
			const response = await apiClient.threads.$post({
				json: {
					...data,
					organizationId: activeOrganization?.id ?? "",
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create thread");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Thread created successfully");
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: threadKeys.all,
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to create thread");
			console.error("Create thread error:", error);
		},
	});
}

function useUpdateThreadMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: UpdateThreadInput) => {
			const response = await apiClient.threads[":id"].$put({
				param: { id: data.id },
				json: {
					...data,
					organizationId: activeOrganization?.id ?? "",
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update thread");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Thread updated successfully");
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: threadKeys.all,
				});
				if (data?.id) {
					queryClient.invalidateQueries({
						queryKey: threadKeys.detail(
							activeOrganization.id,
							data.id,
						),
					});
				}
			}
		},
		onError: (error) => {
			toast.error("Failed to update thread");
			console.error("Update thread error:", error);
		},
	});
}

function useDeleteThreadMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.threads[":id"].$delete({
				param: { id },
				query: { organizationId: activeOrganization?.id ?? "" },
			});

			if (!response.ok) {
				throw new Error("Failed to delete thread");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Thread deleted successfully");
			if (activeOrganization?.id) {
				queryClient.invalidateQueries({
					queryKey: threadKeys.all,
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to delete thread");
			console.error("Delete thread error:", error);
		},
	});
}

function useCreateCommentMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateCommentInput) => {
			const response = await apiClient.threads[":id"].comments.$post({
				param: { id: data.threadId },
				json: data,
			});

			if (!response.ok) {
				throw new Error("Failed to create comment");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Comment added successfully");
			// Invalidate all comment queries since we don't know the exact params
			queryClient.invalidateQueries({
				queryKey: commentKeys.all,
			});
			// Also invalidate the thread detail to update comment count
			if (data?.threadId) {
				queryClient.invalidateQueries({
					queryKey: threadKeys.detail(
						activeOrganization?.id,
						data.threadId,
					),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to add comment");
			console.error("Create comment error:", error);
		},
	});
}

function useUpdateCommentMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: UpdateCommentInput) => {
			const response = await apiClient.comments[":id"].$put({
				param: { id: data.id },
				json: {
					...data,
					organizationId: activeOrganization?.id ?? "",
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update comment");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Comment updated successfully");
			// Invalidate all comment and thread queries
			queryClient.invalidateQueries({
				queryKey: commentKeys.all,
			});
			queryClient.invalidateQueries({
				queryKey: threadKeys.all,
			});
		},
		onError: (error) => {
			toast.error("Failed to update comment");
			console.error("Update comment error:", error);
		},
	});
}

function useDeleteCommentMutation() {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.comments[":id"].$delete({
				param: { id },
				query: { organizationId: activeOrganization?.id ?? "" },
			});

			if (!response.ok) {
				throw new Error("Failed to delete comment");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Comment deleted successfully");
			// Invalidate all comment and thread queries
			queryClient.invalidateQueries({
				queryKey: commentKeys.all,
			});
			queryClient.invalidateQueries({
				queryKey: threadKeys.all,
			});
		},
		onError: (error) => {
			toast.error("Failed to delete comment");
			console.error("Delete comment error:", error);
		},
	});
}

function useAddCommentReactionMutation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			commentId,
			emoji,
		}: { commentId: string; emoji: string }) => {
			const response = await apiClient.comments[":id"].reactions.$post({
				param: { id: commentId },
				json: { emoji },
			});

			if (!response.ok) {
				throw new Error("Failed to add reaction");
			}

			return response.json();
		},
		onSuccess: () => {
			// Invalidate comment queries to refresh reactions
			queryClient.invalidateQueries({
				queryKey: commentKeys.all,
			});
		},
		onError: (error) => {
			toast.error("Failed to add reaction");
			console.error("Add reaction error:", error);
		},
	});
}

function useRemoveCommentReactionMutation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			commentId,
			emoji,
		}: { commentId: string; emoji: string }) => {
			const response = await apiClient.comments[":id"].reactions.$delete({
				param: { id: commentId },
				json: { emoji },
			});

			if (!response.ok) {
				throw new Error("Failed to remove reaction");
			}

			return response.json();
		},
		onSuccess: () => {
			// Invalidate comment queries to refresh reactions
			queryClient.invalidateQueries({
				queryKey: commentKeys.all,
			});
		},
		onError: (error) => {
			toast.error("Failed to remove reaction");
			console.error("Remove reaction error:", error);
		},
	});
}
