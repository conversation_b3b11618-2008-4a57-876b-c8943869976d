"use client";

import React from "react";
import type { ExpenseLineItemWithAllocations } from "../../types/expense-line-items";
import { ExpenseLineItemsWrapper } from "../line-items/expense-line-items-wrapper";

interface ExpenseLineItemsSectionProps {
	lineItems: ExpenseLineItemWithAllocations[];
	onLineItemsChange: (items: ExpenseLineItemWithAllocations[]) => void;
	onLineItemAllocate: (item: ExpenseLineItemWithAllocations) => void;
	disabled?: boolean;
}

export function ExpenseLineItemsSection({
	lineItems,
	onLineItemsChange,
	onLineItemAllocate,
	disabled = false,
}: ExpenseLineItemsSectionProps) {
	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Line Items</h3>
			
			<div className="border rounded-lg">
				<ExpenseLineItemsWrapper
					items={lineItems}
					onItemsChange={onLineItemsChange}
					onAllocateItem={onLineItemAllocate}
					disabled={disabled}
				/>
			</div>

			{lineItems.length === 0 && (
				<div className="text-center py-8 text-muted-foreground">
					<p className="text-sm">No line items added yet</p>
					<p className="text-xs mt-1">
						Click "Add Item" to start adding expense line items
					</p>
				</div>
			)}
		</div>
	);
}
