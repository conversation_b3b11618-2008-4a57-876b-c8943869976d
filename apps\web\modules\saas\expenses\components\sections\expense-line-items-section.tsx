"use client";

import type { ExpenseLineItemInput } from "@repo/api/src/routes/costs/types";
import React from "react";
import type { ExpenseLineItemWithAllocations } from "../../types/expense-line-items";
import { ExpenseLineItemsWrapper } from "../line-items/expense-line-items-wrapper";

interface ExpenseLineItemsSectionProps {
	lineItems: ExpenseLineItemWithAllocations[];
	onLineItemsChange: (items: ExpenseLineItemWithAllocations[]) => void;
	onLineItemAllocate: (item: ExpenseLineItemWithAllocations) => void;
	disabled?: boolean;
}

export function ExpenseLineItemsSection({
	lineItems,
	onLineItemsChange,
	onLineItemAllocate,
}: ExpenseLineItemsSectionProps) {
	// Convert itemId to full item for allocation
	const handleAllocateItem = (itemId: string) => {
		const item = lineItems.find((item) => item.id === itemId);
		if (item) {
			onLineItemAllocate(item);
		}
	};

	// Handle creating a new line item
	const handleCreateItem = (item: ExpenseLineItemInput) => {
		const newItem: ExpenseLineItemWithAllocations = {
			id: crypto.randomUUID(),
			description: item.description,
			totalPrice: item.totalPrice,
			quantity: item.quantity ?? null,
			unit: item.unit ?? null,
			unitPrice: item.unitPrice ?? null,
			vatRate: item.vatRate ?? null,
			vatAmount: item.vatAmount ?? null,
			categoryId: item.categoryId ?? null,
			notes: item.notes ?? null,
			// API response fields that need to be initialized
			allocations: [],
			allocatedAmount: 0,
			remainingAmount: item.totalPrice || 0,
			isFullyAllocated: false,
			category: null,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			expenseId: "", // Will be set when expense is saved
		};
		onLineItemsChange([...lineItems, newItem]);
	};

	// Handle updating an existing line item
	const handleUpdateItem = (
		updatedItem: ExpenseLineItemInput & { id: string },
	) => {
		const updatedItems = lineItems.map((item) =>
			item.id === updatedItem.id
				? {
						...item,
						// Update only the fields that can be changed
						description: updatedItem.description,
						totalPrice: updatedItem.totalPrice,
						quantity: updatedItem.quantity ?? null,
						unit: updatedItem.unit ?? null,
						unitPrice: updatedItem.unitPrice ?? null,
						vatRate: updatedItem.vatRate ?? null,
						vatAmount: updatedItem.vatAmount ?? null,
						categoryId: updatedItem.categoryId ?? null,
						notes: updatedItem.notes ?? null,
						updatedAt: new Date().toISOString(),
						// Recalculate remaining amount if total price changed
						remainingAmount:
							(updatedItem.totalPrice || 0) -
							item.allocatedAmount,
					}
				: item,
		);
		onLineItemsChange(updatedItems);
	};

	// Handle deleting a line item
	const handleDeleteItem = (itemId: string) => {
		const filteredItems = lineItems.filter((item) => item.id !== itemId);
		onLineItemsChange(filteredItems);
	};

	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Line Items</h3>

			<div className="border rounded-lg">
				<ExpenseLineItemsWrapper
					items={lineItems}
					onCreateItem={handleCreateItem}
					onUpdateItem={handleUpdateItem}
					onDeleteItem={handleDeleteItem}
					onAllocateItem={handleAllocateItem}
				/>
			</div>

			{lineItems.length === 0 && (
				<div className="text-center py-8 text-muted-foreground">
					<p className="text-sm">No line items added yet</p>
					<p className="text-xs mt-1">
						Click "Add Item" to start adding expense line items
					</p>
				</div>
			)}
		</div>
	);
}
