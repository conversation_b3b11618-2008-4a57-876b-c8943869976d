"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { But<PERSON> } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	Dialog,
	DialogContent,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import {
	AtSign,
	ExternalLink,
	FileText,
	Loader2,
	Mail,
	Mails,
	Paperclip,
	Pencil,
} from "lucide-react";
import { useEffect, useState } from "react";

import { MultiEmailInput } from "@ui/components/multi-email-input";

import { useUniversalEmailPreview } from "../hooks/use-universal-email-preview";
import {
	formatBytes,
	getAttachmentText,
	getDocumentsKey,
	getEmailPreviewConfig,
	getEmailPreviewTitle,
	isEmailPreview,
} from "../lib/email-preview-config";
import type {
	DocumentInfo,
	SendEmailParams,
	UniversalEmailPreviewProps,
} from "../types/email-preview";

export function UniversalEmailPreview({
	entityId,
	entityType,
	isOpen,
	onClose,
	onSend,
	isSending = false,
	recipientEmail: initialRecipientEmail,
	onBack,
	features: customFeatures,
	title: customTitle,
	attachmentText: customAttachmentText,
}: UniversalEmailPreviewProps) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Get default configuration and merge with custom features
	const defaultFeatures = getEmailPreviewConfig(entityType);
	const features = { ...defaultFeatures, ...customFeatures };

	// Component state
	const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>(
		[],
	);
	const [customSubject, setCustomSubject] = useState<string>("");
	const [isEditingSubject, setIsEditingSubject] = useState(false);
	const [recipientEmail, setRecipientEmail] = useState(
		initialRecipientEmail || "",
	);
	const [ccEmails, setCcEmails] = useState<string[]>([]);
	const [bccEmails, setBccEmails] = useState<string[]>([]);
	const [documentFilenames, setDocumentFilenames] = useState<
		Record<string, string>
	>({});
	const [editingFilenames, setEditingFilenames] = useState<
		Record<string, boolean>
	>({});
	const [isLocalSending, setIsLocalSending] = useState(false);

	// Get email preview data
	const { data, isLoading, error, refetch } = useUniversalEmailPreview({
		entityType,
		entityId: entityId || "",
		organizationId,
		recipientEmail: initialRecipientEmail,
		enabled: isOpen && !!entityId,
	});

	// Check if data has the correct structure
	const emailContent = data && isEmailPreview(data) ? data : null;

	// Reset state when dialog opens or entity changes
	useEffect(() => {
		if (isOpen && features.stateReset) {
			setCustomSubject("");
			setIsEditingSubject(false);
			setSelectedDocumentIds([]);
			setDocumentFilenames({});
			setEditingFilenames({});
			setRecipientEmail(initialRecipientEmail || "");
			setCcEmails([]);
			setBccEmails([]);
		}
	}, [isOpen, entityId, features.stateReset, initialRecipientEmail]);

	// Set default subject when data loads
	useEffect(() => {
		if (emailContent?.subject && !customSubject && features.customSubject) {
			setCustomSubject(emailContent.subject);
		}
	}, [emailContent?.subject, customSubject, features.customSubject]);

	// Get documents from metadata based on entity type
	const documentsKey = getDocumentsKey(entityType);
	const documents: DocumentInfo[] =
		emailContent?.metadata?.[documentsKey] || [];

	// Get configuration values
	const title = customTitle || getEmailPreviewTitle(entityType);
	const attachmentText =
		customAttachmentText || getAttachmentText(entityType);

	// Extract recipient from metadata or use provided one (for display)
	const displayRecipientEmail =
		emailContent?.metadata?.recipient || initialRecipientEmail;

	// Toggle document selection
	const toggleDocument = (documentId: string) => {
		setSelectedDocumentIds((prev) =>
			prev.includes(documentId)
				? prev.filter((id) => id !== documentId)
				: [...prev, documentId],
		);
	};

	// Toggle filename editing for a document
	const toggleFilenameEditing = (
		documentId: string,
		originalFilename: string,
	) => {
		setEditingFilenames((prev) => {
			const newState = { ...prev, [documentId]: !prev[documentId] };

			// Initialize custom filename with original name when enabling editing
			if (newState[documentId] && !documentFilenames[documentId]) {
				setDocumentFilenames((prevFilenames) => ({
					...prevFilenames,
					[documentId]: originalFilename,
				}));
			}

			return newState;
		});
	};

	const handleSend = async () => {
		if (!entityId) {
			return;
		}

		setIsLocalSending(true);
		try {
			// Build send parameters
			const sendParams: SendEmailParams = {
				entityId,
				entityType,
				recipientEmail: displayRecipientEmail,
				...(ccEmails.length > 0 &&
					features.ccBccSupport && { ccEmails }),
				...(bccEmails.length > 0 &&
					features.ccBccSupport && { bccEmails }),
				...(selectedDocumentIds.length > 0 &&
					features.documentAttachments && {
						documentIds: selectedDocumentIds,
					}),
				...(features.customSubject &&
					customSubject &&
					customSubject !== emailContent?.subject && {
						customSubject,
					}),
				...(Object.keys(documentFilenames).length > 0 && {
					documentFilenames,
				}),
			};

			await onSend(sendParams);

			// Close dialog after successful send
			onClose();
		} catch (error) {
			// Error handling is done by the individual send hooks
			// Dialog stays open so user can see the error and retry
			console.error("Failed to send email:", error);
		} finally {
			setIsLocalSending(false);
		}
	};

	// Handle manual refetch for transport orders (they use a different pattern)
	useEffect(() => {
		if (isOpen && entityId && entityType === "transport-order") {
			refetch();
		}
	}, [isOpen, entityId, entityType, refetch]);

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl w-full max-h-[90vh] p-0 flex flex-col">
				<DialogHeader className="px-6 py-4 flex-shrink-0">
					<DialogTitle>{title}</DialogTitle>
				</DialogHeader>

				<ScrollArea className="flex-1 overflow-auto">
					{isLoading ? (
						<div className="flex justify-center items-center py-10">
							<Loader2 className="h-10 w-10 text-primary animate-spin" />
						</div>
					) : error ? (
						<div className="text-destructive text-center py-8">
							<p>Failed to load email preview</p>
							<p className="text-xs mt-2">
								{error instanceof Error
									? error.message
									: "Unknown error"}
							</p>
						</div>
					) : !emailContent ? (
						<div className="text-destructive text-center py-8">
							<p>Invalid email preview format received</p>
						</div>
					) : (
						<div className="px-6 pb-4">
							{/* Email metadata section */}
							<div className="border rounded-md p-3 mb-4 bg-muted/10">
								<div className="space-y-3 text-sm">
									{/* From */}
									<div className="flex items-start">
										<span className="w-24 flex-shrink-0 flex items-center text-muted-foreground">
											<Mail className="h-3.5 w-3.5 mr-2" />
											From:
										</span>
										<div className="flex-1 overflow-hidden">
											<div className="truncate">
												{
													emailContent.metadata
														?.organization
												}
											</div>
											<div className="text-xs text-muted-foreground truncate">
												{emailContent.metadata?.sender}
											</div>
										</div>
									</div>

									{/* To */}
									<div className="flex items-start">
										<span className="w-24 flex-shrink-0 flex items-center text-muted-foreground">
											<AtSign className="h-3.5 w-3.5 mr-2" />
											To:
										</span>
										<div className="truncate">
											{displayRecipientEmail ||
												"No recipient specified"}
										</div>
									</div>

									{/* CC */}
									{features.ccBccSupport && (
										<div className="flex items-start">
											<span className="w-24 flex-shrink-0 flex items-center text-muted-foreground">
												<Mail className="h-3.5 w-3.5 mr-2" />
												CC:
											</span>
											<div className="flex-1">
												<MultiEmailInput
													value={ccEmails}
													onChange={setCcEmails}
													placeholder="Enter CC email addresses..."
													className="text-sm min-h-[2rem]"
												/>
											</div>
										</div>
									)}

									{/* BCC */}
									{features.ccBccSupport && (
										<div className="flex items-start">
											<span className="w-24 flex-shrink-0 flex items-center text-muted-foreground">
												<Mail className="h-3.5 w-3.5 mr-2" />
												BCC:
											</span>
											<div className="flex-1">
												<MultiEmailInput
													value={bccEmails}
													onChange={setBccEmails}
													placeholder="Enter BCC email addresses..."
													className="text-sm min-h-[2rem]"
												/>
											</div>
										</div>
									)}

									{/* Subject */}
									<div className="flex items-start">
										<span className="w-24 flex-shrink-0 flex items-center text-muted-foreground">
											<Mails className="h-3.5 w-3.5 mr-2" />
											Subject:
										</span>
										<div className="flex-1 flex items-center gap-2">
											{features.customSubject &&
											isEditingSubject ? (
												<Input
													value={customSubject}
													onChange={(e) =>
														setCustomSubject(
															e.target.value,
														)
													}
													onBlur={() =>
														setIsEditingSubject(
															false,
														)
													}
													onKeyDown={(e) => {
														if (e.key === "Enter") {
															setIsEditingSubject(
																false,
															);
														}
														if (
															e.key === "Escape"
														) {
															setCustomSubject(
																emailContent.subject ||
																	"",
															);
															setIsEditingSubject(
																false,
															);
														}
													}}
													autoFocus
													className="font-medium"
												/>
											) : (
												<>
													<div className="font-medium truncate flex-1">
														{features.customSubject
															? customSubject
															: emailContent.subject}
														{features.customSubject &&
															emailContent.subject &&
															customSubject !==
																emailContent.subject && (
																<span className="ml-2 text-xs text-blue-600 font-normal">
																	(modified)
																</span>
															)}
													</div>
													{features.customSubject && (
														<Button
															variant="ghost"
															size="sm"
															onClick={() =>
																setIsEditingSubject(
																	true,
																)
															}
															className="h-6 w-6 p-0 hover:bg-muted"
														>
															<Pencil className="h-3.5 w-3.5" />
														</Button>
													)}
												</>
											)}
										</div>
									</div>

									{/* Attachments section */}
									<div className="flex flex-col">
										<span className="mb-2 flex items-center text-muted-foreground">
											<Paperclip className="h-3.5 w-3.5 mr-2" />
											Attachments:
										</span>
										<div className="pl-6">
											{/* Main PDF attachment */}
											<div className="flex items-center">
												<FileText className="h-4 w-4 mr-2 text-muted-foreground" />
												<div className="font-medium">
													{attachmentText}
												</div>
											</div>

											{/* Additional documents selection */}
											{features.documentAttachments &&
												documents.length > 0 && (
													<div className="mt-3">
														<div className="font-medium mb-2">
															Additional
															documents:
														</div>
														<div className="border rounded-md p-2">
															{documents.length <=
															3 ? (
																// Render without ScrollArea for few documents
																<div className="space-y-2">
																	{documents.map(
																		(
																			doc,
																		) => (
																			<div
																				key={
																					doc.id
																				}
																				className="space-y-2"
																			>
																				<div className="flex items-center gap-2">
																					<Checkbox
																						id={`doc-${doc.id}`}
																						checked={selectedDocumentIds.includes(
																							doc.id,
																						)}
																						onCheckedChange={() =>
																							toggleDocument(
																								doc.id,
																							)
																						}
																					/>
																					<label
																						htmlFor={`doc-${doc.id}`}
																						className="flex items-center gap-2 text-sm cursor-pointer flex-1"
																					>
																						<FileText className="h-4 w-4 text-muted-foreground" />
																						<div className="flex flex-1 min-w-0">
																							{selectedDocumentIds.includes(
																								doc.id,
																							) &&
																							editingFilenames[
																								doc
																									.id
																							] ? (
																								<Input
																									value={
																										documentFilenames[
																											doc
																												.id
																										] ||
																										""
																									}
																									onChange={(
																										e,
																									) =>
																										setDocumentFilenames(
																											(
																												prev,
																											) => ({
																												...prev,
																												[doc.id]:
																													e
																														.target
																														.value,
																											}),
																										)
																									}
																									onBlur={() => {
																										// If no custom filename entered, remove from state
																										if (
																											!documentFilenames[
																												doc
																													.id
																											]?.trim()
																										) {
																											setDocumentFilenames(
																												(
																													prev,
																												) => {
																													const {
																														[doc.id]:
																															_,
																														...rest
																													} =
																														prev;
																													return rest;
																												},
																											);
																										}
																										setEditingFilenames(
																											(
																												prev,
																											) => ({
																												...prev,
																												[doc.id]: false,
																											}),
																										);
																									}}
																									onKeyDown={(
																										e,
																									) => {
																										if (
																											e.key ===
																											"Enter"
																										) {
																											setEditingFilenames(
																												(
																													prev,
																												) => ({
																													...prev,
																													[doc.id]: false,
																												}),
																											);
																										}
																										if (
																											e.key ===
																											"Escape"
																										) {
																											setDocumentFilenames(
																												(
																													prev,
																												) => {
																													const {
																														[doc.id]:
																															_,
																														...rest
																													} =
																														prev;
																													return rest;
																												},
																											);
																											setEditingFilenames(
																												(
																													prev,
																												) => ({
																													...prev,
																													[doc.id]: false,
																												}),
																											);
																										}
																									}}
																									placeholder="Enter custom filename..."
																									className="text-sm h-8 flex-1"
																									autoFocus
																								/>
																							) : (
																								<>
																									{doc.signedUrl ? (
																										<button
																											type="button"
																											onClick={(
																												e,
																											) => {
																												e.preventDefault();
																												e.stopPropagation();
																												window.open(
																													doc.signedUrl,
																													"_blank",
																												);
																											}}
																											className="text-left truncate flex-1 hover:text-primary hover:underline transition-colors flex items-center gap-1"
																										>
																											<span className="truncate">
																												{(selectedDocumentIds.includes(
																													doc.id,
																												) &&
																													documentFilenames[
																														doc
																															.id
																													]) ||
																													doc.fileName ||
																													`Document ${doc.id}`}
																												{selectedDocumentIds.includes(
																													doc.id,
																												) &&
																													documentFilenames[
																														doc
																															.id
																													] &&
																													documentFilenames[
																														doc
																															.id
																													] !==
																														doc.fileName && (
																														<span className="ml-2 text-xs text-blue-600 font-normal">
																															(custom)
																														</span>
																													)}
																											</span>
																											<ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
																										</button>
																									) : (
																										<span className="truncate flex-1">
																											{(selectedDocumentIds.includes(
																												doc.id,
																											) &&
																												documentFilenames[
																													doc
																														.id
																												]) ||
																												doc.fileName ||
																												`Document ${doc.id}`}
																											{selectedDocumentIds.includes(
																												doc.id,
																											) &&
																												documentFilenames[
																													doc
																														.id
																												] &&
																												documentFilenames[
																													doc
																														.id
																												] !==
																													doc.fileName && (
																													<span className="ml-2 text-xs text-blue-600 font-normal">
																														(custom)
																													</span>
																												)}
																										</span>
																									)}
																									{selectedDocumentIds.includes(
																										doc.id,
																									) && (
																										<Button
																											variant="ghost"
																											size="sm"
																											onClick={(
																												e,
																											) => {
																												e.preventDefault();
																												e.stopPropagation();
																												toggleFilenameEditing(
																													doc.id,
																													doc.fileName ||
																														`Document ${doc.id}`,
																												);
																											}}
																											className="h-6 w-6 p-0 hover:bg-muted ml-1"
																										>
																											<Pencil className="h-3 w-3" />
																										</Button>
																									)}
																								</>
																							)}
																							{doc.fileSize && (
																								<span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
																									{formatBytes(
																										doc.fileSize,
																									)}
																								</span>
																							)}
																						</div>
																					</label>
																				</div>
																			</div>
																		),
																	)}
																</div>
															) : (
																// Use ScrollArea for many documents
																<ScrollArea className="h-[120px]">
																	<div className="space-y-2">
																		{documents.map(
																			(
																				doc,
																			) => (
																				<div
																					key={
																						doc.id
																					}
																					className="space-y-2"
																				>
																					<div className="flex items-center gap-2">
																						<Checkbox
																							id={`doc-${doc.id}`}
																							checked={selectedDocumentIds.includes(
																								doc.id,
																							)}
																							onCheckedChange={() =>
																								toggleDocument(
																									doc.id,
																								)
																							}
																						/>
																						<label
																							htmlFor={`doc-${doc.id}`}
																							className="flex items-center gap-2 text-sm cursor-pointer flex-1"
																						>
																							<FileText className="h-4 w-4 text-muted-foreground" />
																							<div className="flex flex-1 min-w-0">
																								{selectedDocumentIds.includes(
																									doc.id,
																								) &&
																								editingFilenames[
																									doc
																										.id
																								] ? (
																									<Input
																										value={
																											documentFilenames[
																												doc
																													.id
																											] ||
																											""
																										}
																										onChange={(
																											e,
																										) =>
																											setDocumentFilenames(
																												(
																													prev,
																												) => ({
																													...prev,
																													[doc.id]:
																														e
																															.target
																															.value,
																												}),
																											)
																										}
																										onBlur={() => {
																											// If no custom filename entered, remove from state
																											if (
																												!documentFilenames[
																													doc
																														.id
																												]?.trim()
																											) {
																												setDocumentFilenames(
																													(
																														prev,
																													) => {
																														const {
																															[doc.id]:
																																_,
																															...rest
																														} =
																															prev;
																														return rest;
																													},
																												);
																											}
																											setEditingFilenames(
																												(
																													prev,
																												) => ({
																													...prev,
																													[doc.id]: false,
																												}),
																											);
																										}}
																										onKeyDown={(
																											e,
																										) => {
																											if (
																												e.key ===
																												"Enter"
																											) {
																												setEditingFilenames(
																													(
																														prev,
																													) => ({
																														...prev,
																														[doc.id]: false,
																													}),
																												);
																											}
																											if (
																												e.key ===
																												"Escape"
																											) {
																												setDocumentFilenames(
																													(
																														prev,
																													) => {
																														const {
																															[doc.id]:
																																_,
																															...rest
																														} =
																															prev;
																														return rest;
																													},
																												);
																												setEditingFilenames(
																													(
																														prev,
																													) => ({
																														...prev,
																														[doc.id]: false,
																													}),
																												);
																											}
																										}}
																										placeholder="Enter custom filename..."
																										className="text-sm h-8 flex-1"
																										autoFocus
																									/>
																								) : (
																									<>
																										{doc.signedUrl ? (
																											<button
																												type="button"
																												onClick={(
																													e,
																												) => {
																													e.preventDefault();
																													e.stopPropagation();
																													window.open(
																														doc.signedUrl,
																														"_blank",
																													);
																												}}
																												className="text-left truncate flex-1 hover:text-primary hover:underline transition-colors flex items-center gap-1"
																											>
																												<span className="truncate">
																													{(selectedDocumentIds.includes(
																														doc.id,
																													) &&
																														documentFilenames[
																															doc
																																.id
																														]) ||
																														doc.fileName ||
																														`Document ${doc.id}`}
																													{selectedDocumentIds.includes(
																														doc.id,
																													) &&
																														documentFilenames[
																															doc
																																.id
																														] &&
																														documentFilenames[
																															doc
																																.id
																														] !==
																															doc.fileName && (
																															<span className="ml-2 text-xs text-blue-600 font-normal">
																																(custom)
																															</span>
																														)}
																												</span>
																												<ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
																											</button>
																										) : (
																											<span className="truncate flex-1">
																												{(selectedDocumentIds.includes(
																													doc.id,
																												) &&
																													documentFilenames[
																														doc
																															.id
																													]) ||
																													doc.fileName ||
																													`Document ${doc.id}`}
																												{selectedDocumentIds.includes(
																													doc.id,
																												) &&
																													documentFilenames[
																														doc
																															.id
																													] &&
																													documentFilenames[
																														doc
																															.id
																													] !==
																														doc.fileName && (
																														<span className="ml-2 text-xs text-blue-600 font-normal">
																															(custom)
																														</span>
																													)}
																											</span>
																										)}
																										{selectedDocumentIds.includes(
																											doc.id,
																										) && (
																											<Button
																												variant="ghost"
																												size="sm"
																												onClick={(
																													e,
																												) => {
																													e.preventDefault();
																													e.stopPropagation();
																													toggleFilenameEditing(
																														doc.id,
																														doc.fileName ||
																															`Document ${doc.id}`,
																													);
																												}}
																												className="h-6 w-6 p-0 hover:bg-muted ml-1"
																											>
																												<Pencil className="h-3 w-3" />
																											</Button>
																										)}
																									</>
																								)}
																								{doc.fileSize && (
																									<span className="text-xs text-muted-foreground ml-2 flex-shrink-0">
																										{formatBytes(
																											doc.fileSize,
																										)}
																									</span>
																								)}
																							</div>
																						</label>
																					</div>
																				</div>
																			),
																		)}
																	</div>
																</ScrollArea>
															)}

															{documents.length ===
																0 && (
																<div className="text-muted-foreground text-sm py-2">
																	No documents
																	available
																</div>
															)}
														</div>
													</div>
												)}
										</div>
									</div>
								</div>
							</div>

							{/* Email content tabs */}
							<Tabs defaultValue="html" className="w-full">
								<TabsList>
									<TabsTrigger value="html">HTML</TabsTrigger>
									<TabsTrigger value="text">
										Plain Text
									</TabsTrigger>
								</TabsList>

								<TabsContent value="html" className="mt-3">
									<div className="border rounded-md w-full overflow-hidden bg-white">
										<iframe
											srcDoc={emailContent.html}
											className="w-full"
											title="Email preview"
											sandbox="allow-same-origin"
											style={{
												height: "45vh",
												width: "100%",
											}}
										/>
									</div>
								</TabsContent>

								<TabsContent value="text" className="mt-3">
									<div
										className="border rounded-md p-4 w-full overflow-auto bg-muted/20"
										style={{ height: "45vh" }}
									>
										<pre className="text-sm whitespace-pre-wrap">
											{emailContent.text}
										</pre>
									</div>
								</TabsContent>
							</Tabs>
						</div>
					)}
				</ScrollArea>

				<DialogFooter className="px-6 py-4 border-t flex-shrink-0">
					<div className="flex items-center mr-auto text-sm text-muted-foreground">
						{features.documentAttachments &&
							selectedDocumentIds.length > 0 && (
								<span className="flex items-center">
									<Paperclip className="h-3.5 w-3.5 mr-1" />
									{selectedDocumentIds.length} document
									{selectedDocumentIds.length > 1 ? "s" : ""}{" "}
									selected
								</span>
							)}
					</div>

					{features.backButton && onBack && (
						<Button variant="outline" onClick={onBack}>
							Back to PDF
						</Button>
					)}

					<Button variant="outline" onClick={onClose}>
						Cancel
					</Button>

					<Button
						onClick={handleSend}
						disabled={
							isLoading ||
							isSending ||
							isLocalSending ||
							(entityType === "order-confirmation" &&
								!displayRecipientEmail)
						}
					>
						{isSending || isLocalSending ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Sending...
							</>
						) : (
							"Send Email"
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
