"use client";

import { stopBaseSchema } from "@repo/api/src/routes/stops/types";
import { useOrderView } from "@saas/orders/context/order-view-context";
import {
	useOrderStopMutations,
	useOrderStops,
} from "@saas/orders/hooks/use-stops";
import { StopDialog } from "@saas/shared/components/stops/StopDialog";
import { StopType, TimeType } from "@saas/shared/lib/stops/stop-types";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Skeleton } from "@ui/components/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { format } from "date-fns";
import {
	AlertCircle,
	AlertTriangle,
	ArrowLeftRight,
	Box,
	Clock,
	FileText,
	Info,
	MapPin,
	MoreVertical,
	Package,
	Pencil,
	Plus,
	Ruler,
	Trash,
	TruckIcon,
	Zap,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";

interface StopsPanelProps {
	orderId: string;
}

export function StopsPanel({ orderId }: StopsPanelProps) {
	const t = useTranslations();
	const { activeTab, order } = useOrderView();
	const [activeStopType, setActiveStopType] = useState("all");
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [editingStop, setEditingStop] = useState<any | null>(null);
	const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
	const [stopToDelete, setStopToDelete] = useState<string | null>(null);

	// Use the dedicated hook for order stops
	const { data, isLoading, refetch } = useOrderStops(orderId);
	const stops = data?.items || [];

	// Use stop mutations hook
	const {
		createStop,
		updateStop,
		deleteStop,
		isLoading: isMutating,
	} = useOrderStopMutations(orderId, {
		onSuccess: () => {
			setIsDialogOpen(false);
			setEditingStop(null);
			setIsDeleteConfirmOpen(false);
			setStopToDelete(null);
			refetch();
		},
	});

	// Filter stops based on active tab
	const filteredStops =
		activeStopType === "all"
			? stops
			: stops.filter((stop: any) => stop.stopType === activeStopType);

	// Format date for display
	const formatDateTime = (date: string | Date | null | undefined) => {
		if (!date) {
			return t("common.notSet");
		}
		return format(new Date(date), "PPP p");
	};

	// Get stop address as formatted string
	const getStopAddress = (stop: any) => {
		if (stop.address_id && stop.address) {
			return (
				stop.address.formattedAddress ||
				t("app.orders.stops.addressNotAvailable")
			);
		}

		// Fallback to manual address fields
		const addressParts = [];
		if (stop.nameLine) {
			addressParts.push(stop.nameLine);
		}
		if (stop.street) {
			addressParts.push(stop.street);
		}
		if (stop.addressSupplement) {
			addressParts.push(stop.addressSupplement);
		}
		if (stop.zipCode || stop.city) {
			const cityPart = [stop.zipCode, stop.city]
				.filter(Boolean)
				.join(" ");
			if (cityPart) {
				addressParts.push(cityPart);
			}
		}
		if (stop.country) {
			addressParts.push(stop.country);
		}

		return addressParts.length > 0
			? addressParts.join(", ")
			: t("app.orders.stops.addressNotAvailable");
	};

	// Handle opening dialog for a new stop
	const handleAddStop = () => {
		setEditingStop(null);
		setIsDialogOpen(true);
	};

	// Handle opening dialog to edit an existing stop
	const handleEditStop = (stop: any) => {
		setEditingStop(stop);
		setIsDialogOpen(true);
	};

	// Handle confirming deletion of a stop
	const handleConfirmDelete = (id: string) => {
		setStopToDelete(id);
		setIsDeleteConfirmOpen(true);
	};

	// Handle actual deletion of a stop
	const handleDeleteStop = async () => {
		if (!stopToDelete) {
			return;
		}

		try {
			await deleteStop(stopToDelete);
			toast.success("Stop deleted successfully");
		} catch (error) {
			toast.error("Failed to delete stop");
			console.error("Delete stop error:", error);
		}
	};

	// Get the order details to access customerId
	const customerId = order?.customerId;

	// Handle form submission from the dialog
	const handleSubmitStop = async (values: any) => {
		try {
			// Get the values from the correct structure
			const stopValues = values.stop || values;

			// Get organization ID directly from order
			const organizationId = order?.organizationId;

			if (!organizationId) {
				return;
			}

			// Helper to filter out empty values
			const removeEmptyValues = (obj: Record<string, any>) => {
				const result: Record<string, any> = {};

				Object.entries(obj).forEach(([key, value]) => {
					// Skip null, undefined, empty strings, and 0 for numeric fields
					if (
						value === null ||
						value === undefined ||
						value === "" ||
						value === 0
					) {
						return;
					}

					// For numbers, ensure they're actually numbers
					if (typeof value === "number" && Number.isNaN(value)) {
						return;
					}

					// Include the valid value
					result[key] = value;
				});

				return result;
			};

			// Create the stop data with explicit required fields as a new top-level object
			const stopData: Record<string, any> = {
				// Required fields at top level
				organizationId,
				stopType: stopValues.stopType || StopType.LOADING,
				entityType: "order",
				orderId: orderId,
				// Only use DE as default if country is missing completely
				country:
					stopValues.country !== undefined &&
					stopValues.country !== null
						? stopValues.country
						: "DE",
				time_type: stopValues.time_type || TimeType.LATEST,
			};

			// Process date and time fields
			const timeType = stopValues.time_type || TimeType.LATEST;

			// For start datetime (required for FROM, EXACT, FROM_TO)
			if (
				timeType === TimeType.FROM ||
				timeType === TimeType.EXACT ||
				timeType === TimeType.FROM_TO ||
				timeType === TimeType.DAILY ||
				timeType === TimeType.CONTINUOUS
			) {
				// If datetime_start exists directly use it
				if (stopValues.datetime_start) {
					stopData.datetime_start = stopValues.datetime_start;
				}
				// Otherwise try to build it from separate date and time fields
				else if (stopValues.start_date) {
					const startDate = stopValues.start_date;
					const startTime = stopValues.start_time || "00:00";

					// Combine date and time
					const dateObj = new Date(
						`${startDate.split(".").reverse().join("-")}T${startTime}`,
					);
					if (!Number.isNaN(dateObj.getTime())) {
						stopData.datetime_start = dateObj.toISOString();
					}
				}
			}

			// For end datetime (required for LATEST, FROM_TO)
			if (
				timeType === TimeType.LATEST ||
				timeType === TimeType.FROM_TO ||
				timeType === TimeType.DAILY ||
				timeType === TimeType.CONTINUOUS
			) {
				// If datetime_end exists directly use it
				if (stopValues.datetime_end) {
					stopData.datetime_end = stopValues.datetime_end;
				}
				// Otherwise try to build it from separate date and time fields
				else if (stopValues.end_date) {
					const endDate = stopValues.end_date;
					const endTime = stopValues.end_time || "00:00";

					// Combine date and time
					const dateObj = new Date(
						`${endDate.split(".").reverse().join("-")}T${endTime}`,
					);
					if (!Number.isNaN(dateObj.getTime())) {
						stopData.datetime_end = dateObj.toISOString();
					}
				}
			}

			// Add basic fields with values
			const basicFields = removeEmptyValues({
				street: stopValues.street,
				addressSupplement: stopValues.addressSupplement,
				zipCode: stopValues.zipCode,
				city: stopValues.city,
				latitude: stopValues.latitude,
				longitude: stopValues.longitude,
				reference_number: stopValues.reference_number,
				goods_information: stopValues.goods_information,
				driver_notes: stopValues.driver_notes,
				entrance_number: stopValues.entrance_number,
				neutrality_text: stopValues.neutrality_text,
				information_text: stopValues.information_text,
				dangerous_goods_nr: stopValues.dangerous_goods_nr,
				nameLine: stopValues.nameLine,
				// Add the missing goods-related fields
				loading_meter: stopValues.loading_meter,
				cubic_meters: stopValues.cubic_meters,
				weight: stopValues.weight,
			});

			// Add measurement fields if they exist
			if (stopValues.has_measurements) {
				const measurementFields = removeEmptyValues({
					length: stopValues.length,
					width: stopValues.width,
					height: stopValues.height,
					units: stopValues.units,
					measurement_text: stopValues.measurement_text,
				});

				// Only include fields if there's at least one valid measurement field
				if (Object.keys(measurementFields).length > 0) {
					Object.assign(stopData, measurementFields);
				}
			}

			// Add pallet exchange fields if they exist
			if (stopValues.has_pallet_exchange) {
				const palletFields = removeEmptyValues({
					pallet_exchange_type: stopValues.pallet_exchange_type,
					pallet_exchange_count: stopValues.pallet_exchange_count,
				});

				// Only include fields if there's at least one valid pallet field
				if (Object.keys(palletFields).length > 0) {
					Object.assign(stopData, palletFields);
				}
			}

			// Add special transport fields if they exist
			if (stopValues.has_special_transport) {
				const specialTransportFields = removeEmptyValues({
					special_transport_description:
						stopValues.special_transport_description,
					special_transports_heavy:
						stopValues.special_transports_heavy,
					crane_loading: stopValues.crane_loading,
					crane_unloading: stopValues.crane_unloading,
					loading_equipment_exchange:
						stopValues.loading_equipment_exchange,
					special_transports_height:
						stopValues.special_transports_height,
					special_transports_width:
						stopValues.special_transports_width,
					special_transports_length:
						stopValues.special_transports_length,
				});

				// Only include fields if there's at least one valid special transport field
				if (Object.keys(specialTransportFields).length > 0) {
					Object.assign(stopData, specialTransportFields);
				}
			}

			// Add all basic fields
			Object.assign(stopData, basicFields);

			if (editingStop) {
				// Ensure id is included for update
				await updateStop({
					id: editingStop.id,
					...stopData,
				});
				toast.success("Stop updated successfully");
			} else {
				await createStop(stopData);
				toast.success("Stop created successfully");
			}
		} catch (error) {
			console.error("Error saving stop:", error);
			toast.error("Failed to save stop");
			throw error;
		}
	};

	return (
		<>
			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<div>
						<CardTitle>{t("app.orders.stops.title")}</CardTitle>
						<CardDescription>
							{t("app.orders.stops.description")}
						</CardDescription>
					</div>
					<Button
						onClick={handleAddStop}
						size="sm"
						className="ml-auto"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Stop
					</Button>
				</CardHeader>
				<CardContent>
					<Tabs
						value={activeStopType}
						onValueChange={setActiveStopType}
					>
						<TabsList className="mb-4">
							<TabsTrigger value="all">
								{t("app.orders.stops.tabs.all")}
							</TabsTrigger>
							<TabsTrigger value="loading">
								{t("app.orders.stops.tabs.loading")}
							</TabsTrigger>
							<TabsTrigger value="unloading">
								{t("app.orders.stops.tabs.unloading")}
							</TabsTrigger>
						</TabsList>

						<TabsContent value={activeStopType} className="mt-0">
							{isLoading ? (
								<div className="space-y-4">
									{Array.from({ length: 3 }).map(
										(_, index) => (
											<div
												key={index}
												className="border rounded-lg p-4 relative"
											>
												<div className="flex items-center mb-2">
													<Skeleton className="h-8 w-8 rounded-full mr-3" />
													<div className="w-full">
														<Skeleton className="h-5 w-1/3 mb-2" />
														<Skeleton className="h-4 w-2/3" />
													</div>
												</div>

												<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
													<div>
														<Skeleton className="h-4 w-1/4 mb-2" />
														<Skeleton className="h-5 w-3/4" />
													</div>
													<div>
														<Skeleton className="h-4 w-1/4 mb-2" />
														<Skeleton className="h-5 w-1/2" />
													</div>
												</div>
											</div>
										),
									)}
								</div>
							) : filteredStops.length === 0 ? (
								<div className="text-center py-6 text-muted-foreground">
									{t("app.orders.stops.noStops")}
								</div>
							) : (
								<div className="space-y-4">
									{filteredStops.map(
										(stop: any, index: number) => (
											<div
												key={stop.id}
												className="border rounded-lg p-4 relative overflow-hidden"
											>
												{/* Status indicator bar */}
												<div
													className={`absolute top-0 left-0 w-1 h-full ${
														stop.stopType ===
														"loading"
															? "bg-blue-500 dark:bg-blue-600"
															: "bg-green-500 dark:bg-green-600"
													}`}
												/>

												{/* Action menu */}
												<div className="absolute top-2 right-2 flex items-center">
													<DropdownMenu>
														<DropdownMenuTrigger
															asChild
														>
															<Button
																variant="ghost"
																size="icon"
																className="h-8 w-8"
															>
																<MoreVertical className="h-4 w-4" />
																<span className="sr-only">
																	Actions
																</span>
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align="end">
															<DropdownMenuItem
																onClick={() =>
																	handleEditStop(
																		stop,
																	)
																}
															>
																<Pencil className="h-4 w-4 mr-2" />
																Edit
															</DropdownMenuItem>
															<DropdownMenuItem
																className="text-destructive"
																onClick={() =>
																	handleConfirmDelete(
																		stop.id,
																	)
																}
															>
																<Trash className="h-4 w-4 mr-2" />
																Delete
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</div>

												{/* Header with type and address */}
												<div className="flex items-center mb-4 pl-2">
													<div
														className={`${
															stop.stopType ===
															"loading"
																? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
																: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
														} p-2 rounded-full mr-3`}
													>
														{stop.stopType ===
														"loading" ? (
															<TruckIcon className="h-5 w-5" />
														) : (
															<MapPin className="h-5 w-5" />
														)}
													</div>
													<div className="flex-1">
														<h3 className="font-semibold text-base flex items-center">
															{stop.stopType ===
															"loading"
																? t(
																		"app.orders.stops.loadingPoint",
																		{
																			number:
																				index +
																				1,
																		},
																	)
																: t(
																		"app.orders.stops.unloadingPoint",
																		{
																			number:
																				index +
																				1,
																		},
																	)}
															{stop.reference_number && (
																<span className="ml-2 text-sm text-muted-foreground">
																	(Ref:{" "}
																	{
																		stop.reference_number
																	}
																	)
																</span>
															)}
														</h3>
														<p className="text-sm text-muted-foreground">
															{getStopAddress(
																stop,
															)}
														</p>
													</div>
												</div>

												{/* Main content grid */}
												<div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3 pl-2">
													{/* Date and Time */}
													<div className="space-y-1">
														<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
															<Clock className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
															{t(
																"app.orders.stops.fields.time",
															)}
														</h4>
														<p className="text-sm">
															{stop.time_type ===
																"from-to" &&
															stop.datetime_start &&
															stop.datetime_end ? (
																<>
																	<span className="font-medium">
																		From:
																	</span>{" "}
																	{formatDateTime(
																		stop.datetime_start,
																	)}
																	<br />
																	<span className="font-medium">
																		To:
																	</span>{" "}
																	{formatDateTime(
																		stop.datetime_end,
																	)}
																</>
															) : stop.time_type ===
																	"latest" &&
																stop.datetime_end ? (
																<>
																	<span className="font-medium">
																		Latest
																		by:
																	</span>{" "}
																	{formatDateTime(
																		stop.datetime_end,
																	)}
																</>
															) : stop.time_type ===
																	"from" &&
																stop.datetime_start ? (
																<>
																	<span className="font-medium">
																		From:
																	</span>{" "}
																	{formatDateTime(
																		stop.datetime_start,
																	)}
																</>
															) : stop.time_type ===
																	"exact" &&
																stop.datetime_start ? (
																<>
																	<span className="font-medium">
																		Exactly
																		at:
																	</span>{" "}
																	{formatDateTime(
																		stop.datetime_start,
																	)}
																</>
															) : (
																t(
																	"common.notSet",
																)
															)}
														</p>
													</div>

													{/* Special Transport Indicators */}
													{(stop.special_transports_heavy ||
														stop.crane_loading ||
														stop.crane_unloading ||
														stop.loading_equipment_exchange) && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Zap className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Special
																Requirements
															</h4>
															<div className="flex flex-wrap gap-1">
																{stop.special_transports_heavy && (
																	<span className="inline-flex items-center rounded-md bg-amber-50 px-2 py-1 text-xs font-medium text-amber-800 ring-1 ring-inset ring-amber-600/20 dark:bg-amber-900/30 dark:text-amber-300 dark:ring-amber-400/30">
																		Heavy
																		Transport
																	</span>
																)}
																{stop.crane_loading && (
																	<span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-800 ring-1 ring-inset ring-blue-600/20 dark:bg-blue-900/30 dark:text-blue-300 dark:ring-blue-400/30">
																		Crane
																		Loading
																	</span>
																)}
																{stop.crane_unloading && (
																	<span className="inline-flex items-center rounded-md bg-indigo-50 px-2 py-1 text-xs font-medium text-indigo-800 ring-1 ring-inset ring-indigo-600/20 dark:bg-indigo-900/30 dark:text-indigo-300 dark:ring-indigo-400/30">
																		Crane
																		Unloading
																	</span>
																)}
																{stop.loading_equipment_exchange && (
																	<span className="inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-800 ring-1 ring-inset ring-purple-600/20 dark:bg-purple-900/30 dark:text-purple-300 dark:ring-purple-400/30">
																		Equipment
																		Exchange
																	</span>
																)}
															</div>
														</div>
													)}

													{/* Dimensions and Measurements */}
													{(stop.special_transports_length ||
														stop.special_transports_width ||
														stop.special_transports_height ||
														stop.length ||
														stop.width ||
														stop.height) && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Ruler className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Dimensions
															</h4>
															<div className="text-xs space-y-1">
																{/* Regular measurements */}
																{(stop.length ||
																	stop.width ||
																	stop.height) && (
																	<div className="space-y-1">
																		<p className="font-medium text-xs text-gray-500 dark:text-gray-400">
																			Standard:
																		</p>
																		<div className="grid grid-cols-3 gap-1">
																			{stop.length && (
																				<div className="text-xs">
																					L:{" "}
																					{
																						stop.length
																					}{" "}
																					{stop.units ||
																						""}
																				</div>
																			)}
																			{stop.width && (
																				<div className="text-xs">
																					W:{" "}
																					{
																						stop.width
																					}{" "}
																					{stop.units ||
																						""}
																				</div>
																			)}
																			{stop.height && (
																				<div className="text-xs">
																					H:{" "}
																					{
																						stop.height
																					}{" "}
																					{stop.units ||
																						""}
																				</div>
																			)}
																		</div>
																	</div>
																)}

																{/* Special transport measurements */}
																{(stop.special_transports_length ||
																	stop.special_transports_width ||
																	stop.special_transports_height) && (
																	<div className="space-y-1">
																		<p className="font-medium text-xs text-gray-500 dark:text-gray-400">
																			Special
																			Transport:
																		</p>
																		<div className="grid grid-cols-3 gap-1">
																			{stop.special_transports_length && (
																				<div className="text-xs">
																					L:{" "}
																					{
																						stop.special_transports_length
																					}{" "}
																					m
																				</div>
																			)}
																			{stop.special_transports_width && (
																				<div className="text-xs">
																					W:{" "}
																					{
																						stop.special_transports_width
																					}{" "}
																					m
																				</div>
																			)}
																			{stop.special_transports_height && (
																				<div className="text-xs">
																					H:{" "}
																					{
																						stop.special_transports_height
																					}{" "}
																					m
																				</div>
																			)}
																		</div>
																	</div>
																)}
															</div>
														</div>
													)}

													{/* Pallet Exchange */}
													{stop.pallet_exchange_type && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Package className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Pallet Exchange
															</h4>
															<div className="text-sm">
																<span className="font-medium capitalize">
																	Type:
																</span>{" "}
																{
																	stop.pallet_exchange_type
																}
																{stop.pallet_exchange_count && (
																	<span className="ml-3">
																		<span className="font-medium">
																			Count:
																		</span>{" "}
																		{
																			stop.pallet_exchange_count
																		}
																	</span>
																)}
															</div>
														</div>
													)}

													{/* Goods Metrics */}
													{(stop.loading_meter ||
														stop.cubic_meters ||
														stop.weight) && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Box className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Goods Metrics
															</h4>
															<div className="text-xs space-y-1">
																<div className="grid grid-cols-3 gap-1">
																	{stop.loading_meter && (
																		<div className="text-xs">
																			LDM:{" "}
																			{
																				stop.loading_meter
																			}
																		</div>
																	)}
																	{stop.cubic_meters && (
																		<div className="text-xs">
																			Vol:{" "}
																			{
																				stop.cubic_meters
																			}{" "}
																			m³
																		</div>
																	)}
																	{stop.weight && (
																		<div className="text-xs">
																			Wt:{" "}
																			{
																				stop.weight
																			}{" "}
																			kg
																		</div>
																	)}
																</div>
															</div>
														</div>
													)}

													{/* Dangerous Goods */}
													{stop.dangerous_goods_nr && (
														<div className="mt-4 pl-2">
															<div className="space-y-1">
																<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																	<AlertTriangle className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																	Dangerous
																	Goods Number
																	(ADR)
																</h4>
																<div className="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-sm font-medium text-red-700 ring-1 ring-inset ring-red-600/20 dark:bg-red-900/30 dark:text-red-300 dark:ring-red-500/30">
																	{
																		stop.dangerous_goods_nr
																	}
																</div>
															</div>
														</div>
													)}

													{/* Coordinates - show when available */}
													{stop.latitude &&
														stop.longitude && (
															<div className="mt-4 pl-2">
																<div className="space-y-1">
																	<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																		<MapPin className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																		GPS
																		Coordinates
																	</h4>
																	<div className="text-xs text-muted-foreground">
																		Lat:{" "}
																		{stop.latitude.toFixed(
																			6,
																		)}
																		, Lng:{" "}
																		{stop.longitude.toFixed(
																			6,
																		)}
																	</div>
																</div>
															</div>
														)}
												</div>

												{/* Notes Section */}
												<div className="mt-4 space-y-3 pl-2">
													{stop.neutrality_text && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<ArrowLeftRight className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Neutrality Text
															</h4>
															<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
																{
																	stop.neutrality_text
																}
															</p>
														</div>
													)}

													{stop.goods_information && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Box className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																{t(
																	"app.orders.stops.fields.goodsInformation",
																)}
															</h4>
															<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
																{
																	stop.goods_information
																}
															</p>
														</div>
													)}

													{stop.information_text && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<Info className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																Information Text
															</h4>
															<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
																{
																	stop.information_text
																}
															</p>
														</div>
													)}

													{stop.driver_notes && (
														<div className="space-y-1">
															<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
																<FileText className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
																{t(
																	"app.orders.stops.fields.driverNotes",
																)}
															</h4>
															<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
																{
																	stop.driver_notes
																}
															</p>
														</div>
													)}
												</div>
											</div>
										),
									)}
								</div>
							)}
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>

			{/* Stop Dialog using the shared component */}
			<StopDialog
				isOpen={isDialogOpen}
				onClose={() => setIsDialogOpen(false)}
				onSubmit={handleSubmitStop}
				initialValues={
					editingStop
						? (() => {
								// Helper function to only include fields with actual values
								const hasValue = (value: any) =>
									value !== null &&
									value !== undefined &&
									value !== "";

								// Start with required fields
								const initialValues: Record<string, any> = {
									stopType:
										editingStop.stopType ||
										StopType.LOADING,
									time_type:
										editingStop.time_type ||
										TimeType.LATEST,
									country: editingStop.country || "DE",
								};

								// Get all fields from the schema
								const allSchemaFields = Object.keys(
									stopBaseSchema.shape,
								);

								// Define field categories
								const basicFields = [
									"street",
									"addressSupplement",
									"zipCode",
									"city",
									"latitude",
									"longitude",
									"entrance_number",
									"reference_number",
									"goods_information",
									"driver_notes",
									"neutrality_text",
									"information_text",
									"dangerous_goods_nr",
									"loading_meter",
									"cubic_meters",
									"weight",
									"nameLine",
								];

								const measurementFields = [
									"length",
									"width",
									"height",
									"units",
									"measurement_text",
								];

								const palletFields = [
									"pallet_exchange_type",
									"pallet_exchange_count",
								];

								const specialTransportFields = [
									"special_transport_description",
									"special_transports_heavy",
									"crane_loading",
									"crane_unloading",
									"loading_equipment_exchange",
									"special_transports_height",
									"special_transports_width",
									"special_transports_length",
								];

								// Process basic fields in one go
								basicFields.forEach((field) => {
									if (hasValue(editingStop[field])) {
										initialValues[field] =
											editingStop[field];
									}
								});

								// Add date fields if they exist
								if (editingStop.datetime_start) {
									initialValues.datetime_start = new Date(
										editingStop.datetime_start,
									);
								}
								if (editingStop.datetime_end) {
									initialValues.datetime_end = new Date(
										editingStop.datetime_end,
									);
								}

								// Check and process measurement fields
								const hasMeasurements = measurementFields.some(
									(field) => hasValue(editingStop[field]),
								);

								if (hasMeasurements) {
									initialValues.has_measurements = true;
									measurementFields.forEach((field) => {
										if (hasValue(editingStop[field])) {
											initialValues[field] =
												editingStop[field];
										}
									});
								}

								// Check and process pallet exchange fields
								const hasPalletExchange = palletFields.some(
									(field) => hasValue(editingStop[field]),
								);

								if (hasPalletExchange) {
									initialValues.has_pallet_exchange = true;
									palletFields.forEach((field) => {
										if (hasValue(editingStop[field])) {
											initialValues[field] =
												editingStop[field];
										}
									});
								}

								// Check and process special transport fields
								const hasSpecialTransport =
									specialTransportFields.some((field) =>
										hasValue(editingStop[field]),
									);

								if (hasSpecialTransport) {
									initialValues.has_special_transport = true;
									specialTransportFields.forEach((field) => {
										if (hasValue(editingStop[field])) {
											initialValues[field] =
												editingStop[field];
										}
									});
								}

								return initialValues;
							})()
						: {
								stopType: StopType.LOADING,
								time_type: TimeType.LATEST,
								country: "DE",
							}
				}
				counterpartyId={customerId}
				title={editingStop ? "Edit Stop" : "Create New Stop"}
				description={
					editingStop
						? "Update the details for this stop point"
						: "Add a new stop for pickup or delivery"
				}
				formProps={{
					showSections: {
						basic: true,
						address: true,
						time: true,
						goods: true,
						additionalInfo: true,
						measurements: true,
						palletExchange: true,
						specialTransport: true,
					},
					fieldPrefix: "stop",
				}}
			/>

			{/* Delete Confirmation Dialog */}
			<Dialog
				open={isDeleteConfirmOpen}
				onOpenChange={setIsDeleteConfirmOpen}
			>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Confirm Deletion</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete this stop? This
							action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<div className="flex items-center gap-2 py-3">
						<AlertCircle className="h-6 w-6 text-destructive" />
						<p className="text-sm">
							All data associated with this stop will be
							permanently removed.
						</p>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsDeleteConfirmOpen(false)}
						>
							Cancel
						</Button>
						<Button variant="error" onClick={handleDeleteStop}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
