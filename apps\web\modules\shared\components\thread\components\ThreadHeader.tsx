"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { formatDistanceToNow } from "date-fns";
import { Clock, MoreVertical, Pin, User } from "lucide-react";
import { toast } from "sonner";
import { useThreadMutations } from "../hooks/use-threads";
import { StatusBadge } from "./StatusBadge";

interface ThreadHeaderProps {
	thread: any; // TODO: Type this properly
	entityTitle?: string; // Optional title for better display (e.g., order_number)
}

function formatTimeAgo(dateString: string) {
	return formatDistanceToNow(new Date(dateString), { addSuffix: true });
}

export function ThreadHeader({ thread, entityTitle }: ThreadHeaderProps) {
	const { updateThread } = useThreadMutations({
		onSuccess: () => {
			// Success toast is handled in the mutation hook
		},
	});

	const handleThreadReact = async (emoji: string) => {
		// TODO: Implement thread reactions
		console.log(`Reacting to thread with ${emoji}`);
		toast.info("Thread reactions coming soon!");
	};

	const handleStatusChange = async (
		newStatus: "ACTIVE" | "RESOLVED" | "ARCHIVED",
	) => {
		try {
			await updateThread({ id: thread.id, status: newStatus });
		} catch (error) {
			console.error("Failed to update thread status:", error);
		}
	};

	const handlePinToggle = async () => {
		try {
			await updateThread({
				id: thread.id,
				isPinned: !thread.isPinned,
			});
		} catch (error) {
			console.error("Failed to toggle thread pin:", error);
		}
	};

	return (
		<div className="space-y-4">
			{/* Status and actions row */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<StatusBadge status={thread.status} />
					<Badge status="info" className="text-xs">
						{thread.entityType} #{entityTitle || thread.entityId}
					</Badge>
					{thread.isPinned && (
						<Badge status="warning" className="text-xs">
							<Pin className="w-3 h-3" />
							Pinned
						</Badge>
					)}
				</div>

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="ghost"
							size="sm"
							className="h-8 w-8 p-0"
						>
							<MoreVertical className="w-4 h-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuItem>Edit Thread</DropdownMenuItem>
						<DropdownMenuItem onClick={handlePinToggle}>
							{thread.isPinned ? "Unpin" : "Pin"} Thread
						</DropdownMenuItem>
						<DropdownMenuSeparator />
						{thread.status !== "RESOLVED" && (
							<DropdownMenuItem
								onClick={() => handleStatusChange("RESOLVED")}
							>
								Mark as Resolved
							</DropdownMenuItem>
						)}
						{thread.status !== "ARCHIVED" && (
							<DropdownMenuItem
								onClick={() => handleStatusChange("ARCHIVED")}
							>
								Archive Thread
							</DropdownMenuItem>
						)}
						{thread.status !== "ACTIVE" && (
							<DropdownMenuItem
								onClick={() => handleStatusChange("ACTIVE")}
							>
								Reopen Thread
							</DropdownMenuItem>
						)}
					</DropdownMenuContent>
				</DropdownMenu>
			</div>

			{/* Title and description */}
			<div>
				<h1 className="text-lg font-semibold text-foreground mb-2">
					{thread.title}
				</h1>
				{thread.description && (
					<p className="text-sm text-muted-foreground leading-relaxed">
						{thread.description}
					</p>
				)}
			</div>

			{/* Author and metadata */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<Avatar className="h-8 w-8">
						<AvatarImage
							src={
								thread.author?.profileImage ||
								thread.author?.avatar
							}
							alt={thread.author?.name}
						/>
						<AvatarFallback className="text-xs">
							{thread.author?.name
								?.split(" ")
								.map((n: string) => n[0])
								.join("")
								.toUpperCase() || "U"}
						</AvatarFallback>
					</Avatar>
					<div className="text-sm">
						<div className="font-medium text-foreground">
							{thread.author?.name || "Unknown User"}
						</div>
						<div className="text-muted-foreground flex items-center gap-1">
							<Clock className="w-3 h-3" />
							{formatTimeAgo(thread.createdAt)}
						</div>
					</div>
				</div>

				<div className="flex items-center gap-4 text-sm text-muted-foreground">
					<div className="flex items-center gap-1">
						<User className="w-4 h-4" />
						<span>Created by {thread.author?.name}</span>
					</div>
				</div>
			</div>
		</div>
	);
}
