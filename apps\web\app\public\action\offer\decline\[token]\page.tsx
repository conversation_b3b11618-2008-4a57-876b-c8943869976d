"use client";

import { But<PERSON> } from "@ui/components/button";
import { CheckCircle, XCircle } from "lucide-react";
import { useEffect, useState } from "react";
import DeclineForm from "../components/DeclineForm";

interface DeclinePageProps {
	params: Promise<{
		token: string;
	}>;
	searchParams: Promise<Record<string, string | string[] | undefined>>;
}

type PageStatus = "form" | "success" | "error";

export default function DeclineOfferPage({ params }: DeclinePageProps) {
	const [token, setToken] = useState<string>("");
	const [status, setStatus] = useState<PageStatus>("form");
	const [message, setMessage] = useState<string>("");
	const [offerId, setOfferId] = useState<string | null>(null);
	const [mounted, setMounted] = useState(false);

	// Only set mounted after hydration is complete
	useEffect(() => {
		setMounted(true);
	}, []);

	// Set token after mounting
	useEffect(() => {
		if (!mounted) {
			return;
		}

		const getToken = async () => {
			try {
				const resolvedParams = await params;
				setToken(resolvedParams.token);
			} catch (error) {
				console.error("Error resolving params:", error);
				setStatus("error");
				setMessage("Failed to load the necessary parameters.");
			}
		};

		getToken();
	}, [params, mounted]);

	const handleSuccess = (data: {
		success: boolean;
		message: string;
		offerId?: string;
	}) => {
		setStatus("success");
		setMessage(data.message || "Offer has been declined successfully.");
		if (data.offerId) {
			setOfferId(data.offerId);
		}
	};

	const handleError = (errorMessage: string) => {
		setStatus("error");
		setMessage(errorMessage);
	};

	// Option to decline without reason directly through GET endpoint
	const handleSimpleDecline = async () => {
		if (!token) {
			handleError("Token is not available. Please try again later.");
			return;
		}

		try {
			const response = await fetch(
				`/api/public/action/offer/decline/${token}`,
				{
					method: "GET",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);

			const data = await response.json();

			if (response.ok && data.success) {
				handleSuccess(data);
			} else {
				handleError(
					data.error?.message ||
						"Failed to decline the offer. Please try again or contact support.",
				);
			}
		} catch (error) {
			console.error("Error declining offer:", error);
			handleError(
				"An unexpected error occurred. Please try again later or contact support.",
			);
		}
	};

	if (!mounted) {
		return (
			<>
				<h1 className="text-xl font-semibold mb-2 text-center">
					Decline Offer
				</h1>
				<div className="flex flex-col items-center justify-center py-6">
					<div className="h-6 w-64 bg-gray-200 rounded animate-pulse mb-4" />
					<div className="h-32 w-full bg-gray-200 rounded animate-pulse" />
				</div>
			</>
		);
	}

	return (
		<>
			<h1 className="text-xl font-semibold mb-2 text-center">
				Decline Offer
			</h1>

			{status === "form" ? (
				<div>
					<p className="text-gray-600 mb-6 text-center">
						We're sorry to hear you'd like to decline this offer. If
						possible, please let us know the reason so we can
						improve our service.
					</p>
					{token ? (
						<>
							<DeclineForm
								token={token}
								onSuccess={handleSuccess}
								onError={handleError}
							/>
							<div className="mt-6 text-center">
								<Button
									type="button"
									onClick={handleSimpleDecline}
									variant="ghost"
									className="text-sm text-gray-500 hover:text-gray-700 underline"
								>
									Decline without providing a reason
								</Button>
							</div>
						</>
					) : (
						<div className="flex justify-center">
							<div className="h-12 w-12 rounded-full border-4 border-primary border-t-transparent animate-spin" />
						</div>
					)}
				</div>
			) : status === "success" ? (
				<div className="flex flex-col items-center justify-center py-6 text-center">
					<CheckCircle className="h-16 w-16 text-green-500 mb-4" />
					<p className="text-lg font-medium text-gray-700">
						Thank you for your response
					</p>
					<p className="mt-2 text-gray-600">{message}</p>
					{offerId && (
						<p className="mt-2 text-sm text-gray-500">
							Offer ID: {offerId}
						</p>
					)}
				</div>
			) : (
				<div className="flex flex-col items-center justify-center py-6 text-center">
					<XCircle className="h-16 w-16 text-red-500 mb-4" />
					<p className="text-lg font-medium text-red-700">
						Something went wrong
					</p>
					<p className="mt-2 text-gray-600">{message}</p>
					<Button
						type="button"
						onClick={() => setStatus("form")}
						variant="link"
						className="mt-4 text-primary hover:text-primary-dark"
					>
						Try again
					</Button>
				</div>
			)}
		</>
	);
}
