"use client";

import {
	transformExpenseAllocations,
	useOrderAllocatedExpenses,
} from "@saas/orders/hooks/use-allocated-expenses";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import { FileText } from "lucide-react";
import { useMemo } from "react";

interface AllocatedExpensesTableProps {
	orderId: string;
}

export function AllocatedExpensesTable({
	orderId,
}: AllocatedExpensesTableProps) {
	const { data: expensesData, isLoading } =
		useOrderAllocatedExpenses(orderId);

	// Transform the data to show allocated amounts
	const allocatedLineItems = useMemo(() => {
		if (!expensesData?.items) {
			return [];
		}
		return transformExpenseAllocations(expensesData.items, orderId);
	}, [expensesData?.items, orderId]);

	const formatCurrency = (value: number, currency = "EUR") => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(value);
	};

	const getMethodBadge = (method: "fixed" | "percentage", value: number) => {
		if (method === "fixed") {
			return (
				<Badge status="info" className="text-xs">
					Fixed
				</Badge>
			);
		}
		return (
			<Badge status="warning" className="text-xs">
				{value}%
			</Badge>
		);
	};

	const handleFileClick = (fileUrl: string) => {
		// Open the file in a new tab
		window.open(fileUrl, "_blank", "noopener,noreferrer");
	};

	// Calculate total allocated amount
	const totalAllocated = allocatedLineItems.reduce(
		(sum, item) => sum + item.allocatedAmount,
		0,
	);

	const currency = allocatedLineItems[0]?.currency || "EUR";

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-sm text-muted-foreground">
					Loading allocated expenses...
				</div>
			</div>
		);
	}

	if (allocatedLineItems.length === 0) {
		return (
			<div className="flex items-center justify-center py-8">
				<div className="text-center text-sm text-muted-foreground">
					<p>No expenses allocated to this order</p>
					<p className="text-xs mt-1">
						Expenses will appear here when allocated to this order
					</p>
				</div>
			</div>
		);
	}

	return (
		<TooltipProvider>
			<div className="space-y-4">
				<div className="border rounded-lg overflow-hidden">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead className="w-[120px]">
									Date
								</TableHead>
								<TableHead className="w-[150px]">
									Supplier
								</TableHead>
								<TableHead className="w-[120px]">
									Invoice #
								</TableHead>
								<TableHead className="w-[250px]">
									Description
								</TableHead>
								<TableHead className="w-[100px]">
									Method
								</TableHead>
								<TableHead className="w-[120px]">
									Allocated
								</TableHead>
								<TableHead className="w-[150px]">
									Category
								</TableHead>
								<TableHead className="w-[80px]">File</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{allocatedLineItems.map((item) => (
								<TableRow key={item.id}>
									<TableCell>
										<span className="text-sm">
											{format(
												item.expenseDate,
												"dd.MM.yyyy",
											)}
										</span>
									</TableCell>
									<TableCell>
										<span className="text-sm font-medium">
											{item.supplier}
										</span>
									</TableCell>
									<TableCell>
										<span className="text-sm">
											{item.supplierInvoiceNumber || "-"}
										</span>
									</TableCell>
									<TableCell>
										<div>
											<span className="text-sm font-medium">
												{item.description}
											</span>
											<div className="text-xs text-muted-foreground mt-1">
												Total:{" "}
												{formatCurrency(
													item.totalLineItemPrice,
													item.currency,
												)}
											</div>
										</div>
									</TableCell>
									<TableCell>
										{getMethodBadge(
											item.allocationMethod,
											item.allocationValue,
										)}
									</TableCell>
									<TableCell>
										<span className="text-sm font-medium">
											{formatCurrency(
												item.allocatedAmount,
												item.currency,
											)}
										</span>
									</TableCell>
									<TableCell>
										<span className="text-sm">
											{item.category || "-"}
										</span>
									</TableCell>
									<TableCell>
										{item.supplierDocumentUrl ? (
											<Tooltip>
												<TooltipTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 p-0 hover:bg-muted"
														onClick={() =>
															handleFileClick(
																item.supplierDocumentUrl!,
															)
														}
													>
														<FileText className="h-4 w-4 text-muted-foreground hover:text-foreground" />
													</Button>
												</TooltipTrigger>
												<TooltipContent>
													<p>View uploaded invoice</p>
												</TooltipContent>
											</Tooltip>
										) : (
											<span className="text-xs text-muted-foreground">
												-
											</span>
										)}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>

				{/* Total Summary */}
				<div className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
					<span className="text-sm font-medium">
						Total Allocated Expenses
					</span>
					<span className="text-sm font-bold">
						{formatCurrency(totalAllocated, currency)}
					</span>
				</div>
			</div>
		</TooltipProvider>
	);
}
