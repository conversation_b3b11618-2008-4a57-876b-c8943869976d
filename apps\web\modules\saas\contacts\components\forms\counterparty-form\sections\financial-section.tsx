"use client";

import type { CreateCounterpartyInput } from "@repo/api/src/routes/counterparties/types";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { CurrencySelect } from "@ui/components/currency-select";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { PaymentTermSelector } from "@ui/components/payment-term-selector";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { VatTermSelector } from "@ui/components/vat-term-selector";
import { CreditCard, FileText, HelpCircle, Receipt } from "lucide-react";
import { useEffect } from "react";
import { useForm<PERSON>ontex<PERSON>, useWatch } from "react-hook-form";

// Define the types for VAT and payment terms
export interface VatTermType {
	id: string;
	name: string;
	code: string;
	isDefault?: boolean;
	rate: string | number;
	description?: string;
	country_code?: string;
	isActive?: boolean;
}

export interface PaymentTermType {
	id: string;
	name: string;
	code: string;
	isDefault?: boolean;
	daysToPayment: number;
	description?: string;
	isActive?: boolean;
}

export interface FinancialSectionProps {
	vatTerms?: VatTermType[];
	paymentTerms?: PaymentTermType[];
	isLoadingVatTerms?: boolean;
	isLoadingPaymentTerms?: boolean;
}

// Map of currency codes to their symbols
const CURRENCY_SYMBOLS: Record<string, string> = {
	EUR: "€",
	USD: "$",
	GBP: "£",
	JPY: "¥",
	AUD: "A$",
	CAD: "C$",
	CHF: "Fr",
	CNY: "¥",
	HKD: "HK$",
	NZD: "NZ$",
	SEK: "kr",
	KRW: "₩",
	SGD: "S$",
	NOK: "kr",
	MXN: "$",
	INR: "₹",
	RUB: "₽",
	ZAR: "R",
	TRY: "₺",
	BRL: "R$",
	TWD: "NT$",
	DKK: "kr",
	PLN: "zł",
	THB: "฿",
	IDR: "Rp",
	HUF: "Ft",
	CZK: "Kč",
	ILS: "₪",
	CLP: "$",
	PHP: "₱",
	AED: "د.إ",
	COP: "$",
	SAR: "﷼",
	MYR: "RM",
	RON: "lei",
};

export function FinancialSection({
	vatTerms = [],
	paymentTerms = [],
	isLoadingVatTerms = false,
	isLoadingPaymentTerms = false,
}: FinancialSectionProps) {
	const form = useFormContext<
		CreateCounterpartyInput & {
			financialProfile: {
				vatTermId?: string;
				paymentTermId?: string;
				invoiceType?: string;
				creditNotesEmail?: string;
				currency?: string;
				creditLimit?: string;
				balance?: string;
				invoiceInfo?: string;
				taxNotes?: string;
				invoiceEmail?: string;
				requiredDocuments?: string;
			};
		}
	>();

	// Watch the email field from BusinessSection
	const counterpartyEmail = useWatch({
		control: form.control,
		name: "email",
	});

	// Watch the selected currency
	const selectedCurrency =
		useWatch({
			control: form.control,
			name: "financialProfile.currency",
		}) || "EUR";

	// Update email fields when counterpartyEmail changes
	useEffect(() => {
		if (counterpartyEmail) {
			// Only set if the fields are empty or haven't been touched
			if (!form.getValues("financialProfile.creditNotesEmail")) {
				form.setValue(
					"financialProfile.creditNotesEmail",
					counterpartyEmail,
				);
			}
			if (!form.getValues("financialProfile.invoiceEmail")) {
				form.setValue(
					"financialProfile.invoiceEmail",
					counterpartyEmail,
				);
			}
		}
	}, [counterpartyEmail, form]);

	// Set default currency to EUR if not already set
	useEffect(() => {
		const currentCurrency = form.getValues("financialProfile.currency");
		if (!currentCurrency) {
			form.setValue("financialProfile.currency", "EUR");
		}
	}, [form]);

	// Get currency symbol for the selected currency
	const getCurrencySymbol = (currencyCode: string): string => {
		return CURRENCY_SYMBOLS[currencyCode] || currencyCode;
	};

	// Format value with currency symbol
	const formatWithCurrency = (value: string): string => {
		if (!value) {
			return "";
		}
		// Remove any non-digit characters
		const numericValue = value.replace(/[^\d]/g, "");
		if (!numericValue) {
			return "";
		}

		// Add currency symbol
		return `${getCurrencySymbol(selectedCurrency)} ${numericValue}`;
	};

	// Parse value to remove currency symbol
	const parseFromCurrency = (value: string): string => {
		if (!value) {
			return "";
		}
		// Remove any non-digit characters
		return value.replace(/[^\d]/g, "");
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Financial Information</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				{/* Invoice Settings */}
				<div className="space-y-4">
					<div className="flex items-center gap-2 mb-2">
						<Receipt className="h-4 w-4 text-primary" />
						<h3 className="text-sm font-medium text-foreground">
							Invoice Settings
						</h3>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="financialProfile.invoiceType"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Default Invoice Type
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>
														Type of invoice to be
														used by default for new
														invoices
													</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={
											field.value
												? String(field.value)
												: undefined
										}
									>
										<FormControl>
											<SelectTrigger className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30">
												<SelectValue placeholder="Select invoice type" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="invoice">
												Invoice
											</SelectItem>
											<SelectItem value="credit">
												Credit
											</SelectItem>
											<SelectItem value="cash">
												Cash
											</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="financialProfile.invoiceEmail"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-1">
										Invoice Email
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger asChild>
													<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
												</TooltipTrigger>
												<TooltipContent>
													<p>
														Email address for
														sending invoices
													</p>
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="email"
											placeholder="Email for invoices"
											className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					<Separator className="my-5" />

					{/* Financial Details */}
					<div className="space-y-4">
						<div className="flex items-center gap-2 mb-2">
							<CreditCard className="h-4 w-4 text-primary" />
							<h3 className="text-sm font-medium text-foreground">
								Financial Details
							</h3>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<FormField
								control={form.control}
								name="financialProfile.currency"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Currency
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Default currency for
															this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<CurrencySelect
												name={field.name}
												value={field.value}
												onValueChange={field.onChange}
												placeholder="Select currency"
												currencies="custom"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="financialProfile.creditLimit"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Credit Limit
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Maximum amount of
															credit allowed for
															this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													{...field}
													value={
														field.value
															? formatWithCurrency(
																	field.value,
																)
															: ""
													}
													onChange={(e) => {
														const parsed =
															parseFromCurrency(
																e.target.value,
															);
														field.onChange(parsed);
													}}
													placeholder={`${getCurrencySymbol(selectedCurrency)} 0`}
													className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
												/>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="financialProfile.balance"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Current Balance
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Current outstanding
															balance
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<div className="relative">
												<Input
													{...field}
													value={
														field.value
															? formatWithCurrency(
																	field.value,
																)
															: ""
													}
													onChange={(e) => {
														const parsed =
															parseFromCurrency(
																e.target.value,
															);
														field.onChange(parsed);
													}}
													placeholder={`${getCurrencySymbol(selectedCurrency)} 0`}
													className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
												/>
											</div>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					<Separator className="my-5" />

					{/* Payment Terms */}
					<div className="space-y-4">
						<div className="flex items-center gap-2 mb-2">
							<FileText className="h-4 w-4 text-primary" />
							<h3 className="text-sm font-medium text-foreground">
								Payment Terms & Additional Information
							</h3>
						</div>

						<div className="grid grid-cols-1 gap-4">
							<FormField
								control={form.control}
								name="financialProfile.invoiceInfo"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Invoice Information
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Additional
															information to
															display on invoices
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Special instructions for invoices"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="financialProfile.taxNotes"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Tax Notes
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Special tax
															considerations for
															this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Additional tax information"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="financialProfile.creditNotesEmail"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Credit Notes Email
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Email address for
															sending credit notes
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												type="email"
												placeholder="Email for credit notes"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="financialProfile.requiredDocuments"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Required Documents
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Document
															requirements for
															this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={
												field.value
													? String(field.value)
													: undefined
											}
										>
											<FormControl>
												<SelectTrigger className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30">
													<SelectValue placeholder="Select document requirements" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="no">
													No Documents Required
												</SelectItem>
												<SelectItem value="with_invoice">
													With Invoice
												</SelectItem>
												<SelectItem value="with_invoice_separate_file">
													With Invoice (Separate File)
												</SelectItem>
												<SelectItem value="separate">
													Separate
												</SelectItem>
												<SelectItem value="mail">
													Mail
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* VAT Term Selector */}
							<FormField
								control={form.control}
								name="financialProfile.vatTermId"
								render={({ field }) => (
									<FormItem className="flex flex-col">
										<FormLabel className="flex items-center gap-1">
											VAT Term
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="ml-1 inline h-3 w-3" />
													</TooltipTrigger>
													<TooltipContent className="max-w-80">
														<p>
															VAT terms associated
															with this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<VatTermSelector
												vatTerms={vatTerms}
												value={field.value}
												onChange={field.onChange}
												isLoading={isLoadingVatTerms}
												placeholder="Select VAT term"
												name="vatTermSelector"
												isModal={true}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Payment Term Selector */}
							<FormField
								control={form.control}
								name="financialProfile.paymentTermId"
								render={({ field }) => (
									<FormItem className="flex flex-col">
										<FormLabel className="flex items-center gap-1">
											Payment Term
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="ml-1 inline h-3 w-3" />
													</TooltipTrigger>
													<TooltipContent className="max-w-80">
														<p>
															Payment terms for
															this contact
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<PaymentTermSelector
												paymentTerms={paymentTerms}
												value={field.value}
												onChange={field.onChange}
												isLoading={
													isLoadingPaymentTerms
												}
												placeholder="Select payment term"
												name="paymentTermSelector"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

export default FinancialSection;
