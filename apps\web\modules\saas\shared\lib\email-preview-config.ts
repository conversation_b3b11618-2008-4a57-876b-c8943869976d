import type {
	EmailPreviewData,
	EmailPreviewFeatures,
	EntityType,
} from "../types/email-preview";

/**
 * Get default features configuration for each entity type
 */
export function getEmailPreviewConfig(
	entityType: EntityType,
): EmailPreviewFeatures {
	switch (entityType) {
		case "invoice":
			return {
				customSubject: true,
				documentAttachments: true,
				backButton: false,
				stateReset: true,
				ccBccSupport: true,
			};
		case "offer":
			return {
				customSubject: false,
				documentAttachments: false,
				backButton: false,
				stateReset: true,
				ccBccSupport: true,
			};
		case "transport-order":
			return {
				customSubject: false,
				documentAttachments: true,
				backButton: false,
				stateReset: true,
				ccBccSupport: true,
			};
		case "order-confirmation":
			return {
				customSubject: false,
				documentAttachments: true,
				backButton: true,
				stateReset: true,
				ccBccSupport: true,
			};
		default:
			return {
				customSubject: false,
				documentAttachments: false,
				backButton: false,
				stateReset: true,
				ccBccSupport: false,
			};
	}
}

/**
 * Get default title for each entity type
 */
export function getEmailPreviewTitle(entityType: EntityType): string {
	switch (entityType) {
		case "invoice":
			return "Email Preview";
		case "offer":
			return "Email Preview";
		case "transport-order":
			return "Transport Order Email Preview";
		case "order-confirmation":
			return "Order Confirmation Email";
		default:
			return "Email Preview";
	}
}

/**
 * Get attachment text for each entity type
 */
export function getAttachmentText(entityType: EntityType): string {
	switch (entityType) {
		case "invoice":
			return "Invoice PDF (automatic)";
		case "offer":
			return "Offer PDF (automatic)";
		case "transport-order":
			return "Transport Order PDF (automatic)";
		case "order-confirmation":
			return "Order Confirmation PDF (automatic)";
		default:
			return "PDF (automatic)";
	}
}

/**
 * Get the documents property key from metadata for each entity type
 */
export function getDocumentsKey(
	entityType: EntityType,
): "orderDocuments" | "tourDocuments" {
	switch (entityType) {
		case "invoice":
		case "order-confirmation":
			return "orderDocuments";
		case "transport-order":
			return "tourDocuments";
		case "offer":
			return "orderDocuments"; // offers don't typically have documents, but use this as fallback
		default:
			return "orderDocuments";
	}
}

/**
 * Helper function to check email preview data structure
 */
export const isEmailPreview = (data: any): data is EmailPreviewData => {
	return (
		data &&
		typeof data === "object" &&
		"html" in data &&
		"text" in data &&
		"subject" in data &&
		typeof data.html === "string" &&
		typeof data.text === "string" &&
		typeof data.subject === "string"
	);
};

/**
 * Format file size in bytes to human readable format
 */
export function formatBytes(bytes: number, decimals = 2): string {
	if (bytes === 0) {
		return "0 Bytes";
	}

	const k = 1024;
	const dm = decimals < 0 ? 0 : decimals;
	const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}
