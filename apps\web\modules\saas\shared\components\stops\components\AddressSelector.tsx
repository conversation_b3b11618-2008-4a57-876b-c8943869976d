"use client";

import { getFieldName } from "@saas/shared/lib/stops/stop-form-utils";
import {
	type AddressOption,
	type UseAddressesParams,
	useAddresses,
} from "@saas/shared/lib/stops/use-addresses";
import { Button } from "@ui/components/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@ui/components/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useFormContext, useWatch } from "react-hook-form";

interface AddressSelectorProps {
	fieldPrefix?: string;
	counterpartyId?: string;
	stopType?: string;
	disabled?: boolean;
	label?: string;
	placeholder?: string;
	customAddressParams?: Partial<UseAddressesParams>;
	onChange?: (selectedAddress: AddressOption | null) => void;
}

export function AddressSelector({
	fieldPrefix = "stop",
	counterpartyId,
	stopType: propStopType,
	disabled = false,
	label = "Select an address...",
	placeholder = "Search address...",
	customAddressParams = {},
	onChange,
}: AddressSelectorProps) {
	const [open, setOpen] = useState(false);
	const [value, setValue] = useState("");
	const { control, setValue: setFormValue, watch } = useFormContext();

	// If stopType is not passed as a prop, watch it from the form
	const watchedStopType = useWatch({
		control,
		name: `${fieldPrefix}.stopType`,
	});

	// Use either the prop or watched value
	const stopType = propStopType || watchedStopType;

	// Get addresses using our custom hook
	const { addressOptions, isLoading } = useAddresses({
		counterpartyId,
		stopType,
		...customAddressParams,
	});

	// Handle address selection
	const handleAddressSelect = (selectedValue: string) => {
		// If user selects same value, clear it
		if (selectedValue === value) {
			setValue("");

			// Clear form values
			setFormValue(getFieldName("selectedAddress", fieldPrefix), null);
			setFormValue(getFieldName("street", fieldPrefix), "");
			setFormValue(getFieldName("zipCode", fieldPrefix), "");
			setFormValue(getFieldName("city", fieldPrefix), "");
			setFormValue(getFieldName("country", fieldPrefix), "");
			setFormValue(getFieldName("entrance_number", fieldPrefix), "");
			setFormValue(getFieldName("addressSupplement", fieldPrefix), "");

			// Notify parent if onChange is provided
			if (onChange) {
				onChange(null);
			}

			return;
		}

		// Otherwise, set the new value
		setValue(selectedValue);

		// Find the selected address data
		const selectedAddress = addressOptions.find(
			(option) => option.value === selectedValue,
		);

		if (selectedAddress) {
			// Store the original raw address data with configs for use in display
			setFormValue(
				getFieldName("selectedAddress", fieldPrefix),
				selectedAddress.originalAddress,
			);

			// Set each field in the form
			setFormValue(
				getFieldName("street", fieldPrefix),
				selectedAddress.addressData.street || "",
			);
			setFormValue(
				getFieldName("zipCode", fieldPrefix),
				selectedAddress.addressData.zipCode || "",
			);
			setFormValue(
				getFieldName("city", fieldPrefix),
				selectedAddress.addressData.city || "",
			);
			setFormValue(
				getFieldName("country", fieldPrefix),
				selectedAddress.addressData.country || "",
			);
			setFormValue(
				getFieldName("entrance_number", fieldPrefix),
				selectedAddress.addressData.entrance_number || "",
			);
			setFormValue(
				getFieldName("addressSupplement", fieldPrefix),
				selectedAddress.addressData.addressSupplement || "",
			);

			// Notify parent if onChange is provided
			if (onChange) {
				onChange(selectedAddress);
			}
		}

		// Close the popover
		setOpen(false);
	};

	// If the counterparty or stop type changes, reset the selected address
	useEffect(() => {
		setValue("");
	}, [counterpartyId, stopType]);

	return (
		<Popover open={open} onOpenChange={setOpen} modal={true}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					aria-expanded={open}
					className="w-full justify-between"
					disabled={disabled || !counterpartyId}
					type="button"
				>
					{value && addressOptions.length > 0
						? addressOptions.find(
								(address) => address.value === value,
							)?.label
						: label}
					{isLoading ? (
						<Loader2 className="h-4 w-4 animate-spin opacity-50" />
					) : (
						<ChevronsUpDown className="h-4 w-4 opacity-50" />
					)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[350px] p-0">
				<Command>
					<CommandInput placeholder={placeholder} className="h-9" />
					<CommandList>
						<CommandEmpty>No address found.</CommandEmpty>
						<CommandGroup>
							{addressOptions.map((address) => (
								<CommandItem
									key={address.value}
									value={address.value}
									onSelect={handleAddressSelect}
								>
									{address.label}
									<Check
										className={cn(
											"ml-auto",
											value === address.value
												? "opacity-100"
												: "opacity-0",
										)}
									/>
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
