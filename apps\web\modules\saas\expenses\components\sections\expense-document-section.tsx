"use client";

import { But<PERSON> } from "@ui/components/button";
import { Crown, Upload } from "lucide-react";
import React from "react";

interface ExpenseDocumentSectionProps {
	selectedFile: File | null;
	documentPreviewUrl: string | null;
	ocrMutation: {
		isPending: boolean;
		mutate: (file: File) => void;
	};
	onFileSelect: (file: File | null) => void;
}

export function ExpenseDocumentSection({
	selectedFile,
	documentPreviewUrl,
	ocrMutation,
	onFileSelect,
}: ExpenseDocumentSectionProps) {
	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0] || null;
		onFileSelect(file);
	};

	const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
		const file = event.dataTransfer.files?.[0] || null;
		onFileSelect(file);
	};

	const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
		event.preventDefault();
	};

	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Document Upload</h3>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* File Upload Area */}
				<div className="space-y-4">
					<div
						className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-muted-foreground/50 transition-colors"
						onDrop={handleDrop}
						onDragOver={handleDragOver}
					>
						<Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
						<div className="space-y-2">
							<p className="text-sm font-medium">
								Drop your invoice here, or click to browse
							</p>
							<p className="text-xs text-muted-foreground">
								Supports PDF, PNG, JPG files up to 10MB
							</p>
						</div>
						<input
							type="file"
							accept=".pdf,.png,.jpg,.jpeg"
							onChange={handleFileChange}
							className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
						/>
					</div>

					{selectedFile && (
						<div className="space-y-3">
							<div className="flex items-center justify-between p-3 bg-muted rounded-lg">
								<div className="flex items-center space-x-3">
									<div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
										<Upload className="h-4 w-4 text-primary" />
									</div>
									<div>
										<p className="text-sm font-medium">
											{selectedFile.name}
										</p>
										<p className="text-xs text-muted-foreground">
											{(
												selectedFile.size /
												1024 /
												1024
											).toFixed(2)}{" "}
											MB
										</p>
									</div>
								</div>
								<Button
									onClick={() => onFileSelect(null)}
									variant="ghost"
									size="sm"
								>
									Remove
								</Button>
							</div>

							{/* OCR Extract Button */}
							<div className="flex justify-center">
								<Button
									onClick={() =>
										selectedFile &&
										ocrMutation.mutate(selectedFile)
									}
									disabled={ocrMutation.isPending}
									size="sm"
									variant="outline"
									className="border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-950/20"
								>
									<Crown className="h-4 w-4 mr-2 text-amber-600 dark:text-amber-400" />
									{ocrMutation.isPending
										? "Processing..."
										: "Extract Data"}
								</Button>
							</div>
						</div>
					)}
				</div>

				{/* Document Preview */}
				<div className="space-y-4">
					<h4 className="font-medium">Document Preview</h4>
					<div className="border rounded-lg overflow-hidden bg-muted/50 min-h-[400px]">
						{documentPreviewUrl ? (
							<div className="h-full">
								{selectedFile?.type === "application/pdf" ? (
									<iframe
										src={documentPreviewUrl}
										className="w-full h-[400px]"
										title="Invoice Document Preview"
									/>
								) : (
									<div className="flex items-center justify-center h-[400px]">
										<img
											src={documentPreviewUrl}
											alt="Document Preview"
											className="max-w-full max-h-full object-contain"
										/>
									</div>
								)}
							</div>
						) : (
							<div className="flex items-center justify-center h-[400px] text-muted-foreground">
								<div className="text-center">
									<Upload className="mx-auto h-12 w-12 mb-4 opacity-50" />
									<p className="text-sm">
										No document selected
									</p>
									<p className="text-xs mt-1">
										Upload a document to see preview
									</p>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>

			{/* OCR Processing Status */}
			{ocrMutation.isPending && (
				<div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
					<div className="flex items-center space-x-3">
						<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-600" />
						<div>
							<p className="text-sm font-medium text-amber-800 dark:text-amber-200">
								Processing Document
							</p>
							<p className="text-xs text-amber-600 dark:text-amber-400">
								Extracting data from your invoice using OCR...
							</p>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
