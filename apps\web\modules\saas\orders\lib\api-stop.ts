import type {
	CreateStopInput,
	UpdateStopInput,
} from "@repo/api/src/routes/stops/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Just define the types we need for the query params
export type StopType = "loading" | "unloading";
export type EntityType = "order" | "offer";

type FetchStopsParams = {
	organizationId: string;
	entityId?: string;
	entityType?: EntityType;
	page?: number;
	limit?: number;
	search?: string;
	// Additional parameters to match API capabilities
	tourId?: string;
	isUnassigned?: boolean;
	dateStart?: Date;
	dateEnd?: Date;
	stopType?: StopType;
	customerId?: string;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	groupByOrder?: boolean; // New parameter to signal grouping stops by orders
	// Add map bounds parameters
	minLat?: number;
	maxLat?: number;
	minLng?: number;
	maxLng?: number;
};

export const stopKeys = {
	all: ["stops"] as const,
	list: (params: FetchStopsParams) =>
		[...stopKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...stopKeys.all, "detail", organizationId, id] as const,
};

export const fetchStops = async (params: FetchStopsParams) => {
	const response = await apiClient.stops.$get({
		query: {
			organizationId: params.organizationId,
			entityType: params.entityType,
			entityId: params.entityId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			search: params.search,
			// Add additional query parameters
			tourId: params.tourId,
			isUnassigned: params.isUnassigned?.toString(),
			dateStart: params.dateStart?.toISOString(),
			dateEnd: params.dateEnd?.toISOString(),
			stopType: params.stopType,
			customerId: params.customerId,
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			groupByOrder: params.groupByOrder?.toString(),
			// Add map bounds parameters
			minLat: params.minLat?.toString(),
			maxLat: params.maxLat?.toString(),
			minLng: params.minLng?.toString(),
			maxLng: params.maxLng?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch stops");
	}

	return response.json();
};

export const useStopsQuery = (params: FetchStopsParams) => {
	return useQuery({
		queryKey: stopKeys.list(params),
		queryFn: () => fetchStops(params),
		placeholderData: keepPreviousData,
		enabled:
			!!params.organizationId &&
			// Enable if we're filtering by entity
			((!!params.entityId && !!params.entityType) ||
				// Or if we're filtering by tour
				!!params.tourId ||
				// Or if we're looking for unassigned stops
				params.isUnassigned === true),
	});
};

export const fetchStopById = async (organizationId: string, id: string) => {
	const response = await apiClient.stops[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch stop details");
	}

	return response.json();
};

export const useStopByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: stopKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchStopById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

export const useCreateStopMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: Omit<CreateStopInput, "organizationId">) => {
			const response = await apiClient.stops.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create stop");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Stop created successfully");

			// Get orderId or offerId based on what's in the response
			const entityId = data.orderId || data.offerId || "";
			const entityType = data.orderId ? "order" : "offer";

			// Invalidate entity-specific queries
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.list({
						organizationId,
						entityId,
						entityType,
					}),
				});
			}

			// If the stop is associated with a tour, also invalidate tour-specific queries
			if (data.tourId) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.list({
						organizationId,
						tourId: data.tourId,
					}),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to create stop");
			console.error("Create stop error:", error);
		},
	});
};

export const useUpdateStopMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateStopInput) => {
			const response = await apiClient.stops[":id"].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update stop");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Stop updated successfully");

			// Get orderId or offerId based on what's in the response
			const entityId = data.orderId || data.offerId || "";
			const entityType = data.orderId ? "order" : "offer";

			// Invalidate entity-specific queries
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.list({
						organizationId,
						entityId,
						entityType,
					}),
				});
			}

			// If the stop is associated with a tour, also invalidate tour-specific queries
			if (data.tourId) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.list({
						organizationId,
						tourId: data.tourId,
					}),
				});
			}

			// Also invalidate unassigned stops queries when tour assignment changes
			queryClient.invalidateQueries({
				queryKey: stopKeys.list({
					organizationId,
					isUnassigned: true,
				}),
			});

			// Also invalidate the detail query
			if (data?.id) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update stop");
			console.error("Update stop error:", error);
		},
	});
};

export const useDeleteStopMutation = (
	organizationId: string,
	entityId: string,
	entityType: EntityType = "order",
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.stops[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete stop");
			}

			return response.json();
		},
		onSuccess: (_, stopId) => {
			toast.success("Stop deleted successfully");

			// Invalidate entity-specific queries
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: stopKeys.list({
						organizationId,
						entityId,
						entityType,
					}),
				});
			}

			// Invalidate all tour-related queries
			queryClient.invalidateQueries({
				queryKey: stopKeys.list({
					organizationId,
					// We don't know which tour, so we'll invalidate broader
				}),
			});

			// Invalidate unassigned stops queries
			queryClient.invalidateQueries({
				queryKey: stopKeys.list({
					organizationId,
					isUnassigned: true,
				}),
			});

			// Invalidate the specific stop detail
			queryClient.invalidateQueries({
				queryKey: stopKeys.detail(organizationId, stopId),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete stop");
			console.error("Delete stop error:", error);
		},
	});
};
