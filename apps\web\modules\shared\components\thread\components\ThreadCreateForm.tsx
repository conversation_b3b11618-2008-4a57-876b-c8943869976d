"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { X } from "lucide-react";
import { useState } from "react";
import { useThreadMutations } from "../hooks/use-threads";

interface ThreadCreateFormProps {
	entityType: string;
	entityId: string;
	onCancel: () => void;
	onSuccess: () => void;
}

export function ThreadCreateForm({
	entityType,
	entityId,
	onCancel,
	onSuccess,
}: ThreadCreateFormProps) {
	const [title, setTitle] = useState("");
	const [description, setDescription] = useState("");
	const { activeOrganization } = useActiveOrganization();

	const { createThread, isLoading } = useThreadMutations({
		onSuccess: () => {
			setTitle("");
			setDescription("");
			onSuccess();
		},
	});

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!title.trim() || !activeOrganization?.id) {
			return;
		}

		try {
			await createThread({
				title: title.trim(),
				description: description.trim() || undefined,
				entityType,
				entityId,
				organizationId: activeOrganization.id,
				mentions: [], // Empty mentions array for thread creation
			});
		} catch (error) {
			console.error("Failed to create thread:", error);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Escape") {
			onCancel();
		}
	};

	return (
		<Card className="border-0 shadow-lg">
			<CardHeader className="pb-3">
				<div className="flex items-center justify-between">
					<CardTitle className="text-lg">Create New Thread</CardTitle>
					<Button
						variant="ghost"
						size="sm"
						onClick={onCancel}
						disabled={isLoading}
					>
						<X className="w-4 h-4" />
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="title">Title *</Label>
						<Input
							id="title"
							placeholder="Enter thread title..."
							value={title}
							onChange={(e) => setTitle(e.target.value)}
							onKeyDown={handleKeyDown}
							disabled={isLoading}
							autoFocus
						/>
					</div>

					<div className="space-y-2">
						<Label htmlFor="description">Description</Label>
						<Textarea
							id="description"
							placeholder="Add optional description..."
							value={description}
							onChange={(e) => setDescription(e.target.value)}
							onKeyDown={handleKeyDown}
							className="min-h-[80px] resize-none"
							disabled={isLoading}
						/>
					</div>

					<div className="text-xs text-muted-foreground">
						This thread will be associated with {entityType} #
						{entityId}
					</div>

					<div className="flex items-center justify-end gap-2 pt-2">
						<Button
							type="button"
							variant="ghost"
							onClick={onCancel}
							disabled={isLoading}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={!title.trim() || isLoading}
						>
							{isLoading ? (
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
							) : (
								"Create Thread"
							)}
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}
