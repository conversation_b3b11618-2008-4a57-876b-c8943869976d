import { EntityType } from "@repo/api/src/routes/orders/types";
import type { OrderFormValues } from "@saas/orders/context/order-form-context";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@ui/components/collapsible";
import { CurrencySelect } from "@ui/components/currency-select";
import {
	FormControl,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import currency from "currency.js";
import { ChevronDown, ChevronUp, Plus, Trash } from "lucide-react";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import { Controller, useFieldArray, useFormContext } from "react-hook-form";

// Define interface for line items
interface LineItem {
	id?: string;
	entityType: EntityType;
	orderId?: string;
	offerId?: string;
	description: string;
	// Removed type field - all line items are now revenue-only
	quantity: number;
	unit: string;
	unitPrice: number;
	totalPrice: number;
	currency: string;
	position: number;
	notes: string;
}

// Format currency with currency symbol - for totals display
const formatCurrency = (amount: number, currencyCode: string): string => {
	return currency(amount, {
		symbol: `${currencyCode} `,
		separator: ".",
		decimal: ",",
	}).format();
};

// Convert type enum to display name
// Removed getTypeDisplayName function - no longer needed for revenue-only line items

export function PricesSection() {
	const t = useTranslations("app.orders");
	// Connect to the form context
	const { control, watch } = useFormContext<OrderFormValues>();

	// Set up field array for line items
	const { fields, append, remove, update } = useFieldArray({
		control,
		name: "lineItems",
	});

	// Watch the line items for calculations
	const lineItems = watch("lineItems") || [];

	// Initial line item template
	const getDefaultLineItem = (position: number): LineItem => ({
		entityType: EntityType.ORDER,
		description: "",
		// Removed type field - all line items are now revenue-only
		quantity: 0,
		unit: "",
		unitPrice: 0,
		totalPrice: 0,
		currency: "EUR",
		position,
		notes: "",
	});

	// State for expanded items
	const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(
		{},
	);

	// Calculate total amounts for all line items (revenue-only now)
	const totals = useMemo(() => {
		const currencyTotals: Record<string, number> = {};
		lineItems.forEach((item) => {
			if (!item?.currency) return;
			if (!currencyTotals[item.currency]) {
				currencyTotals[item.currency] = 0;
			}
			currencyTotals[item.currency] += item.totalPrice || 0;
		});
		return currencyTotals;
	}, [lineItems]);

	// Function to check if there are any totals to display
	const hasTotalsToDisplay = useMemo(() => {
		return Object.values(totals).some((amount) => amount > 0);
	}, [totals]);

	// Function to calculate total price
	const calculateTotalPrice = (
		quantity: number,
		unitPrice: number,
	): number => {
		return quantity > 0 && unitPrice > 0 ? quantity * unitPrice : 0;
	};

	// Toggle expand/collapse state for a line item
	const toggleExpand = (id: string) => {
		setExpandedItems((prev) => ({
			...prev,
			[id]: !prev[id],
		}));
	};

	// Check if total price should be disabled (both quantity and unit price are provided)
	const shouldDisableTotalPrice = (item: any): boolean => {
		const quantity = item?.quantity ?? 0;
		const unitPrice = item?.unitPrice ?? 0;
		return quantity > 0 && unitPrice > 0;
	};

	// Add a new line item
	const addLineItem = () => {
		append(getDefaultLineItem(lineItems.length + 1));
	};

	// Remove a line item
	const removeLineItem = (index: number) => {
		remove(index);

		// Reorder positions after deletion
		setTimeout(() => {
			lineItems.forEach((item, idx) => {
				if (item && item.position !== idx + 1) {
					update(idx, { ...item, position: idx + 1 });
				}
			});
		}, 0);
	};

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Prices</CardTitle>
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={addLineItem}
					className="h-8"
				>
					<Plus className="mr-2 h-4 w-4" />
					Add Price
				</Button>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				{fields.length === 0 ? (
					<div className="text-center py-4">
						<p className="text-muted-foreground">
							No prices added yet
						</p>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={addLineItem}
							className="mt-2"
						>
							<Plus className="mr-2 h-4 w-4" />
							Add your first price
						</Button>
					</div>
				) : (
					<div className="space-y-4">
						{/* Table header */}
						<div className="hidden md:grid md:grid-cols-12 gap-4 px-4 py-2 bg-muted text-sm font-medium">
							<div className="col-span-4">Description</div>
							<div className="col-span-2">Quantity</div>
							<div className="col-span-1">Unit</div>
							<div className="col-span-2">Unit Price</div>
							<div className="col-span-2">Total Price</div>
							<div className="col-span-1" />
						</div>

						{/* Line items */}
						{fields.map((field, index) => {
							const item =
								lineItems[index] ||
								getDefaultLineItem(index + 1);
							return (
								<Collapsible
									key={field.id}
									open={expandedItems[field.id]}
									onOpenChange={() => toggleExpand(field.id)}
									className="border rounded-md overflow-hidden"
								>
									<div className="grid grid-cols-12 gap-4 p-4 items-center">
										{/* Description & Type - 4 columns */}
										<div className="col-span-12 md:col-span-4 flex flex-col gap-2">
											<FormItem className="space-y-1">
												<div>
													<FormLabel className="text-xs">
														Description
													</FormLabel>
													{/* Removed type selector - all line items are now revenue-only */}
												</div>
												<Controller
													control={control}
													name={`lineItems.${index}.description`}
													defaultValue=""
													render={({ field }) => (
														<FormControl>
															<Input
																{...field}
																placeholder="Description"
																className="h-9"
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>

										{/* Quantity - 2 columns */}
										<div className="col-span-4 md:col-span-2">
											<FormItem className="space-y-1">
												<FormLabel className="text-xs">
													Quantity
												</FormLabel>
												<Controller
													control={control}
													name={`lineItems.${index}.quantity`}
													defaultValue={0}
													render={({ field }) => (
														<FormControl>
															<Input
																value={
																	field.value
																}
																onChange={(
																	e,
																) => {
																	// Just update the field value without recalculating
																	const value =
																		Number.parseFloat(
																			e
																				.target
																				.value,
																		) || 0;
																	field.onChange(
																		value,
																	);
																}}
																onBlur={(e) => {
																	const value =
																		Number.parseFloat(
																			e
																				.target
																				.value,
																		) || 0;
																	field.onChange(
																		value,
																	);

																	// Only recalculate total price on blur
																	const unitPrice =
																		lineItems[
																			index
																		]
																			?.unitPrice ||
																		0;
																	if (
																		value >
																			0 &&
																		unitPrice >
																			0
																	) {
																		const totalPrice =
																			calculateTotalPrice(
																				value,
																				unitPrice,
																			);
																		update(
																			index,
																			{
																				...lineItems[
																					index
																				],
																				quantity:
																					value,
																				totalPrice,
																			},
																		);
																	}
																	field.onBlur();
																}}
																type="number"
																placeholder="0,00"
																className="h-9"
																step="0.01"
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>

										{/* Unit - 1 column */}
										<div className="col-span-4 md:col-span-1">
											<FormItem className="space-y-1">
												<FormLabel className="text-xs">
													Unit
												</FormLabel>
												<Controller
													control={control}
													name={`lineItems.${index}.unit`}
													defaultValue=""
													render={({ field }) => (
														<FormControl>
															<Input
																{...field}
																placeholder="km, h"
																className="h-9"
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>

										{/* Unit Price - 2 columns */}
										<div className="col-span-4 md:col-span-2">
											<FormItem className="space-y-1">
												<FormLabel className="text-xs">
													Unit Price
												</FormLabel>
												<Controller
													control={control}
													name={`lineItems.${index}.unitPrice`}
													defaultValue={0}
													render={({ field }) => (
														<FormControl>
															<Input
																value={
																	field.value
																}
																onChange={(
																	e,
																) => {
																	// Just update the field value without recalculating
																	const value =
																		Number.parseFloat(
																			e
																				.target
																				.value,
																		) || 0;
																	field.onChange(
																		value,
																	);
																}}
																onBlur={(e) => {
																	const value =
																		Number.parseFloat(
																			e
																				.target
																				.value,
																		) || 0;
																	field.onChange(
																		value,
																	);

																	// Only recalculate total price on blur
																	const quantity =
																		lineItems[
																			index
																		]
																			?.quantity ||
																		0;
																	if (
																		quantity >
																			0 &&
																		value >
																			0
																	) {
																		const totalPrice =
																			calculateTotalPrice(
																				quantity,
																				value,
																			);
																		update(
																			index,
																			{
																				...lineItems[
																					index
																				],
																				unitPrice:
																					value,
																				totalPrice,
																			},
																		);
																	}
																	field.onBlur();
																}}
																type="number"
																placeholder="0,00"
																className="h-9"
																step="0.01"
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>

										{/* Total Price - 2 columns */}
										<div className="col-span-4 md:col-span-2">
											<FormItem className="space-y-1">
												<FormLabel className="text-xs">
													Total Price
												</FormLabel>
												<Controller
													control={control}
													name={`lineItems.${index}.totalPrice`}
													defaultValue={0}
													render={({ field }) => (
														<FormControl>
															<Input
																value={
																	field.value
																}
																onChange={(
																	e,
																) => {
																	// Only allow directly editing total if not auto-calculated
																	if (
																		!shouldDisableTotalPrice(
																			lineItems[
																				index
																			] ||
																				{},
																		)
																	) {
																		const value =
																			Number.parseFloat(
																				e
																					.target
																					.value,
																			) ||
																			0;
																		field.onChange(
																			value,
																		);
																	}
																}}
																onBlur={(e) => {
																	// Update form data when leaving the field
																	if (
																		!shouldDisableTotalPrice(
																			lineItems[
																				index
																			] ||
																				{},
																		)
																	) {
																		const value =
																			Number.parseFloat(
																				e
																					.target
																					.value,
																			) ||
																			0;
																		field.onChange(
																			value,
																		);
																		update(
																			index,
																			{
																				...lineItems[
																					index
																				],
																				totalPrice:
																					value,
																			},
																		);
																	}
																	field.onBlur();
																}}
																type="number"
																placeholder="0,00"
																className="h-9"
																step="0.01"
																disabled={shouldDisableTotalPrice(
																	lineItems[
																		index
																	] || {},
																)}
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>

										{/* Actions - 1 column */}
										<div className="col-span-12 md:col-span-1 flex justify-end gap-1 md:flex-col">
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													removeLineItem(index)
												}
												disabled={fields.length === 1}
												className="h-8 w-8 p-0"
											>
												<Trash className="h-4 w-4" />
												<span className="sr-only">
													Remove price
												</span>
											</Button>
											<CollapsibleTrigger asChild>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 p-0"
												>
													{expandedItems[field.id] ? (
														<ChevronUp className="h-4 w-4" />
													) : (
														<ChevronDown className="h-4 w-4" />
													)}
													<span className="sr-only">
														{expandedItems[field.id]
															? "Collapse"
															: "Expand"}
													</span>
												</Button>
											</CollapsibleTrigger>
										</div>
									</div>

									{/* Expandable section */}
									<CollapsibleContent>
										<div className="border-t px-4 py-3 space-y-4 bg-muted/20">
											<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
												{/* Currency */}
												<FormItem>
													<FormLabel>
														Currency
													</FormLabel>
													<Controller
														control={control}
														name={`lineItems.${index}.currency`}
														defaultValue="EUR"
														render={({ field }) => (
															<FormControl>
																<CurrencySelect
																	name={`currency-${index}`}
																	value={
																		field.value
																	}
																	onValueChange={
																		field.onChange
																	}
																	currencies="custom"
																/>
															</FormControl>
														)}
													/>
													<FormMessage />
												</FormItem>

												{/* Position */}
												<FormItem>
													<FormLabel>
														Position
													</FormLabel>
													<Controller
														control={control}
														name={`lineItems.${index}.position`}
														defaultValue={index + 1}
														render={({ field }) => (
															<FormControl>
																<Input
																	type="number"
																	min="1"
																	step="1"
																	placeholder="1"
																	value={
																		field.value
																	}
																	onChange={(
																		e,
																	) =>
																		field.onChange(
																			Number.parseInt(
																				e
																					.target
																					.value,
																				10,
																			) ||
																				1,
																		)
																	}
																	onBlur={
																		field.onBlur
																	}
																/>
															</FormControl>
														)}
													/>
													<FormMessage />
												</FormItem>
											</div>

											{/* Notes */}
											<FormItem>
												<FormLabel>Notes</FormLabel>
												<Controller
													control={control}
													name={`lineItems.${index}.notes`}
													defaultValue=""
													render={({ field }) => (
														<FormControl>
															<Textarea
																{...field}
																placeholder="Additional notes"
																className="resize-none"
																rows={2}
															/>
														</FormControl>
													)}
												/>
												<FormMessage />
											</FormItem>
										</div>
									</CollapsibleContent>
								</Collapsible>
							);
						})}
					</div>
				)}

				{/* Total amounts */}
				{fields.length > 0 && hasTotalsToDisplay && (
					<div className="mt-6 p-4 bg-muted rounded-lg">
						<div className="flex flex-col gap-3">
							<div className="flex flex-wrap items-center gap-x-4 gap-y-2">
								<span className="font-semibold min-w-24">
									Total:
								</span>
								{Object.entries(totals).map(
									([currency, amount]) => {
										if (amount <= 0) return null;
										return (
											<span
												key={currency}
												className="bg-background px-3 py-1 rounded border text-sm"
											>
												{formatCurrency(
													amount,
													currency,
												)}
											</span>
										);
									},
								)}
							</div>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
