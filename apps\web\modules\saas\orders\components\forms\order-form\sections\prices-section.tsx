import type { LineItemFormValues } from "@repo/api/src/routes/line-items/types";
import { EntityType } from "@repo/api/src/routes/orders/types";
import type { OrderFormValues } from "@saas/orders/context/order-form-context";
import {
	type LineItem,
	LineItemsTable,
} from "@saas/shared/components/line-items/line-items-table";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useFieldArray, useFormContext } from "react-hook-form";

export function PricesSection() {
	const { control, watch } = useFormContext<OrderFormValues>();

	// Set up field array for line items
	const { fields, append, remove, update } = useFieldArray({
		control,
		name: "lineItems",
	});

	// Watch the line items for calculations
	const lineItems = watch("lineItems") || [];

	// Convert form line items to LineItem format for the table
	const tableItems: LineItem[] = lineItems.map((item, index) => ({
		id: fields[index]?.id || `temp-${index}`,
		description: item.description || "",
		quantity: item.quantity || 0,
		unit: item.unit || "",
		unitPrice: item.unitPrice || 0,
		totalPrice: item.totalPrice || 0,
		currency: item.currency || "EUR",
		notes: item.notes || "",
		position: item.position || index + 1,
		// Add required fields for LineItem interface
		entityType: EntityType.ORDER,
		orderId: undefined,
		offerId: undefined,
		vatRate: 0, // Orders don't use VAT in this context
	}));

	// Handle creating new line items
	const handleCreateItem = async (
		item: LineItemFormValues & { _references?: string[] },
	) => {
		const newItem = {
			entityType: EntityType.ORDER,
			description: item.description || "",
			quantity: item.quantity || 0,
			unit: item.unit || "",
			unitPrice: item.unitPrice || 0,
			totalPrice: item.totalPrice || 0,
			currency: item.currency || "EUR",
			position: lineItems.length + 1,
			notes: item.notes || "",
		};
		append(newItem);
	};

	// Handle updating existing line items
	const handleUpdateItem = async (
		item: LineItemFormValues & { id: string; _references?: string[] },
	) => {
		// Find the index of the item to update
		const index = fields.findIndex((field) => field.id === item.id);
		if (index !== -1) {
			const updatedItem = {
				entityType: EntityType.ORDER,
				description: item.description || "",
				quantity: item.quantity || 0,
				unit: item.unit || "",
				unitPrice: item.unitPrice || 0,
				totalPrice: item.totalPrice || 0,
				currency: item.currency || "EUR",
				position: item.position || index + 1,
				notes: item.notes || "",
			};
			update(index, updatedItem);
		}
	};

	// Handle deleting line items
	const handleDeleteItem = async (itemId: string) => {
		const index = fields.findIndex((field) => field.id === itemId);
		if (index !== -1) {
			remove(index);

			// Reorder positions after deletion
			setTimeout(() => {
				lineItems.forEach((item, idx) => {
					if (item && item.position !== idx + 1) {
						update(idx, { ...item, position: idx + 1 });
					}
				});
			}, 0);
		}
	};

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Prices</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border">
				<LineItemsTable
					items={tableItems}
					onCreateItem={handleCreateItem}
					onUpdateItem={handleUpdateItem}
					onDeleteItem={handleDeleteItem}
					hideTypeColumn={true}
					hideVatColumn={true}
					hideTypeTotals={true}
					showCurrencyTotals={true}
					hideCurrencyColumn={false}
				/>
			</CardContent>
		</Card>
	);
}
