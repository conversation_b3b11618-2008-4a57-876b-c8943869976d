import type { UpdateOrderConfigurationInput } from "@repo/api/src/routes/settings/order-settings/types";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Query keys for React Query
export const orderConfigurationKeys = {
	all: ["order-configuration"] as const,
	details: () => [...orderConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...orderConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchOrderConfiguration = async (organizationId: string) => {
	const response = await apiClient.settings["order-settings"].$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch order configuration");
	}

	return response.json();
};

// React Query Hooks
export const useOrderConfigurationQuery = (
	organizationId: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined ? options.enabled : !!organizationId;

	return useQuery({
		queryKey: orderConfigurationKeys.detail(organizationId),
		queryFn: () => fetchOrderConfiguration(organizationId),
		enabled: isEnabled && !!organizationId,
	});
};

// Mutation Hooks
export const useUpdateOrderConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateOrderConfigurationInput, "organizationId">,
		) => {
			const response = await apiClient.settings["order-settings"].$put({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to update order configuration");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Order configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: orderConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update order configuration",
			);
		},
	});
};
