import type { CalendarEvent } from "@repo/api/src/routes/personnel-calendar/types";
import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { addMonths, endOfMonth, startOfMonth, subMonths } from "date-fns";

// Types for API requests
export interface CalendarEventsParams {
	organizationId: string;
	startDate: Date;
	endDate: Date;
	departmentId?: string;
	personnelId?: string;
}

// Query keys for React Query
export const personnelCalendarKeys = {
	all: ["personnel-calendar"] as const,
	events: () => [...personnelCalendarKeys.all, "events"] as const,
	monthEvents: (
		organizationId: string,
		date: Date,
		departmentId?: string,
		personnelId?: string,
	) => {
		const year = date.getFullYear();
		const month = date.getMonth();
		return [
			...personnelCalendarKeys.events(),
			{ organizationId, year, month, departmentId, personnelId },
		] as const;
	},
	dateRangeEvents: (params: CalendarEventsParams) =>
		[...personnelCalendarKeys.events(), params] as const,
};

// API function to fetch calendar events
export const fetchCalendarEvents = async (
	params: CalendarEventsParams,
): Promise<CalendarEvent[]> => {
	const response = await apiClient.calendar.events.$get({
		query: {
			organizationId: params.organizationId,
			startDate: params.startDate.toISOString(),
			endDate: params.endDate.toISOString(),
			departmentId: params.departmentId,
			personnelId: params.personnelId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch calendar events");
	}

	return response.json();
};

// Helper function to check if an event occurs on a specific date
export function isEventOnDate(event: CalendarEvent, date: Date): boolean {
	const eventStart = new Date(event.startDate);
	const eventEnd = new Date(event.endDate);

	// Set all dates to start of day for proper comparison
	const startOfDay = new Date(date);
	startOfDay.setHours(0, 0, 0, 0);

	eventStart.setHours(0, 0, 0, 0);
	eventEnd.setHours(0, 0, 0, 0);

	return startOfDay >= eventStart && startOfDay <= eventEnd;
}

// Custom hook to fetch the current month's calendar events
export function useCalendarMonthEvents(
	organizationId: string,
	date: Date,
	departmentId?: string,
	personnelId?: string,
	options: { enabled?: boolean } = {},
) {
	// Calculate the date range for the month, plus padding
	const queryStartDate = subMonths(startOfMonth(date), 1); // Add previous month for padding
	const queryEndDate = addMonths(endOfMonth(date), 1); // Add next month for padding

	// Create query params
	const queryParams: CalendarEventsParams = {
		organizationId,
		startDate: queryStartDate,
		endDate: queryEndDate,
		departmentId,
		personnelId,
	};

	// Use TanStack Query to fetch and cache the data
	return useQuery({
		queryKey: personnelCalendarKeys.monthEvents(
			organizationId,
			date,
			departmentId,
			personnelId,
		),
		queryFn: () => fetchCalendarEvents(queryParams),
		staleTime: 1000 * 60 * 5, // 5 minutes
		placeholderData: keepPreviousData,
		refetchOnWindowFocus: false, // Don't refetch when window regains focus
		refetchOnMount: false, // Don't refetch when component mounts if data exists
		enabled: options.enabled !== false && !!organizationId,
	});
}

// React Query hook for custom date range
export const useCalendarEvents = (
	params: CalendarEventsParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: personnelCalendarKeys.dateRangeEvents(params),
		queryFn: () => fetchCalendarEvents(params),
		placeholderData: keepPreviousData,
		staleTime: 1000 * 60 * 5, // 5 minutes
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		enabled: isEnabled && !!params.organizationId,
	});
};
