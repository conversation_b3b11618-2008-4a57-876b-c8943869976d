import type {
	CreateTourInput,
	TourStopInput,
	UpdateTourInput,
} from "@repo/api/src/routes/tours/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define types
type FetchToursParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	startDate?: Date;
	endDate?: Date;
};

export const tourKeys = {
	all: ["tours"] as const,
	list: (params: FetchToursParams) =>
		[...tourKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...tourKeys.all, "detail", organizationId, id] as const,
	orders: (tourId: string) => [...tourKeys.all, "orders", tourId] as const,
};

export const fetchTours = async (params: FetchToursParams) => {
	const response = await apiClient.tours.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch tours list");
	}

	return response.json();
};

export const useToursQuery = (params: FetchToursParams) => {
	return useQuery({
		queryKey: tourKeys.list(params),
		queryFn: () => fetchTours(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchTourById = async (organizationId: string, id: string) => {
	const response = await apiClient.tours[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch tour details");
	}

	return response.json();
};

// Export inferred type for tour data from API response
export type TourResponse = NonNullable<
	Awaited<ReturnType<typeof fetchTourById>>
>;

export const useTourByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: tourKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchTourById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create tour mutation
export const useCreateTourMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateTourInput) => {
			const response = await apiClient.tours.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create tour");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Tour created successfully");
			queryClient.invalidateQueries({
				queryKey: tourKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create tour");
			console.error("Create tour error:", error);
		},
	});
};

// Update tour mutation
export const useUpdateTourMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			tourId,
			tourData,
			stopOrders,
			tourStops,
		}: {
			tourId: string;
			tourData: UpdateTourInput;
			stopOrders?: { stopId: string; position: number }[];
			tourStops?: TourStopInput[];
		}) => {
			const response = await apiClient.tours[":id"].$put({
				param: { id: tourId },
				json: {
					...tourData,
					...(stopOrders
						? {
								stopOrders: stopOrders.map((order) => ({
									stopId: order.stopId,
									position: order.position,
								})),
							}
						: {}),
					...(tourStops ? { tourStops } : {}),
				},
			});

			if (!response.ok) {
				throw new Error("Failed to update tour");
			}

			return response.json();
		},
		onSuccess: (_, variables) => {
			toast.success("Tour updated successfully");
			// Invalidate specific tour query
			queryClient.invalidateQueries({
				queryKey: tourKeys.detail(
					variables.tourData.organizationId,
					variables.tourId,
				),
			});
		},
		onError: (error) => {
			toast.error("Failed to update tour");
			console.error("Update tour error:", error);
		},
	});
};

// Delete tour mutation
export const useDeleteTourMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.tours[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete tour");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Tour deleted successfully");
			queryClient.invalidateQueries({
				queryKey: tourKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete tour");
			console.error("Delete tour error:", error);
		},
	});
};

// Transport Order Functions

// Type for the transport order PDF preview API response
export interface TransportOrderPDFPreviewResponse {
	success: boolean;
	message: string;
	pdfBase64?: string; // Optional because it might fail
	error?: { message: string; details?: string }; // For structured errors from backend
}

// Generate Transport Order PDF Preview
export const generateTransportOrderPDFPreview = async (
	organizationId: string,
	tourId: string,
): Promise<TransportOrderPDFPreviewResponse> => {
	const response = await apiClient.tours[":id"][
		"transport-order"
	].preview.$post({
		param: { id: tourId },
		query: { organizationId },
	});

	if (!response.ok) {
		let errorPayload: any;
		try {
			errorPayload = await response.json();
		} catch (e) {
			// If response is not JSON or fails to parse, use default error
			throw new Error(
				"Failed to generate transport order PDF preview. Server returned an invalid error format.",
			);
		}
		// Assuming error payload might be { error: { message: string } } or { message: string }
		const errorMessage =
			errorPayload?.error?.message ||
			errorPayload?.message ||
			"Failed to generate transport order PDF preview";
		throw new Error(errorMessage);
	}

	// If response.ok is true, we expect the success structure.
	return response.json() as Promise<TransportOrderPDFPreviewResponse>;
};

// Email preview query
export const useTransportOrderEmailPreview = (
	organizationId: string,
	tourId: string,
	recipientEmail?: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: [
			...tourKeys.detail(organizationId, tourId),
			"transport-order-email-preview",
			recipientEmail,
		],
		queryFn: async () => {
			if (!tourId) {
				throw new Error("Tour ID is required");
			}

			const response = await apiClient.tours[":id"][
				"transport-order"
			].previewEmail.$post({
				param: { id: tourId },
				query: {
					organizationId,
					recipientEmail,
				},
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => null);
				let errorMessage =
					"Failed to fetch transport order email preview";

				// Check if the error response has the expected structure
				if (errorData && typeof errorData === "object") {
					if (
						"error" in errorData &&
						typeof errorData.error === "object" &&
						errorData.error &&
						"message" in errorData.error
					) {
						errorMessage = errorData.error.message as string;
					}
				}

				throw new Error(errorMessage);
			}

			return response.json();
		},
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!tourId && !!organizationId,
	});
};

// Send transport order email mutation
export interface SendTransportOrderEmailParams {
	id: string;
	recipientEmail?: string;
	ccEmails?: string[];
	bccEmails?: string[];
	documentIds?: string[]; // Support for document attachments
	documentFilenames?: Record<string, string>; // Custom filenames for email attachments
}

export const useSendTransportOrderEmailMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async ({
			id,
			recipientEmail,
			ccEmails,
			bccEmails,
			documentIds,
			documentFilenames,
		}: SendTransportOrderEmailParams) => {
			const response = await apiClient.tours[":id"][
				"transport-order"
			].send.$post({
				param: { id },
				query: { organizationId },
				json: {
					recipientEmail,
					ccEmails,
					bccEmails,
					documentIds,
					documentFilenames,
				},
			});

			if (!response.ok) {
				// Try to parse error message from response, but handle parsing errors gracefully
				const errorData = await response.json().catch(() => null);
				// Extract error message from any potential response format
				const errorMessage = "Failed to send transport order email";

				console.log(errorData);

				throw new Error(errorMessage);
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Transport order email sent successfully");
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to send transport order email",
			);
			console.error("Send transport order email error:", error);
		},
	});
};

// Fetch email logs for a tour
export const fetchTourEmailLogs = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.tours[":id"]["email-logs"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch tour email logs");
	}

	return response.json();
};

// Hook for fetching email logs for a tour
export const useTourEmailLogsQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: [...tourKeys.detail(organizationId, id), "email-logs"],
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchTourEmailLogs(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};
