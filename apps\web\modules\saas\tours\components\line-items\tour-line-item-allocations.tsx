"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Plus, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import { formatCurrency } from "../../../../../lib/format";
import { TourOrderSelector } from "./tour-order-selector";

export interface Allocation {
	orderId: string;
	allocationType: "percentage" | "fixed";
	percentageValue?: number;
	fixedValue?: number;
}

interface TourLineItemAllocationsProps {
	tourId: string;
	allocations: Allocation[];
	onAllocationsChange: (allocations: Allocation[]) => void;
	lineItemTotalPrice: number;
}

export function TourLineItemAllocations({
	tourId,
	allocations,
	onAllocationsChange,
	lineItemTotalPrice,
}: TourLineItemAllocationsProps) {
	const [remainingPercentage, setRemainingPercentage] = useState(100);
	const [remainingAmount, setRemainingAmount] = useState(lineItemTotalPrice);
	const [allocationsValid, setAllocationsValid] = useState(true);

	// Recalculate remaining values when allocations change
	useEffect(() => {
		// Calculate remaining percentage
		const usedPercentage = allocations.reduce(
			(sum, allocation) =>
				allocation.allocationType === "percentage" &&
				allocation.percentageValue
					? sum + allocation.percentageValue
					: sum,
			0,
		);
		setRemainingPercentage(100 - usedPercentage);

		// Calculate remaining amount
		const usedAmount = allocations.reduce((sum, allocation) => {
			if (
				allocation.allocationType === "fixed" &&
				allocation.fixedValue
			) {
				return sum + allocation.fixedValue;
			}
			if (
				allocation.allocationType === "percentage" &&
				allocation.percentageValue
			) {
				return (
					sum +
					(lineItemTotalPrice * allocation.percentageValue) / 100
				);
			}
			return sum;
		}, 0);
		setRemainingAmount(lineItemTotalPrice - usedAmount);

		// Validate allocations
		setAllocationsValid(remainingPercentage >= 0 && remainingAmount >= 0);
	}, [allocations, lineItemTotalPrice, remainingPercentage, remainingAmount]);

	// Add a new empty allocation
	const handleAddAllocation = () => {
		// Default to percentage allocation with remaining percentage value
		const newAllocation: Allocation = {
			orderId: "",
			allocationType: "percentage",
			percentageValue: remainingPercentage,
		};

		onAllocationsChange([...allocations, newAllocation]);
	};

	// Remove allocation at index
	const handleRemoveAllocation = (index: number) => {
		const updatedAllocations = [...allocations];
		updatedAllocations.splice(index, 1);
		onAllocationsChange(updatedAllocations);
	};

	// Update allocation field value
	const handleFieldChange = (
		index: number,
		field: keyof Allocation,
		value: any,
	) => {
		const updatedAllocations = [...allocations];
		updatedAllocations[index] = {
			...updatedAllocations[index],
			[field]: value,
		};

		// If allocation type changed, update the values accordingly
		if (field === "allocationType") {
			if (value === "percentage") {
				updatedAllocations[index].percentageValue = remainingPercentage;
				updatedAllocations[index].fixedValue = undefined;
			} else if (value === "fixed") {
				updatedAllocations[index].fixedValue = remainingAmount;
				updatedAllocations[index].percentageValue = undefined;
			}
		}

		onAllocationsChange(updatedAllocations);
	};

	// Pre-fill with remaining value if a new order is selected
	const handleOrderChange = (index: number, orderId: string) => {
		const updatedAllocations = [...allocations];
		updatedAllocations[index] = {
			...updatedAllocations[index],
			orderId,
		};
		onAllocationsChange(updatedAllocations);
	};

	return (
		<div className="space-y-3">
			{allocations.length === 0 ? (
				<div className="text-sm text-muted-foreground py-2">
					No allocations added. Add an allocation to split this cost
					between orders.
				</div>
			) : (
				<>
					{allocations.map((allocation, index) => (
						<Card key={index} className="overflow-hidden">
							<CardContent className="p-3">
								<div className="grid grid-cols-4 gap-3 items-end">
									<div className="col-span-4 sm:col-span-1">
										<Label
											htmlFor={`order-${index}`}
											className="mb-1 block text-xs"
										>
											Order
										</Label>
										<TourOrderSelector
											tourId={tourId}
											value={allocation.orderId}
											onChange={(value) =>
												handleOrderChange(index, value)
											}
											className="w-full"
											placeholder="Select order"
										/>
									</div>

									<div>
										<Label
											htmlFor={`type-${index}`}
											className="mb-1 block text-xs"
										>
											Type
										</Label>
										<Select
											value={allocation.allocationType}
											onValueChange={(value) =>
												handleFieldChange(
													index,
													"allocationType",
													value as
														| "percentage"
														| "fixed",
												)
											}
										>
											<SelectTrigger>
												<SelectValue placeholder="Allocation type" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="percentage">
													Percentage
												</SelectItem>
												<SelectItem value="fixed">
													Fixed Amount
												</SelectItem>
											</SelectContent>
										</Select>
									</div>

									{allocation.allocationType ===
									"percentage" ? (
										<div>
											<Label
												htmlFor={`value-${index}`}
												className="mb-1 block text-xs"
											>
												Percentage
											</Label>
											<div className="flex items-center">
												<Input
													id={`value-${index}`}
													type="number"
													min="0"
													max="100"
													step="0.01"
													value={
														allocation.percentageValue ||
														0
													}
													onChange={(e) =>
														handleFieldChange(
															index,
															"percentageValue",
															Number.parseFloat(
																e.target.value,
															),
														)
													}
												/>
												<span className="ml-2">%</span>
											</div>
										</div>
									) : (
										<div>
											<Label
												htmlFor={`value-${index}`}
												className="mb-1 block text-xs"
											>
												Amount
											</Label>
											<Input
												id={`value-${index}`}
												type="number"
												min="0"
												step="0.01"
												value={
													allocation.fixedValue || 0
												}
												onChange={(e) =>
													handleFieldChange(
														index,
														"fixedValue",
														Number.parseFloat(
															e.target.value,
														),
													)
												}
											/>
										</div>
									)}

									<div className="flex justify-end">
										<Button
											variant="ghost"
											size="icon"
											onClick={() =>
												handleRemoveAllocation(index)
											}
										>
											<Trash className="h-4 w-4" />
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</>
			)}

			<div className="flex items-center justify-between">
				<Button
					type="button"
					variant="outline"
					size="sm"
					onClick={handleAddAllocation}
					disabled={
						remainingPercentage <= 0 ||
						allocations.some((a) => !a.orderId)
					}
				>
					<Plus className="mr-2 h-4 w-4" />
					Add Allocation
				</Button>

				<div className="text-sm">
					{!allocationsValid && (
						<span className="text-destructive">
							Allocations exceed total amount
						</span>
					)}
					{allocationsValid && allocations.length > 0 && (
						<span>
							Remaining: {remainingPercentage.toFixed(2)}% (
							{formatCurrency(remainingAmount, "EUR")})
						</span>
					)}
				</div>
			</div>
		</div>
	);
}
