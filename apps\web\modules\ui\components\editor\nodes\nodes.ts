import {} from "@lexical/code";
import {} from "@lexical/link";
import {} from "@lexical/list";
import {} from "@lexical/rich-text";
import {} from "@lexical/table";

import type { Klass, LexicalNode, LexicalNodeReplacement } from "lexical";

import { EmojiNode } from "./emoji-node";

import { MentionNode } from "./mention-node";

export const nodes: ReadonlyArray<Klass<LexicalNode> | LexicalNodeReplacement> =
	[MentionNode, EmojiNode];
