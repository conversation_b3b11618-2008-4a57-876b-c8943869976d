"use client";

import { MemberEmailConfigurationStatus } from "@saas/organizations/member-email-configuration/components/member-email-configuration-status";
import { useState } from "react";
import { useMemberEmailConfiguration } from "../../hooks/use-member-email-configuration";
import { MemberEmailConfigurationForm } from "./member-email-configuration-form";

export function MemberEmailConfigurationManager() {
	const { data: memberEmailConfig, isLoading } =
		useMemberEmailConfiguration();
	const [isEditing, setIsEditing] = useState(false);

	// Show loading state
	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[200px]">
				<div className="animate-spin h-8 w-8 border-b-2 border-primary rounded-full" />
			</div>
		);
	}

	// If no configuration exists or user is creating/editing, show the form
	if (!memberEmailConfig || isEditing) {
		return (
			<MemberEmailConfigurationForm
				onCancel={
					memberEmailConfig ? () => setIsEditing(false) : undefined
				}
				onSuccess={() => setIsEditing(false)}
			/>
		);
	}

	// If configuration exists and not editing, show the status
	return <MemberEmailConfigurationStatus onEdit={() => setIsEditing(true)} />;
}
