import { ChangeOrganizationAddressForm } from "@saas/organizations/components/ChangeOrganizationAddressForm";
import { ChangeOrganizationNameForm } from "@saas/organizations/components/ChangeOrganizationNameForm";
import { CompanyDetailsForm } from "@saas/organizations/components/CompanyDetailsForm";
import { OrganizationDocumentLogoForm } from "@saas/organizations/components/OrganizationDocumentLogoForm";
import { OrganizationLogoForm } from "@saas/organizations/components/OrganizationLogoForm";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.settings.title"),
	};
}

export default function OrganizationSettingsPage() {
	return (
		<SettingsList>
			<OrganizationLogoForm />
			<OrganizationDocumentLogoForm />
			<ChangeOrganizationNameForm />
			<ChangeOrganizationAddressForm />
			<CompanyDetailsForm />
		</SettingsList>
	);
}
