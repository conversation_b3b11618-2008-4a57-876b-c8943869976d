"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { createPersonnelSchema } from "@repo/api/src/routes/personnel/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import type { PersonnelFormValues } from "@saas/personnel/lib/types";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { usePersonnelMutations } from "./use-personnel";

interface UsePersonnelFormProps {
	personnelId?: string;
	onSuccess?: () => void;
	defaultValues?: Partial<PersonnelFormValues>;
}

/**
 * Form hook for personnel data
 * This can be used directly or through the PersonnelFormContext
 * @see apps/web/modules/saas/personnel/context/personnel-form-context.tsx
 */
export function usePersonnelForm({
	personnelId,
	onSuccess,
	defaultValues = {},
}: UsePersonnelFormProps) {
	const { activeOrganization } = useActiveOrganization();
	const api = usePersonnelAPI();

	const form = useForm<PersonnelFormValues>({
		resolver: zodResolver(createPersonnelSchema),
		defaultValues: {
			organizationId: activeOrganization?.id || "",
			firstName: "",
			lastName: "",
			dateOfBirth: null,
			workPermitStart: null,
			workPermitEnd: null,
			visaStart: null,
			visaEnd: null,
			registeredAt: null,
			deregisteredAt: null,
			departmentId: "",
			driverLicenses: [],
			...defaultValues,
		},
	});

	async function onSubmit(data: PersonnelFormValues) {
		console.log("Form data before submission:", data); // Debug log to see form data before submission
		try {
			let success = false;

			if (personnelId) {
				success = await api.updatePersonnelRecord(personnelId, data);
			} else {
				success = await api.createPersonnelRecord(data);
			}

			if (success) {
				onSuccess?.();
			}

			return success;
		} catch (error) {
			toast.error("Something went wrong. Please try again.");
			return false;
		}
	}

	return {
		form,
		onSubmit,
		isLoading: api.isLoading,
	};
}

/**
 * Hook for interacting with personnel data API
 * This is a standalone hook that handles API communication
 * for personnel data, separate from the form context.
 */
export function usePersonnelAPI() {
	const { createPersonnel, updatePersonnel, isLoading } =
		usePersonnelMutations();
	const { activeOrganization } = useActiveOrganization();

	/**
	 * Create a new personnel record
	 */
	const createPersonnelRecord = async (
		data: PersonnelFormValues,
	): Promise<boolean> => {
		if (!activeOrganization?.id) {
			toast.error("No active organization selected");
			return false;
		}

		try {
			const submitData = {
				...data,
				organizationId: activeOrganization.id,
			};

			console.log("Form data before submission:", submitData);

			// Always use FormData submission for consistency
			return handleFileUploadSubmission(submitData, "create");
		} catch (error) {
			console.error("Error creating personnel:", error);
			toast.error("Something went wrong. Please try again.");
			return false;
		}
	};

	/**
	 * Update an existing personnel record
	 */
	const updatePersonnelRecord = async (
		id: string,
		data: PersonnelFormValues,
	): Promise<boolean> => {
		if (!activeOrganization?.id) {
			toast.error("No active organization selected");
			return false;
		}

		try {
			const submitData = {
				...data,
				organizationId: activeOrganization.id,
			};

			// Always use FormData submission for consistency
			return handleFileUploadSubmission(submitData, "update", id);
		} catch (error) {
			console.error("Error updating personnel:", error);
			toast.error("Something went wrong. Please try again.");
			return false;
		}
	};

	/**
	 * Handle form submission with file uploads
	 */
	const handleFileUploadSubmission = async (
		data: PersonnelFormValues,
		mode: "create" | "update",
		id?: string,
	): Promise<boolean> => {
		try {
			// Create FormData instance for multipart/form-data
			const formData = new FormData();

			// Add the JSON data as a string
			// We need to clean the data to remove file objects
			const cleanedData = {
				...data,
				// Pass Date objects directly - API now uses z.coerce.date()
				dateOfBirth: data.dateOfBirth,
				workPermitStart: data.workPermitStart,
				workPermitEnd: data.workPermitEnd,
				visaStart: data.visaStart,
				visaEnd: data.visaEnd,
				registeredAt: data.registeredAt,
				deregisteredAt: data.deregisteredAt,

				// Format driver licenses - just remove file properties
				driverLicenses: data.driverLicenses?.map((license) => {
					// Destructure to remove file-related properties
					const { documentFile, documentUrl, documentName, ...rest } =
						license;

					// Keep Date objects intact
					return {
						...rest,
						issuedAt: rest.issuedAt,
						expiresAt: rest.expiresAt,
						// Keep module dates as Date objects too
						modules: rest.modules?.map((module) => ({
							...module,
							validUntil: module.validUntil,
						})),
					};
				}),
			};

			// Add the cleaned data as a JSON string
			formData.append("data", JSON.stringify(cleanedData));

			// Process license documents and add them to FormData
			if (data.driverLicenses?.length) {
				console.log(
					"Processing license documents:",
					data.driverLicenses,
				);

				// Use Promise.all to wait for all document processing
				await Promise.all(
					data.driverLicenses.map(async (license, index) => {
						console.log(`Processing license ${index}:`, license);

						if (license.documentFile) {
							console.log(
								`Document file for license ${index}:`,
								license.documentFile,
							);

							// Handle all possible document file formats
							if (license.documentFile instanceof File) {
								console.log(
									`Adding direct File object for license ${index}`,
								);
								formData.append(
									`license_document_${index}`,
									license.documentFile,
								);
							}
							// Handle blob URLs
							else if (
								license.documentUrl?.startsWith("blob:") &&
								license.documentName
							) {
								try {
									console.log(
										`Fetching blob for license ${index}:`,
										license.documentUrl,
									);
									const response = await fetch(
										license.documentUrl,
									);
									const blob = await response.blob();
									const file = new File(
										[blob],
										license.documentName,
										{
											type:
												blob.type ||
												"application/octet-stream",
										},
									);
									formData.append(
										`license_document_${index}`,
										file,
									);
								} catch (err) {
									console.error(
										`Error fetching blob for license ${index}:`,
										err,
									);
									const errorMessage =
										err instanceof Error
											? err.message
											: "Unknown error";
									throw new Error(
										`Failed to process document: ${errorMessage}`,
									);
								}
							}
							// Handle path objects (from file inputs)
							else if (
								typeof license.documentFile === "object" &&
								license.documentName
							) {
								try {
									console.log(
										`Creating File from path object for license ${index}`,
									);

									// Use fetch to get the file if it's a URL
									if (license.documentUrl) {
										const response = await fetch(
											license.documentUrl,
										);
										if (response.ok) {
											const blob = await response.blob();
											const file = new File(
												[blob],
												license.documentName,
												{
													type:
														blob.type ||
														"application/octet-stream",
												},
											);
											formData.append(
												`license_document_${index}`,
												file,
											);
										} else {
											throw new Error(
												`Failed to fetch document URL: ${license.documentUrl}`,
											);
										}
									} else {
										console.warn(
											`No valid file source for license ${index}`,
										);
									}
								} catch (err) {
									console.error(
										`Error handling path object for license ${index}:`,
										err,
									);
									const errorMessage =
										err instanceof Error
											? err.message
											: "Unknown error";
									throw new Error(
										`Failed to process document from path: ${errorMessage}`,
									);
								}
							} else {
								console.warn(
									`Unsupported document file format for license ${index}:`,
									license.documentFile,
								);
							}
						}
					}),
				);
			}

			// Make API call with formData - endpoints stay the same
			if (mode === "create") {
				const response = await fetch(
					`/api/personnel?organizationId=${data.organizationId}`,
					{
						method: "POST",
						body: formData,
						// No Content-Type header - browser will set it with boundary
					},
				);

				if (!response.ok) {
					const errorData = await response.json();
					console.error("API error:", errorData);
					throw new Error(
						errorData.error || "Failed to create personnel",
					);
				}

				const result = await response.json();
				toast.success("Personnel created successfully");
				return true;
			}

			if (mode === "update" && id) {
				const response = await fetch(
					`/api/personnel/${id}?organizationId=${data.organizationId}`,
					{
						method: "PUT",
						body: formData,
						// No Content-Type header - browser will set it with boundary
					},
				);

				if (!response.ok) {
					const errorData = await response.json();
					console.error("API error:", errorData);
					throw new Error(
						errorData.error || "Failed to update personnel",
					);
				}

				const result = await response.json();
				toast.success("Personnel updated successfully");
				return true;
			}

			return false;
		} catch (error) {
			console.error(
				`Error ${
					mode === "create" ? "creating" : "updating"
				} personnel with files:`,
				error,
			);
			toast.error(
				"Something went wrong uploading files. Please try again.",
			);
			return false;
		}
	};

	return {
		createPersonnelRecord,
		updatePersonnelRecord,
		isLoading,
	};
}
