import { <PERSON><PERSON><PERSON> } from "node:buffer";
import { config } from "@repo/config";
import { db } from "@repo/database";
import { getSignedUrl } from "@repo/storage";
import { HTTPException } from "hono/http-exception";
import type { CreateInvoiceInput } from "../types";
import { InvoiceType } from "../types";
import { getInvoiceById } from "./invoice.crud";
import { generateInvoicePDFForEmail } from "./invoice.pdf";

// Helper function to get order documents by IDs
async function getOrderDocumentsByIds(documentIds: string[]) {
	return await db.document.findMany({
		where: {
			id: { in: documentIds },
		},
	});
}

// Get document file content from S3
async function getDocumentContent(document: {
	url: string;
}): Promise<Buffer | null> {
	try {
		// Get a signed URL for the document
		const signedUrl = await getSignedUrl(document.url, {
			bucket: config.storage.bucketNames.documents,
			expiresIn: 60, // Short expiration time since we're just using it for download
		});

		// Fetch the file content
		const response = await fetch(signedUrl);
		if (!response.ok) {
			console.error(`Failed to fetch document from S3: ${document.url}`);
			return null;
		}

		const arrayBuffer = await response.arrayBuffer();
		return Buffer.from(arrayBuffer);
	} catch (error) {
		console.error(`Error getting document content: ${error}`);
		return null;
	}
}

// Send an invoice via email using the shared email service
export async function sendInvoiceEmail(
	organizationId: string,
	invoiceId: string,
	userId: string,
	recipientEmail?: string,
	ccEmails?: string[],
	bccEmails?: string[],
	documentIds?: string[],
	customSubject?: string,
	documentFilenames?: Record<string, string>,
) {
	try {
		// Get the invoice with all necessary data for the email
		const invoice = await getInvoiceById(organizationId, invoiceId);
		if (!invoice) {
			throw new HTTPException(404, { message: "Invoice not found" });
		}

		const organization = await db.organization.findFirst({
			where: { id: organizationId },
		});

		// Extract customer email
		const customerEmail =
			recipientEmail ||
			invoice?.contact?.email ||
			invoice?.customer?.email;

		if (!customerEmail) {
			throw new HTTPException(400, {
				message:
					"No recipient email provided and no contact or customer email found",
			});
		}

		// Generate PDF for attachment using the prepared data approach
		// Handle multiple orders for the invoice
		const orderIds = invoice.orderInvoices?.map((oi) => oi.orderId) || [];

		const invoiceData: CreateInvoiceInput = {
			organizationId,
			// If we have multiple orders, use orderIds array instead of single orderId
			...(orderIds.length > 0 ? { orderIds } : undefined),
			customerId: invoice.customerId || undefined,
			contactId: invoice.contactId || undefined,
			// Use vehicleOrderTypeId from first orderInvoice if present
			vehicleOrderTypeId:
				invoice.orderInvoices?.[0]?.vehicleOrderTypeId || undefined,
			invoice_date: invoice.invoice_date || undefined,
			due_date: invoice.due_date || undefined,
			invoice_type:
				(invoice.invoice_type as InvoiceType) || InvoiceType.INVOICE,
			customer_name: invoice.customer_name || undefined,
			customer_tax_number: invoice.customer_tax_number || undefined,
			invoice_number: invoice.invoice_number || undefined,
			// Use the stored billing address snapshot from the invoice
			billing_street: invoice.billing_street || undefined,
			billing_addressSupplement:
				invoice.billing_addressSupplement || undefined,
			billing_zipCode: invoice.billing_zipCode || undefined,
			billing_city: invoice.billing_city || undefined,
			billing_country: invoice.billing_country || undefined,
			// Include line items and stops with type casting to match expected types
			lineItems: (invoice.lineItems || []) as any,
			stops: (invoice.stops || []) as any,
		};

		// Get the PDF buffer using the real invoice number
		const pdfBuffer = await generateInvoicePDFForEmail(
			organizationId,
			invoiceData,
		);

		// Create a meaningful filename for the attachment
		const filename = `${invoice.invoice_number || invoiceId}.pdf`;

		// Create attachment object for the invoice PDF
		const attachments: Array<{
			filename: string;
			content: Buffer;
			contentType?: string;
		}> = [
			{
				filename,
				content: pdfBuffer,
				contentType: "application/pdf",
			},
		];

		// Fetch and add document attachments if requested
		if (documentIds && documentIds.length > 0) {
			const documents = await getOrderDocumentsByIds(documentIds);

			// Add each document as an attachment
			for (const doc of documents) {
				// Get document content from S3
				const content = await getDocumentContent(doc);

				if (content) {
					// Use custom filename if provided, otherwise use original filename
					const customFilename = documentFilenames?.[doc.id];
					const finalFilename = customFilename || doc.fileName;

					// Add to attachments
					attachments.push({
						filename: finalFilename,
						content,
						contentType: doc.fileType || "application/octet-stream",
					});
				}
			}
		}

		// Use the shared email service
		const { sendBusinessEmail } = await import(
			"../../../lib/email-service"
		);

		const result = await sendBusinessEmail({
			organizationId,
			userId,
			templateId: "invoiceEmail",
			recipientEmail: customerEmail,
			ccEmails,
			bccEmails,
			context: {
				invoice,
				organization,
			},
			emailType: "INVOICE",
			entityId: invoiceId,
			entityType: "invoice",
			attachments,
			customSubject,
		});

		if (!result.success) {
			throw new HTTPException(500, {
				message: result.error || "Failed to send invoice email",
			});
		}

		return { success: true };
	} catch (error: any) {
		console.error("Failed to send invoice email:", error);
		throw error instanceof HTTPException
			? error
			: new HTTPException(500, {
					message: error.message || "Failed to send invoice email",
				});
	}
}

// Generate a preview of the invoice email without sending it
export async function previewInvoiceEmail(
	organizationId: string,
	invoiceId: string,
	recipientEmail?: string,
	customSubject?: string,
) {
	try {
		// Get the invoice with all necessary data for the email
		const invoice = await getInvoiceById(organizationId, invoiceId);
		if (!invoice) {
			throw new HTTPException(404, { message: "Invoice not found" });
		}

		const organization = await db.organization.findFirst({
			where: { id: organizationId },
		});

		// Extract customer email (same logic as sendInvoiceEmail)
		const customerEmail =
			recipientEmail ||
			invoice?.contact?.email ||
			invoice?.customer?.email;

		// Get order IDs associated with this invoice
		const orderIds = invoice.orderInvoices?.map((oi) => oi.orderId) || [];

		// Get available documents for all orders associated with this invoice
		const orderDocuments = await db.document.findMany({
			where: {
				entityType: "order",
				entityId: {
					in: orderIds,
				},
			},
		});

		// Generate signed URLs for documents (5 minute expiry)
		const documentsWithUrls = await Promise.all(
			orderDocuments.map(async (doc) => {
				try {
					const signedUrl = await getSignedUrl(doc.url, {
						bucket: config.storage.bucketNames.documents,
						expiresIn: 300, // 5 minutes
					});

					return {
						id: doc.id,
						fileName: doc.fileName,
						fileSize: doc.fileSize,
						signedUrl,
					};
				} catch (error) {
					console.error(
						`Failed to generate signed URL for document ${doc.id}:`,
						error,
					);
					// Return document without signed URL if generation fails
					return {
						id: doc.id,
						fileName: doc.fileName,
						fileSize: doc.fileSize,
					};
				}
			}),
		);

		// Use the shared email service for preview
		const { previewBusinessEmail } = await import(
			"../../../lib/email-service"
		);

		const preview = await previewBusinessEmail({
			organizationId,
			templateId: "invoiceEmail",
			context: {
				invoice,
				organization,
			},
			recipientEmail: customerEmail || undefined,
			customSubject,
		});

		return {
			...preview,
			metadata: {
				...preview.metadata,
				// Add invoice-specific metadata
				attachmentName: `${invoice.invoice_number || invoiceId}.pdf`,
				orderDocuments: documentsWithUrls, // Include documents with signed URLs
			},
		};
	} catch (error: any) {
		console.error("Failed to preview invoice email:", error);
		throw error instanceof HTTPException
			? error
			: new HTTPException(500, {
					message: error.message || "Failed to preview invoice email",
				});
	}
}

// Helper functions for formatting invoice data

function formatBillingAddress(invoice: any): string {
	const parts = [];
	if (invoice.billing_street) {
		parts.push(invoice.billing_street);
	}
	if (invoice.billing_addressSupplement) {
		parts.push(invoice.billing_addressSupplement);
	}
	const cityParts = [];
	if (invoice.billing_zipCode) {
		cityParts.push(invoice.billing_zipCode);
	}
	if (invoice.billing_city) {
		cityParts.push(invoice.billing_city);
	}
	if (cityParts.length > 0) {
		parts.push(cityParts.join(" "));
	}
	if (invoice.billing_country) {
		parts.push(invoice.billing_country);
	}

	return parts.join("\n") || "No address provided";
}

function formatDate(date?: Date | string): string {
	if (!date) {
		return "";
	}

	const d = new Date(date);
	return `${d.getDate().toString().padStart(2, "0")}.${(d.getMonth() + 1).toString().padStart(2, "0")}.${d.getFullYear()}`;
}

function formatStopAddress(stop: any): string {
	const parts = [];
	if (stop.address?.street || stop.street) {
		parts.push(stop.address?.street || stop.street);
	}
	if (stop.address?.addressSupplement || stop.addressSupplement) {
		parts.push(stop.address?.addressSupplement || stop.addressSupplement);
	}
	if (stop.address?.zipCode || stop.zipCode) {
		parts.push(stop.address?.zipCode || stop.zipCode);
	}
	if (stop.address?.city || stop.city) {
		parts.push(stop.address?.city || stop.city);
	}
	if (stop.address?.country || stop.country) {
		parts.push(stop.address?.country || stop.country);
	}

	return parts.join(", ") || "Address not provided";
}

// Function to create loading/unloading descriptions from invoice stops
function createStopDescriptions(invoice: any): {
	loading: string;
	unloading: string;
} {
	const result = {
		loading: "",
		unloading: "",
	};

	if (!invoice.stops || invoice.stops.length === 0) {
		return result;
	}

	// Process loading stops
	const loadingStops = invoice.stops.filter(
		(stop: any) => stop.stopType === "loading",
	);
	if (loadingStops.length > 0) {
		const stop = loadingStops[0]; // Use first loading stop
		const address = formatStopAddress(stop);
		const date = stop.datetime_start
			? formatDate(stop.datetime_start)
			: "N/A";
		result.loading = `${date} - ${address}`;
	}

	// Process unloading stops
	const unloadingStops = invoice.stops.filter(
		(stop: any) => stop.stopType === "unloading",
	);
	if (unloadingStops.length > 0) {
		const stop = unloadingStops[0]; // Use first unloading stop
		const address = formatStopAddress(stop);
		const date = stop.datetime_start
			? formatDate(stop.datetime_start)
			: "N/A";
		result.unloading = `${date} - ${address}`;
	}

	return result;
}

// New function that combines order data and invoice items into the new table format
function createInvoiceTableWithOrderData(invoice: any): string[][] {
	const result: string[][] = [];

	try {
		// Create order info row with all details
		let orderDescription = `Order Number: ${invoice.orderNumber || invoice.order_number || "N/A"}\n`;
		if (invoice.customer_order_number) {
			orderDescription += `Customer Order Number: ${invoice.customer_order_number}\n`;
		}
		orderDescription += `Date: ${formatDate(invoice.invoice_date) || "N/A"}\n`;
		orderDescription += `Truck: ${invoice.truck || ""} Trailer: ${invoice.trailer || ""}\n`;

		// Add loading/unloading info
		const stops = createStopDescriptions(invoice);
		if (stops.loading) {
			orderDescription += `Loading: ${stops.loading}\n`;
			const goodsInfo = getGoodsInfoForLoading(invoice);
			orderDescription += `Goods: ${goodsInfo || ""}\n`;
		}
		if (stops.unloading) {
			orderDescription += `Unloading: ${stops.unloading}\n`;
			const goodsInfo = getGoodsInfoForUnloading(invoice);
			orderDescription += `Goods: ${goodsInfo || ""}`;
		}

		// Add order header row - ensure it has all 6 columns with empty values for unused ones
		result.push(["1", orderDescription, "", "", "", ""]);

		// Add line items
		if (invoice.lineItems && invoice.lineItems.length > 0) {
			invoice.lineItems.forEach((item: any) => {
				// Get appropriate quantities with unit if available
				const quantity = item.quantity
					? item.unit
						? `${item.quantity} ${item.unit}`
						: item.quantity.toString()
					: "";

				// Format currency values
				const unitPrice = formatCurrency(item.unitPrice, item.currency);

				// Get VAT rate with % symbol
				const vatRate =
					item.vatRate !== undefined && item.vatRate !== null
						? item.vatRate
						: item.vatRate || 0; // Use vatRate as fallback

				const vat = `${vatRate.toLocaleString("de-DE", {
					minimumFractionDigits: 1,
					maximumFractionDigits: 1,
				})}%`;

				const totalPrice = formatCurrency(
					item.totalPrice,
					item.currency,
				);

				// Create a properly formatted row with all 6 columns
				result.push([
					"",
					item.description || "Service",
					quantity,
					unitPrice,
					vat,
					totalPrice,
				]);
			});
		} else if (invoice.invoiceTable && invoice.invoiceTable.length > 0) {
			// Handle legacy data format by transforming to new format
			invoice.invoiceTable.forEach((row: any) => {
				if (Array.isArray(row)) {
					if (row.length === 4) {
						// Transform from old 4-column format to new 6-column format
						// Old format: [description, quantity, unitPrice, totalPrice]
						// New format: [nr, description, quantity, unitPrice, vat, totalPrice]
						result.push([
							"",
							row[0] || "Service",
							row[1] || "",
							row[2] || "0.00 EUR",
							"0.0%", // Default VAT if not available
							row[3] || "0.00 EUR",
						]);
					} else {
						// If we can't determine the structure, add it as is with empty columns if needed
						const paddedRow = [...row];
						while (paddedRow.length < 6) {
							paddedRow.push("");
						}
						result.push(paddedRow.slice(0, 6));
					}
				}
			});
		} else {
			// Default row if no line items
			result.push(["", "Service", "", "0.00 EUR", "0.0%", "0.00 EUR"]);
		}
	} catch (error) {
		console.error("Error creating invoice table data:", error);
		// Provide at least a basic table in case of error
		if (result.length === 0) {
			result.push(["1", "Order Information", "", "", "", ""]);
			result.push(["", "Service", "", "0.00 EUR", "0.0%", "0.00 EUR"]);
		}
	}

	return result;
}

// Helper functions to extract goods info from loading/unloading stops
function getGoodsInfoForLoading(invoice: any): string {
	if (!invoice.stops || invoice.stops.length === 0) {
		return invoice.goods || "";
	}

	const loadingStops = invoice.stops.filter(
		(stop: any) => stop.stopType === "loading",
	);

	if (loadingStops.length > 0) {
		// Try to get goods_information from the first loading stop
		return loadingStops[0].goods_information || invoice.goods || "";
	}

	return invoice.goods || "";
}

function getGoodsInfoForUnloading(invoice: any): string {
	if (!invoice.stops || invoice.stops.length === 0) {
		return invoice.goods || "";
	}

	const unloadingStops = invoice.stops.filter(
		(stop: any) => stop.stopType === "unloading",
	);

	if (unloadingStops.length > 0) {
		// Try to get goods_information from the first unloading stop
		return unloadingStops[0].goods_information || invoice.goods || "";
	}

	return invoice.goods || "";
}

function createTotalTable(invoice: any): string[][] {
	const result: string[][] = [];

	// Add financial data from invoice.financialData if available
	if (invoice.financialData && invoice.financialData.length > 0) {
		// Use the first currency totals entry
		const totals = invoice.financialData[0];
		const currency = totals.currency || "EUR";

		result.push(["Netto:", formatCurrency(totals.subtotal, currency)]);
		result.push([
			`${totals.taxAmount > 0 ? "19" : "0"}% VAT:`,
			formatCurrency(totals.taxAmount, currency),
		]);
		result.push(["Brutto:", formatCurrency(totals.totalAmount, currency)]);
	} else {
		// Default rows if no financial data
		result.push(["Netto:", "0.00 EUR"]);
		result.push(["0% VAT:", "0.00 EUR"]);
		result.push(["Brutto:", "0.00 EUR"]);
	}

	return result;
}

function formatCurrency(amount?: number, currency = "EUR"): string {
	if (amount === undefined || amount === null) {
		return `0.00 ${currency}`;
	}

	// Format the amount with thousand separators and two decimal places
	const formatted = amount.toLocaleString("de-DE", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	});

	return `${formatted} ${currency}`;
}
