"use client";

import { UniversalEmailPreview } from "@saas/shared/components/universal-email-preview";
import { useUniversalSendEmail } from "@saas/shared/hooks/use-universal-send-email";
import type { SendEmailParams } from "@saas/shared/types/email-preview";

interface OrderConfirmationEmailPreviewProps {
	orderId: string | null;
	recipientEmail?: string;
	isOpen: boolean;
	onClose: () => void;
	onBack?: () => void; // For going back to PDF preview
	onSend: (
		orderId: string,
		email?: string,
		documentIds?: string[],
	) => Promise<void>;
	isSending?: boolean;
}

export function OrderConfirmationEmailPreview({
	orderId,
	recipientEmail,
	isOpen,
	onClose,
	onBack,
	onSend,
	isSending = false,
}: OrderConfirmationEmailPreviewProps) {
	const { sendEmail } = useUniversalSendEmail();

	// Adapter function to convert universal params to the original onSend interface
	const handleUniversalSend = async (
		params: SendEmailParams,
	): Promise<void> => {
		// Always use the universal send email hook for consistency and full feature support
		await sendEmail(params);
	};

	return (
		<UniversalEmailPreview
			entityId={orderId}
			entityType="order-confirmation"
			isOpen={isOpen}
			onClose={onClose}
			onSend={handleUniversalSend}
			isSending={isSending}
			recipientEmail={recipientEmail}
			onBack={onBack}
		/>
	);
}
