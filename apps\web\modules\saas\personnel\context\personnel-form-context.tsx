"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { createPersonnelSchema } from "@repo/api/src/routes/personnel/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { usePersonnelAPI } from "@saas/personnel/hooks/use-personnel-form";
import {
	type PersonnelFormValues,
	defaultPersonnelFormValues,
} from "@saas/personnel/lib/types";
import {
	type ReactNode,
	createContext,
	useContext,
	useEffect,
	useState,
} from "react";
import { FormProvider, useForm } from "react-hook-form";
import { toast } from "sonner";

interface PersonnelFormContextValue {
	// Form state
	formState: PersonnelFormValues;
	isSubmitting: boolean;

	// Form actions
	updateFormValues: (values: Partial<PersonnelFormValues>) => void;
	resetForm: () => void;
	submitForm: () => Promise<boolean>;

	// Helpers
	isDirty: boolean;
	getValues: () => PersonnelFormValues;
}

const PersonnelFormContext = createContext<PersonnelFormContextValue | null>(
	null,
);

interface PersonnelFormProviderProps {
	children: ReactNode;
	personnelId?: string;
	initialValues?: Partial<PersonnelFormValues>;
	onSuccess?: () => void;
	onSubmitStart?: () => void;
	onFormChange?: (hasChanges: boolean) => void;
}

export function PersonnelFormProvider({
	children,
	personnelId,
	initialValues = {},
	onSuccess,
	onSubmitStart,
	onFormChange,
}: PersonnelFormProviderProps) {
	const { activeOrganization } = useActiveOrganization();
	const { createPersonnelRecord, updatePersonnelRecord, isLoading } =
		usePersonnelAPI();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Initialize the form with combined default values and any provided initial values
	const methods = useForm<PersonnelFormValues>({
		resolver: zodResolver(createPersonnelSchema),
		defaultValues: {
			...defaultPersonnelFormValues,
			organizationId: activeOrganization?.id || "",
			dateOfBirth: null,
			workPermitStart: null,
			workPermitEnd: null,
			visaStart: null,
			visaEnd: null,
			registeredAt: null,
			deregisteredAt: null,
			departmentId: "",
			driverLicenses: [],
			languages: [],
			...initialValues,
		},
		mode: "onChange", // Validate on change for better UX
	});

	// Store the form values in a local state ref so we can access them across steps
	const [formState, setFormState] = useState<PersonnelFormValues>(
		methods.getValues(),
	);

	// Update our local formState whenever the form values change
	useEffect(() => {
		const subscription = methods.watch((value, { name, type }) => {
			setFormState(value as PersonnelFormValues);
			// Notify parent component of changes
			if (type === "change") {
				onFormChange?.(true);
			}
		});

		return () => subscription.unsubscribe();
	}, [methods, onFormChange]);

	// Update form values (useful for programmatically setting values)
	const updateFormValues = (values: Partial<PersonnelFormValues>) => {
		Object.entries(values).forEach(([key, value]) => {
			methods.setValue(key as any, value as any, {
				shouldValidate: true,
				shouldDirty: true,
			});
		});
	};

	// Reset form to initial values
	const resetForm = () => {
		methods.reset({
			...defaultPersonnelFormValues,
			organizationId: activeOrganization?.id || "",
			...initialValues,
		});
	};

	// Submit the form
	const submitForm = async (): Promise<boolean> => {
		try {
			const valid = await methods.trigger();
			if (!valid) {
				toast.error(
					"Please fix the errors in the form before submitting.",
				);
				return false;
			}

			if (!activeOrganization?.id) {
				toast.error("No active organization selected");
				return false;
			}

			setIsSubmitting(true);
			onSubmitStart?.(); // Notify parent of submission start

			const formData = methods.getValues();
			let success = false;

			if (personnelId) {
				// Update existing personnel
				success = await updatePersonnelRecord(personnelId, formData);
			} else {
				// Create new personnel
				success = await createPersonnelRecord(formData);
			}

			if (success) {
				onSuccess?.();
			}

			return success;
		} catch (error) {
			console.error(error);
			toast.error("Something went wrong. Please try again.");
			return false;
		} finally {
			setIsSubmitting(false);
		}
	};

	const contextValue: PersonnelFormContextValue = {
		formState,
		isSubmitting: isSubmitting || isLoading,
		updateFormValues,
		resetForm,
		submitForm,
		isDirty: methods.formState.isDirty,
		getValues: methods.getValues,
	};

	return (
		<PersonnelFormContext.Provider value={contextValue}>
			<FormProvider {...methods}>{children}</FormProvider>
		</PersonnelFormContext.Provider>
	);
}

export function usePersonnelForm() {
	const context = useContext(PersonnelFormContext);
	if (!context) {
		throw new Error(
			"usePersonnelForm must be used within a PersonnelFormProvider",
		);
	}
	return context;
}
