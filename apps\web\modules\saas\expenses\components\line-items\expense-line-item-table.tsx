"use client";

import type { ExpenseLineItemInput } from "@repo/api/src/routes/costs/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useExpenseCategoriesQuery } from "@saas/organizations/lib/api-expense-categories";

import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { ExpenseCategorySelector } from "@ui/components/expense-category-selector";
import { Input } from "@ui/components/input";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import {
	AlertTriangle,
	CheckCircle2,
	Edit,
	Plus,
	Settings,
	Trash2,
	X,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

// Infer the line item type from API response instead of hardcoding
type ExpenseResponse = Awaited<
	ReturnType<typeof import("@saas/expenses/lib/api").fetchExpenseById>
>;
type ExpenseLineItemFromAPI = ExpenseResponse["lineItems"][0];

// Extended interface for line items with allocation status - properly typed from API
export interface ExpenseLineItemWithAllocations extends ExpenseLineItemFromAPI {
	// Add computed properties that aren't in the API response but needed for UI
	allocationCount?: number;
	// Store the actual allocation data for frontend editing
	savedAllocations?: Array<{
		type: "general" | "order" | "vehicle" | "personnel";
		method: "fixed" | "percentage";
		value: number;
		percentage?: number;
		entityIds: string[];
		selectAll: boolean;
		allocation_start?: Date;
		allocation_end?: Date;
		notes?: string;
	}>;
	// Mark items generated by OCR for visual indication
	isOcrGenerated?: boolean;
	// Mark items where category was auto-assigned by OCR
	isOcrCategorized?: boolean;
	// Mark items where allocation was auto-assigned by OCR
	isOcrAllocated?: boolean;
	// Note: isFullyAllocated, remainingAmount, and allocatedAmount already exist in API response
}

interface ExpenseLineItemTableProps {
	items: ExpenseLineItemWithAllocations[];
	onCreateItem: (item: ExpenseLineItemInput) => void;
	onUpdateItem: (item: ExpenseLineItemInput & { id: string }) => void;
	onDeleteItem: (itemId: string) => void;
	onAllocateItem: (itemId: string) => void;
	currency?: string;
}

export function ExpenseLineItemTable({
	items,
	onCreateItem,
	onUpdateItem,
	onDeleteItem,
	onAllocateItem,
	currency = "EUR",
}: ExpenseLineItemTableProps) {
	const { activeOrganization } = useActiveOrganization();

	// Fetch categories for name lookup
	const { data: categoriesData } = useExpenseCategoriesQuery({
		organizationId: activeOrganization?.id || "",
		page: 1,
		limit: 100,
	});

	const getCategoryName = (categoryId: string | null | undefined) => {
		if (!categoryId) {
			return "-";
		}
		const category = categoriesData?.items?.find(
			(cat) => cat.id === categoryId,
		);
		return category?.name || "-";
	};

	const [isCreating, setIsCreating] = useState(false);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editingItem, setEditingItem] = useState<ExpenseLineItemInput | null>(
		null,
	);
	const [newItem, setNewItem] = useState<ExpenseLineItemInput>({
		description: "",
		quantity: 1,
		unit: "",
		unitPrice: 0,
		totalPrice: 0,
		vatRate: 0,
		vatAmount: 0,
		categoryId: "",
		notes: "",
		allocations: [], // Required by API schema
	});

	const formatCurrency = (value: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(value);
	};

	const calculateTotalPrice = (quantity: number, unitPrice: number) => {
		return quantity * unitPrice;
	};

	const calculateVatAmount = (totalPrice: number, vatRate: number) => {
		return (totalPrice * vatRate) / 100;
	};

	const calculateItemTotals = (
		quantity: number,
		unitPrice: number,
		vatRate: number,
	) => {
		const totalPrice = calculateTotalPrice(quantity, unitPrice);
		const vatAmount = calculateVatAmount(totalPrice, vatRate);
		return { totalPrice, vatAmount };
	};

	const handleStartCreate = () => {
		setIsCreating(true);
		setNewItem({
			description: "",
			quantity: 1,
			unit: "",
			unitPrice: 0,
			totalPrice: 0,
			vatRate: 0,
			vatAmount: 0,
			categoryId: "",
			notes: "",
			allocations: [], // Required by API schema
		});
	};

	const handleSaveNew = () => {
		try {
			const { totalPrice, vatAmount } = calculateItemTotals(
				newItem.quantity || 1,
				newItem.unitPrice || 0,
				newItem.vatRate || 0,
			);

			const itemToCreate = {
				...newItem,
				totalPrice,
				vatAmount,
			};
			onCreateItem(itemToCreate);
			setIsCreating(false);
			toast.success("Line item added");
		} catch (error) {
			console.error("Create item error:", error);
			toast.error("Failed to add line item");
		}
	};

	const handleCancelCreate = () => {
		setIsCreating(false);
	};

	const handleStartEdit = (item: ExpenseLineItemWithAllocations) => {
		if (!item.id) {
			return;
		}
		setEditingId(item.id);
		setEditingItem({
			description: item.description,
			quantity: item.quantity || 1,
			unit: item.unit || "",
			unitPrice: item.unitPrice || 0,
			totalPrice: item.totalPrice || 0,
			vatRate: item.vatRate || 0,
			vatAmount: item.vatAmount || 0,
			categoryId: item.categoryId || "",
			notes: item.notes || "",
			allocations: [],
		});
	};

	const handleSaveEdit = () => {
		if (!editingId || !editingItem) {
			return;
		}

		const { totalPrice, vatAmount } = calculateItemTotals(
			editingItem.quantity || 1,
			editingItem.unitPrice || 0,
			editingItem.vatRate || 0,
		);

		const updatedItem = {
			...editingItem,
			id: editingId,
			totalPrice,
			vatAmount,
		};

		onUpdateItem(updatedItem);
		setEditingId(null);
		setEditingItem(null);
		toast.success("Line item updated");
	};

	const handleCancelEdit = () => {
		setEditingId(null);
		setEditingItem(null);
	};

	// Calculate allocated amount from savedAllocations (for OCR line items)
	const calculateAllocatedAmountFromSaved = (
		item: ExpenseLineItemWithAllocations,
	): number => {
		if (!item.savedAllocations || item.savedAllocations.length === 0) {
			return 0;
		}

		return item.savedAllocations.reduce((total, allocation) => {
			if (allocation.method === "fixed") {
				return total + allocation.value;
			}
			// Percentage allocation
			return total + ((item.totalPrice || 0) * allocation.value) / 100;
		}, 0);
	};

	// Get the effective allocated amount (backend or calculated from savedAllocations)
	const getEffectiveAllocatedAmount = (
		item: ExpenseLineItemWithAllocations,
	): number => {
		// For new OCR items, calculate from savedAllocations
		if (item.savedAllocations && item.savedAllocations.length > 0) {
			return calculateAllocatedAmountFromSaved(item);
		}
		// For existing items, use backend allocatedAmount
		return item.allocatedAmount || 0;
	};

	const getAllocationStatus = (item: ExpenseLineItemWithAllocations) => {
		// Calculate allocation count from both backend allocations and savedAllocations
		const backendAllocationCount = item.allocations?.length || 0;
		const savedAllocationCount = item.savedAllocations?.length || 0;
		const totalAllocationCount = Math.max(
			backendAllocationCount,
			savedAllocationCount,
		);

		// Get effective allocated amount
		const allocatedAmount = getEffectiveAllocatedAmount(item);
		const totalAmount = item.totalPrice || 0;
		const isFullyAllocated =
			allocatedAmount >= totalAmount && totalAmount > 0;

		if (totalAllocationCount === 0) {
			return {
				icon: <X className="h-4 w-4 text-muted-foreground" />,
				text: "No allocations",
				color: "text-muted-foreground",
			};
		}

		if (isFullyAllocated) {
			return {
				icon: <CheckCircle2 className="h-4 w-4 text-green-600" />,
				text: "Fully allocated",
				color: "text-green-600",
			};
		}

		return {
			icon: <AlertTriangle className="h-4 w-4 text-yellow-600" />,
			text: "Partially allocated",
			color: "text-yellow-600",
		};
	};

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
				<CardTitle className="text-lg">Line Items</CardTitle>
				<Button
					type="button"
					onClick={handleStartCreate}
					disabled={isCreating}
					size="sm"
					variant="outline"
				>
					<Plus className="h-4 w-4 mr-2" />
					Add Line Item
				</Button>
			</CardHeader>
			<CardContent>
				<div className="border rounded-lg overflow-hidden">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead className="w-[300px]">
									Description
								</TableHead>
								<TableHead className="w-[60px]">Qty</TableHead>
								<TableHead className="w-[60px]">Unit</TableHead>
								<TableHead className="w-[140px]">
									Unit Price
								</TableHead>
								<TableHead className="w-[80px]">
									VAT %
								</TableHead>
								<TableHead className="w-[120px]">
									Total
								</TableHead>
								<TableHead className="w-[200px]">
									Category
								</TableHead>
								<TableHead className="w-[150px]">
									Allocation
								</TableHead>
								<TableHead className="w-[120px]">
									Actions
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{/* Create New Item Row */}
							{isCreating && (
								<TableRow className="bg-muted/50">
									<TableCell>
										<Input
											value={newItem.description}
											onChange={(e) =>
												setNewItem({
													...newItem,
													description: e.target.value,
												})
											}
											placeholder="Item description"
											className="border border-muted-foreground/20 bg-background/50"
										/>
									</TableCell>
									<TableCell>
										<Input
											type="number"
											value={newItem.quantity || 1}
											onChange={(e) =>
												setNewItem({
													...newItem,
													quantity:
														Number.parseInt(
															e.target.value,
														) || 1,
												})
											}
											className="border border-muted-foreground/20 bg-background/50 w-14 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
											min="1"
										/>
									</TableCell>
									<TableCell>
										<Input
											value={newItem.unit || ""}
											onChange={(e) =>
												setNewItem({
													...newItem,
													unit: e.target.value,
												})
											}
											placeholder="pcs"
											className="border border-muted-foreground/20 bg-background/50 w-14"
										/>
									</TableCell>
									<TableCell>
										<Input
											type="number"
											step="0.01"
											value={newItem.unitPrice || 0}
											onChange={(e) =>
												setNewItem({
													...newItem,
													unitPrice:
														Number.parseFloat(
															e.target.value,
														) || 0,
												})
											}
											className="border border-muted-foreground/20 bg-background/50 w-full [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
											min="0"
										/>
									</TableCell>
									<TableCell>
										<Input
											type="number"
											step="0.1"
											value={newItem.vatRate || 0}
											onChange={(e) =>
												setNewItem({
													...newItem,
													vatRate:
														Number.parseFloat(
															e.target.value,
														) || 0,
												})
											}
											className="border border-muted-foreground/20 bg-background/50 w-16 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
											min="0"
											max="100"
										/>
									</TableCell>
									<TableCell>
										<div className="space-y-1">
											<span className="font-medium">
												{formatCurrency(
													calculateTotalPrice(
														newItem.quantity || 1,
														newItem.unitPrice || 0,
													),
												)}
											</span>
											{(newItem.vatRate || 0) > 0 && (
												<div className="text-xs text-muted-foreground">
													+
													{formatCurrency(
														calculateVatAmount(
															calculateTotalPrice(
																newItem.quantity ||
																	1,
																newItem.unitPrice ||
																	0,
															),
															newItem.vatRate ||
																0,
														),
													)}{" "}
													VAT
												</div>
											)}
										</div>
									</TableCell>
									<TableCell>
										<ExpenseCategorySelector
											value={newItem.categoryId || ""}
											onChange={(value) =>
												setNewItem({
													...newItem,
													categoryId: value,
												})
											}
											name="categoryId"
											placeholder="Category"
											className="border-0 bg-transparent text-sm h-8"
										/>
									</TableCell>
									<TableCell>
										<span className="text-sm text-muted-foreground">
											No allocations
										</span>
									</TableCell>
									<TableCell>
										<div className="flex gap-1">
											<Button
												type="button"
												size="sm"
												onClick={handleSaveNew}
												variant="outline"
												title="Save"
												className="h-8 w-8 p-0"
											>
												<CheckCircle2 className="h-4 w-4" />
											</Button>
											<Button
												type="button"
												size="sm"
												onClick={handleCancelCreate}
												variant="ghost"
												title="Cancel"
												className="h-8 w-8 p-0"
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									</TableCell>
								</TableRow>
							)}

							{/* Existing Items */}
							{items.map((item) => {
								const allocationStatus =
									getAllocationStatus(item);
								const isEditing = editingId === item.id;

								if (isEditing && editingItem) {
									return (
										<TableRow
											key={item.id}
											className="bg-muted/30"
										>
											<TableCell>
												<Input
													value={
														editingItem.description
													}
													onChange={(e) =>
														setEditingItem({
															...editingItem,
															description:
																e.target.value,
														})
													}
													className="border border-muted-foreground/20 bg-background/50"
												/>
											</TableCell>
											<TableCell>
												<Input
													type="number"
													value={
														editingItem.quantity ||
														1
													}
													onChange={(e) =>
														setEditingItem({
															...editingItem,
															quantity:
																Number.parseInt(
																	e.target
																		.value,
																) || 1,
														})
													}
													className="border border-muted-foreground/20 bg-background/50 w-14 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
													min="1"
												/>
											</TableCell>
											<TableCell>
												<Input
													value={
														editingItem.unit || ""
													}
													onChange={(e) =>
														setEditingItem({
															...editingItem,
															unit: e.target
																.value,
														})
													}
													className="border border-muted-foreground/20 bg-background/50 w-14"
												/>
											</TableCell>
											<TableCell>
												<Input
													type="number"
													step="0.01"
													value={
														editingItem.unitPrice ||
														0
													}
													onChange={(e) =>
														setEditingItem({
															...editingItem,
															unitPrice:
																Number.parseFloat(
																	e.target
																		.value,
																) || 0,
														})
													}
													className="border border-muted-foreground/20 bg-background/50 w-full [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
													min="0"
												/>
											</TableCell>
											<TableCell>
												<Input
													type="number"
													step="0.1"
													value={
														editingItem.vatRate || 0
													}
													onChange={(e) =>
														setEditingItem({
															...editingItem,
															vatRate:
																Number.parseFloat(
																	e.target
																		.value,
																) || 0,
														})
													}
													className="border border-muted-foreground/20 bg-background/50 w-16 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
													min="0"
													max="100"
												/>
											</TableCell>
											<TableCell>
												<div className="space-y-1">
													<span className="font-medium">
														{formatCurrency(
															calculateTotalPrice(
																editingItem.quantity ||
																	1,
																editingItem.unitPrice ||
																	0,
															),
														)}
													</span>
													{(editingItem.vatRate ||
														0) > 0 && (
														<div className="text-xs text-muted-foreground">
															+
															{formatCurrency(
																calculateVatAmount(
																	calculateTotalPrice(
																		editingItem.quantity ||
																			1,
																		editingItem.unitPrice ||
																			0,
																	),
																	editingItem.vatRate ||
																		0,
																),
															)}{" "}
															VAT
														</div>
													)}
												</div>
											</TableCell>
											<TableCell>
												<ExpenseCategorySelector
													value={
														editingItem.categoryId ||
														""
													}
													onChange={(value) =>
														setEditingItem({
															...editingItem,
															categoryId: value,
														})
													}
													name="categoryId"
													placeholder="Category"
													className="border-0 bg-transparent text-sm h-8"
												/>
											</TableCell>
											<TableCell>
												<div className="flex items-center gap-2">
													{allocationStatus.icon}
													<span
														className={`text-sm ${allocationStatus.color}`}
													>
														{formatCurrency(
															getEffectiveAllocatedAmount(
																item,
															),
														)}{" "}
														/{" "}
														{formatCurrency(
															calculateTotalPrice(
																editingItem.quantity ||
																	1,
																editingItem.unitPrice ||
																	0,
															),
														)}
													</span>
												</div>
											</TableCell>
											<TableCell>
												<div className="flex gap-1">
													<Button
														type="button"
														size="sm"
														onClick={handleSaveEdit}
														variant="outline"
														title="Save"
														className="h-8 w-8 p-0"
													>
														<CheckCircle2 className="h-4 w-4" />
													</Button>
													<Button
														type="button"
														size="sm"
														onClick={
															handleCancelEdit
														}
														variant="ghost"
														title="Cancel"
														className="h-8 w-8 p-0"
													>
														<X className="h-4 w-4" />
													</Button>
												</div>
											</TableCell>
										</TableRow>
									);
								}

								return (
									<TableRow key={item.id}>
										<TableCell>
											<div>
												<div className="font-medium">
													{item.description}
												</div>
												{item.notes && (
													<div className="text-sm text-muted-foreground mt-1">
														{item.notes}
													</div>
												)}
											</div>
										</TableCell>
										<TableCell>
											<span>{item.quantity || 1}</span>
										</TableCell>
										<TableCell>
											<span>{item.unit || "-"}</span>
										</TableCell>
										<TableCell>
											<span>
												{formatCurrency(
													item.unitPrice || 0,
												)}
											</span>
										</TableCell>
										<TableCell>
											<span>{item.vatRate || 0}%</span>
										</TableCell>
										<TableCell>
											<span className="font-medium">
												{formatCurrency(
													item.totalPrice || 0,
												)}
											</span>
										</TableCell>
										<TableCell>
											<span>
												{getCategoryName(
													item.categoryId,
												)}
											</span>
										</TableCell>
										<TableCell>
											<div className="flex items-center gap-2">
												{allocationStatus.icon}
												<span
													className={`text-sm ${allocationStatus.color}`}
												>
													{formatCurrency(
														getEffectiveAllocatedAmount(
															item,
														),
													)}{" "}
													/{" "}
													{formatCurrency(
														item.totalPrice || 0,
													)}
												</span>
											</div>
										</TableCell>
										<TableCell>
											<div className="flex gap-1">
												<Button
													type="button"
													size="sm"
													onClick={() =>
														handleStartEdit(item)
													}
													variant="ghost"
													disabled={!item.id}
													title="Edit line item"
													className="h-8 w-8 p-0"
												>
													<Edit className="h-4 w-4" />
												</Button>
												<Button
													type="button"
													size="sm"
													onClick={() =>
														onAllocateItem(
															item.id || "",
														)
													}
													variant="ghost"
													disabled={!item.id}
													title="Manage allocations"
													className="h-8 w-8 p-0"
												>
													<Settings className="h-4 w-4" />
												</Button>
												<Button
													type="button"
													size="sm"
													onClick={() =>
														onDeleteItem(
															item.id || "",
														)
													}
													variant="ghost"
													className="text-destructive hover:text-destructive h-8 w-8 p-0"
													disabled={!item.id}
													title="Delete line item"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								);
							})}

							{/* Empty State */}
							{items.length === 0 && !isCreating && (
								<TableRow>
									<TableCell
										colSpan={9}
										className="text-center py-8"
									>
										<div className="text-muted-foreground">
											<Plus className="h-8 w-8 mx-auto mb-2" />
											<p>No line items added yet</p>
											<p className="text-sm">
												Click "Add Line Item" to start
												adding items
											</p>
										</div>
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>

				{/* Summary Section */}
				{items.length > 0 && (
					<div className="mt-4 p-4 bg-muted/30 rounded-lg">
						<h4 className="font-medium mb-3">Summary</h4>
						<div className="space-y-2 text-sm">
							{/* Net Amount */}
							<div className="flex justify-between">
								<span>Net Amount:</span>
								<span className="font-medium">
									{formatCurrency(
										items.reduce(
											(sum, item) =>
												sum + (item.totalPrice || 0),
											0,
										),
									)}
								</span>
							</div>

							{/* VAT Breakdown */}
							{(() => {
								const vatGroups = items.reduce(
									(acc, item) => {
										const vatRate = item.vatRate || 0;
										const vatAmount = item.vatAmount || 0;
										if (vatRate > 0) {
											if (!acc[vatRate]) {
												acc[vatRate] = 0;
											}
											acc[vatRate] += vatAmount;
										}
										return acc;
									},
									{} as Record<number, number>,
								);

								return Object.entries(vatGroups).map(
									([rate, amount]) => (
										<div
											key={rate}
											className="flex justify-between text-muted-foreground"
										>
											<span>VAT {rate}%:</span>
											<span>
												{formatCurrency(amount)}
											</span>
										</div>
									),
								);
							})()}

							{/* Total VAT */}
							<div className="flex justify-between">
								<span>Total VAT:</span>
								<span className="font-medium">
									{formatCurrency(
										items.reduce(
											(sum, item) =>
												sum + (item.vatAmount || 0),
											0,
										),
									)}
								</span>
							</div>

							{/* Total Gross */}
							<div className="flex justify-between border-t pt-2 text-lg font-semibold">
								<span>Total Gross:</span>
								<span>
									{formatCurrency(
										items.reduce(
											(sum, item) =>
												sum +
												(item.totalPrice || 0) +
												(item.vatAmount || 0),
											0,
										),
									)}
								</span>
							</div>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
