"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import type {
	CreateExpenseInput,
	UpdateExpenseInput,
} from "@repo/api/src/routes/costs/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Form } from "@ui/components/form";
import { Crown } from "lucide-react";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";
import { useExpenseOCR } from "../hooks/use-expense-ocr";
import type { ExpenseLineItemWithAllocations } from "../types/expense-line-items";
import { ExpenseAllocationPanel } from "./sections/expense-allocation-panel";
import { ExpenseBasicInfoSection } from "./sections/expense-basic-info-section";
import { ExpenseLineItemsSection } from "./sections/expense-line-items-section";

// Form schema
const expenseFormSchema = z.object({
	supplierId: z.string().min(1, "Supplier is required"),
	supplier_invoice_number: z.string().optional(),
	expense_date: z.coerce.date(),
	due_date: z.coerce.date().optional(),
	paid_date: z.coerce.date().optional(),
	description: z.string().optional(),
	notes: z.string().optional(),
	currency: z.string().min(1, "Currency is required"),
	status: z.enum(["open", "paid", "overdue"]).default("open"),
});

type ExpenseFormValues = z.infer<typeof expenseFormSchema>;

interface ExpenseCreateDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: CreateExpenseInput | UpdateExpenseInput) => Promise<void>;
	expense?: any;
	mode?: "create" | "edit";
	preselectedCarrierId?: string;
}

export function ExpenseCreateDialog({
	isOpen,
	onClose,
	onSubmit,
	expense,
	mode = "create",
	preselectedCarrierId,
}: ExpenseCreateDialogProps) {
	const { activeOrganization } = useActiveOrganization();

	const [lineItems, setLineItems] = useState<
		ExpenseLineItemWithAllocations[]
	>([]);
	const [allocatingLineItem, setAllocatingLineItem] =
		useState<ExpenseLineItemWithAllocations | null>(null);

	// Form setup
	const form = useForm<ExpenseFormValues>({
		resolver: zodResolver(expenseFormSchema),
		defaultValues: {
			supplierId: preselectedCarrierId || "",
			supplier_invoice_number: "",
			expense_date: new Date(),
			due_date: undefined,
			paid_date: undefined,
			description: "",
			notes: "",
			currency: "EUR",
			status: "open",
		},
	});

	// OCR hook
	const {
		selectedFile,
		documentPreviewUrl,
		ocrPrefilledFields,
		handleFileSelect,
		ocrMutation,
		cleanup,
	} = useExpenseOCR({
		form,
		organizationId: activeOrganization?.id || "",
		onLineItemsExtracted: (extractedLineItems) => {
			setLineItems(extractedLineItems);
		},
	});

	// Initialize form with expense data in edit mode
	useEffect(() => {
		if (mode === "edit" && expense) {
			form.reset({
				supplierId: expense.supplierId || "",
				supplier_invoice_number: expense.supplier_invoice_number || "",
				expense_date: expense.expense_date
					? new Date(expense.expense_date)
					: new Date(),
				due_date: expense.due_date
					? new Date(expense.due_date)
					: undefined,
				paid_date: expense.paid_date
					? new Date(expense.paid_date)
					: undefined,
				description: expense.description || "",
				notes: expense.notes || "",
				currency: expense.currency || "EUR",
				status: expense.status || "open",
			});

			if (expense.lineItems) {
				setLineItems(expense.lineItems);
			}
		}
	}, [mode, expense, form]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			cleanup();
		};
	}, [cleanup]);

	const handleSubmit = async (values: ExpenseFormValues) => {
		try {
			// Calculate totals from line items
			const totalAmount = lineItems.reduce(
				(sum, item) => sum + (item.totalPrice || 0),
				0,
			);
			const totalVat = lineItems.reduce((sum, item) => {
				const vatAmount =
					((item.totalPrice || 0) * (item.vatRate || 0)) / 100;
				return sum + vatAmount;
			}, 0);

			// Base expense data
			const baseExpenseData = {
				...values,
				organizationId: activeOrganization?.id || "",
				totalAmount,
				totalVat,
				totalGross: totalAmount + totalVat,
				isRecurring: false,
				lineItems: lineItems.map((item) => ({
					description: item.description,
					quantity: item.quantity || undefined,
					unit: item.unit || undefined,
					unitPrice: item.unitPrice || undefined,
					totalPrice: item.totalPrice,
					vatRate: item.vatRate || undefined,
					vatAmount:
						((item.totalPrice || 0) * (item.vatRate || 0)) / 100,
					categoryId: item.categoryId || undefined,
					notes: item.notes || undefined,
					allocations: (item.allocations || []).map((alloc: any) => ({
						type: alloc.type as
							| "general"
							| "order"
							| "vehicle"
							| "personnel",
						method: alloc.method as "fixed" | "percentage",
						value: alloc.value,
						selectAll: alloc.selectAll || false,
						entityIds: alloc.entityIds || [],
						allocation_start: alloc.allocation_start,
						allocation_end: alloc.allocation_end,
						notes: alloc.notes || undefined,
					})),
				})),
			};

			// Add ID for update mode
			if (mode === "edit" && expense?.id) {
				const updateData: UpdateExpenseInput = {
					...baseExpenseData,
					id: expense.id,
				};
				await onSubmit(updateData);
			} else {
				const createData: CreateExpenseInput = baseExpenseData;
				await onSubmit(createData);
			}
			toast.success(
				`Expense ${mode === "edit" ? "updated" : "created"} successfully`,
			);
			onClose();
		} catch (error) {
			console.error("Submit error:", error);
			toast.error(
				`Failed to ${mode === "edit" ? "update" : "create"} expense`,
			);
		}
	};

	const handleLineItemAllocate = (item: ExpenseLineItemWithAllocations) => {
		setAllocatingLineItem(item);
	};

	const handleAllocationSave = (allocations: any[]) => {
		if (allocatingLineItem) {
			const updatedLineItems = lineItems.map((item) =>
				item.id === allocatingLineItem.id
					? { ...item, allocations }
					: item,
			);
			setLineItems(updatedLineItems);
		}
		setAllocatingLineItem(null);
	};

	const handleAllocationCancel = () => {
		setAllocatingLineItem(null);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-[90vw] max-h-[85vh] overflow-hidden p-0">
				<DialogHeader className="px-6 pt-6 pb-4">
					<DialogTitle>
						{mode === "edit" ? "Edit Expense" : "Create Expense"}
					</DialogTitle>
					<DialogDescription>
						{mode === "edit"
							? "Update expense details and line items"
							: "Add a new expense with line items and allocations"}
					</DialogDescription>
				</DialogHeader>

				{/* Split Panel Layout */}
				<div className="flex h-[calc(85vh-120px)]">
					{/* LEFT: Document Preview Panel (35% width) */}
					<div className="w-[35%] border-r overflow-hidden flex flex-col">
						<div className="bg-muted p-3 font-medium flex justify-between items-center">
							<span>Document Preview</span>
							<div className="flex gap-2 items-center">
								<input
									type="file"
									onChange={(e) => {
										const file =
											e.target.files?.[0] || null;
										handleFileSelect(file);
									}}
									accept=".pdf,.jpg,.jpeg,.png"
									className="max-w-xs text-xs"
								/>
								{selectedFile && (
									<Button
										onClick={() => {
											if (selectedFile) {
												ocrMutation.mutate(
													selectedFile,
												);
											}
										}}
										disabled={ocrMutation.isPending}
										size="sm"
										variant="outline"
										className="border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-950/20"
									>
										<Crown className="h-4 w-4 mr-2 text-amber-600 dark:text-amber-400" />
										{ocrMutation.isPending
											? "Processing..."
											: "Extract Data"}
									</Button>
								)}
							</div>
						</div>
						<div className="flex-1 overflow-hidden">
							{documentPreviewUrl ? (
								selectedFile?.type === "application/pdf" ? (
									<iframe
										src={documentPreviewUrl}
										className="w-full h-full"
										title="Invoice Document Preview"
									/>
								) : (
									<div className="flex items-center justify-center h-full">
										<img
											src={documentPreviewUrl}
											alt="Document Preview"
											className="max-w-full max-h-full object-contain"
										/>
									</div>
								)
							) : expense?.supplier_document_url &&
								mode === "edit" ? (
								/* Show existing document in edit mode */
								<iframe
									src={expense.supplier_document_url}
									className="w-full h-full"
									title="Existing Invoice Document"
								/>
							) : (
								<div className="flex flex-col items-center justify-center h-full text-muted-foreground p-6">
									<div className="text-center">
										<div className="text-lg font-medium mb-2">
											{mode === "edit" &&
											expense?.supplier_document_url
												? "Document Loaded"
												: "No Document"}
										</div>
										<div className="text-sm">
											{mode === "edit" &&
											expense?.supplier_document_url
												? "Existing invoice document"
												: "Upload an invoice document to preview it here"}
										</div>
									</div>
								</div>
							)}
						</div>
					</div>

					{/* RIGHT: Form Content Panel (65% width) */}
					<div className="w-[65%] flex flex-col h-full overflow-hidden">
						{allocatingLineItem ? (
							// Allocation Panel View
							<div className="flex flex-col h-full">
								<div className="flex items-center justify-between p-6 pb-4 flex-shrink-0 border-b">
									<h3 className="text-lg font-semibold">
										Manage Allocations
									</h3>
									<Button
										variant="ghost"
										size="sm"
										onClick={handleAllocationCancel}
									>
										← Back to Form
									</Button>
								</div>
								<div className="flex-1 overflow-hidden">
									<ExpenseAllocationPanel
										lineItem={allocatingLineItem}
										currency={form.watch("currency")}
										onSave={handleAllocationSave}
										onCancel={handleAllocationCancel}
									/>
								</div>
							</div>
						) : (
							// Normal Form View
							<div className="p-6 overflow-y-auto">
								<Form {...form}>
									<form
										onSubmit={form.handleSubmit(
											handleSubmit,
										)}
										className="space-y-6"
									>
										{/* Basic expense fields using extracted component */}
										<ExpenseBasicInfoSection
											control={form.control}
											ocrPrefilledFields={
												ocrPrefilledFields
											}
										/>

										{/* Line Items Table */}
										<div className="space-y-4">
											<ExpenseLineItemsSection
												lineItems={lineItems}
												onLineItemsChange={setLineItems}
												onLineItemAllocate={
													handleLineItemAllocate
												}
											/>
										</div>

										<DialogFooter className="px-0 pt-4">
											<Button
												type="button"
												variant="outline"
												onClick={onClose}
											>
												Cancel
											</Button>
											<Button
												type="submit"
												disabled={
													lineItems.length === 0
												}
											>
												{mode === "edit"
													? "Update Expense"
													: "Create Expense"}
											</Button>
										</DialogFooter>
									</form>
								</Form>
							</div>
						)}
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
