"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import type {
	CreateExpenseInput,
	UpdateExpenseInput,
} from "@repo/api/src/routes/costs/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Form } from "@ui/components/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";
import { useExpenseOCR } from "../hooks/use-expense-ocr";
import type { ExpenseLineItemWithAllocations } from "../types/expense-line-items";
import { ExpenseAllocationPanel } from "./sections/expense-allocation-panel";
import { ExpenseBasicInfoSection } from "./sections/expense-basic-info-section";
import { ExpenseDocumentSection } from "./sections/expense-document-section";
import { ExpenseLineItemsSection } from "./sections/expense-line-items-section";

// Form schema
const expenseFormSchema = z.object({
	supplierCounterpartyId: z.string().min(1, "Supplier is required"),
	supplier_invoice_number: z.string().optional(),
	expense_date: z.string().min(1, "Expense date is required"),
	due_date: z.string().optional(),
	paid_date: z.string().optional(),
	description: z.string().optional(),
	notes: z.string().optional(),
	currency: z.string().min(1, "Currency is required"),
	status: z.enum(["open", "paid", "overdue"]).default("open"),
});

type ExpenseFormValues = z.infer<typeof expenseFormSchema>;

interface ExpenseCreateDialogdProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: CreateExpenseInput | UpdateExpenseInput) => Promise<void>;
	expense?: any;
	mode?: "create" | "edit";
	preselectedCarrierId?: string;
}

export function ExpenseCreateDialogd({
	isOpen,
	onClose,
	onSubmit,
	expense,
	mode = "create",
	preselectedCarrierId,
}: ExpenseCreateDialogdProps) {
	const { organization } = useActiveOrganization();
	const [activeTab, setActiveTab] = useState("basic");
	const [lineItems, setLineItems] = useState<
		ExpenseLineItemWithAllocations[]
	>([]);
	const [allocatingLineItem, setAllocatingLineItem] =
		useState<ExpenseLineItemWithAllocations | null>(null);

	// Form setup
	const form = useForm<ExpenseFormValues>({
		resolver: zodResolver(expenseFormSchema),
		defaultValues: {
			supplierCounterpartyId: preselectedCarrierId || "",
			supplier_invoice_number: "",
			expense_date: "",
			due_date: "",
			paid_date: "",
			description: "",
			notes: "",
			currency: "EUR",
			status: "open",
		},
	});

	// OCR hook
	const {
		selectedFile,
		documentPreviewUrl,
		ocrPrefilledFields,
		handleFileSelect,
		ocrMutation,
		cleanup,
	} = useExpenseOCR({
		form,
		onLineItemsExtracted: (extractedLineItems) => {
			setLineItems(extractedLineItems);
			setActiveTab("line-items");
		},
	});

	// Initialize form with expense data in edit mode
	useEffect(() => {
		if (mode === "edit" && expense) {
			form.reset({
				supplierCounterpartyId: expense.supplierCounterpartyId || "",
				supplier_invoice_number: expense.supplier_invoice_number || "",
				expense_date: expense.expense_date || "",
				due_date: expense.due_date || "",
				paid_date: expense.paid_date || "",
				description: expense.description || "",
				notes: expense.notes || "",
				currency: expense.currency || "EUR",
				status: expense.status || "open",
			});

			if (expense.lineItems) {
				setLineItems(expense.lineItems);
			}
		}
	}, [mode, expense, form]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			cleanup();
		};
	}, [cleanup]);

	const handleSubmit = async (values: ExpenseFormValues) => {
		try {
			// Calculate totals from line items
			const totalAmount = lineItems.reduce(
				(sum, item) => sum + (item.totalPrice || 0),
				0,
			);
			const totalVat = lineItems.reduce((sum, item) => {
				const vatAmount =
					((item.totalPrice || 0) * (item.vatRate || 0)) / 100;
				return sum + vatAmount;
			}, 0);

			const expenseData = {
				...values,
				organizationId: organization?.id || "",
				totalAmount,
				totalVat,
				totalGross: totalAmount + totalVat,
				lineItems: lineItems.map((item) => ({
					description: item.description,
					quantity: item.quantity,
					unit: item.unit,
					unitPrice: item.unitPrice,
					totalPrice: item.totalPrice,
					currency: item.currency,
					vatRate: item.vatRate,
					notes: item.notes,
					categoryId: item.categoryId,
					allocations: item.allocations || [],
				})),
			};

			await onSubmit(expenseData);
			toast.success(
				`Expense ${mode === "edit" ? "updated" : "created"} successfully`,
			);
			onClose();
		} catch (error) {
			console.error("Submit error:", error);
			toast.error(
				`Failed to ${mode === "edit" ? "update" : "create"} expense`,
			);
		}
	};

	const handleLineItemAllocate = (item: ExpenseLineItemWithAllocations) => {
		setAllocatingLineItem(item);
	};

	const handleAllocationSave = (allocations: any[]) => {
		if (allocatingLineItem) {
			const updatedLineItems = lineItems.map((item) =>
				item.id === allocatingLineItem.id
					? { ...item, allocations }
					: item,
			);
			setLineItems(updatedLineItems);
		}
		setAllocatingLineItem(null);
	};

	const handleAllocationCancel = () => {
		setAllocatingLineItem(null);
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
				<DialogHeader>
					<DialogTitle>
						{mode === "edit"
							? "Edit Expense"
							: "Create New Expense"}
					</DialogTitle>
					<DialogDescription>
						{mode === "edit"
							? "Update expense information and line items"
							: "Add a new expense with line items and allocations"}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleSubmit)}
						className="flex flex-col h-full"
					>
						{allocatingLineItem ? (
							// Allocation Panel
							<div className="flex-1 overflow-hidden">
								<ExpenseAllocationPanel
									lineItem={allocatingLineItem}
									currency={form.watch("currency")}
									onSave={handleAllocationSave}
									onCancel={handleAllocationCancel}
								/>
							</div>
						) : (
							// Main Form Tabs
							<Tabs
								value={activeTab}
								onValueChange={setActiveTab}
								className="flex-1 overflow-hidden"
							>
								<TabsList className="grid w-full grid-cols-3">
									<TabsTrigger value="basic">
										Basic Info
									</TabsTrigger>
									<TabsTrigger value="document">
										Document
									</TabsTrigger>
									<TabsTrigger value="line-items">
										Line Items
									</TabsTrigger>
								</TabsList>

								<div className="flex-1 overflow-y-auto mt-4">
									<TabsContent
										value="basic"
										className="space-y-6"
									>
										<ExpenseBasicInfoSection
											control={form.control}
											ocrPrefilledFields={
												ocrPrefilledFields
											}
										/>
									</TabsContent>

									<TabsContent
										value="document"
										className="space-y-6"
									>
										<ExpenseDocumentSection
											selectedFile={selectedFile}
											documentPreviewUrl={
												documentPreviewUrl
											}
											ocrMutation={ocrMutation}
											onFileSelect={handleFileSelect}
										/>
									</TabsContent>

									<TabsContent
										value="line-items"
										className="space-y-6"
									>
										<ExpenseLineItemsSection
											lineItems={lineItems}
											onLineItemsChange={setLineItems}
											onLineItemAllocate={
												handleLineItemAllocate
											}
										/>
									</TabsContent>
								</div>
							</Tabs>
						)}

						<DialogFooter className="mt-6">
							<Button
								type="button"
								variant="outline"
								onClick={onClose}
							>
								Cancel
							</Button>
							{!allocatingLineItem && (
								<Button
									type="submit"
									disabled={lineItems.length === 0}
								>
									{mode === "edit"
										? "Update Expense"
										: "Create Expense"}
								</Button>
							)}
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
