import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

// Define params type for the fetch function
export type FetchFinanceDataParams = {
	organizationId: string;
	timeRange?: "week" | "month" | "year";
	startDate?: Date;
	endDate?: Date;
};

// We use the inferred types from apiClient instead of hardcoding them
export type RevenueDataPoint = Awaited<
	ReturnType<typeof fetchFinanceData>
>["data"][number];
export type FinanceDataResponse = Awaited<ReturnType<typeof fetchFinanceData>>;

// Define query keys
export const financeKeys = {
	all: ["finance"] as const,
	data: (params: FetchFinanceDataParams) =>
		[...financeKeys.all, "data", params] as const,
};

// Fetch function
export const fetchFinanceData = async (params: FetchFinanceDataParams) => {
	const response = await apiClient.dashboard.finance.$get({
		query: {
			organizationId: params.organizationId,
			timeRange: params.timeRange,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch finance data");
	}

	return response.json();
};

// React Query hook
export const useFinanceDataQuery = (params: FetchFinanceDataParams) => {
	return useQuery({
		queryKey: financeKeys.data(params),
		queryFn: () => fetchFinanceData(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

// Helper functions for processing finance data

// Format currency for display
export const formatCurrency = (amount: number, currency = "EUR"): string => {
	return new Intl.NumberFormat("de-DE", {
		style: "currency",
		currency,
		minimumFractionDigits: 2,
	}).format(amount);
};

// Calculate percentage difference between projected and confirmed
export const calculateDifference = (
	projected: number,
	confirmed: number,
): number => {
	if (projected === 0) {
		return 0;
	}
	return ((confirmed - projected) / projected) * 100;
};

// Format percentage for display
export const formatPercentage = (value: number): string => {
	const formatted = value.toFixed(1);
	return value > 0 ? `+${formatted}%` : `${formatted}%`;
};

// Get appropriate color for percentage display (positive/negative)
export const getDifferenceColor = (value: number): string => {
	if (value > 0) {
		return "text-green-600";
	}
	if (value < 0) {
		return "text-red-600";
	}
	return "text-gray-500";
};
