"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { usePersonnelEntitlementMutations } from "@saas/personnel/hooks/use-personnel-entitlements";
import { Button } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// Define the props for the dialog
interface EntitlementDialogProps {
	open: boolean;
	onClose: () => void;
	personnelData: {
		personnelId: string;
		firstName: string;
		lastName: string;
		departmentId: string | null;
		departmentName?: string | null;
		entitlement?: {
			id?: string;
			year?: number;
			holidayEntitlementDays: number;
			usedDays: number;
		} | null;
	};
}

export function EntitlementDialog({
	open,
	onClose,
	personnelData,
}: EntitlementDialogProps) {
	const t = useTranslations();
	const { createEntitlement, updateEntitlement, isLoading } =
		usePersonnelEntitlementMutations({
			onSuccess: () => onClose(),
		});
	const [error, setError] = useState<string | null>(null);

	const currentYear = new Date().getFullYear();
	const isNewEntitlement = !personnelData.entitlement;

	// Define the form schema
	const formSchema = z.object({
		year: z
			.number()
			.min(currentYear, "Year must be current or future")
			.default(currentYear),
		holidayEntitlementDays: z.number().min(0, "Must be a positive number"),
		usedDays: z.number().min(0, "Must be a positive number").default(0),
	});

	type FormValues = z.infer<typeof formSchema>;

	// Initialize form with existing entitlement data or defaults
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			year: personnelData.entitlement?.year ?? currentYear,
			holidayEntitlementDays:
				personnelData.entitlement?.holidayEntitlementDays ?? 20,
			usedDays: personnelData.entitlement?.usedDays ?? 0,
		},
	});

	async function onSubmit(values: FormValues) {
		try {
			setError(null);

			if (isNewEntitlement) {
				// Create new entitlement
				await createEntitlement({
					personnelId: personnelData.personnelId,
					...values,
				});
			} else if (personnelData.entitlement?.id) {
				// Update existing entitlement
				await updateEntitlement(personnelData.entitlement.id, values);
			}
		} catch (err) {
			setError(
				err instanceof Error
					? err.message
					: "Failed to save entitlement",
			);
		}
	}

	return (
		<Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>
						{isNewEntitlement
							? t("app.personnel.entitlement.create.title")
							: t("app.personnel.entitlement.update.title")}
					</DialogTitle>
				</DialogHeader>

				<div className="py-2">
					<div className="flex items-center space-x-2 mb-4">
						<span className="font-medium">
							{t("app.personnel.title")}:
						</span>
						<span>
							{personnelData.firstName} {personnelData.lastName}
						</span>
						{personnelData.departmentName && (
							<span className="text-xs px-2 py-0.5 rounded-full bg-muted text-muted-foreground">
								{personnelData.departmentName}
							</span>
						)}
					</div>

					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(onSubmit)}
							className="space-y-4"
						>
							<FormField
								control={form.control}
								name="year"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t(
												"app.personnel.entitlement.year",
											)}
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												{...field}
												onChange={(e) =>
													field.onChange(
														Number.parseInt(
															e.target.value,
														),
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="holidayEntitlementDays"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t(
												"app.personnel.entitlement.holiday_days",
											)}
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												{...field}
												onChange={(e) =>
													field.onChange(
														Number.parseInt(
															e.target.value,
														),
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="usedDays"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t(
												"app.personnel.entitlement.used_days",
											)}
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												{...field}
												onChange={(e) =>
													field.onChange(
														Number.parseInt(
															e.target.value,
														),
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{error && (
								<div className="text-sm text-destructive mt-2">
									{error}
								</div>
							)}

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={onClose}
								>
									{t("common.actions.cancel")}
								</Button>
								<Button type="submit" disabled={isLoading}>
									{isLoading
										? t("common.actions.saving")
										: t("common.actions.save")}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</div>
			</DialogContent>
		</Dialog>
	);
}
