"use client";

import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { useEffect, useState } from "react";

interface TourOrderSelectorProps {
	tourId: string;
	value: string;
	onChange: (value: string) => void;
	placeholder?: string;
	className?: string;
}

interface TourOrder {
	id: string;
	order_number?: string;
	customer_order_number?: string;
	customer?: {
		name?: string;
	};
}

export function TourOrderSelector({
	tourId,
	value,
	onChange,
	placeholder = "Select order",
	className,
}: TourOrderSelectorProps) {
	const { stops } = useTourBuilder();
	const [tourOrders, setTourOrders] = useState<TourOrder[]>([]);
	const [isLoading, setIsLoading] = useState(false);

	// Extract orders from the context
	useEffect(() => {
		setIsLoading(true);

		try {
			if (!stops || stops.length === 0) {
				setTourOrders([]);
				setIsLoading(false);
				return;
			}

			// Get orders from stops
			const orderMap: Record<string, TourOrder> = {};

			stops.forEach((stop) => {
				if (stop.orderId && stop.order) {
					orderMap[stop.orderId] = {
						id: stop.orderId,
						order_number: stop.order.order_number,
						customer_order_number: stop.order.customer_order_number,
						customer: {
							name: stop.order.customer?.nameLine1,
						},
					};
				}
			});

			setTourOrders(Object.values(orderMap));
		} catch (error) {
			console.error("Error processing orders from context:", error);
			setTourOrders([]);
		} finally {
			setIsLoading(false);
		}
	}, [stops]);

	// Helper function to get display name
	const getOrderDisplayName = (order: TourOrder) => {
		return (
			order.order_number ||
			order.customer_order_number ||
			(order.customer?.name
				? `Order for ${order.customer.name}`
				: "Order")
		);
	};

	return (
		<Select value={value} onValueChange={onChange} disabled={isLoading}>
			<SelectTrigger className={cn("w-full", className)}>
				<SelectValue placeholder={placeholder}>
					{value && tourOrders.find((o) => o.id === value)
						? getOrderDisplayName(
								tourOrders.find((o) => o.id === value)!,
							)
						: placeholder}
				</SelectValue>
			</SelectTrigger>
			<SelectContent>
				{isLoading ? (
					<SelectItem value="loading" disabled>
						Loading orders...
					</SelectItem>
				) : tourOrders.length === 0 ? (
					<SelectItem value="empty" disabled>
						No orders found
					</SelectItem>
				) : (
					tourOrders.map((order) => (
						<SelectItem key={order.id} value={order.id}>
							{getOrderDisplayName(order)}
							{order.customer?.name && (
								<span className="ml-2 text-xs text-muted-foreground">
									({order.customer.name})
								</span>
							)}
						</SelectItem>
					))
				)}
			</SelectContent>
		</Select>
	);
}
