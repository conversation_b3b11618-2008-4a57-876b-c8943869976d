"use client";
import { CommentItem } from "@shared/components/thread/components/CommentItem";
import { Badge } from "@ui/components/badge";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Skeleton } from "@ui/components/skeleton";
import { format, isSameDay, isToday, isYesterday } from "date-fns";
import { MessageCircle, Pin } from "lucide-react";
import { useState } from "react";
import { useComments, useThreadById } from "../hooks/use-threads";
import { ThreadBottomInput } from "./ThreadBottomInput";
import { ThreadHeader } from "./ThreadHeader";

interface ThreadViewSheetProps {
	threadId?: string;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	embedded?: boolean;
	entityTitle?: string;
}

interface ReplyContext {
	type: "comment" | "thread" | "edit";
	commentId?: string;
	authorName?: string;
	threadId: string;
	editContent?: string; // For edit mode, store the original content
	editRichContent?: string; // For edit mode, store the original rich content
}

function ThreadSheetSkeleton() {
	return (
		<div className="py-6 space-y-6">
			{/* Header skeleton */}
			<div className="space-y-4">
				<div className="flex items-center gap-2">
					<Skeleton className="h-6 w-20" />
					<Skeleton className="h-6 w-24" />
				</div>
				<Skeleton className="h-8 w-3/4" />
				<Skeleton className="h-16 w-full" />
				<div className="flex items-center gap-3">
					<Skeleton className="h-8 w-8 rounded-full" />
					<div className="space-y-2">
						<Skeleton className="h-4 w-32" />
						<Skeleton className="h-3 w-24" />
					</div>
				</div>
			</div>

			<Skeleton className="h-0.5 w-full" />

			{/* Comments skeleton */}
			<div className="space-y-4">
				{Array.from({ length: 3 }).map((_, i) => (
					<div key={i} className="flex gap-3">
						<Skeleton className="h-9 w-9 rounded-full" />
						<div className="flex-1 space-y-2">
							<div className="flex items-center gap-2">
								<Skeleton className="h-4 w-24" />
								<Skeleton className="h-3 w-16" />
							</div>
							<Skeleton className="h-16 w-full" />
							<div className="flex gap-2">
								<Skeleton className="h-6 w-12" />
								<Skeleton className="h-6 w-12" />
								<Skeleton className="h-6 w-16" />
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}

// Day separator component
function DaySeparator({ date }: { date: Date }) {
	const formatDayLabel = (date: Date) => {
		if (isToday(date)) {
			return "Today";
		}
		if (isYesterday(date)) {
			return "Yesterday";
		}
		return format(date, "EEEE, MMMM d"); // e.g., "Monday, January 1"
	};

	return (
		<div className="flex items-center gap-3 my-4">
			<div className="flex-1 h-px bg-border" />
			<span className="text-xs font-medium text-muted-foreground bg-background px-3">
				{formatDayLabel(date)}
			</span>
			<div className="flex-1 h-px bg-border" />
		</div>
	);
}

export function ThreadViewSheet({
	threadId,
	open,
	onOpenChange,
	embedded = false,
	entityTitle,
}: ThreadViewSheetProps) {
	const [replyContext, setReplyContext] = useState<ReplyContext | null>(null);

	const {
		data: thread,
		isLoading: threadLoading,
		error: threadError,
	} = useThreadById(threadId);

	const {
		data: commentsData,
		isLoading: commentsLoading,
		error: commentsError,
	} = useComments(threadId);

	const isLoading = threadLoading || commentsLoading;
	const error = threadError || commentsError;

	// Build comment tree from flat comments array
	const buildCommentTree = (comments: any[]) => {
		if (!comments?.length) {
			return [];
		}

		const commentMap = new Map();
		const rootComments: any[] = [];

		// Create map of all comments
		comments.forEach((comment) => {
			commentMap.set(comment.id, { ...comment, replies: [] });
		});

		// Build tree structure
		comments.forEach((comment) => {
			if (comment.parentId) {
				const parent = commentMap.get(comment.parentId);
				if (parent) {
					parent.replies.push(commentMap.get(comment.id));
				}
			} else {
				rootComments.push(commentMap.get(comment.id));
			}
		});

		return rootComments;
	};

	// Group comments by day for separators
	const groupCommentsByDay = (comments: any[]) => {
		const groups: { date: Date; comments: any[] }[] = [];
		let currentGroup: { date: Date; comments: any[] } | null = null;

		comments.forEach((comment) => {
			const commentDate = new Date(comment.createdAt);

			if (!currentGroup || !isSameDay(currentGroup.date, commentDate)) {
				currentGroup = {
					date: commentDate,
					comments: [comment],
				};
				groups.push(currentGroup);
			} else {
				currentGroup.comments.push(comment);
			}
		});

		return groups;
	};

	const comments = commentsData?.items || [];
	const commentTree = buildCommentTree(comments);

	// Separate pinned and regular comments
	const pinnedComments = commentTree.filter(
		(comment) => comment.isPinned && comment.status === "ACTIVE",
	);
	const regularComments = commentTree.filter((comment) => !comment.isPinned);

	const participantCount = thread
		? new Set([thread.authorId, ...comments.map((c: any) => c.authorId)])
				.size
		: 0;

	// Handle reply context
	const handleReplyToComment = (commentId: string, authorName: string) => {
		if (!threadId) {
			return;
		}

		setReplyContext({
			type: "comment",
			commentId: commentId === "thread" ? undefined : commentId,
			authorName,
			threadId,
		});
	};

	// Handle edit context
	const handleEditComment = (
		commentId: string,
		authorName: string,
		content: string,
		richContent?: string,
	) => {
		if (!threadId) {
			return;
		}

		// Clear any existing context first
		setReplyContext({
			type: "edit",
			commentId,
			authorName,
			threadId,
			editContent: content,
			editRichContent: richContent,
		});
	};

	const handleClearReplyContext = () => {
		setReplyContext(null);
	};

	const handleCommentSuccess = () => {
		// Comment was successfully posted, context will be cleared by ThreadBottomInput
	};

	// Content that can be rendered with or without Sheet wrapper
	const threadContent = (
		<div className="flex flex-col h-full">
			{!embedded && (
				<SheetHeader className="px-6 py-4 border-b flex-shrink-0">
					<div className="flex items-center gap-2">
						<MessageCircle className="h-5 w-5 text-muted-foreground" />
						<SheetTitle className="text-lg text-foreground">
							{thread?.title || "Thread"}
						</SheetTitle>
					</div>
					{thread && (
						<SheetDescription className="text-muted-foreground">
							{thread.entityType}{" "}
							{entityTitle
								? `#${entityTitle}`
								: `#${thread.entityId}`}{" "}
							• {comments.length} comments • {participantCount}{" "}
							participants
						</SheetDescription>
					)}
				</SheetHeader>
			)}

			{/* Scrollable Content Area */}
			<ScrollArea className="flex-1 min-h-0">
				<div className="pl-6 py-4">
					{error ? (
						<div className="text-center py-8">
							<div className="text-muted-foreground text-sm">
								Failed to load thread. Please try again.
							</div>
						</div>
					) : isLoading ? (
						<ThreadSheetSkeleton />
					) : thread ? (
						<div className="space-y-6">
							{/* Thread Header */}
							<ThreadHeader
								thread={thread}
								entityTitle={entityTitle}
							/>

							{/* Pinned Comments Section */}
							{pinnedComments.length > 0 && (
								<div className="space-y-4">
									<div className="flex items-center gap-2 mb-3">
										<Pin className="w-4 h-4 text-blue-600" />
										<Badge
											status="info"
											className="bg-blue-50 text-blue-700 border-blue-200"
										>
											Pinned Comments (
											{pinnedComments.length})
										</Badge>
									</div>
									<div className="space-y-4 border-l-2 border-l-blue-200 pl-4 bg-blue-50/30 py-3 rounded-r-lg">
										{pinnedComments.map((comment) => (
											<CommentItem
												key={comment.id}
												comment={comment}
												isPinned={true}
												onReply={handleReplyToComment}
												onEdit={handleEditComment}
											/>
										))}
									</div>
								</div>
							)}

							{/* Regular Comments Section */}
							{regularComments.length > 0 && (
								<div className="space-y-4">
									<Badge status="info" className="mb-3">
										<MessageCircle className="w-4 h-4" />
										Comments ({regularComments.length})
									</Badge>
									<div className="space-y-0">
										{groupCommentsByDay(
											regularComments,
										).map((group, groupIndex) => (
											<div key={groupIndex}>
												<DaySeparator
													date={group.date}
												/>
												<div className="space-y-4">
													{group.comments.map(
														(comment) => (
															<CommentItem
																key={comment.id}
																comment={
																	comment
																}
																onReply={
																	handleReplyToComment
																}
																onEdit={
																	handleEditComment
																}
															/>
														),
													)}
												</div>
											</div>
										))}
									</div>
								</div>
							)}

							{/* Empty state */}
							{commentTree.length === 0 && (
								<div className="text-center py-8">
									<MessageCircle className="h-12 w-12 mx-auto text-muted-foreground/40 mb-4" />
									<div className="text-muted-foreground text-sm">
										No comments yet. Be the first to
										comment!
									</div>
								</div>
							)}
						</div>
					) : (
						<div className="text-center py-8">
							<div className="text-muted-foreground text-sm">
								Thread not found.
							</div>
						</div>
					)}
				</div>
			</ScrollArea>

			{/* Fixed Bottom Input */}
			{threadId && (
				<div className="flex-shrink-0">
					<ThreadBottomInput
						threadId={threadId}
						replyContext={
							replyContext || { type: "thread", threadId }
						}
						onClearReplyContext={handleClearReplyContext}
						onCommentSuccess={handleCommentSuccess}
					/>
				</div>
			)}
		</div>
	);

	// Return embedded content or wrapped in Sheet
	if (embedded) {
		return threadContent;
	}

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent
				side="right"
				className="sm:max-w-2xl w-full overflow-hidden p-0 flex flex-col"
			>
				{threadContent}
			</SheetContent>
		</Sheet>
	);
}
