"use client";

import { useAbsenceUI } from "@saas/personnel/context/absence-ui-context";
import { usePersonnelAbsenceTable } from "@saas/personnel/hooks/use-personnel-absence";
import { DataTable } from "@saas/shared/components/data-table";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import type { SortingState } from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { AbsenceHistorySheet } from "../absence-history-sheet";
import { EntitlementDialog } from "../entitlement-dialog";
import { useColumns } from "./absence-columns";

export function AbsenceDataTable() {
	const [sorting, setSorting] = useState<SortingState>([]);
	const {
		data,
		isLoading,
		page,
		setPage,
		pageSize,
		setPageSize,
		search,
		setSearch,
	} = usePersonnelAbsenceTable();
	const columns = useColumns();
	const absences = data?.items ?? [];

	const {
		isEntitlementDialogOpen,
		entitlementDialogPersonnel,
		closeEntitlementDialog,
		isHistorySheetOpen,
		historySheetPersonnelId,
		historySheetPersonnelName,
		closeHistorySheet,
	} = useAbsenceUI();

	const {
		registerScopeWithShortcuts,
		addShortcuts,
		removeShortcuts,
		registerCapability,
	} = useShortcuts();

	// Register shortcuts using the standard "table" scope
	useEffect(() => {
		// Register with the standard table scope instead of a custom one
		const cleanup = registerScopeWithShortcuts("table");

		// Add our specific shortcuts to the standard table scope
		addShortcuts([
			{
				id: "table-view-absences",
				combo: "enter",
				description: "View absence history for personnel",
				scope: "table",
				translationKey: "viewRow", // Reuse existing translation
			},
			{
				id: "table-edit-entitlements",
				combo: "ctrl+e",
				description: "Edit leave entitlements",
				scope: "table",
				translationKey: "editRow", // Reuse existing translation
			},
		]);

		// Register capabilities to customize the visual indicators
		registerCapability("table-delete-row", false); // Disable delete row capability
		registerCapability("table-edit-row", true); // Keep edit capability enabled

		return () => {
			cleanup();
			// Clean up our specific shortcuts on unmount
			removeShortcuts(["table-view-absences", "table-edit-entitlements"]);

			// Reset capabilities
			registerCapability("table-delete-row", true);
			registerCapability("table-edit-row", true);
		};
	}, [
		registerScopeWithShortcuts,
		addShortcuts,
		removeShortcuts,
		registerCapability,
	]);

	return (
		<>
			<DataTable
				columns={columns}
				data={absences}
				onSearch={setSearch}
				searchValue={search}
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="table"
			/>

			{isEntitlementDialogOpen && entitlementDialogPersonnel && (
				<EntitlementDialog
					open={isEntitlementDialogOpen}
					onClose={closeEntitlementDialog}
					personnelData={{
						personnelId: entitlementDialogPersonnel.personnelId,
						firstName: entitlementDialogPersonnel.firstName,
						lastName: entitlementDialogPersonnel.lastName,
						departmentId: entitlementDialogPersonnel.departmentId,
						departmentName:
							entitlementDialogPersonnel.departmentName,
						entitlement: entitlementDialogPersonnel.entitlement
							? {
									holidayEntitlementDays:
										entitlementDialogPersonnel.entitlement,
									usedDays: 0,
								}
							: null,
					}}
				/>
			)}

			{isHistorySheetOpen && historySheetPersonnelId && (
				<AbsenceHistorySheet
					open={isHistorySheetOpen}
					onOpenChange={closeHistorySheet}
					personnelId={historySheetPersonnelId}
					personnelName={historySheetPersonnelName || ""}
				/>
			)}
		</>
	);
}
