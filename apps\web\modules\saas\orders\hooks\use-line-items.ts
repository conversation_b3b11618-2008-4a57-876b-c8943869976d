import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useState } from "react";
import type { LineItemParentType } from "../lib/api-line-items";
import {
	useCreateLineItemMutation,
	useDeleteLineItemMutation,
	useLineItemByIdQuery,
	useLineItemsQuery,
	useUpdateLineItemMutation,
} from "../lib/api-line-items";

export function useLineItems(
	parentId?: string,
	parentType: LineItemParentType = "order",
	excludeInvoiced = true,
) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	// If parentId is not provided, disable the query
	const enabled = !!activeOrganization?.id && !!parentId;

	const query = useLineItemsQuery({
		organizationId: activeOrganization?.id ?? "",
		parentId: parentId ?? "",
		parentType,
		page,
		limit: pageSize,
		search: debouncedSearch,
		excludeInvoiced,
	});

	const deleteMutation = useDeleteLineItemMutation(
		activeOrganization?.id ?? "",
		parentId ?? "",
		parentType,
	);

	// Wrap the delete function to refetch after deletion
	const deleteLineItem = async (id: string) => {
		if (activeOrganization?.id && parentId) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete line item error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		refetch: query.refetch,
		deleteLineItem,
		enabled,
	};
}

/**
 * Order-specific hook for line items
 * Uses 'order' for parentType
 */
export function useOrderLineItems(orderId?: string) {
	return useLineItems(orderId, "order");
}

/**
 * Offer-specific hook for line items
 * Uses 'offer' for parentType
 */
export function useOfferLineItems(offerId?: string) {
	return useLineItems(offerId, "offer");
}

/**
 * Tour-specific hook for line items
 * Uses 'tour' for parentType
 */
export function useTourLineItems(tourId?: string) {
	return useLineItems(tourId, "tour");
}

/**
 * Invoice-specific hook for line items
 * Uses 'invoice' for parentType and sets excludeInvoiced to false
 */
export function useInvoiceLineItems(invoiceId?: string) {
	return useLineItems(invoiceId, "invoice", false);
}

export function useLineItemById(lineItemId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useLineItemByIdQuery(activeOrganization?.id, lineItemId);
}

export function useLineItemMutations(
	parentId: string,
	parentType: LineItemParentType = "order",
	options?: { onSuccess?: () => void },
) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateLineItemMutation(orgId);
	const updateMutation = useUpdateLineItemMutation(orgId);
	const deleteMutation = useDeleteLineItemMutation(
		orgId,
		parentId,
		parentType,
	);

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		try {
			// Set the appropriate parent ID field based on parentType
			const dataWithParent = {
				...data,
				orderId: parentType === "order" ? parentId : undefined,
				offerId: parentType === "offer" ? parentId : undefined,
				tourId: parentType === "tour" ? parentId : undefined,
				invoiceId: parentType === "invoice" ? parentId : undefined,
				// Add currency if not provided (required field)
				currency: data.currency || "EUR",
			};

			const result = await createMutation.mutateAsync(dataWithParent);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Create line item error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const updateWithCallback = async (data: any) => {
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Update line item error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const deleteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Delete line item error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	return {
		createLineItem: createWithCallback,
		updateLineItem: updateWithCallback,
		deleteLineItem: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

/**
 * Order-specific hook for line item mutations
 * Uses 'order' for parentType
 */
export function useOrderLineItemMutations(
	orderId: string,
	options?: { onSuccess?: () => void },
) {
	return useLineItemMutations(orderId, "order", options);
}

/**
 * Offer-specific hook for line item mutations
 * Uses 'offer' for parentType
 */
export function useOfferLineItemMutations(
	offerId: string,
	options?: { onSuccess?: () => void },
) {
	return useLineItemMutations(offerId, "offer", options);
}

/**
 * Tour-specific hook for line item mutations
 * Uses 'tour' for parentType
 */
export function useTourLineItemMutations(
	tourId: string,
	options?: { onSuccess?: () => void },
) {
	return useLineItemMutations(tourId, "tour", options);
}

/**
 * Invoice-specific hook for line item mutations
 * Uses 'invoice' for parentType
 */
export function useInvoiceLineItemMutations(
	invoiceId: string,
	options?: { onSuccess?: () => void },
) {
	return useLineItemMutations(invoiceId, "invoice", options);
}
