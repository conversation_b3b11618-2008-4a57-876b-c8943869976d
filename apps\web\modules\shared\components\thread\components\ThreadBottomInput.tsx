"use client";
import { RichCommentForm } from "./RichCommentForm";

interface ReplyContext {
	type: "comment" | "thread" | "edit";
	commentId?: string;
	authorName?: string;
	threadId: string;
	editContent?: string; // For edit mode, store the original content
	editRichContent?: string; // For edit mode, store the original rich content
}

interface ThreadBottomInputProps {
	threadId: string;
	replyContext: ReplyContext | null;
	onClearReplyContext: () => void;
	onCommentSuccess: () => void;
}

export function ThreadBottomInput({
	threadId,
	replyContext,
	onClearReplyContext,
	onCommentSuccess,
}: ThreadBottomInputProps) {
	if (!replyContext) {
		return null;
	}

	return (
		<div className="border-t bg-background flex-shrink-0">
			<div className="pl-6 pr-0 py-4">
				<RichCommentForm
					threadId={replyContext.threadId}
					parentId={
						replyContext.type === "comment"
							? replyContext.commentId
							: undefined
					}
					editCommentId={
						replyContext.type === "edit"
							? replyContext.commentId
							: undefined
					}
					initialContent={
						replyContext.type === "edit"
							? replyContext.editContent
							: undefined
					}
					initialRichContent={
						replyContext.type === "edit"
							? replyContext.editRichContent
							: undefined
					}
					replyTo={
						replyContext.type === "comment" &&
						replyContext.authorName
							? {
									id: replyContext.commentId!,
									author: replyContext.authorName,
								}
							: undefined
					}
					onCancel={
						replyContext.type === "thread"
							? undefined
							: onClearReplyContext
					}
					onSuccess={() => {
						if (replyContext.type !== "thread") {
							onClearReplyContext();
						}
						onCommentSuccess?.();
					}}
					placeholder={
						replyContext.type === "comment"
							? `Reply to ${replyContext.authorName}...`
							: replyContext.type === "edit"
								? "Edit your comment..."
								: "Add a comment..."
					}
				/>
			</div>
		</div>
	);
}
