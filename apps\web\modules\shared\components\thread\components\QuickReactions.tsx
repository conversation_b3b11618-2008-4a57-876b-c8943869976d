"use client";

import { Button } from "@ui/components/button";

interface Reaction {
	id: string;
	emoji: string;
	userId: string;
	user: {
		id: string;
		name: string;
		email: string;
	};
	createdAt: string;
}

interface QuickReactionsProps {
	reactions: Reaction[];
	onReact: (emoji: string) => void;
	className?: string;
}

const COMMON_EMOJIS = ["👍", "❤️", "🎉", "😊", "👎", "😢"];

export function QuickReactions({
	reactions,
	onReact,
	className,
}: QuickReactionsProps) {
	// Count reactions by emoji
	const reactionCounts = reactions.reduce(
		(acc, reaction) => {
			acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
			return acc;
		},
		{} as Record<string, number>,
	);

	return (
		<div className={`flex items-center gap-1 ${className}`}>
			{/* Existing reactions with counts */}
			{Object.entries(reactionCounts).map(([emoji, count]) => (
				<Button
					key={emoji}
					variant="ghost"
					size="sm"
					className="h-7 px-2 text-xs hover:bg-blue-50 transition-colors"
					onClick={() => onReact(emoji)}
				>
					{emoji} {count}
				</Button>
			))}

			{/* Quick add buttons for common emojis not yet used */}
			<div className="flex items-center">
				{COMMON_EMOJIS.filter((emoji) => !reactionCounts[emoji])
					.slice(0, 3)
					.map((emoji) => (
						<Button
							key={emoji}
							variant="ghost"
							size="sm"
							className="h-7 w-7 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-50 transition-colors"
							onClick={() => onReact(emoji)}
						>
							{emoji}
						</Button>
					))}
			</div>
		</div>
	);
}
