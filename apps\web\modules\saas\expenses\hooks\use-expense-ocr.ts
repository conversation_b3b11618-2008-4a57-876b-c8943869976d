"use client";

import { useState, useCallback } from "react";
import { toast } from "sonner";
import { useInvoiceOCRMutation } from "../lib/api-ocr";
import type { UseFormReturn } from "react-hook-form";

interface UseExpenseOCRProps {
	form: UseFormReturn<any>;
	onLineItemsExtracted?: (lineItems: any[]) => void;
}

export function useExpenseOCR({ form, onLineItemsExtracted }: UseExpenseOCRProps) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [documentPreviewUrl, setDocumentPreviewUrl] = useState<string | null>(null);
	const [ocrPrefilledFields, setOcrPrefilledFields] = useState<Set<string>>(new Set());

	const ocrMutation = useInvoiceOCRMutation({
		onSuccess: (data) => {
			console.log("OCR Success:", data);
			
			// Track which fields were prefilled by OCR
			const prefilledFields = new Set<string>();

			// Prefill form fields from OCR data
			if (data.supplierName) {
				// Note: This would need to be mapped to counterparty ID in real implementation
				prefilledFields.add("supplierCounterpartyId");
			}

			if (data.invoiceNumber) {
				form.setValue("supplier_invoice_number", data.invoiceNumber);
				prefilledFields.add("supplier_invoice_number");
			}

			if (data.invoiceDate) {
				form.setValue("expense_date", data.invoiceDate);
				prefilledFields.add("expense_date");
			}

			if (data.dueDate) {
				form.setValue("due_date", data.dueDate);
				prefilledFields.add("due_date");
			}

			if (data.currency) {
				form.setValue("currency", data.currency);
				prefilledFields.add("currency");
			}

			if (data.description) {
				form.setValue("description", data.description);
				prefilledFields.add("description");
			}

			// Handle line items if extracted
			if (data.lineItems && data.lineItems.length > 0 && onLineItemsExtracted) {
				const mappedLineItems = data.lineItems.map((item: any, index: number) => ({
					id: `ocr-item-${index}`,
					description: item.description || "",
					quantity: item.quantity || 1,
					unit: item.unit || "",
					unitPrice: item.unitPrice || 0,
					totalPrice: item.totalPrice || item.unitPrice || 0,
					currency: data.currency || "EUR",
					vatRate: item.vatRate || 0,
					notes: "",
					// OCR indicators
					isOcrGenerated: true,
					isOcrCategorized: false,
					isOcrAllocated: false,
				}));
				onLineItemsExtracted(mappedLineItems);
			}

			// Update OCR prefilled fields state
			setOcrPrefilledFields(prefilledFields);

			toast.success("Document data extracted successfully!");
		},
		onError: (error) => {
			console.error("OCR Error:", error);
			toast.error("Failed to extract data from document. Please try again.");
		},
	});

	const handleFileSelect = useCallback((file: File | null) => {
		setSelectedFile(file);
		
		// Create preview URL
		if (file) {
			const url = URL.createObjectURL(file);
			setDocumentPreviewUrl(url);
		} else {
			if (documentPreviewUrl) {
				URL.revokeObjectURL(documentPreviewUrl);
			}
			setDocumentPreviewUrl(null);
		}
	}, [documentPreviewUrl]);

	const processOCR = useCallback((file: File) => {
		if (!file) {
			toast.error("Please select a file first");
			return;
		}

		// Validate file type
		const allowedTypes = ["application/pdf", "image/png", "image/jpeg", "image/jpg"];
		if (!allowedTypes.includes(file.type)) {
			toast.error("Please select a PDF, PNG, or JPG file");
			return;
		}

		// Validate file size (10MB limit)
		const maxSize = 10 * 1024 * 1024; // 10MB in bytes
		if (file.size > maxSize) {
			toast.error("File size must be less than 10MB");
			return;
		}

		ocrMutation.mutate(file);
	}, [ocrMutation]);

	// Cleanup function
	const cleanup = useCallback(() => {
		if (documentPreviewUrl) {
			URL.revokeObjectURL(documentPreviewUrl);
		}
	}, [documentPreviewUrl]);

	return {
		// State
		selectedFile,
		documentPreviewUrl,
		ocrPrefilledFields,
		
		// Actions
		handleFileSelect,
		processOCR,
		cleanup,
		
		// OCR mutation
		ocrMutation: {
			...ocrMutation,
			mutate: processOCR,
		},
	};
}
