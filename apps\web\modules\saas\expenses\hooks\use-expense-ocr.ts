"use client";

import { useCallback, useState } from "react";
import type { UseFormReturn } from "react-hook-form";
import { toast } from "sonner";
import { useInvoiceOCRMutation } from "../lib/api-ocr";

interface UseExpenseOCRProps {
	form: UseFormReturn<any>;
	organizationId: string;
	onLineItemsExtracted?: (lineItems: any[]) => void;
}

export function useExpenseOCR({
	form,
	organizationId,
	onLineItemsExtracted,
}: UseExpenseOCRProps) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [documentPreviewUrl, setDocumentPreviewUrl] = useState<string | null>(
		null,
	);
	const [ocrPrefilledFields, setOcrPrefilledFields] = useState<Set<string>>(
		new Set(),
	);

	const ocrMutation = useInvoiceOCRMutation(organizationId, (data) => {
		console.log("OCR Success:", data);

		// Track which fields were prefilled by OCR
		const prefilledFields = new Set<string>();

		// Access the extracted data from the response
		const extractedData = data.extractedData;

		// Prefill form fields from OCR data
		if (extractedData.supplierName) {
			// Note: This would need to be mapped to counterparty ID in real implementation
			prefilledFields.add("supplierId");
		}

		if (extractedData.invoiceNumber) {
			form.setValue(
				"supplier_invoice_number",
				extractedData.invoiceNumber,
			);
			prefilledFields.add("supplier_invoice_number");
		}

		if (extractedData.invoiceDate) {
			form.setValue("expense_date", new Date(extractedData.invoiceDate));
			prefilledFields.add("expense_date");
		}

		if (extractedData.dueDate) {
			form.setValue("due_date", new Date(extractedData.dueDate));
			prefilledFields.add("due_date");
		}

		if (extractedData.currency) {
			form.setValue("currency", extractedData.currency);
			prefilledFields.add("currency");
		}

		if (extractedData.description) {
			form.setValue("description", extractedData.description);
			prefilledFields.add("description");
		}

		// Handle line items if extracted
		if (
			extractedData.lineItems &&
			extractedData.lineItems.length > 0 &&
			onLineItemsExtracted
		) {
			const mappedLineItems = extractedData.lineItems.map(
				(item: any, index: number) => ({
					id: `ocr-item-${index}`,
					description: item.description || "",
					quantity: item.quantity || 1,
					unit: item.unit || "",
					unitPrice: item.unitPrice || 0,
					totalPrice: item.totalPrice || item.unitPrice || 0,
					currency: extractedData.currency || "EUR",
					vatRate: item.vatRate || 0,
					notes: "",
					// OCR indicators
					isOcrGenerated: true,
					isOcrCategorized: false,
					isOcrAllocated: false,
				}),
			);
			onLineItemsExtracted(mappedLineItems);
		}

		// Update OCR prefilled fields state
		setOcrPrefilledFields(prefilledFields);

		toast.success("Document data extracted successfully!");
	});

	const handleFileSelect = useCallback(
		(file: File | null) => {
			setSelectedFile(file);

			// Create preview URL
			if (file) {
				const url = URL.createObjectURL(file);
				setDocumentPreviewUrl(url);
			} else {
				if (documentPreviewUrl) {
					URL.revokeObjectURL(documentPreviewUrl);
				}
				setDocumentPreviewUrl(null);
			}
		},
		[documentPreviewUrl],
	);

	const processOCR = useCallback(
		(file: File) => {
			if (!file) {
				toast.error("Please select a file first");
				return;
			}

			// Validate file type
			const allowedTypes = [
				"application/pdf",
				"image/png",
				"image/jpeg",
				"image/jpg",
			];
			if (!allowedTypes.includes(file.type)) {
				toast.error("Please select a PDF, PNG, or JPG file");
				return;
			}

			// Validate file size (10MB limit)
			const maxSize = 10 * 1024 * 1024; // 10MB in bytes
			if (file.size > maxSize) {
				toast.error("File size must be less than 10MB");
				return;
			}

			ocrMutation.mutate(file);
		},
		[ocrMutation],
	);

	// Cleanup function
	const cleanup = useCallback(() => {
		if (documentPreviewUrl) {
			URL.revokeObjectURL(documentPreviewUrl);
		}
	}, [documentPreviewUrl]);

	return {
		// State
		selectedFile,
		documentPreviewUrl,
		ocrPrefilledFields,

		// Actions
		handleFileSelect,
		processOCR,
		cleanup,

		// OCR mutation
		ocrMutation: {
			...ocrMutation,
			mutate: processOCR,
		},
	};
}
