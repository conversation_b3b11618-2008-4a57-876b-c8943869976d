"use client";

import type { LineItem } from "@saas/shared/components/line-items/line-items-table";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { MoreHorizontal, Pencil, Trash } from "lucide-react";
import { formatCurrency } from "../../../../../lib/format";

interface TourLineItemsTableProps {
	items: LineItem[];
	onEditItem?: (item: LineItem) => void;
	onDeleteItem?: (itemId: string) => void;
}

export function TourLineItemsTable({
	items = [],
	onEditItem,
	onDeleteItem,
}: TourLineItemsTableProps) {
	// Helper function to check if a line item has allocations
	const hasAllocations = (item: LineItem) => {
		return (
			item.tourLineItemAllocations &&
			item.tourLineItemAllocations.length > 0
		);
	};

	// Calculate grand total
	const grandTotal = items.reduce(
		(total, item) => total + (item.totalPrice || 0),
		0,
	);

	// Get currency from first item or default to EUR
	const currency = items.length > 0 ? items[0].currency : "EUR";

	return (
		<div className="space-y-3">
			{items.map((item) => (
				<div
					key={item.id}
					className="flex flex-col p-3 border rounded-md"
				>
					<div className="flex items-start justify-between">
						<div>
							<div className="font-medium text-sm">
								{item.description || "-"}
							</div>
							<div className="text-xs text-muted-foreground mt-1 space-y-1">
								{item.quantity && item.unitPrice ? (
									<p>
										{item.quantity} {item.unit} ×{" "}
										{formatCurrency(
											item.unitPrice || 0,
											item.currency,
										)}
									</p>
								) : item.quantity ? (
									<p>
										{item.quantity} {item.unit}
									</p>
								) : null}

								<p>
									Total:{" "}
									{formatCurrency(
										item.totalPrice || 0,
										item.currency,
									)}
								</p>

								{hasAllocations(item) && (
									<p>
										<Badge className="mt-1 text-xs">
											Allocated
										</Badge>
									</p>
								)}
							</div>
						</div>

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="h-8 w-8 p-0"
								>
									<MoreHorizontal className="h-4 w-4" />
									<span className="sr-only">Actions</span>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem
									onClick={() => onEditItem?.(item)}
								>
									<Pencil className="h-3.5 w-3.5 mr-2" />
									Edit Item
								</DropdownMenuItem>
								<DropdownMenuItem
									className="text-destructive"
									onClick={() =>
										item.id && onDeleteItem?.(item.id)
									}
								>
									<Trash className="h-3.5 w-3.5 mr-2" />
									Delete
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			))}

			{/* Grand total section */}
			<div className="flex justify-between items-center p-3 border rounded-md bg-muted/30">
				<div className="font-semibold">Total</div>
				<div className="font-bold">
					{formatCurrency(grandTotal, currency)}
				</div>
			</div>
		</div>
	);
}
