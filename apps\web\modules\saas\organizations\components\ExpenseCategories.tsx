"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { DotsHorizontalIcon, PlusIcon, TrashIcon } from "@radix-ui/react-icons";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { Textarea } from "@ui/components/textarea";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
	useExpenseCategories,
	useExpenseCategoryMutations,
} from "../hooks/use-config";

// Form schema for creating/editing expense categories
const formSchema = z.object({
	name: z.string().min(1, "Name is required"),
	description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function ExpenseCategories() {
	const [isOpen, setIsOpen] = useState(false);
	const [editingCategory, setEditingCategory] = useState<any | null>(null);

	const { data, isLoading, refetch } = useExpenseCategories();

	const {
		createExpenseCategory,
		updateExpenseCategory,
		deleteExpenseCategory,
		isLoading: isMutating,
	} = useExpenseCategoryMutations({
		onSuccess: () => {
			refetch();
			setIsOpen(false);
			setEditingCategory(null);
		},
	});

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			description: "",
		},
	});

	function onOpenChange(open: boolean) {
		if (!open) {
			setEditingCategory(null);
			form.reset();
		}
		setIsOpen(open);
	}

	function onEdit(category: any) {
		setEditingCategory(category);
		form.reset({
			name: category.name,
			description: category.description || "",
		});
		setIsOpen(true);
	}

	async function onSubmit(values: FormValues) {
		try {
			if (editingCategory) {
				await updateExpenseCategory(editingCategory.id, values);
			} else {
				await createExpenseCategory(values);
			}
		} catch (error) {
			console.error("Error managing expense category:", error);
		}
	}

	async function onDelete(id: string) {
		if (confirm("Are you sure you want to delete this expense category?")) {
			try {
				await deleteExpenseCategory(id);
			} catch (error) {
				console.error("Error deleting expense category:", error);
			}
		}
	}

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<div>
					<CardTitle>Expense Categories</CardTitle>
					<CardDescription>
						Manage expense categories for your organization
					</CardDescription>
				</div>
				<Dialog open={isOpen} onOpenChange={onOpenChange}>
					<DialogTrigger asChild>
						<Button variant="primary" size="sm">
							<PlusIcon className="mr-2 h-4 w-4" />
							Add Category
						</Button>
					</DialogTrigger>
					<DialogContent>
						<DialogHeader>
							<DialogTitle>
								{editingCategory
									? "Edit Expense Category"
									: "Create Expense Category"}
							</DialogTitle>
							<DialogDescription>
								{editingCategory
									? "Update the details of this expense category."
									: "Add a new expense category to your organization."}
							</DialogDescription>
						</DialogHeader>
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-4"
							>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Name</FormLabel>
											<FormControl>
												<Input
													placeholder="Enter category name"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Description</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Enter description (optional)"
													{...field}
													value={field.value || ""}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<DialogFooter>
									<Button
										type="button"
										variant="outline"
										onClick={() => setIsOpen(false)}
									>
										Cancel
									</Button>
									<Button type="submit" disabled={isMutating}>
										{editingCategory ? "Update" : "Create"}
									</Button>
								</DialogFooter>
							</form>
						</Form>
					</DialogContent>
				</Dialog>
			</CardHeader>
			<CardContent>
				{isLoading ? (
					<div className="space-y-3">
						{[...Array(3)].map((_, i) => (
							<div
								key={i}
								className="flex items-center justify-between p-4 border rounded-lg"
							>
								<div className="space-y-2 flex-1">
									<Skeleton className="h-4 w-32" />
									<Skeleton className="h-3 w-48" />
								</div>
								<Skeleton className="h-8 w-8" />
							</div>
						))}
					</div>
				) : (
					<div className="space-y-3">
						{data?.items?.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								No expense categories found. Create your first
								category to get started.
							</div>
						) : (
							data?.items?.map((category) => (
								<div
									key={category.id}
									className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
								>
									<div className="space-y-1 flex-1">
										<div className="font-medium">
											{category.name}
										</div>
										{category.description && (
											<div className="text-sm text-muted-foreground">
												{category.description}
											</div>
										)}
									</div>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" size="sm">
												<DotsHorizontalIcon className="h-4 w-4" />
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() => onEdit(category)}
											>
												Edit
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() =>
													onDelete(category.id)
												}
												className="text-destructive"
											>
												<TrashIcon className="mr-2 h-4 w-4" />
												Delete
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							))
						)}
					</div>
				)}
			</CardContent>
		</Card>
	);
}
