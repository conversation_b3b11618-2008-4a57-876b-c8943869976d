"use client";

import type {
	CreateExpenseInput,
	UpdateExpenseInput,
} from "@repo/api/src/routes/costs/types";
import { useExpenseMutations } from "@saas/expenses/hooks/use-expenses";
import { useExpensesQuery } from "@saas/expenses/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { Loader2, Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { ExpenseCreateDialog } from "../../../expenses/components/expense-create-dialog";

interface TourExpensesPanelProps {
	tourId: string;
}

// Type for order information extracted from tour stops
interface TourOrder {
	id: string;
	orderNumber: string;
	customerName: string;
}

export function TourExpensesPanel({ tourId }: TourExpensesPanelProps) {
	const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false);
	const { stops, tourData } = useTourBuilder();
	const { activeOrganization } = useActiveOrganization();

	// Extract unique orders from tour stops for preselection
	const tourOrders = useMemo((): TourOrder[] => {
		if (!stops || stops.length === 0) {
			return [];
		}

		const orderMap: Record<string, TourOrder> = {};

		stops.forEach((stop) => {
			if (stop.orderId && stop.order) {
				orderMap[stop.orderId] = {
					id: stop.orderId,
					orderNumber:
						stop.order.order_number ||
						stop.order.customer_order_number ||
						"No order number",
					customerName:
						stop.order.customer?.nameLine1 || "Unknown Customer",
				};
			}
		});

		return Object.values(orderMap);
	}, [stops]);

	// Fetch expenses for all orders in the tour
	// Since the API doesn't support multiple orderIds, we'll fetch all expenses and filter client-side
	const {
		data: expensesData,
		isLoading: isLoadingExpenses,
		refetch: refetchExpenses,
	} = useExpensesQuery({
		organizationId: activeOrganization?.id || "",
		limit: 100, // Get more expenses to ensure we capture tour-related ones
	});

	// Filter expenses that are allocated to any of the tour orders
	const tourExpenses = useMemo(() => {
		if (!expensesData?.items || !tourOrders.length) {
			return [];
		}

		const tourOrderIds = new Set(tourOrders.map((order) => order.id));

		return expensesData.items.filter((expense) => {
			// Check if any line item has allocations to tour orders
			return expense.lineItems?.some((lineItem) =>
				lineItem.allocations?.some(
					(allocation) =>
						allocation.type === "order" &&
						allocation.entities?.some(
							(entity) =>
								entity.entityType === "order" &&
								tourOrderIds.has(entity.entityId),
						),
				),
			);
		});
	}, [expensesData?.items, tourOrders]);

	// Use expense mutations for creating expenses
	const { createExpense, isCreating } = useExpenseMutations({
		onSuccess: () => {
			// Refresh expenses data when a new expense is created
			refetchExpenses();
		},
	});

	const handleCreateExpense = () => {
		setIsExpenseDialogOpen(true);
	};

	const handleExpenseSubmit = async (
		data: CreateExpenseInput | UpdateExpenseInput,
	) => {
		try {
			await createExpense(data as CreateExpenseInput);
			setIsExpenseDialogOpen(false);
		} catch (error) {
			console.error("Failed to create expense:", error);
			// Error handling is done in the mutation
		}
	};

	const formatCurrency = (value: number, currency = "EUR") => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(value);
	};

	return (
		<div className="space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Tour Expenses</h3>
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<Button
								variant="outline"
								size="sm"
								onClick={handleCreateExpense}
								disabled={isCreating || tourOrders.length === 0}
							>
								{isCreating ? (
									<>
										<Loader2 className="h-4 w-4 mr-2 animate-spin" />
										Creating...
									</>
								) : (
									<>
										<Plus className="h-4 w-4 mr-2" />
										Add Expense
									</>
								)}
							</Button>
						</TooltipTrigger>
						{tourOrders.length === 0 && (
							<TooltipContent>
								<p>Add orders to the tour to create expenses</p>
							</TooltipContent>
						)}
					</Tooltip>
				</TooltipProvider>
			</div>

			{tourOrders.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No orders in this tour. Add stops with orders to create
					expenses.
				</div>
			) : (
				<div className="space-y-2">
					<div className="text-xs text-muted-foreground">
						Orders available for expense allocation:
					</div>
					<div className="flex flex-wrap gap-1">
						{tourOrders.map((order) => (
							<Badge
								key={order.id}
								status="info"
								className="text-xs"
							>
								{order.orderNumber} - {order.customerName}
							</Badge>
						))}
					</div>

					{/* Tour Expenses List */}
					{isLoadingExpenses ? (
						<div className="flex justify-center items-center py-6">
							<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
							<span className="ml-2 text-sm text-muted-foreground">
								Loading expenses...
							</span>
						</div>
					) : tourExpenses.length === 0 ? (
						<div className="text-center py-4 text-sm text-muted-foreground">
							No expenses allocated to tour orders yet
						</div>
					) : (
						<div className="space-y-2">
							<div className="text-xs text-muted-foreground">
								Expenses allocated to tour orders (
								{tourExpenses.length}):
							</div>
							<div className="space-y-2 max-h-40 overflow-y-auto">
								{tourExpenses.map((expense) => (
									<div
										key={expense.id}
										className="flex items-center justify-between p-2 border rounded-md text-sm"
									>
										<div className="flex-1">
											<div className="font-medium">
												{expense.description ||
													"Expense"}
											</div>
											<div className="text-xs text-muted-foreground">
												{expense.supplier?.nameLine1 ||
													"Unknown Supplier"}{" "}
												•{" "}
												{expense.supplier_invoice_number ||
													"No invoice #"}
											</div>
										</div>
										<div className="text-right">
											<div className="font-medium">
												{formatCurrency(
													expense.totalGross,
													expense.currency,
												)}
											</div>
											<div className="text-xs text-muted-foreground">
												{new Date(
													expense.expense_date,
												).toLocaleDateString()}
											</div>
										</div>
									</div>
								))}
							</div>
						</div>
					)}
				</div>
			)}

			{/* Expense Creation Dialog */}
			<ExpenseCreateDialog
				isOpen={isExpenseDialogOpen}
				onClose={() => setIsExpenseDialogOpen(false)}
				onSubmit={handleExpenseSubmit}
				mode="create"
				preselectedCarrierId={tourData.carrierId || undefined}
			/>
		</div>
	);
}
