import type {
	CreateSmtpConfiguration,
	TestSmtpConfiguration,
	UpdateSmtpConfiguration,
} from "@repo/api/src/routes/organizations/smtp-configuration/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useState } from "react";

import {
	type TestSmtpConnection,
	useActivateSmtpConfigurationMutation,
	useCreateSmtpConfigurationMutation,
	useDeleteSmtpConfigurationMutation,
	useOAuth2FlowMutation,
	useSmtpConfigurationQuery,
	useTestSmtpConfigurationMutation,
	useTestSmtpConnectionMutation,
	useUpdateSmtpConfigurationMutation,
} from "../lib/api-smtp-configuration";

// Main hook for SMTP configuration
export function useSmtpConfiguration() {
	const { activeOrganization, loaded } = useActiveOrganization();

	const query = useSmtpConfigurationQuery({
		organizationId: activeOrganization?.id ?? "",
	});

	const testMutation = useTestSmtpConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	const activateMutation = useActivateSmtpConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	const deleteMutation = useDeleteSmtpConfigurationMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the test function
	const testSmtpConfiguration = (
		data: Omit<TestSmtpConfiguration, "organizationId">,
	) => {
		if (activeOrganization?.id) {
			testMutation.mutate(data);
		}
	};

	// Wrap the activate function
	const activateSmtpConfiguration = (isActive: boolean) => {
		if (activeOrganization?.id) {
			activateMutation.mutate(isActive);
		}
	};

	// Wrap the delete function
	const deleteSmtpConfiguration = () => {
		if (activeOrganization?.id) {
			deleteMutation.mutate();
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading || !loaded,
		error: query.error,
		refetch: query.refetch,
		testSmtpConfiguration,
		activateSmtpConfiguration,
		deleteSmtpConfiguration,
		isTestingConnection: testMutation.isPending,
		isActivating: activateMutation.isPending,
		isDeleting: deleteMutation.isPending,
	};
}

// Hook for SMTP configuration creation
export function useSmtpConfigurationCreation(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const [isSubmitting, setIsSubmitting] = useState(false);

	const createMutation = useCreateSmtpConfigurationMutation(orgId);

	const createSmtpConfiguration = async (
		data: Omit<CreateSmtpConfiguration, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await createMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} finally {
			setIsSubmitting(false);
		}
	};

	return {
		createSmtpConfiguration,
		isSubmitting,
	};
}

// Hook for SMTP configuration updates
export function useSmtpConfigurationUpdate(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const [isSubmitting, setIsSubmitting] = useState(false);

	const updateMutation = useUpdateSmtpConfigurationMutation(orgId);

	const updateSmtpConfiguration = async (
		data: Omit<UpdateSmtpConfiguration, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} finally {
			setIsSubmitting(false);
		}
	};

	return {
		updateSmtpConfiguration,
		isSubmitting,
	};
}

// Hook for testing SMTP connection with new settings
export function useSmtpConnectionTest() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const testMutation = useTestSmtpConnectionMutation(orgId);

	const testSmtpConnection = (
		data: Omit<TestSmtpConnection, "organizationId">,
	) => {
		if (!orgId) {
			return;
		}

		testMutation.mutate(data);
	};

	return {
		testSmtpConnection,
		isTestingConnection: testMutation.isPending,
	};
}

// Hook for OAuth2 authentication flow
export function useOAuth2Authentication() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const oauthMutation = useOAuth2FlowMutation(orgId);

	const initiateOAuth2Flow = (provider: "microsoft") => {
		if (!orgId) {
			return Promise.reject(new Error("No active organization"));
		}

		return oauthMutation.mutateAsync(provider);
	};

	return {
		initiateOAuth2Flow,
		isAuthenticating: oauthMutation.isPending,
	};
}
