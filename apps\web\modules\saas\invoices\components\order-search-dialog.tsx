"use client";

import { useOrdersWithUninvoicedItems } from "@saas/orders/hooks/use-orders";
import { But<PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";
import { Check, Loader2, Search, ShoppingBag } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface OrderSearchDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	selectedOrderIds: string[];
	onOrderSelect: (order: any) => void;
	customerId?: string;
}

export function OrderSearchDialog({
	open,
	onOpenChange,
	selectedOrderIds,
	onOrderSelect,
	customerId,
}: OrderSearchDialogProps) {
	const t = useTranslations();
	const [localSearch, setLocalSearch] = useState("");

	const {
		data: ordersData,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		customerId: hookCustomerId,
		setCustomerId,
		onlyWithUninvoicedItems,
		setOnlyWithUninvoicedItems,
	} = useOrdersWithUninvoicedItems();

	// When dialog opens, configure for search across all customers
	useEffect(() => {
		if (open) {
			setCustomerId(undefined); // Search across all customers
			setOnlyWithUninvoicedItems(true); // Still filter out fully invoiced
			setPage(1); // Reset to first page
		}
	}, [open, setCustomerId, setOnlyWithUninvoicedItems, setPage]);

	// Sync local search with hook
	useEffect(() => {
		setSearch(localSearch);
	}, [localSearch, setSearch]);

	// Reset search when dialog closes
	useEffect(() => {
		if (!open) {
			setLocalSearch("");
			setSearch("");
		}
	}, [open, setSearch]);

	const handleOrderSelect = (orderId: string) => {
		// Find the full order object
		const selectedOrder = availableOrders.find(
			(order: any) => order.id === orderId,
		);

		if (selectedOrder) {
			onOrderSelect(selectedOrder); // Pass full order object instead of just ID
			onOpenChange(false);
		}
	};

	// Filter out already selected orders
	const availableOrders =
		ordersData?.items?.filter(
			(order: any) => !selectedOrderIds.includes(order.id),
		) || [];

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh]">
				<DialogHeader>
					<DialogTitle>Search Orders</DialogTitle>
					<DialogDescription>
						Search for orders by order number, customer order
						number, or customer name to add to the invoice.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{/* Search Input */}
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
						<Input
							placeholder="Search by order number, customer order number, or customer name..."
							value={localSearch}
							onChange={(e) => setLocalSearch(e.target.value)}
							className="pl-10"
							autoFocus
						/>
					</div>

					{/* Results */}
					<div className="border rounded-lg">
						<ScrollArea className="h-[400px]">
							{isLoading ? (
								<div className="flex items-center justify-center py-8">
									<Loader2 className="h-6 w-6 animate-spin mr-2" />
									<span>Searching orders...</span>
								</div>
							) : availableOrders.length === 0 ? (
								<div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
									<ShoppingBag className="h-12 w-12 mb-4 opacity-40" />
									{localSearch ? (
										<div className="text-center">
											<p className="font-medium">
												No orders found
											</p>
											<p className="text-sm">
												Try searching with different
												keywords
											</p>
										</div>
									) : (
										<div className="text-center">
											<p className="font-medium">
												Start typing to search
											</p>
											<p className="text-sm">
												Search by order number, customer
												order number, or customer name
											</p>
										</div>
									)}
								</div>
							) : (
								<div className="p-2 space-y-2">
									{availableOrders.map((order: any) => (
										<button
											key={order.id}
											type="button"
											className={cn(
												"w-full p-3 border rounded-lg transition-colors text-left",
												"hover:bg-muted/50 focus:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-ring",
											)}
											onClick={() =>
												handleOrderSelect(order.id)
											}
										>
											<div className="flex items-center gap-2 mb-1">
												<span className="font-medium">
													#
													{order.order_number ||
														"N/A"}
												</span>
												{order.customer_order_number && (
													<span className="text-sm text-muted-foreground">
														Ref:{" "}
														{
															order.customer_order_number
														}
													</span>
												)}
												<div
													className={cn(
														"px-2 py-1 rounded-full text-xs font-medium",
														order.invoiceStatus ===
															"none" &&
															"bg-blue-100 text-blue-700",
														order.invoiceStatus ===
															"partial" &&
															"bg-orange-100 text-orange-700",
														order.invoiceStatus ===
															"complete" &&
															"bg-green-100 text-green-700",
													)}
												>
													{order.invoiceStatus ===
														"none" &&
														"Not Invoiced"}
													{order.invoiceStatus ===
														"partial" &&
														"Partially Invoiced"}
													{order.invoiceStatus ===
														"complete" &&
														"Fully Invoiced"}
												</div>
												<div className="ml-auto">
													<Check className="h-4 w-4 text-muted-foreground" />
												</div>
											</div>
											<div className="flex items-center gap-4 text-sm text-muted-foreground">
												<span>
													{order.customer
														?.nameLine1 ||
														"Unknown Customer"}
												</span>
												<span>
													Remaining:{" "}
													{new Intl.NumberFormat(
														"de-DE",
														{
															style: "currency",
															currency: "EUR",
														},
													).format(
														order.remainingToInvoice ||
															0,
													)}
												</span>
												<span>
													Created:{" "}
													{new Date(
														order.createdAt,
													).toLocaleDateString()}
												</span>
											</div>
										</button>
									))}
								</div>
							)}
						</ScrollArea>
					</div>

					{/* Pagination */}
					{ordersData && ordersData.totalPages > 1 && (
						<div className="flex items-center justify-between text-sm text-muted-foreground">
							<span>
								Showing {availableOrders.length} of{" "}
								{ordersData.total} orders
							</span>
							<div className="flex items-center gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setPage(Math.max(1, page - 1))
									}
									disabled={page <= 1}
								>
									Previous
								</Button>
								<span>
									Page {page} of {ordersData.totalPages}
								</span>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setPage(
											Math.min(
												ordersData.totalPages,
												page + 1,
											),
										)
									}
									disabled={page >= ordersData.totalPages}
								>
									Next
								</Button>
							</div>
						</div>
					)}
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
					>
						Cancel
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
