"use client";

import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";
import { usePersonnelAbsenceMutations } from "@saas/personnel/hooks/use-personnel-absence";
import { personnelAbsenceKeys } from "@saas/personnel/lib/api-absence";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { DELETE_DIALOG_SHORTCUTS } from "@saas/shared/components/shortcuts/registry/shortcut-registry";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
	type ReactNode,
	createContext,
	useCallback,
	useContext,
	useEffect,
	useRef,
	useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import { usePersonnelAbsences } from "../hooks/use-personnel-absence";

type AbsenceData = {
	id: string;
	personnel: {
		id: string;
		firstName: string;
		lastName: string;
		departmentId: string | null;
		departmentName?: string | null;
	};
	createdBy: {
		id: string;
		name: string;
	};
	personnelId: string;
	type: AbsenceType;
	dateRanges: {
		id: string;
		startDate: string;
		endDate: string;
		durationDays: string;
	}[];
	notes: string | null;
	description: string | null;
	isPaidLeave: boolean;
	document?: {
		id: string;
		url: string;
		fileName: string;
		fileType: string;
		fileSize: number;
		uploadedAt: string;
	} | null;
};

interface AbsenceUIContextValue {
	// State
	deleteAbsence: AbsenceData | null;
	selectedAbsence: AbsenceData | null;
	isDeleteDialogOpen: boolean;
	isCreateDialogOpen: boolean;
	isEditDialogOpen: boolean;
	focusedDeleteButton: "cancel" | "confirm";
	cancelRef: React.RefObject<HTMLButtonElement | null>;
	confirmRef: React.RefObject<HTMLButtonElement | null>;

	// History sheet state
	isHistorySheetOpen: boolean;
	historySheetPersonnelId: string | null;
	historySheetPersonnelName: string | null;

	// Entitlement dialog state
	isEntitlementDialogOpen: boolean;
	entitlementDialogPersonnel: {
		personnelId: string;
		firstName: string;
		lastName: string;
		departmentId: string | null;
		departmentName?: string | null;
		entitlement: number | null;
	} | null;

	// Actions
	handleDeleteAbsence: (absence: AbsenceData) => void;
	handleCancelDelete: () => void;
	handleConfirmDelete: () => Promise<void>;
	handleSetDeleteButtonFocus: (button: "cancel" | "confirm") => void;
	setSelectedAbsence: (absence: AbsenceData | null) => void;
	closeAllDialogs: () => void;

	// Dialog management
	setDeleteDialogOpen: (open: boolean) => void;
	setCreateDialogOpen: (open: boolean) => void;
	setEditDialogOpen: (open: boolean) => void;

	// History sheet management
	openHistorySheet: (personnelId: string, personnelName: string) => void;
	closeHistorySheet: () => void;
	setHistorySheetOpen: (open: boolean) => void;

	// Entitlement dialog management
	openEntitlementDialog: (personnel: {
		personnelId: string;
		firstName: string;
		lastName: string;
		departmentId: string | null;
		departmentName?: string | null;
		entitlement: number | null;
	}) => void;
	closeEntitlementDialog: () => void;
	setEntitlementDialogOpen: (open: boolean) => void;
}

const AbsenceUIContext = createContext<AbsenceUIContextValue | null>(null);

export function AbsenceUIProvider({
	children,
	onAbsenceDeleted,
}: {
	children: ReactNode;
	onAbsenceDeleted?: () => void;
}) {
	const t = useTranslations();
	const { deleteAbsence: deleteAbsenceMutation } =
		usePersonnelAbsenceMutations();
	const { data } = usePersonnelAbsences();
	const queryClient = useQueryClient();
	const {
		enableScope,
		disableScope,
		addShortcuts,
		removeShortcuts,
		activeScopes,
	} = useShortcuts();

	// State
	const [deleteAbsence, setDeleteAbsence] = useState<AbsenceData | null>(
		null,
	);
	const [selectedAbsence, setSelectedAbsence] = useState<AbsenceData | null>(
		null,
	);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [focusedDeleteButton, setFocusedDeleteButton] = useState<
		"cancel" | "confirm"
	>("cancel");

	// History sheet state
	const [isHistorySheetOpen, setIsHistorySheetOpen] = useState(false);
	const [historySheetPersonnel, setHistorySheetPersonnel] = useState<{
		id: string | null;
		name: string | null;
	}>({ id: null, name: null });

	// Entitlement dialog state
	const [isEntitlementDialogOpen, setIsEntitlementDialogOpen] =
		useState(false);
	const [entitlementDialogPersonnel, setEntitlementDialogPersonnel] =
		useState<{
			personnelId: string;
			firstName: string;
			lastName: string;
			departmentId: string | null;
			departmentName?: string | null;
			entitlement: number | null;
		} | null>(null);

	// Refs for focus management
	const cancelRef = useRef<HTMLButtonElement>(null);
	const confirmRef = useRef<HTMLButtonElement>(null);

	// History sheet actions
	const openHistorySheet = useCallback(
		(personnelId: string, personnelName: string) => {
			setHistorySheetPersonnel({ id: personnelId, name: personnelName });
			setIsHistorySheetOpen(true);
		},
		[],
	);

	const closeHistorySheet = useCallback(() => {
		setIsHistorySheetOpen(false);
	}, []);

	// Entitlement dialog actions
	const openEntitlementDialog = useCallback(
		(personnel: {
			personnelId: string;
			firstName: string;
			lastName: string;
			departmentId: string | null;
			departmentName?: string | null;
			entitlement: number | null;
		}) => {
			setEntitlementDialogPersonnel(personnel);
			setIsEntitlementDialogOpen(true);
		},
		[],
	);

	const closeEntitlementDialog = useCallback(() => {
		setIsEntitlementDialogOpen(false);
	}, []);

	// Actions
	const handleDeleteAbsence = useCallback(
		(absence: AbsenceData) => {
			setDeleteAbsence(absence);
			setFocusedDeleteButton("cancel");
			setIsDeleteDialogOpen(true);

			// When delete dialog opens, disable table scope and enable dialog scope
			disableScope("absence-shortcuts");
			enableScope("absence-delete-dialog");

			// Register shortcuts for the delete dialog
			addShortcuts(DELETE_DIALOG_SHORTCUTS);

			return () => {
				removeShortcuts(DELETE_DIALOG_SHORTCUTS.map((s) => s.id));
				disableScope("absence-delete-dialog");
				enableScope("absence-shortcuts");
			};
		},
		[addShortcuts, disableScope, enableScope, removeShortcuts],
	);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setDeleteAbsence(null);
		setFocusedDeleteButton("cancel");

		// Re-enable table scope when dialog closes
		disableScope("absence-delete-dialog");
		enableScope("absence-shortcuts");
	}, [disableScope, enableScope]);

	const handleConfirmDelete = useCallback(async () => {
		if (!deleteAbsence) {
			return;
		}

		try {
			await deleteAbsenceMutation(deleteAbsence.id);
			toast.success(t("app.personnel.absence.delete.success"));

			// Invalidate queries to refresh the data
			await queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.all,
			});

			// Close dialog and reset state
			setIsDeleteDialogOpen(false);
			setDeleteAbsence(null);
			setFocusedDeleteButton("cancel");

			// Re-enable table scope when dialog closes
			disableScope("absence-delete-dialog");
			enableScope("absence-shortcuts");

			// Notify parent component
			onAbsenceDeleted?.();
		} catch (error) {
			toast.error(t("app.personnel.absence.delete.error"));
		}
	}, [
		deleteAbsence,
		deleteAbsenceMutation,
		disableScope,
		enableScope,
		onAbsenceDeleted,
		queryClient,
		t,
	]);

	const handleSetDeleteButtonFocus = useCallback(
		(button: "cancel" | "confirm") => {
			setFocusedDeleteButton(button);
		},
		[],
	);

	const closeAllDialogs = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setIsCreateDialogOpen(false);
		setIsEditDialogOpen(false);
		setIsHistorySheetOpen(false);
		setIsEntitlementDialogOpen(false);
		setDeleteAbsence(null);
		setFocusedDeleteButton("cancel");
	}, []);

	// Add keyboard handlers for arrow keys in delete dialog
	useHotkeys(
		"left",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("cancel");
			cancelRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("absence-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"right",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("confirm");
			confirmRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("absence-delete-dialog"),
			preventDefault: true,
		},
	);

	// Add keyboard handlers for enter/esc in delete dialog
	useHotkeys(
		"enter",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				if (focusedDeleteButton === "cancel") {
					handleCancelDelete();
				} else {
					void handleConfirmDelete();
				}
			}
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("absence-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"esc",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen || isCreateDialogOpen || isEditDialogOpen) {
				closeAllDialogs();
			}
		},
		{
			enabled:
				(isDeleteDialogOpen &&
					activeScopes.includes("absence-delete-dialog")) ||
				isCreateDialogOpen ||
				isEditDialogOpen,
			preventDefault: true,
		},
	);

	// Add keyboard handler for Ctrl+D to trigger delete
	useHotkeys(
		"ctrl+d",
		(e) => {
			e.preventDefault();
			const highlightedRow = document.querySelector(
				"[data-row-id].bg-muted",
			);
			if (!isDeleteDialogOpen && highlightedRow) {
				const rowId = highlightedRow.getAttribute("data-row-id");
				const absence = data?.items?.find((item) => item.id === rowId);
				if (absence) {
					handleDeleteAbsence(absence as unknown as AbsenceData);
				}
			}
		},
		{
			enabled: activeScopes.includes("absence-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	// Render dialogs directly in the context provider
	useEffect(() => {
		if (isDeleteDialogOpen) {
			// Add tab trap focus management
			enableScope("absence-delete-dialog");
			disableScope("absence-shortcuts");
		} else {
			disableScope("absence-delete-dialog");
			enableScope("absence-shortcuts");
		}
	}, [isDeleteDialogOpen, enableScope, disableScope]);

	return (
		<AbsenceUIContext.Provider
			value={{
				// State
				deleteAbsence,
				selectedAbsence,
				isDeleteDialogOpen,
				isCreateDialogOpen,
				isEditDialogOpen,
				focusedDeleteButton,
				cancelRef,
				confirmRef,

				// History sheet state
				isHistorySheetOpen,
				historySheetPersonnelId: historySheetPersonnel.id,
				historySheetPersonnelName: historySheetPersonnel.name,

				// Entitlement dialog state
				isEntitlementDialogOpen,
				entitlementDialogPersonnel,

				// Actions
				handleDeleteAbsence,
				handleCancelDelete,
				handleConfirmDelete,
				handleSetDeleteButtonFocus,
				setSelectedAbsence,
				closeAllDialogs,

				// Dialog management
				setDeleteDialogOpen: setIsDeleteDialogOpen,
				setCreateDialogOpen: setIsCreateDialogOpen,
				setEditDialogOpen: setIsEditDialogOpen,

				// History sheet management
				openHistorySheet,
				closeHistorySheet,
				setHistorySheetOpen: setIsHistorySheetOpen,

				// Entitlement dialog management
				openEntitlementDialog,
				closeEntitlementDialog,
				setEntitlementDialogOpen: setIsEntitlementDialogOpen,
			}}
		>
			{children}
		</AbsenceUIContext.Provider>
	);
}

export function useAbsenceUI() {
	const context = useContext(AbsenceUIContext);
	if (!context) {
		throw new Error(
			"useAbsenceUI must be used within an AbsenceUIProvider",
		);
	}
	return context;
}
