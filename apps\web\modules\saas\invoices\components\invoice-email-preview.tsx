"use client";

import { UniversalEmailPreview } from "@saas/shared/components/universal-email-preview";
import { useUniversalSendEmail } from "@saas/shared/hooks/use-universal-send-email";
import type { SendEmailParams } from "@saas/shared/types/email-preview";

interface InvoiceEmailPreviewProps {
	invoiceId: string | null;
	isOpen: boolean;
	onClose: () => void;
	onSend: (
		invoiceId: string,
		documentIds?: string[],
		customSubject?: string,
	) => Promise<void>;
	isSending?: boolean;
}

export function InvoiceEmailPreview({
	invoiceId,
	isOpen,
	onClose,
	onSend, // Keep for interface compatibility but not used
	isSending = false,
}: InvoiceEmailPreviewProps) {
	const { sendEmail } = useUniversalSendEmail();

	// Adapter function to convert universal params to the original onSend interface
	const handleUniversalSend = async (
		params: SendEmailParams,
	): Promise<void> => {
		// Always use the universal send email hook for consistency and full feature support
		await sendEmail(params);
	};

	return (
		<UniversalEmailPreview
			entityId={invoiceId}
			entityType="invoice"
			isOpen={isOpen}
			onClose={onClose}
			onSend={handleUniversalSend}
			isSending={isSending}
		/>
	);
}
