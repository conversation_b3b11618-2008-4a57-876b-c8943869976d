import type { AbsenceType } from "@prisma/client";
import type { StatisticsResponse } from "@repo/api/src/routes/personnel-statistics/types";
import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { subMonths } from "date-fns";

// Types for API requests
export interface StatisticsQueryParams {
	organizationId: string;
	startDate?: Date;
	endDate?: Date;
	departmentIds?: string[];
	absenceTypes?: AbsenceType[];
	personnelId?: string;
}

// Query keys for React Query
export const personnelStatisticsKeys = {
	all: ["personnel-statistics"] as const,
	statistics: () => [...personnelStatisticsKeys.all, "data"] as const,
	filteredStatistics: (params: StatisticsQueryParams) =>
		[...personnelStatisticsKeys.statistics(), params] as const,
};

// API function to fetch statistics
export const fetchStatistics = async (
	params: StatisticsQueryParams,
): Promise<StatisticsResponse> => {
	const response = await apiClient.statistics.personnel.$get({
		query: {
			organizationId: params.organizationId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
			personnelId: params.personnelId,
			departmentIds:
				params.departmentIds && params.departmentIds.length > 0
					? params.departmentIds
					: undefined,
			absenceTypes:
				params.absenceTypes && params.absenceTypes.length > 0
					? params.absenceTypes
					: undefined,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch personnel statistics");
	}

	return response.json();
};

// Default time range for statistics (6 months)
export function getDefaultDateRange(): { startDate: Date; endDate: Date } {
	const endDate = new Date();
	const startDate = subMonths(endDate, 6);
	return { startDate, endDate };
}

// Custom hook to fetch personnel statistics with filters
export function usePersonnelStatistics(
	params: StatisticsQueryParams,
	options: { enabled?: boolean } = {},
) {
	// Apply default date range if not provided
	const queryParams = {
		...params,
		startDate: params.startDate || getDefaultDateRange().startDate,
		endDate: params.endDate || getDefaultDateRange().endDate,
	};

	// Use TanStack Query to fetch and cache the data
	return useQuery({
		queryKey: personnelStatisticsKeys.filteredStatistics(queryParams),
		queryFn: () => fetchStatistics(queryParams),
		staleTime: 1000 * 60 * 5, // 5 minutes
		placeholderData: keepPreviousData,
		refetchOnWindowFocus: false, // Don't refetch when window regains focus
		refetchOnMount: false, // Don't refetch when component mounts if data exists
		enabled: options.enabled !== false && !!params.organizationId,
	});
}
