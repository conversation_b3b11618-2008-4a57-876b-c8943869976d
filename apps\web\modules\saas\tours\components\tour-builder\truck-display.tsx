import {
	Tooltip,
	TooltipContent,
	Toolt<PERSON>Provider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { HelpCircle, Loader2 } from "lucide-react";
import { useState } from "react";

interface TruckIconProps {
	className?: string;
	alt?: string;
	onLoad?: () => void;
	onError?: () => void;
}

function TruckIcon({
	className,
	alt = "Truck illustration",
	onLoad,
	onError,
}: TruckIconProps) {
	return (
		<img
			src="/images/truck.svg"
			alt={alt}
			onLoad={onLoad}
			onError={onError}
			className={cn(
				"w-full h-auto",
				"scale-x-[-1]",
				"dark:invert dark:brightness-0 dark:contrast-100",
				className,
			)}
		/>
	);
}

interface CargoOverlayProps {
	fillPercentage: number;
	currentLoadingMeters?: number;
	maxLoadingMeters?: number;
	className?: string;
}

function CargoOverlay({
	fillPercentage,
	currentLoadingMeters,
	maxLoadingMeters,
	className,
}: CargoOverlayProps) {
	// Clamp percentage between 0 and 100
	const clampedPercentage = Math.max(0, Math.min(100, fillPercentage));

	return (
		<div className={cn("absolute inset-0", className)}>
			{/* Cargo area outline - positioned to match the truck's cargo space */}
			<div
				className="absolute border-2 border-dashed border-blue-500 dark:border-blue-400"
				style={{
					// These values position the overlay over the cargo area of the truck
					// Adjust these percentages based on where the cargo area is in the SVG
					left: "0.5%",
					top: "3%",
					width: "83%",
					height: "67%",
				}}
			>
				{/* Fill indicator */}
				<div
					className="absolute top-0 right-0 bg-blue-500/30 dark:bg-blue-400/30 transition-all duration-300"
					style={{
						width: `${clampedPercentage}%`,
						height: "100%",
					}}
				/>

				{/* Fill labels */}
				<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center">
					<div className="bg-white/90 dark:bg-gray-800/90 px-2 py-1 rounded shadow-sm">
						<div className="flex items-center gap-1 justify-center">
							<div className="text-xs font-bold text-blue-600 dark:text-blue-300">
								{Math.round(clampedPercentage)}%
							</div>
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger>
										<HelpCircle className="h-3 w-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300" />
									</TooltipTrigger>
									<TooltipContent>
										<div className="text-xs max-w-64">
											<p className="font-medium mb-1">
												Cargo Fill Calculation:
											</p>
											<p>
												Shows cargo level after the last
												loading stop.
											</p>
											<p className="mt-1">
												• Adds cargo from loading stops
											</p>
											<p>
												• Subtracts cargo from unloading
												stops
											</p>
											<p>
												• Matches unloading to orders
												automatically
											</p>
										</div>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						</div>
						{currentLoadingMeters !== undefined &&
							maxLoadingMeters !== undefined && (
								<div className="text-[10px] text-gray-600 dark:text-gray-400 mt-0.5">
									{currentLoadingMeters.toFixed(1)}m /{" "}
									{maxLoadingMeters.toFixed(1)}m
								</div>
							)}
					</div>
				</div>
			</div>
		</div>
	);
}

interface DimensionLineProps {
	orientation: "horizontal" | "vertical";
	label: string;
	className?: string;
}

function DimensionLine({ orientation, label, className }: DimensionLineProps) {
	if (orientation === "vertical") {
		return (
			<div
				className={cn(
					"absolute left-0 top-0 h-full flex items-center",
					className,
				)}
			>
				<div className="flex flex-col items-center h-full">
					{/* Top arrow */}
					<div className="w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-b-[8px] border-b-gray-600 dark:border-b-gray-400" />

					{/* Vertical line */}
					<div className="w-[2px] bg-gray-600 dark:bg-gray-400 flex-1 mx-2" />

					{/* Label */}
					<div className="absolute left-[12px] top-1/2 -translate-y-1/2 -translate-x-full">
						<span className="text-xs font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap transform -rotate-90 block">
							{label}
						</span>
					</div>

					{/* Bottom arrow */}
					<div className="w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[8px] border-t-gray-600 dark:border-t-gray-400" />
				</div>
			</div>
		);
	}

	return (
		<div
			className={cn(
				"absolute bottom-0 left-0 w-full flex justify-center",
				className,
			)}
		>
			<div className="flex items-center w-full">
				{/* Left arrow */}
				<div className="w-0 h-0 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent border-r-[8px] border-r-gray-600 dark:border-r-gray-400" />

				{/* Horizontal line */}
				<div className="h-[2px] bg-gray-600 dark:bg-gray-400 flex-1 my-2" />

				{/* Label */}
				<div className="absolute bottom-[10px] left-1/2 -translate-x-1/2 translate-y-full pt-0">
					<span className="text-xs font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">
						{label}
					</span>
				</div>

				{/* Right arrow */}
				<div className="w-0 h-0 border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent border-l-[8px] border-l-gray-600 dark:border-l-gray-400" />
			</div>
		</div>
	);
}

interface TruckDisplayProps {
	className?: string;
	length?: number;
	height?: number;
	cargoFill?: number; // Percentage of cargo space filled (0-100)
	currentLoadingMeters?: number; // Current loading meters
	maxLoadingMeters?: number; // Maximum loading meters (vehicle capacity)
}

export function TruckDisplay({
	className,
	length,
	height,
	cargoFill = 0,
	currentLoadingMeters,
	maxLoadingMeters,
}: TruckDisplayProps) {
	const [isImageLoaded, setIsImageLoaded] = useState(false);
	const [hasImageError, setHasImageError] = useState(false);

	const lengthLabel = length ? `${length} m` : "? m";
	const heightLabel = height ? `${height} m` : "? m";

	const handleImageLoad = () => {
		setIsImageLoaded(true);
		setHasImageError(false);
	};

	const handleImageError = () => {
		setHasImageError(true);
		setIsImageLoaded(false);
	};

	return (
		<div className={cn("py-8 px-8", className)}>
			{/* Loading state */}
			{!isImageLoaded && !hasImageError && (
				<div className="flex items-center justify-center h-32">
					<div className="flex items-center gap-2 text-gray-500">
						<Loader2 className="h-4 w-4 animate-spin" />
						<span className="text-sm">
							Loading truck display...
						</span>
					</div>
				</div>
			)}

			{/* Error state */}
			{hasImageError && (
				<div className="flex items-center justify-center h-32">
					<div className="text-center text-gray-500">
						<div className="text-sm">
							Failed to load truck illustration
						</div>
						<div className="text-xs mt-1">
							Dimensions: {lengthLabel} × {heightLabel}
						</div>
						{currentLoadingMeters !== undefined &&
							maxLoadingMeters !== undefined && (
								<div className="text-xs mt-1">
									Cargo: {currentLoadingMeters.toFixed(1)}m /{" "}
									{maxLoadingMeters.toFixed(1)}m (
									{Math.round(cargoFill)}%)
								</div>
							)}
					</div>
				</div>
			)}

			{/* Truck display - only show when image is loaded */}
			{isImageLoaded && (
				<div className="relative">
					{/* Height indicator */}
					<DimensionLine
						orientation="vertical"
						label={heightLabel}
						className="-ml-6"
					/>

					{/* Truck icon container */}
					<div className="relative">
						{/* Truck icon */}
						<TruckIcon
							className="relative z-10"
							onLoad={handleImageLoad}
							onError={handleImageError}
						/>

						{/* Cargo overlay */}
						<CargoOverlay
							fillPercentage={cargoFill}
							currentLoadingMeters={currentLoadingMeters}
							maxLoadingMeters={maxLoadingMeters}
							className="z-20"
						/>
					</div>

					{/* Length indicator */}
					<DimensionLine
						orientation="horizontal"
						label={lengthLabel}
						className="-mb-4"
					/>
				</div>
			)}

			{/* Hidden truck icon for loading detection */}
			{!isImageLoaded && !hasImageError && (
				<TruckIcon
					className="absolute opacity-0 pointer-events-none"
					onLoad={handleImageLoad}
					onError={handleImageError}
				/>
			)}
		</div>
	);
}
