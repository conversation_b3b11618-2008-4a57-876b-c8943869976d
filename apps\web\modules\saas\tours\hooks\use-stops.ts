import {
	type EntityType,
	useCreateStopMutation,
	useDeleteStopMutation,
	useStopByIdQuery,
	useStopsQuery,
	useUpdateStopMutation,
} from "@saas/orders/lib/api-stop";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";

// Main hook for managing stops list
export function useStops(
	options: {
		entityId?: string;
		entityType?: EntityType;
		tourId?: string;
		isUnassigned?: boolean;
		dateStart?: Date;
		dateEnd?: Date;
		stopType?: "loading" | "unloading";
		usePrioritySort?: boolean;
		groupByOrder?: boolean;
		// Add map bounds filtering options
		minLat?: number;
		maxLat?: number;
		minLng?: number;
		maxLng?: number;
	} = {},
) {
	console.log("useStops hook called with options:", options);
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	// Default to priority sorting unless explicitly disabled or another sorting is specified
	const usePrioritySort =
		options.usePrioritySort !== false && !debouncedSearch;

	const query = useStopsQuery({
		organizationId: activeOrganization?.id ?? "",
		entityId: options.entityId,
		entityType: options.entityType,
		search: debouncedSearch,
		page,
		limit: pageSize,
		// Add the additional parameters supported by the API
		tourId: options.tourId,
		isUnassigned: options.isUnassigned,
		dateStart: options.dateStart,
		dateEnd: options.dateEnd,
		stopType: options.stopType,
		// Use priority sorting by default if nothing else overrides it
		sortBy: usePrioritySort
			? "priority"
			: sorting.length > 0
				? sorting[0].id
				: undefined,
		sortDirection:
			sorting.length > 0 ? (sorting[0].desc ? "desc" : "asc") : undefined,
		groupByOrder: options.groupByOrder,
		// Add map bounds parameters if they exist
		minLat: options.minLat,
		maxLat: options.maxLat,
		minLng: options.minLng,
		maxLng: options.maxLng,
	});

	const deleteMutation = useDeleteStopMutation(
		activeOrganization?.id ?? "",
		options.entityId ?? "",
		options.entityType ?? "order",
	);

	// Wrap the delete function to refetch after deletion
	const deleteStop = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete stop error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deleteStop,
	};
}

// Hook for fetching a single stop by ID
export function useStopById(stopId: string) {
	const { activeOrganization } = useActiveOrganization();
	return useStopByIdQuery(activeOrganization?.id, stopId);
}

// Hook for CRUD operations on stops
export function useStopMutations(options?: {
	entityId?: string;
	entityType?: EntityType;
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const entityType = options?.entityType ?? "order";
	const entityId = options?.entityId ?? "";

	const createMutation = useCreateStopMutation(orgId);
	const updateMutation = useUpdateStopMutation(orgId);
	const deleteMutation = useDeleteStopMutation(orgId, entityId, entityType);

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		const result = await createMutation.mutateAsync(data);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (data: any) => {
		const result = await updateMutation.mutateAsync(data);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createStop: createWithCallback,
		updateStop: updateWithCallback,
		deleteStop: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Hook for managing tour-related stops
export function useTourStops(tourId: string) {
	// Use the main useStops hook but with tourId as a filter parameter
	// This correctly matches the API's filtering capabilities
	const stopsHook = useStops({
		tourId,
	});

	return {
		...stopsHook,
		// Get stops that aren't assigned to any tour yet
		useUnassignedStops: () => useStops({ isUnassigned: true }),
		// Return stop mutations with the tour context
		useTourStopMutations: (options?: { onSuccess?: () => void }) =>
			useStopMutations({
				...options,
				// When creating a stop for a tour, we need to provide callback
				// that refreshes the tour's stops list
				onSuccess: () => {
					options?.onSuccess?.();
					stopsHook.refetch();
				},
			}),
	};
}
