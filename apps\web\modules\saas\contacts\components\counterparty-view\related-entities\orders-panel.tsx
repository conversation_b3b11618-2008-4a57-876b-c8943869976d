"use client";

import { OrdersUIProvider } from "@saas/orders/context/orders-ui-context";
import type { Order } from "@saas/orders/context/orders-ui-context";
import { useOrders } from "@saas/orders/hooks/use-orders";
import { useColumns } from "@saas/orders/orders-table/columns";
import { DataTable } from "@saas/shared/components/data-table";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Package } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface OrdersPanelProps {
	counterpartyId: string;
}

export function OrdersPanel({ counterpartyId }: OrdersPanelProps) {
	const t = useTranslations();
	const [pageSize, setPageSize] = useState(5);
	const [page, setPage] = useState(1);

	// Use the orders hook to fetch data filtered by counterpartyId
	const {
		data,
		isLoading,
		search,
		setSearch,
		sorting,
		setSorting,
		setCustomerId,
		refetch,
	} = useOrders();

	// Set the customerId to the counterpartyId prop
	useEffect(() => {
		setSearch("");
		setPage(1);
		setCustomerId(counterpartyId);
	}, [counterpartyId, setSearch, setPage, setCustomerId]);

	// We'll cast the data to Order[] to handle any type mismatch
	const orders = (data?.items as unknown as Order[]) ?? [];

	// Empty state component
	const EmptyState = () => (
		<div className="flex flex-col items-center justify-center py-12 text-center">
			<Package className="h-12 w-12 text-muted-foreground mb-4" />
			<h3 className="text-lg font-medium mb-2">No Orders Yet</h3>
			<p className="text-muted-foreground mb-6 max-w-md">
				This contact doesn't have any orders yet. Create your first
				order to start tracking purchases.
			</p>
			{/* <Button asChild>
				<Link href={`/app/orders/new?customerId=${counterpartyId}`}>
					<Plus className="h-4 w-4 mr-2" />
					Create Order
				</Link>
			</Button> */}
		</div>
	);

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Orders</CardTitle>
				{/* <Button size="sm" asChild>
					<Link href={`/app/orders/new?customerId=${counterpartyId}`}>
						<Plus className="h-4 w-4 mr-2" />
						Create Order
					</Link>
				</Button> */}
			</CardHeader>
			<CardContent>
				<OrdersUIProvider onOrderDeleted={refetch}>
					{orders.length === 0 && !isLoading ? (
						<EmptyState />
					) : (
						<DataTableWrapper
							orders={orders}
							isLoading={isLoading}
							search={search}
							setSearch={setSearch}
							page={page}
							setPage={setPage}
							pageSize={pageSize}
							setPageSize={setPageSize}
							data={data}
							sorting={sorting}
							setSorting={setSorting}
						/>
					)}
				</OrdersUIProvider>
			</CardContent>
		</Card>
	);
}

// Separate component inside which we can use the useColumns hook
interface DataTableWrapperProps {
	orders: Order[];
	isLoading: boolean;
	search: string;
	setSearch: (search: string) => void;
	page: number;
	setPage: (page: number) => void;
	pageSize: number;
	setPageSize: (pageSize: number) => void;
	data: any;
	sorting: any;
	setSorting: (sorting: any) => void;
}

function DataTableWrapper({
	orders,
	isLoading,
	search,
	setSearch,
	page,
	setPage,
	pageSize,
	setPageSize,
	data,
	sorting,
	setSorting,
}: DataTableWrapperProps) {
	const columns = useColumns();

	return (
		<DataTable
			columns={columns}
			data={orders}
			defaultColumnVisibility={{
				id: false,
				"customer.nameLine1": false,
			}}
			onSearch={setSearch}
			searchValue={search}
			searchPlaceholder="Search orders..."
			pagination={{
				page,
				setPage,
				pageSize,
				setPageSize,
				totalPages: data?.totalPages ?? 1,
				total: data?.total ?? 0,
			}}
			isLoading={isLoading}
			sorting={sorting}
			onSortingChange={setSorting}
			manualSorting={true}
			shortcutsScope="customer-orders-shortcuts"
		/>
	);
}
