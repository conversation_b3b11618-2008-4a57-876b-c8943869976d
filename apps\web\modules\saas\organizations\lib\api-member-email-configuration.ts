import type {
	ActivateMemberEmailConfiguration,
	CreateMemberEmailConfiguration,
	TestMemberConnection,
	TestMemberEmailConfiguration,
	UpdateMemberEmailConfiguration,
} from "@repo/api/src/routes/organizations/member-email-configuration/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface GetMemberEmailConfigurationParams {
	organizationId: string;
}

// Query keys for React Query
export const memberEmailConfigurationKeys = {
	all: ["member-email-configuration"] as const,
	details: () => [...memberEmailConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...memberEmailConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchMemberEmailConfiguration = async (
	params: GetMemberEmailConfigurationParams,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].$get({
		query: {
			organizationId: params.organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch member email configuration");
	}

	return response.json();
};

export const createMemberEmailConfiguration = async (
	data: CreateMemberEmailConfiguration,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to create member email configuration");
	}

	return response.json();
};

export const updateMemberEmailConfiguration = async (
	data: UpdateMemberEmailConfiguration,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].$put({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to update member email configuration");
	}

	return response.json();
};

export const deleteMemberEmailConfiguration = async (
	organizationId: string,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].$delete({
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to delete member email configuration");
	}

	return response.json();
};

export const testMemberEmailConfiguration = async (
	data: TestMemberEmailConfiguration,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].test.$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to test member email configuration");
	}

	return response.json();
};

export const testMemberConnection = async (data: TestMemberConnection) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	]["test-connection"].$post({
		json: data,
	});

	if (!response.ok) {
		// Parse the error response and throw it
		const errorData = await response.json();
		throw errorData;
	}

	return response.json();
};

export const activateMemberEmailConfiguration = async (
	data: ActivateMemberEmailConfiguration,
) => {
	const response = await apiClient.organizations[
		"member-email-configuration"
	].activate.$post({
		json: data,
	});

	if (!response.ok) {
		throw new Error("Failed to activate member email configuration");
	}

	return response.json();
};

// OAuth2 API functions for member email configuration
export const initiateMemberOAuth2Flow = async (
	organizationId: string,
	provider: "microsoft",
) => {
	// Use fetch directly since the OAuth routes are dynamic in the auth router
	const baseUrl = process.env.NEXT_PUBLIC_SITE_URL;
	const response = await fetch(
		`${baseUrl}/api/oauth/smtp/${provider}/member/initiate`,
		{
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			credentials: "include",
			body: JSON.stringify({ organizationId }),
		},
	);

	if (!response.ok) {
		throw new Error("Failed to initiate member OAuth2 flow");
	}

	return response.json();
};

// React Query Hooks
export const useMemberEmailConfigurationQuery = (
	params: GetMemberEmailConfigurationParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: memberEmailConfigurationKeys.detail(params.organizationId),
		queryFn: () => fetchMemberEmailConfiguration(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

export const useCreateMemberEmailConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<CreateMemberEmailConfiguration, "organizationId">,
		) => {
			return createMemberEmailConfiguration({ ...data, organizationId });
		},
		onSuccess: () => {
			toast.success("Member email configuration created successfully");
			queryClient.invalidateQueries({
				queryKey: memberEmailConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create member email configuration",
			);
		},
	});
};

export const useUpdateMemberEmailConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateMemberEmailConfiguration, "organizationId">,
		) => {
			return updateMemberEmailConfiguration({ ...data, organizationId });
		},
		onSuccess: () => {
			toast.success("Member email configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: memberEmailConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update member email configuration",
			);
		},
	});
};

export const useDeleteMemberEmailConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async () => {
			return deleteMemberEmailConfiguration(organizationId);
		},
		onSuccess: () => {
			toast.success("Member email configuration deleted successfully");
			queryClient.invalidateQueries({
				queryKey: memberEmailConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete member email configuration",
			);
		},
	});
};

export const useTestMemberEmailConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<TestMemberEmailConfiguration, "organizationId">,
		) => {
			const result = await testMemberEmailConfiguration({
				...data,
				organizationId,
			});

			// Check if the email test actually succeeded
			if (!result.success) {
				// Use the detailed error message if available
				const errorMessage =
					result.error ||
					result.message ||
					"Member email configuration test failed";
				throw new Error(errorMessage);
			}

			return result;
		},
		onSuccess: () => {
			toast.success("Member email configuration tested successfully");
			queryClient.invalidateQueries({
				queryKey: memberEmailConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to test member email configuration",
				{
					duration: 10000, // Longer duration for errors
				},
			);
		},
	});
};

export const useTestMemberConnectionMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (
			data: Omit<TestMemberConnection, "organizationId">,
		) => {
			const result = await testMemberConnection({
				...data,
				organizationId,
			});

			// Check if the connection test actually succeeded
			if (!result.success) {
				throw new Error(
					result.message || "Member email connection test failed",
				);
			}

			return result;
		},
		onSuccess: () => {
			toast.success("Member email connection tested successfully");
		},
		onError: (error: any) => {
			// Just display whatever error message we get from the backend
			const message =
				error.message || "Failed to test member email connection";
			toast.error(message, {
				duration: 10000, // Longer duration for errors
			});
		},
	});
};

export const useActivateMemberEmailConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (isActive: boolean) => {
			return activateMemberEmailConfiguration({
				organizationId,
				isActive,
			});
		},
		onSuccess: (_, isActive) => {
			toast.success(
				`Member email configuration ${isActive ? "activated" : "deactivated"} successfully`,
			);
			queryClient.invalidateQueries({
				queryKey: memberEmailConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to activate member email configuration",
			);
		},
	});
};

export const useMemberOAuth2FlowMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (provider: "microsoft") => {
			const result = await initiateMemberOAuth2Flow(
				organizationId,
				provider,
			);

			// Open popup window for OAuth2 flow
			const popup = window.open(
				result.authUrl,
				"oauth2-popup",
				"width=600,height=700,scrollbars=yes,resizable=yes",
			);

			if (!popup) {
				throw new Error(
					"Popup window blocked. Please allow popups for this site.",
				);
			}

			// Wait for OAuth2 callback message
			return new Promise<{
				refreshToken: string;
				accessToken: string;
				tokenExpires?: string;
				userEmail?: string;
			}>((resolve, reject) => {
				const messageHandler = (event: MessageEvent) => {
					if (event.origin !== window.location.origin) {
						return;
					}

					if (
						event.data.type === "oauth2-success" &&
						event.data.configType === "member"
					) {
						window.removeEventListener("message", messageHandler);
						popup.close();

						// Extract user email from access token if available
						let userEmail: string | undefined;
						try {
							if (event.data.tokens.accessToken) {
								const tokenParts =
									event.data.tokens.accessToken.split(".");
								if (tokenParts.length === 3) {
									// Decode JWT payload
									const payload = JSON.parse(
										atob(
											tokenParts[1]
												.replace(/-/g, "+")
												.replace(/_/g, "/"),
										),
									);
									userEmail =
										payload.preferred_username ||
										payload.upn ||
										payload.unique_name ||
										payload.email;
								}
							}
						} catch (error) {
							console.warn(
								"Failed to extract user email from access token:",
								error,
							);
						}

						resolve({
							...event.data.tokens,
							userEmail,
						});
					}
				};

				window.addEventListener("message", messageHandler);

				// Check if popup was closed manually
				const checkClosed = setInterval(() => {
					if (popup.closed) {
						clearInterval(checkClosed);
						window.removeEventListener("message", messageHandler);
						reject(new Error("Member OAuth2 flow was cancelled"));
					}
				}, 1000);
			});
		},
		onSuccess: () => {
			toast.success(
				"Member OAuth2 authentication successful! You can now save your personal email configuration.",
			);
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to complete member OAuth2 authentication",
			);
		},
	});
};

// Export types for use in components
export type {
	CreateMemberEmailConfiguration,
	UpdateMemberEmailConfiguration,
	TestMemberEmailConfiguration,
	TestMemberConnection,
	ActivateMemberEmailConfiguration,
};
