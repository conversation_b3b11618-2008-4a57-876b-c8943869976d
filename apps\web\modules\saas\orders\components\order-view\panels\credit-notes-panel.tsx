"use client";

import {
	useCustomerCreditNoteById,
	useCustomerCreditNotes,
} from "@saas/invoices/hooks/use-customer-credit-notes";
import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import {
	ExternalLink,
	FileText,
	FileX,
	Loader2,
	MoreHorizontal,
} from "lucide-react";
import { useState } from "react";

// Define the credit note interface based on the schema
interface CreditNoteWithDetails {
	id: string;
	credit_note_number?: string;
	credit_note_date: Date | string;
	description?: string;
	gross_amount: number;
	net_amount: number;
	vat_amount: number;
	vat_rate: number;
	customer_document_url?: string;
	customer?: {
		nameLine1: string;
		email?: string;
	};
	orderCreditNotes?: Array<{
		orderId: string;
		gross_amount_applied_to_order?: number;
	}>;
}

interface CreditNotesPanelProps {
	orderId: string;
	onCreateCreditNote?: () => void;
}

// PDF viewer dialog component
function CreditNotePdfDialog({
	creditNoteId,
	isOpen,
	onClose,
}: {
	creditNoteId: string | null;
	isOpen: boolean;
	onClose: () => void;
}) {
	// Use the hook to fetch credit note with document
	const {
		creditNote: creditNoteWithDoc,
		document,
		isLoading,
	} = useCustomerCreditNoteById(
		creditNoteId || undefined,
		true, // includeDocument=true
	);

	// Check if we have document data in the response
	const documentUrl = document?.url;
	const documentData = document?.data;

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="max-w-5xl max-h-[90vh]">
				<DialogHeader>
					<DialogTitle>Credit Note Document</DialogTitle>
				</DialogHeader>
				<div className="h-[80vh]">
					{isLoading ? (
						<div className="flex items-center justify-center h-full">
							<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
						</div>
					) : documentData ? (
						<iframe
							src={`data:application/pdf;base64,${documentData}`}
							width="100%"
							height="100%"
							title="Credit Note Document"
						/>
					) : documentUrl ? (
						<iframe
							src={documentUrl}
							width="100%"
							height="100%"
							title="Credit Note Document"
						/>
					) : (
						<div className="flex items-center justify-center h-full text-muted-foreground">
							PDF preview not available
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

export function CreditNotesPanel({
	orderId,
	onCreateCreditNote,
}: CreditNotesPanelProps) {
	const [isProcessing, setIsProcessing] = useState(false);
	const [viewCreditNoteId, setViewCreditNoteId] = useState<string | null>(
		null,
	);
	const [isPdfDialogOpen, setIsPdfDialogOpen] = useState(false);

	// Fetch credit notes data for this specific order
	const { creditNotes, isLoading, error, refetch } = useCustomerCreditNotes({
		orderId: orderId,
		limit: 100,
	});

	const handleViewDocument = (creditNoteId: string) => {
		setViewCreditNoteId(creditNoteId);
		setIsPdfDialogOpen(true);
	};

	const formatCurrency = (
		amount?: string | number | null,
		currency = "EUR",
	) => {
		if (!amount && amount !== 0) {
			return "-";
		}
		return new Intl.NumberFormat("de-DE", {
			style: "currency",
			currency,
		}).format(Number(amount));
	};

	const formatDate = (date?: string | Date | null) => {
		if (!date) {
			return "-";
		}
		return format(new Date(date), "PP");
	};

	return (
		<TooltipProvider>
			<div className="space-y-2">
				{isLoading ? (
					<div className="flex justify-center items-center py-6">
						<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
					</div>
				) : error ? (
					<div className="text-center py-4 text-sm text-destructive">
						Error loading credit notes
					</div>
				) : creditNotes.length === 0 ? (
					<div className="text-center py-4 text-sm text-muted-foreground">
						No credit notes found for this order
					</div>
				) : (
					<ScrollArea className="h-[300px] pr-3 -mr-3">
						<div className="space-y-3">
							{creditNotes.map((originalCreditNote) => {
								// Cast to our extended type
								const creditNote =
									originalCreditNote as unknown as CreditNoteWithDetails;
								return (
									<div
										key={creditNote.id}
										className="border rounded-lg p-4 bg-card"
									>
										{/* Header with Credit Note Number and Actions */}
										<div className="flex items-center justify-between mb-3">
											<div className="flex items-center gap-2">
												<FileX className="h-4 w-4 text-muted-foreground" />
												<h4 className="font-semibold text-sm">
													Credit Note #
													{creditNote.credit_note_number ||
														creditNote.id.slice(-8)}
												</h4>
											</div>
											<div className="flex items-center gap-2">
												{creditNote.orderCreditNotes &&
													creditNote.orderCreditNotes
														.length > 1 && (
														<Badge
															status="info"
															className="text-xs"
														>
															{
																creditNote
																	.orderCreditNotes
																	.length
															}{" "}
															Orders
														</Badge>
													)}
												<DropdownMenu>
													<DropdownMenuTrigger
														asChild
													>
														<Button
															variant="ghost"
															size="sm"
															className="h-8 w-8 p-0"
														>
															<MoreHorizontal className="h-4 w-4" />
															<span className="sr-only">
																Actions
															</span>
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end">
														<DropdownMenuItem
															onClick={() =>
																handleViewDocument(
																	creditNote.id,
																)
															}
														>
															<ExternalLink className="h-3.5 w-3.5 mr-2" />
															View Document
														</DropdownMenuItem>
													</DropdownMenuContent>
												</DropdownMenu>
											</div>
										</div>

										{/* Credit Note Details Grid */}
										<div className="grid grid-cols-2 gap-4 mb-3 text-sm">
											<div>
												<p className="text-muted-foreground text-xs">
													Credit Note Date:
												</p>
												<p className="font-medium">
													{formatDate(
														creditNote.credit_note_date,
													)}
												</p>
											</div>
											<div>
												<p className="text-muted-foreground text-xs">
													Customer:
												</p>
												<p className="font-medium">
													{creditNote.customer
														?.nameLine1 ||
														"Unknown Customer"}
												</p>
											</div>
											{creditNote.vat_rate > 0 && (
												<div>
													<p className="text-muted-foreground text-xs">
														VAT Rate:
													</p>
													<p className="font-medium">
														{creditNote.vat_rate}%
													</p>
												</div>
											)}
											{creditNote.orderCreditNotes &&
												creditNote.orderCreditNotes
													.length === 1 && (
													<div>
														<p className="text-muted-foreground text-xs">
															Applied Amount:
														</p>
														<p className="font-medium">
															{formatCurrency(
																creditNote
																	.orderCreditNotes[0]
																	.gross_amount_applied_to_order,
															)}
														</p>
													</div>
												)}
										</div>

										{/* Amount - Prominent Display */}
										<div className="mb-3 p-3 bg-muted/50 rounded-md">
											<p className="text-muted-foreground text-xs">
												Total Credit Amount:
											</p>
											<p className="font-bold text-lg">
												{formatCurrency(
													creditNote.gross_amount,
												)}
											</p>
											{creditNote.net_amount !==
												creditNote.gross_amount && (
												<div className="text-xs text-muted-foreground mt-1">
													Net:{" "}
													{formatCurrency(
														creditNote.net_amount,
													)}{" "}
												</div>
											)}
										</div>

										{creditNote.description && (
											<div className="mb-3 p-3 bg-muted/50 rounded-md">
												<p className="text-muted-foreground text-xs">
													Description:
												</p>
												<p className="text-sm">
													{creditNote.description}
												</p>
											</div>
										)}

										{/* Action buttons */}
										<div className="flex gap-2 mt-3">
											{creditNote.customer_document_url && (
												<Tooltip>
													<TooltipTrigger asChild>
														<Button
															variant="outline"
															size="sm"
															className="h-7 w-7 p-0"
															onClick={() =>
																handleViewDocument(
																	creditNote.id,
																)
															}
														>
															<FileText className="h-3 w-3" />
														</Button>
													</TooltipTrigger>
													<TooltipContent>
														<p>View document</p>
													</TooltipContent>
												</Tooltip>
											)}
										</div>
									</div>
								);
							})}
						</div>
					</ScrollArea>
				)}

				{/* PDF Dialog */}
				<CreditNotePdfDialog
					creditNoteId={viewCreditNoteId}
					isOpen={isPdfDialogOpen}
					onClose={() => {
						setIsPdfDialogOpen(false);
						setViewCreditNoteId(null);
					}}
				/>
			</div>
		</TooltipProvider>
	);
}
