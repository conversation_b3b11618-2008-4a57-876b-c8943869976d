"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { contactConfigurationBaseSchema } from "@repo/api/src/routes/settings/contact-settings/types";
import { useCarrierSettings } from "@saas/organizations/hooks/use-carrier-settings";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InfoIcon, Loader2 } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import type { z } from "zod";

// Form values derived from the base schema, omitting fields managed by the backend
type CarrierSettingsFormValues = Omit<
	z.infer<typeof contactConfigurationBaseSchema>,
	"organizationId"
>;

// Function to generate a sample carrier number based on the current settings
function generateExampleCarrierNumber(
	prefix: string | undefined,
	leadingZeros: number,
): string {
	// Example sequential number with leading zeros
	const seq = "1".padStart(leadingZeros || 4, "0");

	// Prepend prefix if set
	return (prefix || "") + seq;
}

export function CarrierSettingsForm() {
	const { data, isLoading, updateCarrierSettings, isUpdating } =
		useCarrierSettings();

	const form = useForm<CarrierSettingsFormValues>({
		resolver: zodResolver(contactConfigurationBaseSchema),
		defaultValues: {
			prefix: "",
			leadingZeros: 4,
			lastNumber: 0,
		},
		mode: "onBlur",
		reValidateMode: "onChange",
	});

	// Watch form values for preview
	const watchedValues = useWatch({
		control: form.control,
		defaultValue: form.getValues(),
	});

	// Generate example carrier number based on current form values
	const exampleCarrierNumber = useMemo(() => {
		return generateExampleCarrierNumber(
			watchedValues.prefix,
			watchedValues.leadingZeros || 4,
		);
	}, [watchedValues.prefix, watchedValues.leadingZeros]);

	// Update form values when data is loaded
	useEffect(() => {
		if (data) {
			form.reset({
				prefix: data.prefix ?? "",
				leadingZeros: data.leadingZeros,
				lastNumber: data.lastNumber ?? 0,
			});
		}
	}, [data, form]);

	const onSubmit = async (values: CarrierSettingsFormValues) => {
		try {
			await updateCarrierSettings(values);
		} catch (error) {
			console.error("Failed to update carrier settings", error);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full">
				<CardContent className="pt-6">
					<div className="flex items-center justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>Carrier Number Configuration</CardTitle>
				<CardDescription>
					Configure how carrier numbers are generated for your
					organization.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Alert className="mb-6">
					<InfoIcon className="h-4 w-4" />
					<AlertDescription>
						<span className="font-medium">
							Example carrier number:
						</span>{" "}
						{exampleCarrierNumber}
					</AlertDescription>
				</Alert>

				<Form {...form}>
					<form
						id="carrier-settings-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="prefix"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Carrier Prefix</FormLabel>
									<FormControl>
										<Input
											placeholder="6"
											{...field}
											value={field.value || ""}
											disabled={isUpdating}
										/>
									</FormControl>
									<FormDescription>
										Optional prefix for carrier numbers
										(e.g., 6, 7).
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="leadingZeros"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Leading Zeros</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											max={10}
											{...field}
											disabled={isUpdating}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("leadingZeros");
											}}
											className={
												form.formState.errors
													.leadingZeros
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Number of leading zeros in the
										sequential part (e.g., 4 = "0001")
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="lastNumber"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Current Sequence Number
									</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											value={field.value || 0}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("lastNumber");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors.lastNumber
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										The current sequence number. Set this
										when migrating from another system to
										avoid duplicate carrier numbers.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
			</CardContent>
			<CardFooter>
				<Button
					type="submit"
					form="carrier-settings-form"
					disabled={isUpdating}
				>
					{isUpdating && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					Save Configuration
				</Button>
			</CardFooter>
		</Card>
	);
}
