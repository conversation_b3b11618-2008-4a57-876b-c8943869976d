"use client";

import { useOrderView } from "@saas/orders/context/order-view-context";
import type { useOrderById } from "@saas/orders/hooks/use-orders";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { VehicleOrderTypeSelector } from "@ui/components/vehicle-order-type-selector";
import { format } from "date-fns";
import { useTranslations } from "next-intl";
import { DocumentsPanel } from "./documents-panel";

interface DetailsPanelProps {
	order: ReturnType<typeof useOrderById>["data"];
}

export function DetailsPanel({ order }: DetailsPanelProps) {
	const t = useTranslations();
	const { isEditMode, updateField, fieldChanges } = useOrderView();

	// Helper function to get current value from fieldChanges or original data
	const getValue = (field: string): any => {
		if (fieldChanges && field in fieldChanges) {
			const value = fieldChanges[field as keyof typeof fieldChanges];
			return value !== undefined
				? value
				: order?.[field as keyof typeof order];
		}
		return order?.[field as keyof typeof order];
	};

	// Helper function to get string value safely
	const getStringValue = (field: string): string => {
		const value = getValue(field);
		if (typeof value === "string") {
			return value;
		}
		return "";
	};

	// Helper function to get boolean value safely
	const getBooleanValue = (field: string): boolean => {
		const value = getValue(field);
		return !!value;
	};

	// Format date for display
	const formatDate = (date: Date | string | null | undefined) => {
		if (!date) {
			return "";
		}
		return format(new Date(date), "yyyy-MM-dd'T'HH:mm");
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>{t("app.orders.details.title")}</CardTitle>
				<CardDescription>
					{isEditMode
						? t("app.orders.details.editDescription")
						: t("app.orders.details.viewDescription")}
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Basic Information */}
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div className="space-y-2">
						<Label htmlFor="customer_order_number">
							{t("app.orders.fields.customerOrderNumber")}
						</Label>
						{isEditMode ? (
							<Input
								id="customer_order_number"
								value={getStringValue("customer_order_number")}
								onChange={(e) =>
									updateField(
										"customer_order_number",
										e.target.value,
									)
								}
							/>
						) : (
							<p className="text-sm py-2">
								{order?.customer_order_number ||
									t("common.notSet")}
							</p>
						)}
					</div>

					<div className="space-y-2">
						<Label htmlFor="appointment">
							{t("app.orders.fields.appointment")}
						</Label>
						{isEditMode ? (
							<Input
								id="appointment"
								type="datetime-local"
								value={formatDate(order?.appointment)}
								onChange={(e) => {
									const date = e.target.value
										? new Date(e.target.value)
										: null;
									updateField("appointment", date);
								}}
							/>
						) : (
							<p className="text-sm py-2">
								{order?.appointment
									? format(
											new Date(order.appointment),
											"PPP p",
										)
									: t("common.notSet")}
							</p>
						)}
					</div>
				</div>

				{/* Type Information */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<div className="space-y-2">
						<Label htmlFor="order_type">
							{t("app.orders.fields.orderType")}
						</Label>
						{isEditMode ? (
							<Select
								value={getStringValue("order_type")}
								onValueChange={(value) =>
									updateField("order_type", value)
								}
							>
								<SelectTrigger id="order_type">
									<SelectValue
										placeholder={t(
											"common.selectPlaceholder",
										)}
									/>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="full">
										{t("app.orders.types.full")}
									</SelectItem>
									<SelectItem value="partial">
										{t("app.orders.types.partial")}
									</SelectItem>
									<SelectItem value="regie">
										{t("app.orders.types.regie")}
									</SelectItem>
									<SelectItem value="special">
										{t("app.orders.types.special")}
									</SelectItem>
									<SelectItem value="pharmacy">
										{t("app.orders.types.pharmacy")}
									</SelectItem>
									<SelectItem value="container">
										{t("app.orders.types.container")}
									</SelectItem>
								</SelectContent>
							</Select>
						) : (
							<p className="text-sm py-2">
								{order?.order_type || t("common.notSet")}
							</p>
						)}
					</div>

					<div className="space-y-2">
						<Label htmlFor="invoice_type">
							{t("app.orders.fields.invoiceType")}
						</Label>
						{isEditMode ? (
							<Select
								value={getStringValue("invoice_type")}
								onValueChange={(value) =>
									updateField("invoice_type", value)
								}
							>
								<SelectTrigger id="invoice_type">
									<SelectValue
										placeholder={t(
											"common.selectPlaceholder",
										)}
									/>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="invoice">
										{t("app.orders.invoiceTypes.invoice")}
									</SelectItem>
									<SelectItem value="credit">
										{t("app.orders.invoiceTypes.credit")}
									</SelectItem>
									<SelectItem value="cash">
										{t("app.orders.invoiceTypes.cash")}
									</SelectItem>
								</SelectContent>
							</Select>
						) : (
							<p className="text-sm py-2">
								{order?.invoice_type || t("common.notSet")}
							</p>
						)}
					</div>

					<div className="space-y-2">
						<Label htmlFor="vehicleOrderTypeId">
							Vehicle Order Type
						</Label>
						{isEditMode ? (
							<VehicleOrderTypeSelector
								name="vehicleOrderTypeId"
								value={getValue("vehicleOrderTypeId")}
								onChange={(value) =>
									updateField("vehicleOrderTypeId", value)
								}
								placeholder="Select vehicle order type..."
								showCreateOption={true}
							/>
						) : (
							<p className="text-sm py-2">
								{order?.vehicleOrderType?.name ||
									t("common.notSet")}
							</p>
						)}
					</div>
				</div>
				{/* Export/Import Custom Clearance */}
				<div className="border rounded-lg p-4">
					<h3 className="text-base font-medium mb-4">
						{t("app.orders.fields.customClearance")}
					</h3>
					<div className="space-y-4">
						{/* Export Custom Clearance */}
						<div>
							<div className="flex items-center gap-3 mb-2">
								{isEditMode ? (
									<Switch
										id="export_custom_clearance"
										checked={getBooleanValue(
											"export_custom_clearance",
										)}
										onCheckedChange={(checked) =>
											updateField(
												"export_custom_clearance",
												checked,
											)
										}
									/>
								) : (
									<div
										className={`h-4 w-4 rounded-full ${
											order?.export_custom_clearance
												? "bg-green-500"
												: "bg-red-500"
										}`}
									/>
								)}
								<Label
									htmlFor="export_custom_clearance"
									className="cursor-pointer"
								>
									{t(
										"app.orders.fields.exportCustomClearance",
									)}
								</Label>
							</div>
							{(getBooleanValue("export_custom_clearance") ||
								order?.export_custom_clearance) && (
								<div className="pl-7">
									{isEditMode ? (
										<Textarea
											id="export_custom_clearance_text"
											value={getStringValue(
												"export_custom_clearance_text",
											)}
											onChange={(e) =>
												updateField(
													"export_custom_clearance_text",
													e.target.value,
												)
											}
											rows={2}
											placeholder={t(
												"app.orders.placeholders.exportCustomClearanceText",
											)}
										/>
									) : (
										<p className="text-sm py-2 whitespace-pre-line">
											{order?.export_custom_clearance_text ||
												t("common.notSet")}
										</p>
									)}
								</div>
							)}
						</div>

						{/* Import Custom Clearance */}
						<div>
							<div className="flex items-center gap-3 mb-2">
								{isEditMode ? (
									<Switch
										id="import_custom_clearance"
										checked={getBooleanValue(
											"import_custom_clearance",
										)}
										onCheckedChange={(checked) =>
											updateField(
												"import_custom_clearance",
												checked,
											)
										}
									/>
								) : (
									<div
										className={`h-4 w-4 rounded-full ${
											order?.import_custom_clearance
												? "bg-green-500"
												: "bg-red-500"
										}`}
									/>
								)}
								<Label
									htmlFor="import_custom_clearance"
									className="cursor-pointer"
								>
									{t(
										"app.orders.fields.importCustomClearance",
									)}
								</Label>
							</div>
							{(getBooleanValue("import_custom_clearance") ||
								order?.import_custom_clearance) && (
								<div className="pl-7">
									{isEditMode ? (
										<Textarea
											id="import_custom_clearance_text"
											value={getStringValue(
												"import_custom_clearance_text",
											)}
											onChange={(e) =>
												updateField(
													"import_custom_clearance_text",
													e.target.value,
												)
											}
											rows={2}
											placeholder={t(
												"app.orders.placeholders.importCustomClearanceText",
											)}
										/>
									) : (
										<p className="text-sm py-2 whitespace-pre-line">
											{order?.import_custom_clearance_text ||
												t("common.notSet")}
										</p>
									)}
								</div>
							)}
						</div>
					</div>
				</div>

				{/* Loading Equipment */}
				<div className="space-y-2">
					<div className="flex items-center gap-3">
						{isEditMode ? (
							<Switch
								id="loading_equipment"
								checked={getBooleanValue("loading_equipment")}
								onCheckedChange={(checked) =>
									updateField("loading_equipment", checked)
								}
							/>
						) : (
							<div
								className={`h-4 w-4 rounded-full ${
									order?.loading_equipment
										? "bg-green-500"
										: "bg-red-500"
								}`}
							/>
						)}
						<Label
							htmlFor="loading_equipment"
							className="cursor-pointer"
						>
							{t("app.orders.fields.loadingEquipment")}
						</Label>
					</div>
				</div>

				{/* Comments */}
				<div className="space-y-6">
					<div className="space-y-2">
						<Label htmlFor="internal_comment">
							{t("app.orders.fields.internalComment")}
						</Label>
						{isEditMode ? (
							<Textarea
								id="internal_comment"
								value={getStringValue("internal_comment")}
								onChange={(e) =>
									updateField(
										"internal_comment",
										e.target.value,
									)
								}
								rows={4}
							/>
						) : (
							<p className="text-sm py-2 whitespace-pre-line">
								{order?.internal_comment || t("common.notSet")}
							</p>
						)}
					</div>

					<div className="space-y-2">
						<Label htmlFor="invoice_note">
							{t("app.orders.fields.invoiceNote")}
						</Label>
						{isEditMode ? (
							<Textarea
								id="invoice_note"
								value={getStringValue("invoice_note")}
								onChange={(e) =>
									updateField("invoice_note", e.target.value)
								}
								rows={3}
							/>
						) : (
							<p className="text-sm py-2 whitespace-pre-line">
								{order?.invoice_note || t("common.notSet")}
							</p>
						)}
					</div>
				</div>

				{/* Documents Section */}
				{!isEditMode && order?.id && (
					<div className="border-t pt-6 mt-6">
						<DocumentsPanel orderId={order.id} />
					</div>
				)}
			</CardContent>
		</Card>
	);
}
