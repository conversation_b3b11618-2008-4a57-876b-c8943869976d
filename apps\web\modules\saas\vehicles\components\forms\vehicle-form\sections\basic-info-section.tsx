import { EngineType, VehicleType } from "@repo/api/src/routes/vehicles/types";
import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { FormVehicleSelector } from "@saas/shared/components/VehicleSelector";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { useTranslations } from "next-intl";
import { useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";

export function BasicInfoSection() {
	const { control, watch } = useFormContext();
	const t = useTranslations("app.vehicles");
	const firstInputRef = useRef<HTMLInputElement>(null);
	const vehicleType = watch("vehicleType");

	// Focus the first input when the section mounts
	useEffect(() => {
		firstInputRef.current?.focus();
	}, []);

	return (
		<Card>
			<CardHeader>
				<CardTitle>Basic Information</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				{/* Hidden Organization ID field - populated automatically by the form context */}
				<FormField
					control={control}
					name="organizationId"
					render={({ field }) => <input type="hidden" {...field} />}
				/>

				{/* Vehicle Type */}
				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
					<FormField
						control={control}
						name="vehicleType"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Vehicle Type *</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Select vehicle type" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value={VehicleType.TRUCK}>
											Truck
										</SelectItem>
										<SelectItem value={VehicleType.TRAILER}>
											Trailer
										</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>

					{vehicleType === VehicleType.TRUCK && (
						<FormField
							control={control}
							name="engineType"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Engine Type</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select engine type" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem
												value={EngineType.DIESEL}
											>
												Diesel
											</SelectItem>
											<SelectItem
												value={EngineType.GASOLINE}
											>
												Gasoline
											</SelectItem>
											<SelectItem
												value={EngineType.ELECTRIC}
											>
												Electric
											</SelectItem>
											<SelectItem
												value={EngineType.HYBRID}
											>
												Hybrid
											</SelectItem>
											<SelectItem
												value={EngineType.HYDROGEN}
											>
												Hydrogen
											</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					)}

					{vehicleType === VehicleType.TRAILER && (
						<FormField
							control={control}
							name="trailerType"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Trailer Type</FormLabel>
									<FormControl>
										<Input
											{...field}
											value={field.value || ""}
											placeholder="Enter trailer type"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					)}
				</div>

				{/* Owner Selector */}
				<div className="grid grid-cols-1 gap-4">
					<FormField
						control={control}
						name="ownerId"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Owner</FormLabel>
								<FormControl>
									<CounterpartySelector
										name="ownerId"
										placeholder="Select owner"
										value={field.value || ""}
										onChange={(value: string | null) =>
											field.onChange(value)
										}
										type="carrier"
										allowClear
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
					{/* License Plate */}
					<FormField
						control={control}
						name="licensePlate"
						render={({ field }) => (
							<FormItem>
								<FormLabel>License Plate *</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter license plate"
										ref={firstInputRef}
										required
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Manufacturer */}
					<FormField
						control={control}
						name="manufacturer"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Manufacturer *</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter manufacturer"
										required
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Model */}
					<FormField
						control={control}
						name="model"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Model *</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter vehicle model"
										required
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Chassis Number */}
					<FormField
						control={control}
						name="chassisNumber"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Chassis Number</FormLabel>
								<FormControl>
									<Input
										{...field}
										value={field.value || ""}
										placeholder="Enter chassis number"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Euro Class - Only for trucks */}
					{vehicleType === VehicleType.TRUCK && (
						<FormField
							control={control}
							name="euroClass"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Euro Class</FormLabel>
									<FormControl>
										<Input
											{...field}
											value={field.value || ""}
											placeholder="Enter euro class"
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					)}
				</div>

				{/* Attached To Truck - Only for trailers */}
				{vehicleType === VehicleType.TRAILER && (
					<div className="grid grid-cols-1 gap-4">
						<FormVehicleSelector
							name="attachedToId"
							label="Attached to Truck"
							placeholder="Select a truck to attach this trailer to"
							type="TRUCK"
							allowClear={true}
						/>
					</div>
				)}

				{/* Physical Dimensions & Capacity */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">
						Physical Dimensions & Capacity
					</h3>

					{/* Dimensions Row */}
					<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
						<FormField
							control={control}
							name="length"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Length (meters)</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="number"
											step="0.01"
											min="0"
											value={field.value || ""}
											placeholder="0.00"
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(
													value === ""
														? undefined
														: Number.parseFloat(
																value,
															),
												);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name="width"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Width (meters)</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="number"
											step="0.01"
											min="0"
											value={field.value || ""}
											placeholder="0.00"
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(
													value === ""
														? undefined
														: Number.parseFloat(
																value,
															),
												);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name="height"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Height (meters)</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="number"
											step="0.01"
											min="0"
											value={field.value || ""}
											placeholder="0.00"
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(
													value === ""
														? undefined
														: Number.parseFloat(
																value,
															),
												);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Capacity Row */}
					<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
						<FormField
							control={control}
							name="loadingWeight"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Loading Weight (kg)</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="number"
											step="0.01"
											min="0"
											value={field.value || ""}
											placeholder="0.00"
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(
													value === ""
														? undefined
														: Number.parseFloat(
																value,
															),
												);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={control}
							name="euroPalletsAmount"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Euro Pallets Capacity</FormLabel>
									<FormControl>
										<Input
											{...field}
											type="number"
											min="0"
											step="1"
											value={field.value || ""}
											placeholder="0"
											onChange={(e) => {
												const value = e.target.value;
												field.onChange(
													value === ""
														? undefined
														: Number.parseInt(
																value,
																10,
															),
												);
											}}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				{/* Active in Dispatch */}
				<div className="grid grid-cols-1 gap-4">
					<FormField
						control={control}
						name="isActiveInDispatch"
						render={({ field }) => (
							<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
								<div className="space-y-0.5">
									<FormLabel className="text-base">
										Active in Dispatch *
									</FormLabel>
									<FormMessage />
								</div>
								<FormControl>
									<Switch
										checked={field.value}
										onCheckedChange={field.onChange}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</div>

				{/* Notes */}
				<div className="grid grid-cols-1 gap-4">
					<FormField
						control={control}
						name="notes"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Notes</FormLabel>
								<FormControl>
									<Textarea
										{...field}
										value={field.value || ""}
										placeholder="Enter additional notes"
										className="min-h-[100px]"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</CardContent>
		</Card>
	);
}
