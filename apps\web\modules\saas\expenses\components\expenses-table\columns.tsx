"use client";

// Type inference from API client
type ExpenseResponse = Awaited<
	ReturnType<typeof import("@saas/expenses/lib/api").fetchExpenses>
>["items"][0];

import { useExpensesUI } from "@saas/expenses/context/expenses-ui-context";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { format } from "date-fns";
import {
	AlertTriangle,
	Calendar,
	CheckCircle2,
	Clock,
	CreditCard,
	Download,
	FileText,
	MoreHorizontal,
	PenLine,
	Tag,
	Trash,
	User,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

type ActionsCellProps = {
	row: Row<ExpenseResponse>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const expense = row.original;
	const { handleDeleteExpense, handleEditExpense } = useExpensesUI();

	const handleDownloadDocument = () => {
		if (expense.supplier_document_url) {
			// Open the signed URL in a new tab for download
			window.open(expense.supplier_document_url, "_blank");
		}
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>Actions</DropdownMenuLabel>
				<DropdownMenuItem onClick={() => handleEditExpense(expense)}>
					<PenLine className="mr-2 h-4 w-4" />
					Edit
				</DropdownMenuItem>

				{expense.supplier_document_url && (
					<DropdownMenuItem onClick={handleDownloadDocument}>
						<Download className="mr-2 h-4 w-4" />
						Download Document
					</DropdownMenuItem>
				)}

				<DropdownMenuSeparator />

				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteExpense(expense)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					Delete
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<ExpenseResponse>[] {
	const { handleDeleteExpense, handleEditExpense } = useExpensesUI();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "expense_date",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Date & Invoice" />
			),
			cell: ({ row }) => {
				const expenseDate = row.getValue("expense_date") as string;
				const invoiceNumber = row.original.supplier_invoice_number;
				return (
					<div className="flex flex-col gap-1">
						<div className="flex items-center gap-2">
							<Calendar className="h-4 w-4 text-muted-foreground" />
							<span className="font-medium">
								{expenseDate
									? format(new Date(expenseDate), "PPP")
									: "-"}
							</span>
						</div>
						{invoiceNumber && (
							<div className="flex items-center gap-2 text-sm text-muted-foreground">
								<FileText className="h-3 w-3" />
								<span className="truncate max-w-[120px]">
									{invoiceNumber}
								</span>
							</div>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "lineItems",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Category" />
			),
			cell: ({ row }) => {
				const lineItems = row.original.lineItems || [];

				// Get unique categories from line items
				const categories = lineItems.reduce((acc, item) => {
					if (
						item.category?.name &&
						!acc.includes(item.category.name)
					) {
						acc.push(item.category.name);
					}
					return acc;
				}, [] as string[]);

				return (
					<div className="flex items-center gap-2">
						<Tag className="h-4 w-4 text-muted-foreground" />
						<span>
							{categories.length > 0
								? categories.length === 1
									? categories[0]
									: `${categories[0]} +${categories.length - 1}`
								: "Uncategorized"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "totalAmount",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Amount" />
			),
			cell: ({ row }) => {
				const amount = row.getValue("totalAmount") as number;
				const currency = row.original.currency || "EUR";

				return (
					<div className="flex items-center gap-2">
						<CreditCard className="h-4 w-4 text-muted-foreground" />
						<span className="font-medium">
							{new Intl.NumberFormat("de-DE", {
								style: "currency",
								currency: currency,
							}).format(amount)}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "supplier",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Supplier" />
			),
			cell: ({ row }) => {
				const supplier = row.original.supplier;
				const supplierName = supplier ? supplier.nameLine1 : "-";

				return (
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-muted-foreground" />
						{supplier?.id ? (
							<Link
								href={`/app/${params.organizationSlug}/contacts/${supplier.id}`}
								className="hover:underline cursor-pointer"
							>
								<span className="truncate max-w-[120px]">
									{supplierName}
								</span>
							</Link>
						) : (
							<span className="truncate max-w-[120px]">
								{supplierName}
							</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "allocatedAmount",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Allocated" />
			),
			cell: ({ row }) => {
				const allocatedAmount = row.original.allocatedAmount || 0;
				const totalAmount = row.original.totalAmount || 0;
				const currency = row.original.currency || "EUR";
				const isFullyAllocated = row.original.isFullyAllocated || false;

				const allocationPercentage =
					totalAmount > 0 ? (allocatedAmount / totalAmount) * 100 : 0;

				return (
					<div className="flex flex-col gap-1">
						<div className="flex items-center gap-2">
							{isFullyAllocated ? (
								<CheckCircle2 className="h-4 w-4 text-green-600" />
							) : allocatedAmount > 0 ? (
								<AlertTriangle className="h-4 w-4 text-yellow-600" />
							) : (
								<Clock className="h-4 w-4 text-muted-foreground" />
							)}
							<span className="font-medium">
								{new Intl.NumberFormat("de-DE", {
									style: "currency",
									currency: currency,
								}).format(allocatedAmount)}
							</span>
						</div>
						{!isFullyAllocated && (
							<div className="text-xs text-muted-foreground">
								{allocationPercentage.toFixed(0)}% allocated
							</div>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "status",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Status" />
			),
			cell: ({ row }) => {
				const status = row.getValue("status") as string;

				const getStatusBadge = (status: string) => {
					switch (status) {
						case "paid":
							return <Badge status="success">Paid</Badge>;
						case "overdue":
							return <Badge status="error">Overdue</Badge>;
						default:
							return <Badge status="info">Open</Badge>;
					}
				};

				return getStatusBadge(status);
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Created" />
			),
			cell: ({ row }) => {
				const date = row.getValue("createdAt") as string;
				return (
					<div className="text-sm">
						{format(new Date(date), "PP")}
					</div>
				);
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
		},
	];
}
