"use client";
import { NovuProvider } from "@novu/react";
import type { ReactNode } from "react";
import type { NotificationConfig } from "./types";

interface NotificationProviderProps extends NotificationConfig {
	children: ReactNode;
}

export function NotificationProvider({
	children,
	subscriberId,
	applicationIdentifier,
	socketUrl,
	apiUrl,
	subscriberHash,
}: NotificationProviderProps) {
	return (
		<NovuProvider
			subscriberId={subscriberId}
			applicationIdentifier={applicationIdentifier}
			socketUrl={socketUrl}
			apiUrl={apiUrl}
			subscriberHash={subscriberHash}
		>
			{children}
		</NovuProvider>
	);
}
