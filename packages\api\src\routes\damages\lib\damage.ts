import type { Prisma } from "@prisma/client";
import { db } from "@repo/database";
import { HTTPException } from "hono/http-exception";
import type { CreateDamageInput, UpdateDamageInput } from "../types";

interface PaginationParams {
	page?: number;
	limit?: number;
}

/**
 * List damage events for a specific order with pagination
 */
export async function listDamages(
	orderId: string,
	organizationId: string,
	{ page = 1, limit = 10 }: PaginationParams = {},
) {
	try {
		const whereClause: Prisma.OrderDamageEventWhereInput = {
			orderId,
			order: {
				organizationId,
			},
		};

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		const [total, items] = await Promise.all([
			db.orderDamageEvent.count({
				where: whereClause,
			}),
			db.orderDamageEvent.findMany({
				where: whereClause,
				skip,
				take: limit,
				orderBy: {
					createdAt: "desc", // Sort by creation date descending (newest first)
				},
				include: {
					order: {
						select: {
							id: true,
							customer_order_number: true,
							order_number: true,
							customer: {
								select: {
									id: true,
									nameLine1: true,
								},
							},
						},
					},
				},
			}),
		]);

		// Calculate total pages
		const totalPages = Math.ceil(total / limit);

		return {
			items,
			total,
			page,
			totalPages,
		};
	} catch (error) {
		console.error("Failed to list damage events:", error);
		throw new HTTPException(500, {
			message: "Failed to list damage events",
		});
	}
}

/**
 * List all damage events across all orders with pagination and filtering
 */
export async function listAllDamages(
	organizationId: string,
	{ page = 1, limit = 10 }: PaginationParams = {},
) {
	try {
		const whereClause: Prisma.OrderDamageEventWhereInput = {
			order: {
				organizationId,
			},
		};

		// Calculate skip value for pagination
		const skip = (page - 1) * limit;

		const [total, items] = await Promise.all([
			db.orderDamageEvent.count({
				where: whereClause,
			}),
			db.orderDamageEvent.findMany({
				where: whereClause,
				skip,
				take: limit,
				orderBy: {
					createdAt: "desc", // Sort by creation date descending (newest first)
				},
				include: {
					order: {
						select: {
							id: true,
							customer_order_number: true,
							order_number: true,
							customer: {
								select: {
									id: true,
									nameLine1: true,
								},
							},
						},
					},
				},
			}),
		]);

		// Calculate total pages
		const totalPages = Math.ceil(total / limit);

		return {
			items,
			total,
			page,
			totalPages,
		};
	} catch (error) {
		console.error("Failed to list all damage events:", error);
		throw new HTTPException(500, {
			message: "Failed to list all damage events",
		});
	}
}

/**
 * Get damage event by ID with organization security check
 */
export async function getDamageById(damageId: string, organizationId: string) {
	try {
		const damage = await db.orderDamageEvent.findFirst({
			where: {
				id: damageId,
				order: {
					organizationId,
				},
			},
			include: {
				order: {
					select: {
						id: true,
						customer_order_number: true,
						order_number: true,
						customer: {
							select: {
								id: true,
								nameLine1: true,
							},
						},
					},
				},
			},
		});

		if (!damage) {
			throw new HTTPException(404, {
				message:
					"Damage event not found or does not belong to this organization",
			});
		}

		return damage;
	} catch (error) {
		console.error("Failed to fetch damage event:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch damage event",
		});
	}
}

/**
 * Create a new damage event with organization validation
 */
export async function createDamage(data: CreateDamageInput) {
	try {
		const { organizationId, ...createData } = data;

		// Verify the referenced order belongs to the organization
		const order = await db.order.findFirst({
			where: {
				id: createData.orderId,
				organizationId,
			},
		});

		if (!order) {
			throw new HTTPException(404, {
				message:
					"Order not found or does not belong to this organization",
			});
		}

		const damage = await db.orderDamageEvent.create({
			data: createData,
			include: {
				order: {
					select: {
						id: true,
						customer_order_number: true,
						order_number: true,
					},
				},
			},
		});

		return damage;
	} catch (error) {
		console.error("Failed to create damage event:", error);
		throw new HTTPException(500, {
			message: "Failed to create damage event",
		});
	}
}

/**
 * Update an existing damage event with organization validation
 */
export async function updateDamage(data: UpdateDamageInput) {
	try {
		const { id, organizationId, ...updateData } = data;

		// Check if damage event exists and belongs to the organization
		const damage = await db.orderDamageEvent.findFirst({
			where: {
				id,
				order: {
					organizationId,
				},
			},
		});

		if (!damage) {
			throw new HTTPException(404, {
				message:
					"Damage event not found or does not belong to this organization",
			});
		}

		// Update the damage event
		const updated = await db.orderDamageEvent.update({
			where: { id },
			data: updateData,
			include: {
				order: {
					select: {
						id: true,
						customer_order_number: true,
						order_number: true,
					},
				},
			},
		});

		return updated;
	} catch (error) {
		console.error("Failed to update damage event:", error);
		throw new HTTPException(500, {
			message: "Failed to update damage event",
		});
	}
}

/**
 * Delete a damage event with organization validation
 */
export async function deleteDamage(id: string, organizationId: string) {
	try {
		// Check if damage event exists and belongs to the organization
		const damage = await db.orderDamageEvent.findFirst({
			where: {
				id,
				order: {
					organizationId,
				},
			},
		});

		if (!damage) {
			throw new HTTPException(404, {
				message:
					"Damage event not found or does not belong to this organization",
			});
		}

		// Delete the damage event
		await db.orderDamageEvent.delete({
			where: { id },
		});

		return { success: true };
	} catch (error) {
		console.error("Failed to delete damage event:", error);
		throw new HTTPException(500, {
			message: "Failed to delete damage event",
		});
	}
}
