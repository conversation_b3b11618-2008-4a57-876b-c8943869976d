"use client";

import type { Table } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import { Calendar } from "@ui/components/calendar";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { CalendarIcon, X } from "lucide-react";
import { type RefObject, memo, useState } from "react";

interface DataTableToolbarProps<TData> {
	table: Table<TData>;
	filterColumn?: string;
	filterOptions?: {
		value: string;
		label: string;
	}[];
	onSearch?: (value: string) => void;
	searchValue?: string;
	searchPlaceholder?: string;
	searchRef?: RefObject<HTMLInputElement | null>;
	onSearchFocus?: () => void;
	onSearchBlur?: () => void;
	// Date range filter props
	dateRange?: { from?: Date; to?: Date };
	onDateRangeChange?: (
		dateRange: { from?: Date; to?: Date } | undefined,
	) => void;
	dateRangeLabel?: string;
}

export const DataTableToolbar = memo(function DataTableToolbar<TData>({
	table,
	filterColumn,
	filterOptions,
	onSearch,
	searchValue,
	searchPlaceholder,
	searchRef,
	onSearchFocus,
	onSearchBlur,
	dateRange,
	onDateRangeChange,
	dateRangeLabel = "Filter by date",
}: DataTableToolbarProps<TData>) {
	const isFiltered = table.getState().columnFilters.length > 0;
	const [isCalendarOpen, setIsCalendarOpen] = useState(false);

	// Check if the date range filter is active
	const hasDateFilter = dateRange?.from || dateRange?.to;

	return (
		<div className="flex items-center justify-between">
			<div className="flex flex-1 items-center space-x-2">
				<Input
					ref={searchRef}
					placeholder={searchPlaceholder ?? "Search..."}
					value={searchValue ?? ""}
					onChange={(event) => onSearch?.(event.target.value)}
					className="h-8 w-[300px] lg:w-[400px]"
					onFocus={onSearchFocus}
					onBlur={onSearchBlur}
					onKeyDown={(e) => {
						if (e.key === "Escape") {
							e.currentTarget.blur();
						}
					}}
				/>
				{filterColumn && filterOptions && (
					<Select
						value={
							(table
								.getColumn(filterColumn)
								?.getFilterValue() as string) ?? "all"
						}
						onValueChange={(value) =>
							table.getColumn(filterColumn)?.setFilterValue(value)
						}
					>
						<SelectTrigger className="h-8 w-[150px]">
							<SelectValue placeholder="Filter by city" />
						</SelectTrigger>
						<SelectContent>
							{filterOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
								>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				)}

				{/* Date Range Picker */}
				{onDateRangeChange && (
					<div className="flex items-center">
						<Popover
							open={isCalendarOpen}
							onOpenChange={setIsCalendarOpen}
						>
							<PopoverTrigger asChild>
								<Button
									variant={
										hasDateFilter ? "primary" : "outline"
									}
									size="sm"
									className={cn(
										"h-8 border-dashed border-cyan-500 text-foreground",
										hasDateFilter &&
											"bg-primary text-primary-foreground",
									)}
								>
									<CalendarIcon className="mr-2 h-4 w-4" />
									{dateRange?.from ? (
										dateRange.to ? (
											<>
												{format(
													dateRange.from,
													"LLL dd, y",
												)}{" "}
												-{" "}
												{format(
													dateRange.to,
													"LLL dd, y",
												)}
											</>
										) : (
											format(dateRange.from, "LLL dd, y")
										)
									) : (
										dateRangeLabel
									)}
								</Button>
							</PopoverTrigger>
							<PopoverContent
								className="w-auto p-0"
								align="start"
							>
								<Calendar
									mode="range"
									selected={{
										from: dateRange?.from,
										to: dateRange?.to,
									}}
									onSelect={(range) => {
										onDateRangeChange(range);
										// Only close when a complete range is selected
										if (range?.from && range?.to) {
											setIsCalendarOpen(false);
										}
									}}
									initialFocus
								/>
								<div className="flex items-center justify-between p-3 border-t border-border">
									<Button
										variant="outline"
										className="text-xs h-7"
										onClick={() => {
											onDateRangeChange(undefined);
											setIsCalendarOpen(false);
										}}
									>
										Clear
									</Button>
									<Button
										className="text-xs h-7"
										onClick={() => setIsCalendarOpen(false)}
									>
										Apply
									</Button>
								</div>
							</PopoverContent>
						</Popover>

						{hasDateFilter && (
							<Button
								variant="ghost"
								size="sm"
								className="h-8 px-2"
								onClick={() => onDateRangeChange(undefined)}
							>
								<X className="h-4 w-4" />
							</Button>
						)}
					</div>
				)}

				{isFiltered && (
					<Button
						variant="ghost"
						onClick={() => {
							// Reset to "all" for department filters
							if (filterColumn) {
								table
									.getColumn(filterColumn)
									?.setFilterValue("all");
							} else {
								table.resetColumnFilters();
							}
						}}
						className="h-8 px-2 lg:px-3"
					>
						Reset
						<X className="ml-2 h-4 w-4" />
					</Button>
				)}
			</div>
		</div>
	);
}) as <T>(props: DataTableToolbarProps<T>) => JSX.Element;
