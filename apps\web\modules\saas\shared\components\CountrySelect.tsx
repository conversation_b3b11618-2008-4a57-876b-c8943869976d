import React from "react";

// data
import { countries as AllCountries } from "country-data-list";

// shadcn
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";

// types
export interface Country {
	alpha2: string;
	alpha3: string;
	name: string;
	status?: string;
	currencies?: string[];
	countryCallingCodes?: string[];
	ioc?: string;
	languages?: string[];
	emoji?: string;
}

import type { SelectProps } from "@radix-ui/react-select";
import { cn } from "@ui/lib";

// Optional - import flags if needed
import flags from "react-phone-number-input/flags";

// Extract valid country codes for validation and ensure uniqueness
export const VALID_COUNTRY_CODES = Array.from(
	new Set(
		AllCountries.all
			.filter((country) => country.alpha2 && country.name)
			.map((country) => country.alpha2),
	),
);

// Validation function to check if a country code is valid
export const isValidCountryCode = (code: string): boolean => {
	if (!code) {
		return false;
	}

	// Standardize to uppercase for comparison
	const normalizedCode = code.toUpperCase();
	const normalizedValidCodes = VALID_COUNTRY_CODES.map((c) =>
		c.toUpperCase(),
	);
	const isValid = normalizedValidCodes.includes(normalizedCode);
	return isValid;
};

interface CountrySelectProps extends Omit<SelectProps, "onValueChange"> {
	onValueChange?: (value: string) => void;
	onCountrySelect?: (country: Country) => void;
	name: string;
	placeholder?: string;
	variant?: "default" | "small";
	displayValue?: "alpha2" | "name" | "both";
	valid?: boolean;
	className?: string;
}

const CountrySelect = React.forwardRef<HTMLButtonElement, CountrySelectProps>(
	(
		{
			value,
			onValueChange,
			onCountrySelect,
			name,
			placeholder = "Select country",
			variant = "default",
			displayValue = "both",
			valid = true,
			className,
			...props
		},
		ref,
	) => {
		const [selectedCountry, setSelectedCountry] =
			React.useState<Country | null>(null);

		const countries = React.useMemo<Country[]>(() => {
			// Filter out countries with no alpha2 code or name
			const filteredCountries = AllCountries.all.filter(
				(country) => country.alpha2 && country.name,
			);

			// Create a map to ensure uniqueness by alpha2 code
			const uniqueCountries = new Map<string, Country>();
			filteredCountries.forEach((country) => {
				if (!uniqueCountries.has(country.alpha2)) {
					uniqueCountries.set(country.alpha2, country);
				}
			});

			// Convert to array and sort by country name
			return Array.from(uniqueCountries.values()).sort((a, b) =>
				a.name.localeCompare(b.name),
			);
		}, []);

		// Validate the initial value if one is provided
		React.useEffect(() => {
			if (value) {
				const isValid = isValidCountryCode(value);
				if (!isValid) {
				}
			}
		}, [value, onValueChange]);

		const handleValueChange = (newValue: string | undefined) => {
			// Allow undefined
			if (newValue === undefined || newValue === "") {
				if (onValueChange) {
					onValueChange(undefined as any); // Explicitly pass undefined
				}
				if (onCountrySelect) {
					onCountrySelect(undefined as any);
				}
				setSelectedCountry(null);
				return;
			}

			const isValid = isValidCountryCode(newValue);
			// Case-insensitive validation to ensure only valid country codes are accepted
			if (!isValid) {
				return;
			}

			// Standardize to the correct case
			const standardizedValue =
				VALID_COUNTRY_CODES.find(
					(code) => code.toUpperCase() === newValue.toUpperCase(),
				) || newValue;

			const fullCountryData = countries.find(
				(country) =>
					country.alpha2.toUpperCase() ===
					standardizedValue.toUpperCase(),
			);

			if (fullCountryData) {
				setSelectedCountry(fullCountryData);
				if (onValueChange) {
					onValueChange(fullCountryData.alpha2); // Use the correct case from the data
				}
				if (onCountrySelect) {
					onCountrySelect(fullCountryData);
				}
			}
		};

		// Format the selected value for display in the trigger
		const formatSelectedValue = () => {
			if (!value) {
				return placeholder;
			}

			const country = countries.find((c) => c.alpha2 === value);
			if (!country) {
				return value;
			}

			return (
				<div className="flex items-center gap-2">
					{renderFlag(country.alpha2)}
					<span>{country.name}</span>
				</div>
			);
		};

		// Optional - render flag function if needed
		const renderFlag = (countryCode: string) => {
			const Flag = flags[countryCode.toUpperCase() as keyof typeof flags];
			if (!Flag) {
				return null;
			}

			return (
				<span className="flex h-4 w-6 overflow-hidden rounded-sm bg-foreground/20 mr-1 [&_svg]:size-full">
					<Flag title={countryCode} />
				</span>
			);
		};

		return (
			<Select
				value={value}
				onValueChange={handleValueChange}
				{...props}
				name={name}
				data-valid={valid}
			>
				<SelectTrigger
					className={cn(
						"w-full",
						variant === "small" && "w-fit gap-2",
						className,
					)}
					data-valid={valid}
					ref={ref}
				>
					<SelectValue placeholder={placeholder}>
						{value && formatSelectedValue()}
					</SelectValue>
				</SelectTrigger>
				<SelectContent className="max-h-[300px] overflow-y-auto">
					<SelectGroup>
						{countries.map((country) => (
							<SelectItem
								key={country.alpha2}
								value={country.alpha2}
							>
								<div className="flex items-center gap-2">
									{renderFlag(country.alpha2)}
									<span>{country.name}</span>
								</div>
							</SelectItem>
						))}
					</SelectGroup>
				</SelectContent>
			</Select>
		);
	},
);

CountrySelect.displayName = "CountrySelect";

export { CountrySelect };
