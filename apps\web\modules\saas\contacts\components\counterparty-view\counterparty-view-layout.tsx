"use client";

import { useCounterpartyView } from "@saas/contacts/context/counterparty-view-context";
import { Card, CardContent } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { useTranslations } from "next-intl";
import { useCallback, useMemo } from "react";

import { AddressesPanel } from "@saas/contacts/components/counterparty-view/related-entities/addresses-panel";
import { ContactList } from "./contact-list";
import { CounterpartyViewHeader } from "./counterparty-view-header";
import { DetailsPanel } from "./details-panel";
import { QuickInfoPanel } from "./quick-info-panel";
import { ActivityPanel } from "./related-entities/activity-panel";
import { InvoicesPanel } from "./related-entities/invoices-panel";
import { OffersPanel } from "./related-entities/offers-panel";
import { OrdersPanel } from "./related-entities/orders-panel";
import { WarningList } from "./warning-list";

export function CounterpartyViewLayout() {
	const { activeTab, setActiveTab, isLoading, counterparty } =
		useCounterpartyView();
	const t = useTranslations();

	// Handle contact actions
	const handleEditContact = useCallback(
		(contactId: string) => {
			// Switch to the persons tab and pass the contactId to edit
			setActiveTab("persons");
			// You could add more logic here to scroll to the contact or open an edit modal
		},
		[setActiveTab],
	);

	const handleCreateContact = useCallback(() => {
		// Switch to the persons tab for contact creation
		setActiveTab("persons");
		// You could add more logic here to open a create modal
	}, [setActiveTab]);

	const handleContactDeleted = useCallback(() => {
		// Refresh the counterparty data after a contact is deleted
		// For now, we'll handle this in the parent component if needed
	}, []);

	// Handle warning actions simplistically to avoid hook order changes
	const handleEditWarning = useCallback(
		(warningId: string) => {
			// Switch to the details tab where warnings are displayed
			setActiveTab("details");
		},
		[setActiveTab],
	);

	// Keep this as just a simple tab navigation to avoid hook order changes
	const handleCreateWarning = useCallback(() => {
		setActiveTab("details");
	}, [setActiveTab]);

	// Create common props for each panel to avoid duplicate code
	const addressesPanelProps = useMemo(
		() => (counterparty ? { counterparty } : undefined),
		[counterparty],
	);

	const idOnlyPanelProps = useMemo(
		() => (counterparty ? { counterpartyId: counterparty.id } : undefined),
		[counterparty],
	);

	// Always render the same component structure, but use null for content when loading
	return (
		<div className="">
			{counterparty ? (
				<CounterpartyViewHeader counterparty={counterparty} />
			) : (
				<CounterpartyViewSkeleton />
			)}

			{!isLoading && counterparty && (
				<Tabs
					value={activeTab}
					onValueChange={setActiveTab}
					className="w-full"
				>
					<TabsList className="grid grid-cols-6 mb-6">
						<TabsTrigger value="details">Details</TabsTrigger>
						<TabsTrigger value="addresses">Addresses</TabsTrigger>
						<TabsTrigger value="invoices">Invoices</TabsTrigger>
						<TabsTrigger value="offers">Offers</TabsTrigger>
						<TabsTrigger value="orders">Orders</TabsTrigger>
						<TabsTrigger value="activity">Activity</TabsTrigger>
					</TabsList>

					<div className="flex gap-6">
						{/* Left sidebar */}
						<div className="w-1/4 space-y-6">
							<Card>
								<CardContent className="p-4">
									<QuickInfoPanel
										counterparty={counterparty}
									/>
								</CardContent>
							</Card>

							{/* Contacts in separate card */}
							<Card>
								<CardContent className="p-4">
									<ContactList
										counterpartyId={counterparty.id}
										counterparty={counterparty}
										onEditContact={handleEditContact}
										onCreateContact={handleCreateContact}
									/>
								</CardContent>
							</Card>

							{/* Warnings in separate card */}
							<Card>
								<CardContent className="p-4">
									<WarningList
										counterpartyId={counterparty.id}
										onEditWarning={handleEditWarning}
										onCreateWarning={handleCreateWarning}
									/>
								</CardContent>
							</Card>
						</div>

						{/* Main content area */}
						<div className="flex-1">
							<TabsContent value="details" className="mt-0">
								<DetailsPanel counterparty={counterparty} />
							</TabsContent>
							<TabsContent value="addresses" className="mt-0">
								<AddressesPanel counterparty={counterparty} />
							</TabsContent>

							<TabsContent value="invoices" className="mt-0">
								<InvoicesPanel
									counterpartyId={counterparty.id}
								/>
							</TabsContent>

							<TabsContent value="offers" className="mt-0">
								<OffersPanel counterpartyId={counterparty.id} />
							</TabsContent>

							<TabsContent value="orders" className="mt-0">
								<OrdersPanel counterpartyId={counterparty.id} />
							</TabsContent>

							<TabsContent value="activity" className="mt-0">
								<ActivityPanel
									counterpartyId={counterparty.id}
								/>
							</TabsContent>
						</div>
					</div>
				</Tabs>
			)}
		</div>
	);
}

function CounterpartyViewSkeleton() {
	return (
		<div className="">
			<div className="flex items-center justify-between pb-4 border-b">
				<div className="flex items-center gap-4">
					<Skeleton className="h-12 w-12 rounded-full" />
					<div className="space-y-2">
						<Skeleton className="h-8 w-64" />
						<Skeleton className="h-4 w-32" />
					</div>
				</div>
				<Skeleton className="h-10 w-24" />
			</div>

			{/* <div className="flex">
				<Skeleton className="h-10 w-full" />
			</div> */}

			<div className="flex gap-6 mt-5">
				<div className="w-1/4 space-y-6">
					<Skeleton className="h-64" />
					<Skeleton className="h-80" />
					<Skeleton className="h-60" />
				</div>
				<Skeleton className="flex-1 h-80" />
			</div>
		</div>
	);
}
