"use client";

import { usePersonnelView } from "@saas/personnel/context/personnel-view-context";
import type { usePersonnelById } from "@saas/personnel/hooks/use-personnel";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Loader2, Pencil, Save, X } from "lucide-react";
import { useTranslations } from "next-intl";

interface PersonnelViewHeaderProps {
	personnel: ReturnType<typeof usePersonnelById>["data"];
}

export function PersonnelViewHeader({ personnel }: PersonnelViewHeaderProps) {
	const t = useTranslations();
	const {
		isEditMode,
		toggleEditMode,
		saveChanges,
		cancelEdit,
		activeTab,
		isSaving,
	} = usePersonnelView();

	// Only show edit buttons on the details tab
	const showEditButtons = activeTab === "details";

	return (
		<div className="flex justify-between items-center">
			<div>
				<h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
					{personnel?.firstName} {personnel?.lastName}
					{personnel?.department && (
						<Badge className="ml-2">
							{personnel.department.name}
						</Badge>
					)}
				</h1>
				<p className="text-muted-foreground">
					{personnel?.email || t("app.personnel.noEmail")}
				</p>
			</div>
			<div className="flex gap-2">
				{showEditButtons && isEditMode ? (
					<>
						<Button
							variant="outline"
							size="sm"
							onClick={cancelEdit}
							disabled={isSaving}
						>
							<X className="h-4 w-4 mr-2" />
							{t("app.personnel.actions.cancel")}
						</Button>
						<Button
							variant="primary"
							size="sm"
							onClick={saveChanges}
							disabled={isSaving}
						>
							{isSaving ? (
								<Loader2 className="h-4 w-4 mr-2 animate-spin" />
							) : (
								<Save className="h-4 w-4 mr-2" />
							)}
							{t("app.personnel.actions.save")}
						</Button>
					</>
				) : showEditButtons ? (
					<Button
						variant="outline"
						size="sm"
						onClick={toggleEditMode}
					>
						<Pencil className="h-4 w-4 mr-2" />
						{t("app.personnel.actions.edit")}
					</Button>
				) : null}
			</div>
		</div>
	);
}
