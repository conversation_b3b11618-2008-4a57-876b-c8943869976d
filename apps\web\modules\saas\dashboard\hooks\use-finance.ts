import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useState } from "react";
import {
	type FetchFinanceDataParams,
	calculateDifference,
	formatCurrency,
	formatPercentage,
	getDifferenceColor,
	useFinanceDataQuery,
} from "../lib/api-finance";

/**
 * Finance dashboard hook for fetching revenue data
 * Returns projected and confirmed revenue data for charts
 */
export function useFinance() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [timeRange, setTimeRange] = useState<"week" | "month" | "year">(
		"month",
	);
	const [customDateRange, setCustomDateRange] = useState<{
		startDate: Date | undefined;
		endDate: Date | undefined;
	}>({
		startDate: undefined,
		endDate: undefined,
	});

	// Set whether to use custom date range or predefined range
	const [useCustomDates, setUseCustomDates] = useState(false);

	// Get params for the query based on selected options
	const getQueryParams = (): FetchFinanceDataParams => {
		const baseParams = {
			organizationId: activeOrganization?.id ?? "",
		};

		if (
			useCustomDates &&
			customDateRange.startDate &&
			customDateRange.endDate
		) {
			return {
				...baseParams,
				startDate: customDateRange.startDate,
				endDate: customDateRange.endDate,
			};
		}

		return {
			...baseParams,
			timeRange,
		};
	};

	// Fetch financial data
	const financeQuery = useFinanceDataQuery(getQueryParams());

	// Helper to set time range and clear custom dates
	const handleTimeRangeChange = (newRange: "week" | "month" | "year") => {
		setTimeRange(newRange);
		setUseCustomDates(false);
	};

	// Helper to set custom date range
	const handleCustomDateChange = (startDate: Date, endDate: Date) => {
		setCustomDateRange({ startDate, endDate });
		setUseCustomDates(true);
	};

	return {
		// Revenue data
		revenueData: financeQuery.data?.data ?? [],
		totalProjected: financeQuery.data?.totalProjected ?? 0,
		totalConfirmed: financeQuery.data?.totalConfirmed ?? 0,

		// Order metrics
		orderMetrics: financeQuery.data?.orderMetrics ?? {
			totalOrders: 0,
			revenuePerOrder: 0,
		},

		// Team comparison data
		averageUserRevenue: financeQuery.data?.averageUserRevenue ?? 0,
		activeUserCount: financeQuery.data?.activeUserCount ?? 0,
		userPerformance: financeQuery.data?.userPerformance ?? {
			totalRevenue: 0,
			percentOfAverage: 0,
		},

		// Percentage difference between projected and confirmed
		percentageDifference: calculateDifference(
			financeQuery.data?.totalProjected ?? 0,
			financeQuery.data?.totalConfirmed ?? 0,
		),

		// Status
		isLoading: financeQuery.isLoading || !loaded,
		error: financeQuery.error,
		refetch: financeQuery.refetch,

		// Time range controls
		timeRange,
		setTimeRange: handleTimeRangeChange,
		customDateRange,
		setCustomDateRange: handleCustomDateChange,
		useCustomDates,
		setUseCustomDates,

		// Export utility functions for easy use in components
		utils: {
			formatCurrency,
			formatPercentage,
			calculateDifference,
			getDifferenceColor,
		},
	};
}
