import type {
	CreateCounterpartyInput,
	UpdateCounterpartyInput,
} from "@repo/api/src/routes/counterparties/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define query keys for caching and invalidation
export const counterpartyKeys = {
	all: ["counterparties"] as const,
	list: (params: FetchCounterpartiesParams) =>
		[...counterpartyKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...counterpartyKeys.all, "detail", organizationId, id] as const,
};

type FetchCounterpartiesParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	includeBlocked?: boolean;
};

// Fetch counterparties list with optional filtering and pagination
export const fetchCounterparties = async (
	params: FetchCounterpartiesParams,
) => {
	const response = await apiClient.counterparties.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			includeBlocked: params.includeBlocked?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparties list");
	}

	return response.json();
};

// Hook for fetching counterparties list
export const useCounterpartiesQuery = (params: FetchCounterpartiesParams) => {
	return useQuery({
		queryKey: counterpartyKeys.list(params),
		queryFn: () => fetchCounterparties(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

// Fetch a single counterparty by ID
export const fetchCounterpartyById = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.counterparties[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparty details");
	}

	return response.json();
};

// Hook for fetching a single counterparty
export const useCounterpartyByIdQuery = (
	organizationId?: string,
	id?: string,
) => {
	return useQuery({
		queryKey: counterpartyKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchCounterpartyById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create counterparty mutation
export const useCreateCounterpartyMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateCounterpartyInput) => {
			const response = await apiClient.counterparties.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create counterparty");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Counterparty created successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create counterparty");
			console.error("Create counterparty error:", error);
		},
	});
};

// Update counterparty mutation
export const useUpdateCounterpartyMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateCounterpartyInput) => {
			const response = await apiClient.counterparties[":id"].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update counterparty");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Counterparty updated successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: counterpartyKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: counterpartyKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update counterparty");
			console.error("Update counterparty error:", error);
		},
	});
};

// Delete counterparty mutation
export const useDeleteCounterpartyMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.counterparties[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete counterparty");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Counterparty deleted successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete counterparty");
			console.error("Delete counterparty error:", error);
		},
	});
};

// Block counterparty mutation
export const useBlockCounterpartyMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, reason }: { id: string; reason?: string }) => {
			const response = await apiClient.counterparties[":id"].block.$post({
				param: { id },
				json: { organizationId, reason },
			});

			if (!response.ok) {
				throw new Error("Failed to block counterparty");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Counterparty blocked successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: counterpartyKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: counterpartyKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to block counterparty");
			console.error("Block counterparty error:", error);
		},
	});
};

// Unblock counterparty mutation
export const useUnblockCounterpartyMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.counterparties[
				":id"
			].unblock.$post({
				param: { id },
				json: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to unblock counterparty");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Counterparty unblocked successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: counterpartyKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: counterpartyKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to unblock counterparty");
			console.error("Unblock counterparty error:", error);
		},
	});
};
