/**
 * Expense line item types for UI components
 * Extends API types with UI-specific fields needed for frontend functionality
 */

// Infer the line item type from API response to maintain type safety
type ExpenseResponse = Awaited<
	ReturnType<typeof import("@saas/expenses/lib/api").fetchExpenseById>
>;
type ExpenseLineItemFromAPI = ExpenseResponse["lineItems"][0];

/**
 * Extended interface for expense line items with UI-specific fields
 * Extends the API type with additional properties needed for frontend functionality
 */
export interface ExpenseLineItemWithAllocations extends ExpenseLineItemFromAPI {
	// Add computed properties that aren't in the API response but needed for UI
	allocationCount?: number;
	
	// Store the actual allocation data for frontend editing (before saving to backend)
	savedAllocations?: Array<{
		type: "general" | "order" | "vehicle" | "personnel";
		method: "fixed" | "percentage";
		value: number;
		percentage?: number;
		entityIds: string[];
		selectAll: boolean;
		allocation_start?: Date;
		allocation_end?: Date;
		notes?: string;
	}>;
	
	// Mark items generated by OCR for visual indication
	isOcrGenerated?: boolean;
	// Mark items where category was auto-assigned by OCR
	isOcrCategorized?: boolean;
	// Mark items where allocation was auto-assigned by OCR
	isOcrAllocated?: boolean;
	
	// Note: isFullyAllocated, remainingAmount, and allocatedAmount already exist in API response
}
