import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
	type CreateCreditNoteInput,
	type UpdateCreditNoteInput,
	useCreateCreditNoteMutation,
	useCreditNoteByIdQuery,
	useCreditNotesQuery,
	useDeleteCreditNoteMutation,
	useUpdateCreditNoteMutation,
} from "../lib/api-customer-credit-notes";

// Add this type definition
interface DocumentInfo {
	id: string;
	url?: string;
	data?: string;
	fileName?: string;
	fileType?: string;
	fileSize?: number;
	uploadedAt?: string;
	updatedAt?: string;
}

/**
 * Hook to manage credit notes
 */
export function useCustomerCreditNotes(options?: {
	customerId?: string;
	orderId?: string;
	search?: string;
	page?: number;
	limit?: number;
}) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";
	const [search, setSearch] = useState(options?.search || "");
	const [page, setPage] = useState(options?.page || 1);
	const [limit, setLimit] = useState(options?.limit || 10);

	// Add debouncing for search to prevent excessive API calls
	const debouncedSearch = useDebounce(search, 300);

	const creditNotesQuery = useCreditNotesQuery({
		organizationId,
		search: debouncedSearch,
		page,
		limit,
		customerId: options?.customerId,
		orderId: options?.orderId,
	});

	return {
		data: creditNotesQuery.data,
		creditNotes:
			creditNotesQuery.data && "items" in creditNotesQuery.data
				? creditNotesQuery.data.items
				: [],
		pagination:
			creditNotesQuery.data && "items" in creditNotesQuery.data
				? {
						total: creditNotesQuery.data.total,
						page: creditNotesQuery.data.page,
						totalPages: creditNotesQuery.data.totalPages,
					}
				: undefined,
		isLoading: creditNotesQuery.isLoading,
		error: creditNotesQuery.error,
		refetch: creditNotesQuery.refetch,
		search,
		setSearch,
		page,
		setPage,
		limit,
		setLimit,
	};
}

/**
 * Hook to fetch a single credit note by ID with optional document
 */
export function useCustomerCreditNoteById(
	id?: string,
	includeDocument = false,
) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const creditNoteQuery = useCreditNoteByIdQuery(
		id ? organizationId : undefined,
		id,
		includeDocument,
	);

	return {
		creditNote: creditNoteQuery.data,
		document: creditNoteQuery.data
			? ((creditNoteQuery.data as any).document as
					| DocumentInfo
					| undefined)
			: undefined,
		isLoading: creditNoteQuery.isLoading,
		error: creditNoteQuery.error,
		refetch: creditNoteQuery.refetch,
	};
}

/**
 * Hook to manage credit note mutations (create, update, delete)
 */
export function useCustomerCreditNoteMutations(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";
	const queryClient = useQueryClient();

	// Use the existing mutations
	const createMutation = useCreateCreditNoteMutation(organizationId);
	const updateMutation = useUpdateCreditNoteMutation(organizationId);
	const deleteMutation = useDeleteCreditNoteMutation(organizationId);

	// Add state to track loading for document upload operations
	const [isDocumentLoading, setIsDocumentLoading] = useState(false);

	// Helper function to invalidate audit logs
	const invalidateAuditLogs = () => {
		// Invalidate all audit logs for credit notes
		queryClient.invalidateQueries({
			predicate: (query) => {
				const queryKey = query.queryKey;
				return (
					Array.isArray(queryKey) &&
					queryKey[0] === "audit-logs" &&
					// Check if it's related to credit notes
					Array.isArray(queryKey[3]) &&
					queryKey[3].includes("CREDIT_NOTE")
				);
			},
		});
	};

	// Helper function to invalidate uninvoiced orders
	const invalidateUninvoicedOrders = () => {
		// Invalidate all uninvoiced orders queries to refresh the list
		queryClient.invalidateQueries({
			predicate: (query) => {
				const queryKey = query.queryKey;
				return (
					Array.isArray(queryKey) &&
					queryKey[0] === "orders" &&
					queryKey[1] === "uninvoiced"
				);
			},
		});
	};

	// Helper function to invalidate credit note queries
	const invalidateCreditNotes = () => {
		// Invalidate all credit note queries to refresh the list
		queryClient.invalidateQueries({
			predicate: (query) => {
				const queryKey = query.queryKey;
				return (
					Array.isArray(queryKey) &&
					queryKey[0] === "customer-credit-notes"
				);
			},
		});
	};

	const createCreditNoteWithCallback = async (
		data: CreateCreditNoteInput & { documentFile?: File },
	) => {
		try {
			const result = await createMutation.mutateAsync(data);

			// Invalidate credit notes queries
			invalidateCreditNotes();

			// Invalidate audit logs
			invalidateAuditLogs();

			// Invalidate uninvoiced orders to update the list
			invalidateUninvoicedOrders();

			if (options?.onSuccess) {
				options.onSuccess();
			}

			return result;
		} catch (error) {
			console.error("Error in createCreditNoteWithCallback:", error);
			throw error;
		}
	};

	const updateCreditNoteWithCallback = async (
		id: string,
		data: UpdateCreditNoteInput & { documentFile?: File },
	) => {
		try {
			const result = await updateMutation.mutateAsync({ id, ...data });

			// Invalidate credit notes queries
			invalidateCreditNotes();

			// Invalidate audit logs
			invalidateAuditLogs();

			// Invalidate uninvoiced orders to update the list
			invalidateUninvoicedOrders();

			if (options?.onSuccess) {
				options.onSuccess();
			}

			return result;
		} catch (error) {
			console.error("Error in updateCreditNoteWithCallback:", error);
			throw error;
		}
	};

	const deleteCreditNoteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);

			// Invalidate credit notes queries
			invalidateCreditNotes();

			// Invalidate audit logs
			invalidateAuditLogs();

			// Invalidate uninvoiced orders to update the list
			invalidateUninvoicedOrders();

			if (options?.onSuccess) {
				options.onSuccess();
			}

			return result;
		} catch (error) {
			console.error("Error in deleteCreditNoteWithCallback:", error);
			throw error;
		}
	};

	return {
		createCreditNote: createCreditNoteWithCallback,
		updateCreditNote: updateCreditNoteWithCallback,
		deleteCreditNote: deleteCreditNoteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending ||
			isDocumentLoading,
	};
}

/**
 * Utility function to format credit note data for API requests
 */
export function formatCreditNoteForApi(creditNote: {
	organizationId: string;
	customerId?: string;
	credit_note_number?: string;
	credit_note_date?: Date | string;
	description?: string;
	gross_amount?: number;
	net_amount?: number;
	// VAT tracked per line item, not document level
	orderIds?: string[];
	order_allocations?: Array<{
		orderId: string;
		gross_amount?: number;
		net_amount?: number;
		// VAT tracked per line item, not allocation level
		lineItems?: Array<{
			orderLineItemId: string;
			quantity?: number;
			unitPrice?: number;
			totalPrice: number;
			description?: string;
		}>;
	}>;
	documentFile?: File;
	selectedLineItems?: Record<string, boolean>;
	orderLineItems?: Record<string, any[]>;
}): CreateCreditNoteInput & { documentFile?: File } {
	let formattedOrderAllocations = creditNote.order_allocations || [];

	// If we have selectedLineItems and orderLineItems, but no line items in allocations yet,
	// process them into the expected format
	if (
		creditNote.selectedLineItems &&
		creditNote.orderLineItems &&
		creditNote.orderIds?.length &&
		(!formattedOrderAllocations.length ||
			!formattedOrderAllocations.some((a) => a.lineItems?.length))
	) {
		formattedOrderAllocations = creditNote.orderIds
			.map((orderId) => {
				// Get existing allocation or create new one
				const existingAllocation = formattedOrderAllocations.find(
					(a) => a.orderId === orderId,
				) || {
					orderId,
					gross_amount: 0,
					net_amount: 0,
					// VAT tracked per line item, not allocation level
				};

				// Get line items for this order
				const items = creditNote.orderLineItems?.[orderId] || [];

				// Filter to selected items and format them
				const lineItems = items
					.filter((item, index) => {
						const key = `${orderId}:${item.id || index}`;
						return creditNote.selectedLineItems?.[key] === true;
					})
					.map((item) => ({
						orderLineItemId: item.id,
						quantity: item.quantity,
						unitPrice: item.unitPrice,
						totalPrice: item.totalPrice,
						description: item.description,
					}));

				return {
					...existingAllocation,
					lineItems,
				};
			})
			.filter((allocation) => allocation.lineItems.length > 0); // Only include orders with selected line items
	}

	return {
		organizationId: creditNote.organizationId,
		customerId: creditNote.customerId || "",
		credit_note_number: creditNote.credit_note_number,
		credit_note_date: creditNote.credit_note_date
			? new Date(creditNote.credit_note_date)
			: new Date(),
		description: creditNote.description || null,
		gross_amount: creditNote.gross_amount || 0,
		net_amount: creditNote.net_amount || 0,
		// VAT tracked per line item, not document level
		orderIds: creditNote.orderIds || [],
		order_allocations: formattedOrderAllocations,
		documentFile: creditNote.documentFile,
	};
}
