// Universal types for email preview functionality across all entity types

export interface DocumentInfo {
	id: string;
	fileName?: string;
	fileSize?: number;
	signedUrl?: string; // For clickable preview links
}

export interface EmailPreviewMetadata {
	organization?: string;
	sender?: string;
	recipient?: string;
	// Support both property names for backward compatibility
	orderDocuments?: DocumentInfo[];
	tourDocuments?: DocumentInfo[];
}

export interface EmailPreviewData {
	html: string;
	text: string;
	subject: string;
	metadata?: EmailPreviewMetadata;
}

export interface EmailPreviewFeatures {
	customSubject?: boolean;
	documentAttachments?: boolean;
	backButton?: boolean;
	stateReset?: boolean;
	ccBccSupport?: boolean;
}

export type EntityType =
	| "invoice"
	| "offer"
	| "transport-order"
	| "order-confirmation";

export interface SendEmailParams {
	entityId: string;
	entityType: EntityType;
	recipientEmail?: string;
	ccEmails?: string[];
	bccEmails?: string[];
	documentIds?: string[];
	customSubject?: string;
	documentFilenames?: Record<string, string>; // documentId -> customFilename for email attachments
}

export interface UniversalEmailPreviewProps {
	entityId: string | null;
	entityType: EntityType;
	isOpen: boolean;
	onClose: () => void;
	onSend: (params: SendEmailParams) => Promise<void>;
	isSending?: boolean;
	recipientEmail?: string;
	onBack?: () => void;
	features?: EmailPreviewFeatures;
	title?: string;
	attachmentText?: string;
}

// Hook interfaces
export interface UseUniversalEmailPreviewConfig {
	entityType: EntityType;
	entityId: string;
	organizationId: string;
	recipientEmail?: string;
	enabled?: boolean;
}

export interface UseUniversalEmailPreviewReturn {
	data: EmailPreviewData | null;
	isLoading: boolean;
	error: Error | null;
	refetch: () => void;
}

export interface UseUniversalSendEmailReturn {
	sendEmail: (params: SendEmailParams) => Promise<void>;
	isLoading: boolean;
	error: Error | null;
}

// Helper function type for checking email preview data structure
export type EmailPreviewValidator = (data: any) => data is EmailPreviewData;
