"use client";

import {
	useCounterpartyGroupsQuery,
	useCreateCounterpartyGroupMutation,
} from "@saas/contacts/lib/api-counterparty-groups";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { Plus, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

export interface CounterpartyGroupType {
	id: string;
	name: string;
	description?: string;
}

export interface CounterpartyGroupBadgesProps {
	value?: string[];
	onChange?: (value: string[]) => void;
	groups?: CounterpartyGroupType[];
	className?: string;
	placeholder?: string;
	disabled?: boolean;
}

// Form schema for creating counterparty groups
const createCounterpartyGroupSchema = z.object({
	name: z.string().min(1, "Name is required"),
	description: z.string().optional(),
});

type CreateCounterpartyGroupValues = z.infer<
	typeof createCounterpartyGroupSchema
>;

/**
 * Component that displays counterparty groups as removable badges with add functionality
 */
export function CounterpartyGroupBadges({
	value = [],
	onChange,
	groups,
	className,
	placeholder = "No groups assigned",
	disabled = false,
}: CounterpartyGroupBadgesProps) {
	const { activeOrganization } = useActiveOrganization();
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isDialogOpen, setIsDialogOpen] = useState(false);

	const createForm = useForm<CreateCounterpartyGroupValues>({
		defaultValues: {
			name: "",
			description: "",
		},
	});

	const createMutation = useCreateCounterpartyGroupMutation(
		activeOrganization?.id || "",
	);

	// Get available groups from API if not provided
	const { data: apiGroups, isLoading } = useCounterpartyGroupsQuery(
		{ organizationId: activeOrganization?.id || "" },
		{
			enabled: !groups?.length && !!activeOrganization?.id,
		},
	);

	// Combine provided groups with API groups
	const allGroups = groups?.length ? groups : apiGroups?.items || [];

	// Get selected groups for badge display
	const selectedGroups = allGroups.filter((group) =>
		value.includes(group.id),
	);

	// Get available groups for dropdown (not already selected)
	const availableGroups = allGroups.filter(
		(group) => !value.includes(group.id),
	);

	// Handle removing a group
	const handleRemoveGroup = (groupId: string) => {
		if (onChange && !disabled) {
			const newValue = value.filter((id) => id !== groupId);
			onChange(newValue);
		}
	};

	// Handle adding a group
	const handleAddGroup = (groupId: string) => {
		if (onChange && !disabled) {
			const newValue = [...value, groupId];
			onChange(newValue);
		}
		setIsDropdownOpen(false);
	};

	// Handle creating a new group
	const handleCreateGroup = async (values: CreateCounterpartyGroupValues) => {
		try {
			if (!activeOrganization?.id) {
				toast.error("No active organization");
				return;
			}

			const result = await createMutation.mutateAsync({
				...values,
				organizationId: activeOrganization?.id,
			});

			// Add the newly created group to selection
			if (result && onChange) {
				const newValue = [...value, result.id];
				onChange(newValue);
			}

			setIsDialogOpen(false);
			setIsDropdownOpen(false);
			createForm.reset();
		} catch (error) {
			console.error("Error creating counterparty group:", error);
		}
	};

	// Handle form submission with event prevention
	const handleFormSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		e.stopPropagation();
		createForm.handleSubmit(handleCreateGroup)(e);
	};

	return (
		<div className={cn("space-y-2", className)}>
			{/* Display selected groups as badges */}
			<div className="flex flex-wrap gap-2">
				{selectedGroups.map((group) => (
					<Badge
						key={group.id}
						status="info"
						className="flex items-center gap-1 pr-1"
					>
						<span>{group.name}</span>
						{!disabled && (
							<Button
								variant="ghost"
								size="sm"
								className="h-4 w-4 p-0 hover:bg-transparent"
								onClick={() => handleRemoveGroup(group.id)}
							>
								<X className="h-3 w-3" />
							</Button>
						)}
					</Badge>
				))}

				{/* Add group button */}
				{!disabled && (
					<DropdownMenu
						open={isDropdownOpen}
						onOpenChange={setIsDropdownOpen}
					>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								size="sm"
								className="h-6 border-dashed"
								disabled={isLoading}
							>
								<Plus className="h-3 w-3 mr-1" />
								Add Group
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent className="w-56">
							{/* Available groups to add */}
							{availableGroups.length > 0 ? (
								<>
									{availableGroups.map((group) => (
										<DropdownMenuItem
											key={group.id}
											onClick={() =>
												handleAddGroup(group.id)
											}
										>
											<div className="flex flex-col">
												<span className="font-medium">
													{group.name}
												</span>
												{group.description && (
													<span className="text-sm text-muted-foreground">
														{group.description}
													</span>
												)}
											</div>
										</DropdownMenuItem>
									))}
									<div className="border-t my-1" />
								</>
							) : (
								<DropdownMenuItem disabled>
									All groups already selected
								</DropdownMenuItem>
							)}

							{/* Create new group option */}
							<Dialog
								open={isDialogOpen}
								onOpenChange={setIsDialogOpen}
							>
								<DialogTrigger asChild>
									<DropdownMenuItem
										onSelect={(e) => {
											e.preventDefault();
											setIsDialogOpen(true);
										}}
										className="bg-muted/50"
									>
										<Plus className="mr-2 h-4 w-4" />
										Create New Group
									</DropdownMenuItem>
								</DialogTrigger>
								<DialogContent className="sm:max-w-md">
									<DialogHeader>
										<DialogTitle>
											Create Counterparty Group
										</DialogTitle>
										<DialogDescription>
											Create a new counterparty group to
											organize your contacts.
										</DialogDescription>
									</DialogHeader>
									<Form {...createForm}>
										<form
											onSubmit={handleFormSubmit}
											className="space-y-4"
										>
											<FormField
												control={createForm.control}
												name="name"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Name
														</FormLabel>
														<FormControl>
															<Input
																placeholder="Group name"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={createForm.control}
												name="description"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Description
															(Optional)
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Group description"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<DialogFooter>
												<Button
													type="button"
													variant="outline"
													onClick={() => {
														setIsDialogOpen(false);
														createForm.reset();
													}}
												>
													Cancel
												</Button>
												<Button
													type="submit"
													disabled={
														createMutation.isPending
													}
												>
													{createMutation.isPending
														? "Creating..."
														: "Create Group"}
												</Button>
											</DialogFooter>
										</form>
									</Form>
								</DialogContent>
							</Dialog>
						</DropdownMenuContent>
					</DropdownMenu>
				)}
			</div>

			{/* Empty state */}
			{selectedGroups.length === 0 && (
				<p className="text-sm text-muted-foreground">{placeholder}</p>
			)}
		</div>
	);
}
