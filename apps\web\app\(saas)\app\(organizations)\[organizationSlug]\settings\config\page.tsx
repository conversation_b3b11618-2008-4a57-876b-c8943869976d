import { ExpenseCategories } from "@saas/organizations/components/ExpenseCategories";
import { PaymentTerms } from "@saas/organizations/components/PaymentTerms";
import { VatTerms } from "@saas/organizations/components/VatTerms";
import { VehicleOrderTypes } from "@saas/organizations/components/VehicleOrderTypes";
import { Departments } from "@saas/organizations/components/departments";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("organizations.settings.title"),
	};
}

export default function OrganizationSettingsPage() {
	return (
		<div className="space-y-6">
			<Tabs defaultValue="vat-terms" className="w-full">
				<TabsList className="grid w-full grid-cols-5">
					<TabsTrigger value="vat-terms">VAT Terms</TabsTrigger>
					<TabsTrigger value="payment-terms">
						Payment Terms
					</TabsTrigger>
					<TabsTrigger value="expense-categories">
						Expense Categories
					</TabsTrigger>
					<TabsTrigger value="vehicle-order-types">
						Vehicle Order Types
					</TabsTrigger>
					<TabsTrigger value="departments">Departments</TabsTrigger>
				</TabsList>
				<TabsContent value="vat-terms" className="mt-6">
					<VatTerms />
				</TabsContent>
				<TabsContent value="payment-terms" className="mt-6">
					<PaymentTerms />
				</TabsContent>
				<TabsContent value="expense-categories" className="mt-6">
					<ExpenseCategories />
				</TabsContent>
				<TabsContent value="vehicle-order-types" className="mt-6">
					<VehicleOrderTypes />
				</TabsContent>
				<TabsContent value="departments" className="mt-6">
					<Departments />
				</TabsContent>
			</Tabs>
		</div>
	);
}
