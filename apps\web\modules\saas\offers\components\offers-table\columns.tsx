"use client";

import { useOffersUI } from "@saas/offers/context/offers-ui-context";
import type { Offer } from "@saas/offers/lib/types";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import { useRouter } from "@shared/hooks/router";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { format } from "date-fns";
import {
	Calendar,
	Eye,
	Mail,
	MoreHorizontal,
	Tag,
	Trash,
	User,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

type ActionsCellProps = {
	row: Row<Offer>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const offer = row.original;
	const { handleDeleteOffer, handleViewOffer, handleSendOfferEmail } =
		useOffersUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>Actions</DropdownMenuLabel>
				<DropdownMenuItem onClick={() => handleViewOffer(offer)}>
					<Eye className="mr-2 h-4 w-4" />
					View
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => handleSendOfferEmail(offer)}
					data-send-email-action
				>
					<Mail className="mr-2 h-4 w-4" />
					Send Email
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteOffer(offer)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					Delete
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<Offer>[] {
	const { handleDeleteOffer, handleViewOffer, handleSendOfferEmail } =
		useOffersUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorFn: (row) => row.order?.order_number,
			id: "orderNumber",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Order Number" />
			),
			cell: ({ row }) => {
				const offer = row.original;
				const orderNumber = row.getValue("orderNumber") as
					| string
					| null
					| undefined;
				return (
					<div className="flex items-center gap-2">
						<Tag className="h-4 w-4 text-muted-foreground" />
						{offer.orderId ? (
							<Link
								href={`/app/${params.organizationSlug}/orders/${offer.orderId}`}
								className="hover:underline cursor-pointer"
							>
								<span className="font-medium">
									{orderNumber || "-"}
								</span>
							</Link>
						) : (
							<span className="font-medium">
								{orderNumber || "-"}
							</span>
						)}
					</div>
				);
			},
		},
		{
			accessorFn: (row) => row.customer?.nameLine1,
			id: "customerName",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Customer" />
			),
			cell: ({ row }) => {
				const offer = row.original;
				const customerName = row.getValue("customerName") as
					| string
					| null
					| undefined;
				return (
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-muted-foreground" />
						{offer.customerId ? (
							<Link
								href={`/app/${params.organizationSlug}/contacts/${offer.customerId}`}
								className="hover:underline cursor-pointer"
							>
								<span>{customerName || "-"}</span>
							</Link>
						) : (
							<span>{customerName || "-"}</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "status",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Status" />
			),
			cell: ({ row }) => {
				const status = row.getValue("status") as string | null;

				let statusClass = "bg-gray-100 text-gray-800";
				if (status === "accepted") {
					statusClass = "bg-green-100 text-green-800";
				} else if (status === "cancelled") {
					statusClass = "bg-red-100 text-red-800";
				} else if (status === "expired") {
					statusClass = "bg-amber-100 text-amber-800";
				} else if (status === "open") {
					statusClass = "bg-blue-100 text-blue-800";
				}

				return (
					<span
						className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}`}
					>
						{status || "Unknown"}
					</span>
				);
			},
		},
		{
			accessorKey: "valid_until",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Valid Until" />
			),
			cell: ({ row }) => {
				const validUntil = row.getValue("valid_until") as string | null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{validUntil
								? format(new Date(validUntil), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "accepted_at",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Accepted At" />
			),
			cell: ({ row }) => {
				const acceptedAt = row.getValue("accepted_at") as string | null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{acceptedAt
								? format(new Date(acceptedAt), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "declined_at",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Declined At" />
			),
			cell: ({ row }) => {
				const declinedAt = row.getValue("declined_at") as string | null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{declinedAt
								? format(new Date(declinedAt), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorFn: (row) => {
				// Calculate the sum of totalPrice for all prices (all line items are now revenue-only)
				const prices = row.prices || [];
				return prices.reduce(
					(sum, item) => sum + (item.totalPrice || 0),
					0,
				);
			},
			id: "totalAmount",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Amount" />
			),
			cell: ({ row }) => {
				const totalAmount = row.getValue("totalAmount") as number;

				// Get the currency from the first price item or default to EUR
				const prices = row.original.prices || [];
				const currency =
					prices.length > 0 && prices[0].currency
						? prices[0].currency
						: "EUR";

				// Format the amount with currency
				const formattedAmount = new Intl.NumberFormat("de-DE", {
					style: "currency",
					currency,
				}).format(totalAmount);

				return formattedAmount;
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Created At" />
			),
			cell: ({ row }) => {
				const date = row.getValue("createdAt") as string;
				return format(new Date(date), "PPP");
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<Offer>) => {
					const offer = row.original;
					return {
						openView: () => handleViewOffer(offer),
						openDelete: () => handleDeleteOffer(row.original),
					};
				},
			},
		},
	];
}
