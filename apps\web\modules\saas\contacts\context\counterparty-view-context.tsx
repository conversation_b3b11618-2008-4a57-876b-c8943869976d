"use client";

import { ENTITY_TYPES } from "@repo/database";
import { useCounterpartyById } from "@saas/contacts/hooks/use-counterparty";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { useRouter } from "@shared/hooks/router";
import {
	type AuditLog,
	useEntityAuditLogs,
} from "@shared/hooks/use-audit-logs";
import { useParams, usePathname } from "next/navigation";
import React, {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useLayoutEffect,
	useMemo,
	useState,
	useRef,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";

// Use useLayoutEffect on client, useEffect on server
const useIsomorphicLayoutEffect =
	typeof window !== "undefined" ? useLayoutEffect : useEffect;

// Extract the counterparty type from the hook's return value
type CounterpartyResponse = ReturnType<
	typeof useCounterpartyById
>["counterparty"];

interface CounterpartyViewContextProps {
	counterpartyId: string;
	activeTab: string;
	setActiveTab: (tab: string) => void;
	isLoading: boolean;
	counterparty: NonNullable<CounterpartyResponse> | null | undefined;
	refreshCounterparty: () => void;
	auditLogs: AuditLog[] | undefined;
	auditLogsLoading: boolean;
	refreshAuditLogs: () => Promise<unknown>;
	auditLogsPagination: {
		page: number;
		setPage: (page: number) => void;
		hasNextPage: boolean;
		hasPreviousPage: boolean;
		goToNextPage: () => void;
		goToPreviousPage: () => void;
		total?: number;
		totalPages?: number;
	};
	isEditing: boolean;
	setIsEditing: (editing: boolean) => void;
	startEditing: () => void;
	stopEditing: () => void;
	submitForm: () => Promise<boolean>;
	setSubmitForm: (fn: (() => Promise<boolean>) | null) => void;
}

const CounterpartyViewContext = createContext<
	CounterpartyViewContextProps | undefined
>(undefined);

export function CounterpartyViewProvider({
	children,
}: { children: React.ReactNode }) {
	const params = useParams<{ contactId: string; organizationSlug: string }>();
	const pathname = usePathname();
	const router = useRouter();
	const counterpartyId = params.contactId as string;
	const { registerScopeWithShortcuts, activeScopes } = useShortcuts();
	const { activeOrganization } = useActiveOrganization();
	const [isEditing, setIsEditing] = useState(false);

	// Use a ref instead of state for the submitForm function to prevent re-renders
	const submitFormRef = useRef<(() => Promise<boolean>) | null>(null);

	// Function to update the ref
	const setSubmitForm = useCallback((fn: (() => Promise<boolean>) | null) => {
		submitFormRef.current = fn;
	}, []);

	// Define tabs in order for arrow navigation
	const tabs = [
		"details",
		"addresses",
		"invoices",
		"offers",
		"orders",
		"activity",
	];

	// Extract tab from URL or use default
	const [activeTab, setActiveTab] = useState<string>(() => {
		// Initialize from URL hash only on client-side
		if (typeof window !== "undefined") {
			const hash = window.location.hash.replace("#", "");
			if (hash && tabs.includes(hash)) {
				return hash;
			}
		}
		return "details";
	});

	// Fetch the specific counterparty by ID using the hook from use-contacts.ts
	const { counterparty, isLoading, refetch } =
		useCounterpartyById(counterpartyId);

	// Fetch audit logs for this counterparty
	const {
		auditLogs,
		isLoading: auditLogsLoading,
		refetch: refreshAuditLogs,
		page,
		setPage,
		hasNextPage,
		hasPreviousPage,
		goToNextPage,
		goToPreviousPage,
		total,
		totalPages,
	} = useEntityAuditLogs(
		activeOrganization?.id || "",
		ENTITY_TYPES.COUNTERPARTY,
		counterpartyId,
		{
			enabled: !!activeOrganization?.id,
		},
	);

	// Update URL when tab changes - client side only
	const updateTab = useCallback(
		(tab: string) => {
			setActiveTab(tab);
			// This only runs on the client
			window.history.pushState(null, "", `${pathname}#${tab}`);
		},
		[pathname],
	);

	// Register shortcuts scope
	useEffect(() => {
		// Register and enable the counterparty view shortcuts scope
		const cleanup = registerScopeWithShortcuts("counterparty-view");

		// Return cleanup function
		return cleanup;
	}, [registerScopeWithShortcuts]);

	// Handle right arrow - next tab
	useHotkeys(
		"ArrowRight",
		() => {
			const currentIndex = tabs.indexOf(activeTab);
			if (currentIndex === -1) {
				return;
			}

			const nextIndex = (currentIndex + 1) % tabs.length;
			updateTab(tabs[nextIndex]);
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes("counterparty-view"),
			enableOnFormTags: false,
			keyup: false,
			keydown: true,
		},
		[activeTab, updateTab, tabs, activeScopes],
	);

	// Handle left arrow - previous tab
	useHotkeys(
		"ArrowLeft",
		() => {
			const currentIndex = tabs.indexOf(activeTab);
			if (currentIndex === -1) {
				return;
			}

			const prevIndex = (currentIndex - 1 + tabs.length) % tabs.length;
			updateTab(tabs[prevIndex]);
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes("counterparty-view"),
			enableOnFormTags: false,
			keyup: false,
			keydown: true,
		},
		[activeTab, updateTab, tabs, activeScopes],
	);

	// Handle escape key - back to counterparties list
	useHotkeys(
		"Escape",
		() => {
			const organizationSlug = params.organizationSlug as string;
			router.push(`/app/${organizationSlug}/contacts`);
		},
		{
			preventDefault: true,
			enabled: activeScopes.includes("counterparty-view"),
			enableOnFormTags: false,
			keyup: false,
			keydown: true,
		},
		[router, params.organizationSlug],
	);

	// Expose context values
	const contextValue = useMemo(
		() => ({
			counterpartyId,
			activeTab,
			setActiveTab: updateTab,
			isLoading,
			counterparty,
			refreshCounterparty: refetch,
			auditLogs,
			auditLogsLoading,
			refreshAuditLogs,
			auditLogsPagination: {
				page,
				setPage,
				hasNextPage,
				hasPreviousPage,
				goToNextPage,
				goToPreviousPage,
				total,
				totalPages,
			},
			isEditing,
			setIsEditing,
			startEditing: () => setIsEditing(true),
			stopEditing: () => setIsEditing(false),
			submitForm: () =>
				submitFormRef.current?.() || Promise.resolve(false),
			setSubmitForm,
		}),
		[
			counterpartyId,
			activeTab,
			updateTab,
			isLoading,
			counterparty,
			refetch,
			auditLogs,
			auditLogsLoading,
			refreshAuditLogs,
			page,
			setPage,
			hasNextPage,
			hasPreviousPage,
			goToNextPage,
			goToPreviousPage,
			total,
			totalPages,
			isEditing,
			setIsEditing,
			setSubmitForm,
		],
	);

	return (
		<CounterpartyViewContext.Provider value={contextValue}>
			{children}
		</CounterpartyViewContext.Provider>
	);
}

export function useCounterpartyView() {
	const context = useContext(CounterpartyViewContext);
	if (!context) {
		throw new Error(
			"useCounterpartyView must be used within a CounterpartyViewProvider",
		);
	}
	return context;
}
