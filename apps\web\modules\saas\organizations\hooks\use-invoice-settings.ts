import type { UpdateInvoiceConfigurationInput } from "@repo/api/src/routes/settings/invoice-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	fetchInvoiceConfiguration,
	useInvoiceConfigurationQuery,
	useUpdateInvoiceConfigurationMutation,
} from "../lib/api-invoice-settings";

// Type for frontend form values (excludes backend-managed dates)
export type InvoiceSettingsFormValues = Omit<
	UpdateInvoiceConfigurationInput,
	"organizationId" | "lastDate"
>;

// Main hook for invoice configuration
export function useInvoiceSettings() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	// Query for invoice configuration
	const query = useInvoiceConfigurationQuery(organizationId, {
		enabled: !!organizationId,
	});

	// Mutation for updating invoice configuration
	const updateMutation =
		useUpdateInvoiceConfigurationMutation(organizationId);

	// Wrapped update function
	const updateInvoiceSettings = async (data: InvoiceSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values for lastDate from the query data
		const currentValues = query.data;

		return await updateMutation.mutateAsync(data);
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
		refetch: query.refetch,
		updateInvoiceSettings,
		isUpdating: updateMutation.isPending,
	};
}

// Hook for accessing raw fetch function
export function useInvoiceSettingsRaw() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const updateMutation =
		useUpdateInvoiceConfigurationMutation(organizationId);

	const fetchSettings = async () => {
		if (!organizationId) {
			throw new Error("No active organization");
		}
		return await fetchInvoiceConfiguration(organizationId);
	};

	const updateSettings = async (data: InvoiceSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values to preserve lastDate
		const currentValues = await fetchInvoiceConfiguration(organizationId);

		return await updateMutation.mutateAsync(data);
	};

	return {
		fetchSettings,
		updateSettings,
		isUpdating: updateMutation.isPending,
	};
}
