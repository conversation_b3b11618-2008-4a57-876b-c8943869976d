import type { UpdateOrderConfigurationInput } from "@repo/api/src/routes/settings/order-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	fetchOrderConfiguration,
	useOrderConfigurationQuery,
	useUpdateOrderConfigurationMutation,
} from "@saas/organizations/lib/api-order-settings";
import {} from "../lib/api-invoice-settings";

// Type for frontend form values (excludes backend-managed dates)
export type OrderSettingsFormValues = Omit<
	UpdateOrderConfigurationInput,
	"organizationId"
>;

// Main hook for order configuration
export function useOrderSettings() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	// Query for order configuration
	const query = useOrderConfigurationQuery(organizationId, {
		enabled: !!organizationId,
	});

	// Mutation for updating order configuration
	const updateMutation = useUpdateOrderConfigurationMutation(organizationId);

	// Wrapped update function
	const updateOrderSettings = async (data: OrderSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values for lastDate from the query data
		const currentValues = query.data;

		return await updateMutation.mutateAsync(data);
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
		refetch: query.refetch,
		updateOrderSettings,
		isUpdating: updateMutation.isPending,
	};
}

// Hook for accessing raw fetch function
export function useOrderSettingsRaw() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const updateMutation = useUpdateOrderConfigurationMutation(organizationId);

	const fetchSettings = async () => {
		if (!organizationId) {
			throw new Error("No active organization");
		}
		return await fetchOrderConfiguration(organizationId);
	};

	const updateSettings = async (data: OrderSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values to preserve lastDate
		const currentValues = await fetchOrderConfiguration(organizationId);

		return await updateMutation.mutateAsync(data);
	};

	return {
		fetchSettings,
		updateSettings,
		isUpdating: updateMutation.isPending,
	};
}
