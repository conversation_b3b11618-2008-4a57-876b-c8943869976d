"use client";

import { useOrdersUI } from "@saas/orders/context/orders-ui-context";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import { useRouter } from "@shared/hooks/router";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import {
	Eye,
	Flag,
	FlagTriangleRight,
	MapPin,
	MoreHorizontal,
	Package,
	Trash,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useParams } from "next/navigation";
import type { Order } from "../context/orders-ui-context";

declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

type ActionsCellProps = {
	row: Row<Order>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const t = useTranslations();
	const order = row.original;
	const { handleDeleteOrder } = useOrdersUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	const navigateToOrderView = () => {
		router.push(`/app/${params.organizationSlug}/orders/${order.id}`);
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{t("app.orders.table.actions.title")}
				</DropdownMenuLabel>
				<DropdownMenuItem onClick={navigateToOrderView}>
					<Eye className="mr-2 h-4 w-4" />
					{t("app.orders.table.actions.view")}
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeleteOrder(order)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					{t("app.orders.table.actions.delete")}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<Order>[] {
	const t = useTranslations();
	const { handleDeleteOrder } = useOrdersUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "order_number",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Order Number" />
			),
			cell: ({ row }) => {
				const order = row.original;
				const orderNumber = row.getValue("order_number") as
					| string
					| null;
				return (
					<div className="flex items-center gap-2">
						<Package className="h-4 w-4 text-muted-foreground" />
						<Link
							href={`/app/${params.organizationSlug}/orders/${order.id}`}
							className="hover:underline cursor-pointer"
						>
							<span>{orderNumber || "-"}</span>
						</Link>
					</div>
				);
			},
		},
		{
			accessorKey: "customer.nameLine1",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Customer" />
			),
			cell: ({ row }) => {
				const order = row.original;
				const customerName = order.customer?.nameLine1 || "-";
				return (
					<div>
						{order.customer?.id ? (
							<Link
								href={`/app/${params.organizationSlug}/contacts/${order.customer.id}`}
								className="hover:underline cursor-pointer"
							>
								{customerName}
							</Link>
						) : (
							customerName
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "stops",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Stops" />
			),
			cell: ({ row }) => {
				const order = row.original;
				const stops = order.stops || [];
				const stopsCount = stops.length;

				if (stopsCount === 0) {
					return <Badge status="error">No stops</Badge>;
				}

				const firstStop = stops[0];
				const lastStop = stopsCount > 1 ? stops[stopsCount - 1] : null;

				return (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="flex items-center gap-2 cursor-help">
									<Badge>{stopsCount} stops</Badge>
								</div>
							</TooltipTrigger>
							<TooltipContent side="bottom" className="w-[280px]">
								<div className="space-y-3 max-w-sm">
									<h4 className="font-medium">
										All Stops ({stopsCount})
									</h4>
									<div className="space-y-2">
										{stops.map((stop, index) => (
											<div
												key={index}
												className="border-b border-border pb-2 last:border-0"
											>
												<div className="flex items-center gap-1 font-medium">
													<MapPin className="h-3 w-3" />
													Stop {index + 1} (
													{stop.stopType})
												</div>
												<div className="text-xs">
													{stop.street
														? `${stop.street}, `
														: ""}
													{stop.zipCode || ""}{" "}
													{stop.city || ""}
													{stop.country
														? `, ${stop.country}`
														: ""}
												</div>
												<div className="text-xs text-muted-foreground">
													{stop.datetime_start
														? format(
																new Date(
																	stop.datetime_start,
																),
																"dd.MM.yyyy, HH:mm",
															)
														: ""}
													{stop.datetime_end &&
														stop.datetime_start &&
														` - ${format(new Date(stop.datetime_end), "HH:mm")}`}
													{!stop.datetime_start &&
														stop.datetime_end &&
														format(
															new Date(
																stop.datetime_end,
															),
															"dd.MM.yyyy, HH:mm",
														)}
												</div>
											</div>
										))}
									</div>
								</div>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				);
			},
		},
		{
			accessorKey: "first_stop",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="First Stop" />
			),
			cell: ({ row }) => {
				const order = row.original;
				const stops = order.stops || [];

				if (stops.length === 0) {
					return <div className="text-muted-foreground">-</div>;
				}

				const firstStop = stops[0];

				return (
					<div className="flex flex-col">
						<div className="flex items-center">
							<FlagTriangleRight className="h-3.5 w-3.5 mr-1 text-emerald-500" />
							<span>
								{firstStop.zipCode || ""} {firstStop.city || ""}
							</span>
						</div>
						<div className="text-xs text-muted-foreground">
							{firstStop.datetime_start
								? format(
										new Date(firstStop.datetime_start),
										"dd.MM.yyyy, HH:mm",
									)
								: "No date"}
						</div>
					</div>
				);
			},
		},
		{
			accessorKey: "last_stop",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Last Stop" />
			),
			cell: ({ row }) => {
				const order = row.original;
				const stops = order.stops || [];

				if (stops.length <= 1) {
					return <div className="text-muted-foreground">-</div>;
				}

				const lastStop = stops[stops.length - 1];

				return (
					<div className="flex flex-col">
						<div className="flex items-center">
							<Flag className="h-3.5 w-3.5 mr-1 text-red-500" />
							<span>
								{lastStop.zipCode || ""} {lastStop.city || ""}
							</span>
						</div>
						<div className="text-xs text-muted-foreground">
							{lastStop.datetime_start
								? format(
										new Date(lastStop.datetime_start),
										"dd.MM.yyyy, HH:mm",
									)
								: lastStop.datetime_end
									? format(
											new Date(lastStop.datetime_end),
											"dd.MM.yyyy, HH:mm",
										)
									: "No date"}
						</div>
					</div>
				);
			},
		},
		{
			accessorKey: "order_status",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Status" />
			),
			cell: ({ row }) => {
				const status = row.getValue("order_status") as string | null;
				return (
					<div className="flex items-center">
						<span
							className={`px-2 py-1 rounded-md text-xs uppercase font-medium ${
								status === "open"
									? "bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-400"
									: status === "cancelled"
										? "bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400"
										: status === "closed"
											? "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400"
											: status === "confirmed"
												? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800/20 dark:text-yellow-400"
												: "bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400"
							}`}
						>
							{status || "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "invoice_status",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Invoice Status" />
			),
		},

		{
			accessorKey: "createdAt",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Created Date" />
			),
			cell: ({ row }) => {
				const date = row.getValue("createdAt") as string;
				return <div>{new Date(date).toLocaleDateString()}</div>;
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<Order>) => {
					const order = row.original;
					return {
						openView: () => {
							router.push(
								`/app/${params.organizationSlug}/orders/${order.id}`,
							);
						},
						openDelete: () => handleDeleteOrder(row.original),
					};
				},
			},
		},
	];
}
