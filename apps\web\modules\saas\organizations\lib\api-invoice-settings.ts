import type { UpdateInvoiceConfigurationInput } from "@repo/api/src/routes/settings/invoice-settings/types";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Query keys for React Query
export const invoiceConfigurationKeys = {
	all: ["invoice-configuration"] as const,
	details: () => [...invoiceConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...invoiceConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchInvoiceConfiguration = async (organizationId: string) => {
	const response = await apiClient.settings["invoice-settings"].$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch invoice configuration");
	}

	return response.json();
};

// React Query Hooks
export const useInvoiceConfigurationQuery = (
	organizationId: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined ? options.enabled : !!organizationId;

	return useQuery({
		queryKey: invoiceConfigurationKeys.detail(organizationId),
		queryFn: () => fetchInvoiceConfiguration(organizationId),
		enabled: isEnabled && !!organizationId,
	});
};

// Mutation Hooks
export const useUpdateInvoiceConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateInvoiceConfigurationInput, "organizationId">,
		) => {
			const response = await apiClient.settings["invoice-settings"].$put({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to update invoice configuration");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: invoiceConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update invoice configuration",
			);
		},
	});
};
