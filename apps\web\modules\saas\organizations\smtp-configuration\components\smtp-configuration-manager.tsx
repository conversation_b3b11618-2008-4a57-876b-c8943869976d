"use client";

import { useState } from "react";

import { useSmtpConfiguration } from "@saas/organizations/hooks/use-smtp-configuration";
import { SmtpConfigurationForm } from "./smtp-configuration-form";
import { SmtpConfigurationStatus } from "./smtp-configuration-status";

export function SmtpConfigurationManager() {
	const { data: smtpConfiguration, isLoading } = useSmtpConfiguration();
	const [isEditing, setIsEditing] = useState(false);

	// Show loading state
	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-[200px]">
				<div className="animate-spin h-8 w-8 border-b-2 border-primary rounded-full" />
			</div>
		);
	}

	// If no configuration exists or user is creating/editing, show the form
	if (!smtpConfiguration || isEditing) {
		return (
			<SmtpConfigurationForm
				onCancel={
					smtpConfiguration ? () => setIsEditing(false) : undefined
				}
				onSuccess={() => setIsEditing(false)}
			/>
		);
	}

	// If configuration exists and not editing, show the status
	return <SmtpConfigurationStatus onEdit={() => setIsEditing(true)} />;
}
