"use client";

import type { VehicleWithTours } from "@repo/api/src/routes/vehicles/types";
import type { TourSummary } from "@repo/api/src/routes/vehicles/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import { useVehiclesWithToursQuery } from "@saas/tours/lib/api-vehicle-tours";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { Slider } from "@ui/components/slider";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import {
	addDays,
	differenceInMinutes,
	format,
	isSameDay,
	startOfDay,
	subDays,
} from "date-fns";
import {
	ArrowDown,
	ArrowUp,
	Calendar,
	ChevronLeft,
	ChevronRight,
	Truck,
} from "lucide-react";
import {
	type KeyboardEvent,
	useCallback,
	useEffect,
	useRef,
	useState,
} from "react";

// Helper function to transform date strings to Date objects
function transformVehicleData(vehicle: any): VehicleWithTours {
	return {
		...vehicle,
		tours: vehicle.tours.map((tour: any) => ({
			...tour,
			startDate: new Date(tour.startDate),
			endDate: new Date(tour.endDate),
			stops: tour.stops.map((stop: any) => ({
				...stop,
				datetime_start: stop.datetime_start
					? new Date(stop.datetime_start)
					: null,
				datetime_end: stop.datetime_end
					? new Date(stop.datetime_end)
					: null,
			})),
		})),
	};
}

// Format address for display
function formatAddress(stop: any) {
	return `${stop.street}, ${stop.zipCode} ${stop.city}, ${stop.country}`;
}

// Format shortened address for the tour cards
function formatShortAddress(stop: any) {
	return `${stop.zipCode} ${stop.city}, ${stop.country}`;
}

// Format stop time based on time_type
function formatStopTime(stop: any) {
	if (!stop.datetime_start && !stop.datetime_end) {
		return "";
	}

	if (
		stop.time_type === "from-to" &&
		stop.datetime_start &&
		stop.datetime_end
	) {
		return `${format(stop.datetime_start, "HH:mm")} - ${format(stop.datetime_end, "HH:mm")}`;
	}

	if (stop.time_type === "from" && stop.datetime_start) {
		return `From ${format(stop.datetime_start, "HH:mm")}`;
	}

	if (stop.time_type === "to" && stop.datetime_end) {
		return `Until ${format(stop.datetime_end, "HH:mm")}`;
	}

	if (stop.datetime_start) {
		return format(stop.datetime_start, "HH:mm");
	}

	if (stop.datetime_end) {
		return format(stop.datetime_end, "HH:mm");
	}

	return "";
}

// Format stop type
function formatStopType(stopType: string) {
	if (!stopType) {
		return "";
	}

	return stopType.charAt(0).toUpperCase() + stopType.slice(1);
}

export function VehicleCalendar() {
	const { activeOrganization } = useActiveOrganization();
	const [currentDate, setCurrentDate] = useState(() =>
		subDays(new Date(), 1),
	);
	const [daysToShow, setDaysToShow] = useState(7);
	const orgId = activeOrganization?.id || "";

	// State for resizable height
	const [calendarHeight, setCalendarHeight] = useState(400); // Default height
	const [isDragging, setIsDragging] = useState(false);
	const minHeight = 300; // Minimum calendar height
	const maxHeight = 800; // Maximum calendar height
	const containerRef = useRef<HTMLDivElement>(null);

	// Generate array of days based on the slider value
	const calendarDays = Array.from({ length: daysToShow }).map((_, i) =>
		addDays(currentDate, i),
	);

	// Calculate date range for the API call
	const startDate = calendarDays[0];
	const endDate = addDays(calendarDays[calendarDays.length - 1], 1); // Add 1 day to include the full last day

	// Get vehicles and their tours from API
	const { data: vehiclesWithTours, isLoading } = useVehiclesWithToursQuery({
		organizationId: orgId,
		startDate,
		endDate,
	});

	// Tour builder context for setting vehicles
	const { selectVehicle } = useTourBuilder();

	// Navigate to previous period
	const previousPeriod = () => {
		setCurrentDate(addDays(currentDate, -daysToShow));
	};

	// Navigate to next period
	const nextPeriod = () => {
		setCurrentDate(addDays(currentDate, daysToShow));
	};

	// Reset to yesterday
	const resetToYesterday = () => {
		setCurrentDate(subDays(new Date(), 1));
	};

	// Filter tours to only include those in the visible date range
	const filterToursInVisibleRange = (tours: TourSummary[]) => {
		const calendarStart = startOfDay(calendarDays[0]);
		const calendarEnd = addDays(
			startOfDay(calendarDays[calendarDays.length - 1]),
			1,
		);

		return tours.filter((tour) => {
			const tourStart = new Date(tour.startDate);
			const tourEnd = new Date(tour.endDate);

			// Include tour if it overlaps with the calendar range
			return (
				(tourStart <= calendarEnd && tourEnd >= calendarStart) ||
				(tourStart >= calendarStart && tourStart <= calendarEnd) ||
				(tourEnd >= calendarStart && tourEnd <= calendarEnd)
			);
		});
	};

	// Calculate position and width for a tour
	const calculateTourStyle = (tourStart: Date, tourEnd: Date) => {
		const firstDay = startOfDay(calendarDays[0]);
		const totalMinutesInView = daysToShow * 24 * 60;

		// Calculate start position (in percentage)
		let startOffset = 0;
		if (tourStart > firstDay) {
			const minutesFromStart = differenceInMinutes(tourStart, firstDay);
			startOffset = (minutesFromStart / totalMinutesInView) * 100;
		}

		// Calculate width (in percentage)
		let durationMinutes = differenceInMinutes(tourEnd, tourStart);
		// If tour starts before view
		if (tourStart < firstDay) {
			durationMinutes = differenceInMinutes(tourEnd, firstDay);
		}
		// If tour ends after view
		const lastDayEnd = addDays(firstDay, daysToShow);
		if (tourEnd > lastDayEnd) {
			durationMinutes = differenceInMinutes(
				lastDayEnd,
				tourStart > firstDay ? tourStart : firstDay,
			);
		}

		const width = (durationMinutes / totalMinutesInView) * 100;

		return {
			left: `${startOffset}%`,
			width: `${width}%`,
		};
	};

	// Handle clicking on a vehicle row to select it for the tour
	const handleVehicleSelect = (vehicleId: string) => {
		selectVehicle(vehicleId, orgId);
	};

	// Handle keyboard events for accessibility
	const handleKeyDown = (
		e: KeyboardEvent<HTMLDivElement>,
		vehicleId: string,
	) => {
		if (e.key === "Enter" || e.key === " ") {
			e.preventDefault();
			handleVehicleSelect(vehicleId);
		}
	};

	// Format time for display
	const formatTime = (date: Date) => {
		return format(date, "h:mm a");
	};

	// Render a vehicle row
	const renderVehicleRow = (vehicle: VehicleWithTours) => {
		const vehicleLabel = vehicle.licensePlate || "No License Plate";

		// Filter tours to only include those in the visible date range
		const visibleTours = filterToursInVisibleRange(vehicle.tours);

		return (
			<div
				key={vehicle.id}
				className="grid grid-cols-[200px_1fr] border-b last:border-b-0"
			>
				{/* Vehicle Info with button wrapper */}
				<Button
					variant="ghost"
					className="h-auto p-0 w-full justify-start text-left hover:bg-muted/20"
					onClick={() => handleVehicleSelect(vehicle.id)}
					title={`Select vehicle ${vehicleLabel}`}
				>
					<div className="p-3 w-full flex items-center gap-2">
						<Truck className="h-5 w-5 text-muted-foreground flex-shrink-0" />
						<div>
							<div className="font-medium">{vehicleLabel}</div>
							<div className="text-xs text-muted-foreground">
								{vehicle.manufacturer || ""}{" "}
								{vehicle.model || ""} • {vehicle.vehicleType}
							</div>
							{vehicle.driver && (
								<div className="text-xs text-muted-foreground">
									Driver: {vehicle.driver.firstName}{" "}
									{vehicle.driver.lastName}
								</div>
							)}
						</div>
					</div>
				</Button>

				{/* Week Grid for this Vehicle */}
				<div
					className="relative min-h-[80px]"
					style={{
						display: "grid",
						gridTemplateColumns: `repeat(${daysToShow}, 1fr)`,
					}}
				>
					{/* Day columns */}
					{calendarDays.map((day, dayIndex) => (
						<div
							key={dayIndex}
							className={cn(
								"border-r last:border-r-0 h-full",
								isSameDay(day, new Date()) &&
									"bg-blue-50 dark:bg-blue-950/30",
							)}
						/>
					))}

					{/* Tours with precise positioning */}
					<div className="absolute inset-0">
						{visibleTours.map((tour) => {
							// Get first and last stop if they exist
							const sortedStops = [...tour.stops].sort(
								(a, b) =>
									(a.positionTour || 0) -
									(b.positionTour || 0),
							);

							const firstStop =
								sortedStops.length > 0 ? sortedStops[0] : null;
							const lastStop =
								sortedStops.length > 1
									? sortedStops[sortedStops.length - 1]
									: null;

							// Determine start and end times for tour positioning
							// Use stop times if available, otherwise fall back to tour dates
							let effectiveStartDate = new Date(tour.startDate);
							let effectiveEndDate = new Date(tour.endDate);

							// If first stop has start time, use it
							if (firstStop?.datetime_start) {
								effectiveStartDate = new Date(
									firstStop.datetime_start,
								);
							}

							// If last stop has end time, use it (or start time if end is not available)
							if (lastStop) {
								if (lastStop.datetime_end) {
									effectiveEndDate = new Date(
										lastStop.datetime_end,
									);
								} else if (lastStop.datetime_start) {
									// Add 1 hour if only start time is available for end stop
									effectiveEndDate = new Date(
										lastStop.datetime_start,
									);
									effectiveEndDate.setHours(
										effectiveEndDate.getHours() + 1,
									);
								}
							}

							const style = calculateTourStyle(
								effectiveStartDate,
								effectiveEndDate,
							);

							return (
								<TooltipProvider key={tour.id}>
									<Tooltip>
										<TooltipTrigger asChild>
											<Card
												className="absolute top-[5px] bottom-[5px] bg-primary text-primary-foreground p-2 text-xs overflow-hidden rounded-md"
												style={{
													...style,
													fontSize: "0.65rem",
												}}
											>
												<div className="h-full flex flex-col">
													{/* Row 1: Tour Number */}
													<div className="font-medium truncate border-b border-primary-foreground/20 pb-1">
														{tour.tourNumber ||
															`Tour ${tour.id.substring(0, 6)}`}
													</div>

													{/* Row 2: Start Location */}
													<div className="flex items-center gap-1 my-1">
														<ArrowDown className="h-3 w-3 flex-shrink-0 text-blue-300" />
														<div className="min-w-0 flex-1">
															{firstStop ? (
																<div className="flex justify-between items-center">
																	<span className="truncate">
																		{formatShortAddress(
																			firstStop,
																		)}
																	</span>
																	{formatStopTime(
																		firstStop,
																	) && (
																		<span className="text-[10px] whitespace-nowrap ml-1 text-primary-foreground/80">
																			{formatStopTime(
																				firstStop,
																			)}
																		</span>
																	)}
																</div>
															) : (
																<div className="truncate">
																	No starting
																	location
																</div>
															)}
														</div>
													</div>

													{/* Row 3: End Location */}
													<div className="flex items-center gap-1 mt-auto">
														<ArrowUp className="h-3 w-3 flex-shrink-0 text-blue-300" />
														<div className="min-w-0 flex-1">
															{lastStop &&
															firstStop !==
																lastStop ? (
																<div className="flex justify-between items-baseline gap-1">
																	<div className="truncate">
																		{formatShortAddress(
																			lastStop,
																		)}
																	</div>
																	<div className="text-[10px] whitespace-nowrap text-primary-foreground/70">
																		{formatStopTime(
																			lastStop,
																		) || ""}
																	</div>
																</div>
															) : (
																<div className="truncate">
																	Same as
																	start
																</div>
															)}
														</div>
													</div>
												</div>
											</Card>
										</TooltipTrigger>
										<TooltipContent className="max-w-[400px] max-h-[500px] overflow-y-auto p-4">
											<div className="font-medium text-lg mb-2">
												{tour.tourNumber ||
													`Tour ${tour.id.substring(
														0,
														6,
													)}`}
											</div>
											<div className="text-sm mb-4">
												<span className="font-medium">
													Tour Period:
												</span>{" "}
												{firstStop?.datetime_start
													? format(
															new Date(
																firstStop.datetime_start,
															),
															"MMM d HH:mm",
														)
													: format(
															new Date(
																tour.startDate,
															),
															"MMM d HH:mm",
														)}{" "}
												-{" "}
												{lastStop &&
												(lastStop.datetime_end ||
													lastStop.datetime_start)
													? format(
															new Date(
																lastStop.datetime_end ||
																	lastStop.datetime_start ||
																	tour.endDate,
															),
															"MMM d HH:mm",
														)
													: format(
															new Date(
																tour.endDate,
															),
															"MMM d HH:mm",
														)}
											</div>

											{tour.stops.length > 0 && (
												<>
													<div className="text-sm font-medium mb-2">
														All Stops (
														{tour.stops.length}):
													</div>
													<div className="grid gap-3">
														{tour.stops.map(
															(stop, index) => (
																<div
																	key={
																		stop.id
																	}
																	className="border p-2 rounded bg-background"
																>
																	<div className="text-sm font-medium flex justify-between">
																		<span>
																			{formatStopType(
																				stop.stopType,
																			)}{" "}
																			Stop
																			#
																			{index +
																				1}
																		</span>
																		{stop.goodsInformation && (
																			<span className="text-xs bg-muted px-2 py-0.5 rounded-full">
																				{
																					stop.goodsInformation
																				}
																			</span>
																		)}
																	</div>
																	<div className="text-sm mt-1">
																		{formatAddress(
																			stop,
																		)}
																	</div>
																	{formatStopTime(
																		stop,
																	) && (
																		<div className="text-xs mt-1">
																			{formatStopTime(
																				stop,
																			)}
																		</div>
																	)}
																	{stop.order && (
																		<>
																			<div className="text-xs mt-1 font-medium">
																				Order:{" "}
																				{
																					stop
																						.order
																						.order_number
																				}
																			</div>
																			{stop
																				.order
																				.customer && (
																				<div className="text-xs mt-0.5">
																					Customer:{" "}
																					{
																						stop
																							.order
																							.customer
																							.nameLine1
																					}
																				</div>
																			)}
																		</>
																	)}
																	{(
																		stop as any
																	)
																		.entrance_number && (
																		<div className="text-xs mt-1">
																			Entrance:{" "}
																			{
																				(
																					stop as any
																				)
																					.entrance_number
																			}
																		</div>
																	)}
																	{(
																		stop as any
																	)
																		.addressSupplement && (
																		<div className="text-xs mt-1">
																			Note:{" "}
																			{
																				(
																					stop as any
																				)
																					.addressSupplement
																			}
																		</div>
																	)}
																</div>
															),
														)}
													</div>
												</>
											)}
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							);
						})}
					</div>
				</div>
			</div>
		);
	};

	// Handle resize start
	const handleResizeStart = (e: React.MouseEvent) => {
		e.preventDefault();
		setIsDragging(true);
	};

	// Handle resize during drag
	const handleResize = useCallback(
		(e: MouseEvent) => {
			if (!isDragging) {
				return;
			}

			// Get the container's position
			const container = containerRef.current;
			if (!container) {
				return;
			}

			const rect = container.getBoundingClientRect();
			// Calculate new height based on mouse Y position
			const newHeight = Math.max(
				minHeight,
				Math.min(maxHeight, e.clientY - rect.top),
			);
			setCalendarHeight(newHeight);
		},
		[isDragging],
	);

	// Handle resize end
	const handleResizeEnd = useCallback(() => {
		setIsDragging(false);
	}, []);

	// Set up global event listeners for resize
	useEffect(() => {
		if (isDragging) {
			window.addEventListener("mousemove", handleResize);
			window.addEventListener("mouseup", handleResizeEnd);
			// Add touch events for mobile support
			window.addEventListener("touchmove", (e) =>
				handleResize(e.touches[0] as unknown as MouseEvent),
			);
			window.addEventListener("touchend", handleResizeEnd);
		}

		return () => {
			window.removeEventListener("mousemove", handleResize);
			window.removeEventListener("mouseup", handleResizeEnd);
			window.removeEventListener("touchmove", (e) =>
				handleResize(e.touches[0] as unknown as MouseEvent),
			);
			window.removeEventListener("touchend", handleResizeEnd);
		};
	}, [isDragging, handleResize, handleResizeEnd]);

	return (
		<Card
			ref={containerRef}
			className="flex flex-col relative"
			style={{ height: `${calendarHeight}px` }}
		>
			<CardHeader className="shrink-0 flex flex-row items-center justify-between pb-0">
				<CardTitle className="flex items-center gap-2">
					<Calendar className="h-5 w-5 flex-shrink-0" />
					<span className="truncate">
						{format(calendarDays[0], "MMMM d, yyyy")} -{" "}
						{format(
							calendarDays[calendarDays.length - 1],
							"MMMM d, yyyy",
						)}
					</span>
				</CardTitle>
				<div className="flex justify-end items-center gap-4">
					{/* Days Slider */}
					<div className="flex items-center gap-2">
						<div className="text-sm font-medium whitespace-nowrap">
							Days:
						</div>
						<Slider
							value={[daysToShow]}
							min={1}
							max={14}
							step={1}
							onValueChange={(value) => setDaysToShow(value[0])}
							className="w-32 md:w-40"
						/>
						<div className="text-sm font-medium min-w-[24px] text-center">
							{daysToShow}
						</div>
					</div>

					<div className="flex gap-2">
						<Button
							variant="outline"
							size="icon"
							onClick={previousPeriod}
						>
							<ChevronLeft className="h-4 w-4" />
						</Button>
						<Button variant="outline" onClick={resetToYesterday}>
							Today
						</Button>
						<Button
							variant="outline"
							size="icon"
							onClick={nextPeriod}
						>
							<ChevronRight className="h-4 w-4" />
						</Button>
					</div>
				</div>
			</CardHeader>

			<CardContent className="p-4 flex-1 flex flex-col min-h-0">
				{/* Calendar Grid */}
				{isLoading ? (
					// Loading state
					<div className="border rounded-lg overflow-hidden flex-1">
						<div className="grid grid-cols-[200px_1fr] border-b">
							<Skeleton className="h-16 w-full" />
							<Skeleton className="h-16 w-full" />
						</div>
						{Array.from({ length: 3 }).map((_, index) => (
							<div
								key={index}
								className="grid grid-cols-[200px_1fr] border-b"
							>
								<Skeleton className="h-20 w-full" />
								<Skeleton className="h-20 w-full" />
							</div>
						))}
					</div>
				) : (
					/* Calendar Grid */
					<div className="border rounded-lg overflow-hidden overflow-y-auto flex-1">
						{/* Days Header */}
						<div className="grid grid-cols-[200px_1fr] border-b sticky top-0 bg-background z-10">
							<div className="p-3 font-medium bg-muted/50 border-r">
								Vehicles
							</div>
							<div
								className="grid"
								style={{
									gridTemplateColumns: `repeat(${daysToShow}, 1fr)`,
								}}
							>
								{calendarDays.map((day, index) => (
									<div
										key={index}
										className={cn(
											"p-3 text-center font-medium border-r last:border-r-0",
											isSameDay(day, new Date()) &&
												"bg-blue-50 dark:bg-blue-950/30",
										)}
									>
										<div>{format(day, "EEE")}</div>
										<div className="text-sm">
											{format(day, "MMM d")}
										</div>
									</div>
								))}
							</div>
						</div>

						{/* Vehicle Rows */}
						<div>
							{vehiclesWithTours?.map((vehicle) =>
								renderVehicleRow(transformVehicleData(vehicle)),
							)}

							{vehiclesWithTours?.length === 0 && (
								<div className="p-8 text-center text-muted-foreground">
									No vehicles found with tours in this period
								</div>
							)}
						</div>
					</div>
				)}
			</CardContent>

			{/* Resize handle */}
			<div
				className="absolute bottom-0 left-0 right-0 flex justify-center items-center z-10"
				onMouseDown={handleResizeStart}
				onTouchStart={(e) => {
					e.preventDefault();
					setIsDragging(true);
				}}
			>
				<div className="w-10 h-5 flex flex-col justify-center items-center bg-background rounded-t-md shadow-md cursor-ns-resize hover:bg-gray-100 transition-colors">
					<div className="w-4 h-0.5 bg-gray-400 rounded-full mb-1" />
					<div className="w-4 h-0.5 bg-gray-400 rounded-full" />
				</div>
			</div>
		</Card>
	);
}
