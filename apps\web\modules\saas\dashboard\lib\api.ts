import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

// Define params type for the fetch function
type FetchVehiclesWithToursParams = {
	organizationId: string;
	page?: number;
	limit?: number;
};

// Same params type for available vehicles
type FetchAvailableVehiclesParams = {
	organizationId: string;
	page?: number;
	limit?: number;
};

// Params type for available orders
type FetchAvailableOrdersParams = {
	organizationId: string;
	page?: number;
	limit?: number;
};

// Define query keys
export const dashboardKeys = {
	all: ["dashboard"] as const,
	vehicles: (params: FetchVehiclesWithToursParams) =>
		[...dashboardKeys.all, "vehicles", params] as const,
	availableVehicles: (params: FetchAvailableVehiclesParams) =>
		[...dashboardKeys.all, "available-vehicles", params] as const,
	availableOrders: (params: FetchAvailableOrdersParams) =>
		[...dashboardKeys.all, "available-orders", params] as const,
};

// Fetch function - let TypeScript infer the return type
export const fetchVehiclesWithTours = async (
	params: FetchVehiclesWithToursParams,
) => {
	const response = await apiClient.dashboard.vehicles.$get({
		query: {
			organizationId: params.organizationId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch dashboard vehicles data");
	}

	return response.json();
};

// Fetch function for available vehicles
export const fetchAvailableVehicles = async (
	params: FetchAvailableVehiclesParams,
) => {
	const response = await apiClient.dashboard["available-vehicles"].$get({
		query: {
			organizationId: params.organizationId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch available vehicles data");
	}

	return response.json();
};

// Fetch function for available orders
export const fetchAvailableOrders = async (
	params: FetchAvailableOrdersParams,
) => {
	const response = await apiClient.dashboard["available-orders"].$get({
		query: {
			organizationId: params.organizationId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch available orders data");
	}

	return response.json();
};

// React Query hook
export const useVehiclesWithToursQuery = (
	params: FetchVehiclesWithToursParams,
) => {
	return useQuery({
		queryKey: dashboardKeys.vehicles(params),
		queryFn: () => fetchVehiclesWithTours(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

// React Query hook for available vehicles
export const useAvailableVehiclesQuery = (
	params: FetchAvailableVehiclesParams,
) => {
	return useQuery({
		queryKey: dashboardKeys.availableVehicles(params),
		queryFn: () => fetchAvailableVehicles(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

// React Query hook for available orders
export const useAvailableOrdersQuery = (params: FetchAvailableOrdersParams) => {
	return useQuery({
		queryKey: dashboardKeys.availableOrders(params),
		queryFn: () => fetchAvailableOrders(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

// Helper function to format time (HH:MM)
export const formatTime = (date: string | Date | null | undefined): string => {
	if (!date) {
		return "--:--";
	}
	const dateObj = typeof date === "string" ? new Date(date) : date;
	return dateObj.toLocaleTimeString([], {
		hour: "2-digit",
		minute: "2-digit",
		hour12: false,
	});
};

// Helper function to format address from stop
export const formatStopAddress = (stop: any): string => {
	if (!stop) {
		return "N/A";
	}

	const parts = [stop.street, stop.zipCode, stop.city, stop.country].filter(
		Boolean,
	);

	return parts.join(", ") || "N/A";
};

// Helper to determine the stop time display based on time_type
export const getStopTimeDisplay = (stop: any): string => {
	if (!stop || !stop.time_type) {
		return "N/A";
	}

	switch (stop.time_type) {
		case "latest":
			return `By ${formatTime(stop.datetime_start)}`;
		case "from":
			return `From ${formatTime(stop.datetime_start)}`;
		case "exact":
			return formatTime(stop.datetime_start);
		case "from-to":
			return `${formatTime(stop.datetime_start)} - ${formatTime(stop.datetime_end)}`;
		case "daily":
		case "continuous":
			return "All day";
		default:
			return formatTime(stop.datetime_start);
	}
};
