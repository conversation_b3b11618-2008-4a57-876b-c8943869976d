"use client";

import { InvoiceFormDialog } from "@saas/invoices/components/invoice-form-dialog";
import { useColumns } from "@saas/invoices/components/invoices-table/columns";
import { InvoicesUIProvider } from "@saas/invoices/context/invoices-ui-context";
import { useInvoicesUI } from "@saas/invoices/context/invoices-ui-context";
import { useInvoices } from "@saas/invoices/hooks/use-invoice";
import { DataTable } from "@saas/shared/components/data-table";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Loader2, Plus, Receipt } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface InvoicesPanelProps {
	counterpartyId: string;
}

export function InvoicesPanel({ counterpartyId }: InvoicesPanelProps) {
	const t = useTranslations();
	const [pageSize, setPageSize] = useState(5);
	const [page, setPage] = useState(1);

	// Use the invoices hook to fetch data filtered by counterpartyId
	const {
		data,
		isLoading,
		search,
		setSearch,
		sorting,
		setSorting,
		setCustomerId,
		refetch,
	} = useInvoices();

	// Set the customerId to the counterpartyId prop
	useEffect(() => {
		setSearch("");
		setPage(1);
		setCustomerId(counterpartyId);
	}, [counterpartyId, setSearch, setPage, setCustomerId]);

	// Convert date strings to Date objects with explicit type conversion
	const rawItems = data?.items ?? [];
	const invoices = rawItems.map((item: any) => ({
		...item,
		createdAt: new Date(item.createdAt),
		invoice_date: item.invoice_date ? new Date(item.invoice_date) : null,
		due_date: item.due_date ? new Date(item.due_date) : null,
		payment_date: item.payment_date ? new Date(item.payment_date) : null,
		financialData: item.financialData || [],
	}));

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Invoices</CardTitle>
			</CardHeader>
			<CardContent>
				<InvoicesUIProvider onInvoiceDeleted={refetch}>
					<InvoicesPanelContent
						invoices={invoices}
						isLoading={isLoading}
						search={search}
						setSearch={setSearch}
						page={page}
						setPage={setPage}
						pageSize={pageSize}
						setPageSize={setPageSize}
						data={data}
						sorting={sorting}
						setSorting={setSorting}
						counterpartyId={counterpartyId}
						refetch={refetch}
					/>
				</InvoicesUIProvider>
			</CardContent>
		</Card>
	);
}

// Content component inside which we can use the InvoicesUI hooks
interface InvoicesPanelContentProps extends DataTableWrapperProps {
	counterpartyId: string;
	refetch: () => void;
}

function InvoicesPanelContent({
	invoices,
	isLoading,
	search,
	setSearch,
	page,
	setPage,
	pageSize,
	setPageSize,
	data,
	sorting,
	setSorting,
	counterpartyId,
	refetch,
}: InvoicesPanelContentProps) {
	const { isCreateDialogOpen, setCreateDialogOpen } = useInvoicesUI();
	const t = useTranslations();

	// Empty state component
	const EmptyState = () => (
		<div className="flex flex-col items-center justify-center py-12 text-center">
			<Receipt className="h-12 w-12 text-muted-foreground mb-4" />
			<h3 className="text-lg font-medium mb-2">No Invoices Yet</h3>
			<p className="text-muted-foreground mb-6 max-w-md">
				This contact doesn't have any invoices yet. Create your first
				invoice to start tracking payments.
			</p>
			{/* <Button onClick={() => setCreateDialogOpen(true)}>
				<Plus className="h-4 w-4 mr-2" />
				Create Invoice
			</Button> */}
		</div>
	);

	return (
		<>
			<div className="flex justify-end mb-4">
				<Button size="sm" onClick={() => setCreateDialogOpen(true)}>
					<Plus className="h-4 w-4 mr-2" />
					Create Invoice
				</Button>
			</div>

			{invoices.length === 0 && !isLoading ? (
				<EmptyState />
			) : (
				<DataTableWrapper
					invoices={invoices}
					isLoading={isLoading}
					search={search}
					setSearch={setSearch}
					page={page}
					setPage={setPage}
					pageSize={pageSize}
					setPageSize={setPageSize}
					data={data}
					sorting={sorting}
					setSorting={setSorting}
				/>
			)}
			<InvoicePdfDialog />
			<InvoiceFormDialog
				open={isCreateDialogOpen}
				onOpenChange={setCreateDialogOpen}
				onSuccess={refetch}
			/>
		</>
	);
}

// Separate component inside which we can use the useColumns hook
interface DataTableWrapperProps {
	invoices: any[];
	isLoading: boolean;
	search: string;
	setSearch: (search: string) => void;
	page: number;
	setPage: (page: number) => void;
	pageSize: number;
	setPageSize: (pageSize: number) => void;
	data: any;
	sorting: any;
	setSorting: (sorting: any) => void;
}

function DataTableWrapper({
	invoices,
	isLoading,
	search,
	setSearch,
	page,
	setPage,
	pageSize,
	setPageSize,
	data,
	sorting,
	setSorting,
}: DataTableWrapperProps) {
	const columns = useColumns();

	return (
		<DataTable
			columns={columns}
			data={invoices}
			defaultColumnVisibility={{
				id: false,
				createdAt: false,
				payment_date: false,
				customer_name: false, // Hide customer column since we're already in customer context
			}}
			onSearch={setSearch}
			searchValue={search}
			searchPlaceholder="Search invoices..."
			pagination={{
				page,
				setPage,
				pageSize,
				setPageSize,
				totalPages: data?.totalPages ?? 1,
				total: data?.total ?? 0,
			}}
			isLoading={isLoading}
			sorting={sorting}
			onSortingChange={setSorting}
			manualSorting={true}
			shortcutsScope="customer-invoices-shortcuts"
		/>
	);
}

// PDF viewer dialog component
function InvoicePdfDialog() {
	const {
		isViewPdfDialogOpen,
		setViewPdfDialogOpen,
		viewInvoiceId,
		viewInvoicePdfData,
		isLoadingPdf,
	} = useInvoicesUI();

	return (
		<Dialog
			open={isViewPdfDialogOpen}
			onOpenChange={(open) => {
				if (!open) {
					setViewPdfDialogOpen(false);
				}
			}}
		>
			<DialogContent className="max-w-5xl max-h-[90vh]">
				<DialogHeader>
					<DialogTitle>Invoice Details</DialogTitle>
				</DialogHeader>
				<div className="h-[80vh]">
					{isLoadingPdf ? (
						<div className="flex items-center justify-center h-full">
							<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
						</div>
					) : viewInvoicePdfData ? (
						<iframe
							src={`data:application/pdf;base64,${viewInvoicePdfData}`}
							width="100%"
							height="100%"
							title="Invoice Details"
						/>
					) : (
						<div className="flex items-center justify-center h-full text-muted-foreground">
							Failed to load invoice PDF
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
