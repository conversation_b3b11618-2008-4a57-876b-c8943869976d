"use client";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import { useStops } from "@saas/tours/hooks/use-stops";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsList, TabsTrigger } from "@ui/components/tabs";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import {
	AlertTriangle,
	Clock,
	Info,
	Loader2,
	MapIcon,
	MapPin,
	Package,
	Plus,
	Search,
	TruckIcon,
	Weight,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { CraneIcon } from "./icons/crane-icon";

type ViewMode = "stops" | "orders";

export function StopSearchPanel() {
	const { activeOrganization } = useActiveOrganization();
	const {
		addStop,
		stops: tourStops,
		isSaving,
		// Add map bounds and filter flag from context
		mapBounds,
		filterStopsByMap,
	} = useTourBuilder();
	const [searchQuery, setSearchQuery] = useState("");
	const [stopType, setStopType] = useState<string | undefined>(undefined);
	const [viewMode, setViewMode] = useState<ViewMode>("orders");

	// Use mapBounds directly only if filterStopsByMap is true
	const currentMapBounds = filterStopsByMap ? mapBounds : null;

	// Stabilize map bounds to prevent micro-adjustments from triggering API calls
	const stabilizedMapBounds = useMemo(() => {
		if (!currentMapBounds) {
			return null;
		}

		// Round to 4 decimal places (about 11 meters precision)
		// This prevents tiny map adjustments from triggering new queries
		return {
			minLat: Math.round(currentMapBounds.minLat * 10000) / 10000,
			maxLat: Math.round(currentMapBounds.maxLat * 10000) / 10000,
			minLng: Math.round(currentMapBounds.minLng * 10000) / 10000,
			maxLng: Math.round(currentMapBounds.maxLng * 10000) / 10000,
		};
	}, [
		currentMapBounds?.minLat,
		currentMapBounds?.maxLat,
		currentMapBounds?.minLng,
		currentMapBounds?.maxLng,
	]);

	// Log the map bounds being used for filtering
	useEffect(() => {
		if (stabilizedMapBounds) {
			console.log(
				"StopSearchPanel: Preparing to filter with stabilized map bounds:",
				stabilizedMapBounds,
			);
		} else if (filterStopsByMap) {
			console.log(
				"StopSearchPanel: Filter by map is ON, but stabilizedMapBounds is null.",
			);
		} else {
			console.log("StopSearchPanel: Filter by map is OFF.");
		}
	}, [stabilizedMapBounds, filterStopsByMap]);

	const {
		data: stopsData,
		isLoading,
		search,
		setSearch,
		refetch,
	} = useStops({
		isUnassigned: true,
		stopType: stopType as "loading" | "unloading" | undefined,
		entityType: "order",
		usePrioritySort: true, // Enable priority-based sorting
		// Use backend grouping when in orders view
		groupByOrder: viewMode === "orders",
		// Add map bounds filtering when enabled and bounds are available
		...(stabilizedMapBounds
			? {
					minLat: stabilizedMapBounds.minLat,
					maxLat: stabilizedMapBounds.maxLat,
					minLng: stabilizedMapBounds.minLng,
					maxLng: stabilizedMapBounds.maxLng,
				}
			: {}),
	});

	// Log the results after data is fetched
	useEffect(() => {
		if (filterStopsByMap && stopsData) {
			console.log(
				`Filtered stops: ${stopsData.items?.length || 0} results with map bounds filtering enabled`,
			);
		}
	}, [filterStopsByMap, stopsData]);

	// Refresh when changing view mode or when map bounds filter changes
	useEffect(() => {
		refetch();
	}, [viewMode, refetch, filterStopsByMap]);

	// Handle search input
	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		setSearchQuery(value);
		setSearch(value);
	};

	// Check if a stop is already in the tour
	const isStopInTour = (stopId: string) => {
		return tourStops.some((tourStop) => tourStop.id === stopId);
	};

	// Filter out stops that are already in the tour
	const availableStops =
		stopsData?.items?.filter((stop) => !isStopInTour(stop.id)) || [];

	// Group stops by order for orders view
	// Since the stops are already priority-sorted from the backend,
	// we maintain that order within our grouping
	const orderGroups = useMemo(() => {
		if (!availableStops.length) {
			return [];
		}

		const groups: { [orderId: string]: any[] } = {};

		// Group stops by orderId while preserving original order from API
		availableStops.forEach((stop) => {
			if (stop.orderId && stop.order) {
				if (!groups[stop.orderId]) {
					groups[stop.orderId] = [];
				}
				groups[stop.orderId].push(stop);
			}
		});

		// Calculate priority for each order based on its first stop
		const now = new Date();
		return (
			Object.entries(groups)
				.map(([orderId, stops]) => {
					// Get the first stop by original sort order (priority)
					const firstStop = stops[0];

					// Calculate order priority (for debugging)
					let orderPriority = 0;
					if (firstStop.datetime_start) {
						const startDate = new Date(firstStop.datetime_start);
						if (startDate < now) {
							// Past/overdue stops have highest priority
							orderPriority =
								2000000000000 +
								(now.getTime() - startDate.getTime());
						} else {
							// Future stops have priority based on soonest deadline
							orderPriority =
								1000000000000 -
								(startDate.getTime() - now.getTime());
						}
					}

					return {
						orderId,
						orderNumber: stops[0]?.order?.order_number || "N/A",
						customerName:
							stops[0]?.order?.customer?.nameLine1 ||
							"Unknown customer",
						stopsCount: stops.length,
						firstStop: stops[0],
						lastStop:
							stops.length > 1 ? stops[stops.length - 1] : null,
						allStops: stops,
						priority: orderPriority, // Store priority for potential debugging
					};
				})
				// Sort orders by their first stop's priority (same logic as backend)
				.sort((a, b) => b.priority - a.priority)
		);
	}, [availableStops]);

	// Handle adding a stop to the tour
	const handleAddStop = (stop: any) => {
		// Pass the stop with required organizationId
		addStop({
			...stop,
			organizationId: activeOrganization?.id || "",
		});
	};

	// Handle adding all stops from an order
	const handleAddOrderStops = (orderStops: any[]) => {
		orderStops.forEach((stop) => {
			if (!isStopInTour(stop.id)) {
				addStop({
					...stop,
					organizationId: activeOrganization?.id || "",
				});
			}
		});
	};

	return (
		<Card className="h-full flex flex-col min-h-0 overflow-hidden">
			<CardHeader className="shrink-0">
				<CardTitle className="flex items-center justify-between">
					<span>Available Stops</span>
					{filterStopsByMap && (
						<div className="flex items-center text-sm font-normal text-muted-foreground">
							<MapIcon className="h-3.5 w-3.5 mr-1" />
							<span>Filtered by map view</span>
						</div>
					)}
				</CardTitle>
			</CardHeader>

			<CardContent className="p-0 flex-1 min-h-0 overflow-hidden flex flex-col">
				<div className="px-6 flex flex-col flex-1 min-h-0 overflow-hidden">
					<div className="space-y-3 mb-4 flex-shrink-0">
						<Tabs
							defaultValue="orders"
							onValueChange={(value) =>
								setViewMode(value as ViewMode)
							}
							className="mb-2"
						>
							<TabsList className="grid w-full grid-cols-2">
								<TabsTrigger value="orders">Orders</TabsTrigger>
								<TabsTrigger value="stops">
									Individual Stops
								</TabsTrigger>
							</TabsList>
						</Tabs>

						<div className="relative">
							<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Search stops..."
								className="pl-8"
								value={searchQuery}
								onChange={handleSearchChange}
								disabled={isSaving}
							/>
						</div>

						{viewMode === "stops" && (
							<Select
								value={stopType || "all"}
								onValueChange={(value) =>
									setStopType(
										value === "all" ? undefined : value,
									)
								}
								disabled={isSaving}
							>
								<SelectTrigger>
									<SelectValue placeholder="Filter by type" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">
										All Types
									</SelectItem>
									<SelectItem value="loading">
										Loading
									</SelectItem>
									<SelectItem value="unloading">
										Unloading
									</SelectItem>
								</SelectContent>
							</Select>
						)}
					</div>

					<div className="flex-1 min-h-0 overflow-hidden flex flex-col">
						{isLoading ? (
							<div className="h-full flex items-center justify-center">
								<Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
							</div>
						) : viewMode === "stops" ? (
							// Individual Stops View
							availableStops.length > 0 ? (
								<div className="flex-1 min-h-0 pr-4 pb-4 overflow-y-auto">
									{availableStops.map((stop) => (
										<StopCard
											key={stop.id}
											stop={stop}
											isInTour={false}
											onAdd={() => handleAddStop(stop)}
											disabled={isSaving}
										/>
									))}
								</div>
							) : (
								<div className="h-full flex items-center justify-center">
									<p className="text-muted-foreground">
										{stopsData?.items &&
										stopsData.items.length > 0
											? "All stops have been added to the tour"
											: filterStopsByMap
												? "No unassigned stops found in current map view"
												: "No unassigned stops found"}
									</p>
								</div>
							)
						) : // Orders View
						orderGroups.length > 0 ? (
							<div className="flex-1 min-h-0 pr-4 pb-4 overflow-y-auto">
								{orderGroups.map((orderGroup) => (
									<OrderCard
										key={orderGroup.orderId}
										order={orderGroup}
										onAddAll={() =>
											handleAddOrderStops(
												orderGroup.allStops,
											)
										}
										disabled={isSaving}
									/>
								))}
							</div>
						) : (
							<div className="h-full flex items-center justify-center">
								<p className="text-muted-foreground">
									{filterStopsByMap
										? "No orders with unassigned stops found in current map view"
										: "No orders with unassigned stops found"}
								</p>
							</div>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

interface OrderCardProps {
	order: {
		orderId: string;
		orderNumber: string;
		customerName: string;
		stopsCount: number;
		firstStop: any;
		lastStop: any | null;
		allStops: any[];
	};
	onAddAll: () => void;
	disabled?: boolean;
}

function OrderCard({ order, onAddAll, disabled }: OrderCardProps) {
	return (
		<div className="border rounded-md p-3 mb-3 overflow-hidden">
			<div className="flex items-center justify-between mb-2">
				<div>
					<div className="text-base font-medium">
						Order: {order.orderNumber}
					</div>
					<div className="text-sm font-medium text-primary">
						{order.customerName}
					</div>
				</div>
				<Button
					size="sm"
					variant="primary"
					onClick={onAddAll}
					disabled={disabled}
				>
					<Plus className="h-3 w-3 mr-1" />
					Add All Stops ({order.stopsCount})
				</Button>
			</div>

			<div className="border-t border-dashed border-gray-200 pt-2 mt-2 space-y-3">
				{order.firstStop && (
					<div>
						<div className="flex items-center mb-1">
							<Badge
								status={
									order.firstStop.stopType === "loading"
										? "success"
										: "info"
								}
							>
								{order.firstStop.stopType}
							</Badge>
							<span className="ml-2 text-sm font-medium">
								First Stop
							</span>
						</div>
						<div className="grid grid-cols-[16px_1fr] gap-x-2 text-sm">
							<MapPin className="h-4 w-4 text-muted-foreground" />
							<span className="truncate">
								{order.firstStop.street && order.firstStop.city
									? `${order.firstStop.street}, ${order.firstStop.city}`
									: "Address not available"}
							</span>

							{order.firstStop.datetime_start && (
								<>
									<Clock className="h-4 w-4 text-muted-foreground" />
									<span className="text-muted-foreground">
										{format(
											new Date(
												order.firstStop.datetime_start,
											),
											"PP p",
										)}
									</span>
								</>
							)}
						</div>
					</div>
				)}

				{order.lastStop && order.stopsCount > 1 && (
					<div>
						<div className="flex items-center mb-1">
							<Badge
								status={
									order.lastStop.stopType === "loading"
										? "success"
										: "info"
								}
							>
								{order.lastStop.stopType}
							</Badge>
							<span className="ml-2 text-sm font-medium">
								Last Stop
							</span>
						</div>
						<div className="grid grid-cols-[16px_1fr] gap-x-2 text-sm">
							<MapPin className="h-4 w-4 text-muted-foreground" />
							<span className="truncate">
								{order.lastStop.street && order.lastStop.city
									? `${order.lastStop.street}, ${order.lastStop.city}`
									: "Address not available"}
							</span>

							{order.lastStop.datetime_end && (
								<>
									<Clock className="h-4 w-4 text-muted-foreground" />
									<span className="text-muted-foreground">
										{format(
											new Date(
												order.lastStop.datetime_end,
											),
											"PP p",
										)}
									</span>
								</>
							)}
						</div>
					</div>
				)}

				{order.stopsCount > 2 && (
					<div className="text-sm text-muted-foreground italic">
						+ {order.stopsCount - 2} more stops in between
					</div>
				)}
			</div>
		</div>
	);
}

interface StopCardProps {
	stop: any; // Would use a proper type from API
	isInTour: boolean;
	onAdd: () => void;
	disabled?: boolean;
}

function StopCard({ stop, isInTour, onAdd, disabled }: StopCardProps) {
	const customerName = stop.order?.customer?.nameLine1 || "Unknown customer";

	return (
		<div className="border rounded-md p-2 mb-2 overflow-hidden">
			<div className="flex items-center justify-between mb-1">
				<div className="flex items-center space-x-2">
					<Badge
						status={
							stop.stopType === "loading" ? "success" : "info"
						}
					>
						{stop.stopType}
					</Badge>
					<span className="text-sm font-medium">
						{stop.order?.order_number
							? `Order: ${stop.order.order_number}`
							: "Not Set"}
					</span>
				</div>
				<Button
					size="sm"
					variant={isInTour ? "outline" : "primary"}
					onClick={onAdd}
					disabled={isInTour || disabled}
				>
					{isInTour ? (
						"Added"
					) : (
						<>
							<Plus className="h-3 w-3 mr-1" />
							Add
						</>
					)}
				</Button>
			</div>

			<div className="text-sm font-medium mb-1 text-primary truncate">
				{customerName}
			</div>

			<div className="grid grid-cols-[16px_1fr] gap-x-2 text-sm mb-1">
				<MapPin className="h-4 w-4 text-muted-foreground" />
				<span className="truncate">
					{stop.street && stop.city
						? `${stop.street}, ${stop.city}`
						: "Address not available"}
				</span>

				{(stop.datetime_start || stop.datetime_end) && (
					<>
						<Clock className="h-4 w-4 text-muted-foreground" />
						<span className="text-muted-foreground">
							{stop.datetime_start && (
								<span>
									{format(
										new Date(stop.datetime_start),
										"PP",
									)}
								</span>
							)}
							{stop.datetime_start && stop.datetime_end && (
								<span> - </span>
							)}
							{stop.datetime_end && (
								<span>
									{format(new Date(stop.datetime_end), "PP")}
								</span>
							)}
						</span>
					</>
				)}
			</div>

			{/* Display goods information always */}
			{stop.goods_information && (
				<div className="mt-1 mb-1 text-sm border-t border-dashed border-gray-200 pt-1">
					<span className="font-medium">Goods:</span>{" "}
					<span className="break-words">
						{stop.goods_information}
					</span>
				</div>
			)}

			{/* Key indicators with icons */}
			<div className="flex flex-wrap gap-2 mt-2 mb-2">
				<TooltipProvider>
					{stop.dangerous_goods_nr && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-red-100">
									<AlertTriangle className="h-4 w-4 text-red-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Dangerous goods: {stop.dangerous_goods_nr}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.special_transports_height > 0 ||
						stop.special_transports_width > 0 ||
						stop.special_transports_length > 0) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-amber-100">
									<TruckIcon className="h-4 w-4 text-amber-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Special transport:{" "}
									{stop.special_transports_length}x
									{stop.special_transports_width}x
									{stop.special_transports_height}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.crane_loading || stop.crane_unloading) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-blue-100">
									<CraneIcon className="h-4 w-4 text-blue-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Crane needed for{" "}
									{stop.crane_loading && stop.crane_unloading
										? "loading & unloading"
										: stop.crane_loading
											? "loading"
											: "unloading"}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{stop.special_transports_heavy && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-purple-100">
									<Weight className="h-4 w-4 text-purple-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>Heavy transport required</p>
							</TooltipContent>
						</Tooltip>
					)}

					{stop.pallet_exchange_count > 0 && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-green-100">
									<Package className="h-4 w-4 text-green-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									Pallet exchange:{" "}
									{stop.pallet_exchange_count}{" "}
									{stop.pallet_exchange_type || "units"}
								</p>
							</TooltipContent>
						</Tooltip>
					)}

					{(stop.driver_notes || stop.information_text) && (
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="w-7 h-7 flex items-center justify-center rounded-full bg-gray-100">
									<Info className="h-4 w-4 text-gray-600" />
								</div>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									{stop.driver_notes || stop.information_text}
								</p>
							</TooltipContent>
						</Tooltip>
					)}
				</TooltipProvider>
			</div>

			{/* All details - replacing collapsible */}
			<div className="mt-2 pt-2 border-t border-dashed border-gray-200">
				<div className="space-y-2 text-sm overflow-hidden break-words">
					{(stop.length || stop.width || stop.height) && (
						<div>
							<span className="font-medium">Dimensions:</span>{" "}
							{stop.length}x{stop.width}x{stop.height}{" "}
							{stop.units || "m"}
						</div>
					)}

					{stop.weight && (
						<div>
							<span className="font-medium">Weight:</span>{" "}
							{stop.weight} kg
						</div>
					)}

					{stop.cubic_meters && (
						<div>
							<span className="font-medium">Volume:</span>{" "}
							{stop.cubic_meters} m³
						</div>
					)}

					{stop.loading_meter && (
						<div>
							<span className="font-medium">Loading Meters:</span>{" "}
							{stop.loading_meter}
						</div>
					)}

					{stop.measurement_text && (
						<div>
							<span className="font-medium">
								Measurement Notes:
							</span>{" "}
							{stop.measurement_text}
						</div>
					)}

					{stop.reference_number && (
						<div>
							<span className="font-medium">Reference:</span>{" "}
							{stop.reference_number}
						</div>
					)}

					{stop.neutrality_text && (
						<div>
							<span className="font-medium">Neutrality:</span>{" "}
							{stop.neutrality_text}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
