// Define types for the offers module
import type { LineItem as ApiLineItem } from "@repo/api/src/routes/orders/types";

// Use LineItem from API
export type LineItem = ApiLineItem;

// Counterparty (Customer) type
export interface Counterparty {
	id: string;
	nameLine1?: string | null;
	nameLine2?: string | null;
	street?: string | null;
	zipCode?: string | null;
	city?: string | null;
	country?: string | null;
	email?: string | null;
	phone?: string | null;
	addresses?: CounterpartyAddress[];
}

// Address usage for counterparties
export interface CounterpartyAddress {
	id: string;
	type: string;
	addressId: string;
	counterpartyId: string;
	isDefault: boolean;
	isGlobal: boolean;
	address?: Address;
}

// Physical address
export interface Address {
	id: string;
	street?: string | null;
	addressSupplement?: string | null;
	zipCode?: string | null;
	city?: string | null;
	country?: string | null;
}

// Order type with fields we need
export interface Order {
	id: string;
	customer_order_number?: string | null;
	order_number?: string | null;
	order_status?: string | null;
	dispo_status?: string | null;
	invoice_status?: string | null;
}

// Main offer type used throughout the offers UI
export interface Offer {
	id: string;
	status: string | null;
	valid_until: string | null;
	orderId: string;
	order?: {
		customer_order_number: string | null;
		order_number: string | null;
	};
	offer_type: string | null;
	invoice_type: string | null;
	organizationId: string;
	creatorId: string;
	customerId: string | null;
	customer?: Counterparty | null;
	loading_meter?: number | null;
	cubic_meters?: number | null;
	goods?: string | null;
	weight?: number | null;
	dangerous_goods?: boolean | null;
	special_transports?: boolean | null;
	pallet_exchange?: boolean | null;
	pallet_exchange_type?: string | null;
	pallet_exchange_count?: number | null;
	order_amount?: string | null;
	internal_comment?: string | null;
	prices?: LineItem[];
	stops?: Stop[];
	createdAt: string;
	updatedAt: string;
	offer_number?: string | null;
	decline_reason?: string | null;
	declined_at?: string | null;
	accepted_at?: string | null;
}

// Offer status enum matching the backend
export enum OfferStatus {
	OPEN = "open",
	ACCEPTED = "accepted",
	CANCELLED = "cancelled",
	EXPIRED = "expired",
}

export interface Stop {
	id: string;
	orderId?: string | null;
	offerId?: string | null;
	stopType: string;
	entityType: string;
	post_carriage?: string | null;
	vehicleId?: string | null;
	trailerVehicleId?: string | null;
	driverId?: string | null;
	address_id?: string | null;
	entrance_number?: string | null;
	street?: string | null;
	addressSupplement?: string | null;
	zipCode?: string | null;
	city?: string | null;
	country?: string | null;
	loading_meter?: number | null;
	cubic_meters?: number | null;
	goods_information?: string | null;
	weight?: number | null;
	dangerous_goods_nr?: string | null;
	special_transports_height?: number | null;
	special_transports_width?: number | null;
	special_transports_length?: number | null;
	special_transports_heavy?: boolean | null;
	crane_loading?: boolean | null;
	crane_unloading?: boolean | null;
	length?: number | null;
	width?: number | null;
	height?: number | null;
	units?: string | null;
	measurement_text?: string | null;
	pallet_exchange_type?: string | null;
	pallet_exchange_count?: number | null;
	neutrality_text?: string | null;
	reference_number?: string | null;
	loading_equipment_exchange?: boolean | null;
	driver_notes?: string | null;
	information_text?: string | null;
	time_type?: string | null;
	datetime_start?: string | null;
	datetime_end?: string | null;
	positionTour?: number | null;
	tourId?: string | null;
	createdAt: string;
	updatedAt: string;
}
