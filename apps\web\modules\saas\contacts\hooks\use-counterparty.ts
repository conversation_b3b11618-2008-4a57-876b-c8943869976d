import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";

import type {
	CreateAddressUsageInput,
	CreateContactInput,
	CreateCounterpartyInput,
	CreateCounterpartyWarningInput,
	UpdateAddressUsageInput,
	UpdateContactInput,
	UpdateCounterpartyInput,
	UpdateCounterpartyWarningInput,
} from "@repo/api/src/routes/counterparties/types";
import {} from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useCallback, useState } from "react";
import {
	counterpartyKeys,
	useBlockCounterpartyMutation,
	useCounterpartiesQuery,
	useCounterpartyByIdQuery,
	useCreateCounterpartyMutation,
	useDeleteCounterpartyMutation,
	useUnblockCounterpartyMutation,
	useUpdateCounterpartyMutation,
} from "../lib/api";
import {
	addressKeys,
	useAddressByIdQuery,
	useAddressesQuery,
	useCreateAddressMutation,
	useDeleteAddressMutation,
	useUpdateAddressMutation,
} from "../lib/api-addresses";
import {
	contactKeys,
	useContactsQuery,
	useCreateContactMutation,
	useDeleteContactMutation,
	useUpdateContactMutation,
} from "../lib/api-contacts";
import {
	useCreateWarningMutation,
	useDeleteWarningMutation,
	useUpdateWarningMutation,
	warningKeys,
} from "../lib/api-warnings";

// Use the counterpartyKeys from api.ts
export const counterpartiesListQueryKey = counterpartyKeys.list;
export const counterpartyByIdQueryKey = counterpartyKeys.detail;

// Use the contactKeys from api-contacts.ts
export const contactsListQueryKey = contactKeys.list;
export const contactByIdQueryKey = contactKeys.detail;

// Use the warningKeys from api-warnings.ts
export const warningsListQueryKey = warningKeys.list;
export const warningByIdQueryKey = warningKeys.detail;

// Use the addressKeys from api-addresses.ts
export const addressesListQueryKey = addressKeys.list;
export const addressByIdQueryKey = addressKeys.detail;

export function useCounterparties(options: { includeBlocked?: boolean } = {}) {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	// Debounce search to prevent excessive API calls
	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = useCallback((size: number) => {
		setPageSize(size);
		setPage(1); // Reset to first page when changing page size
	}, []);

	const { data, isLoading, error, refetch } = useCounterpartiesQuery({
		organizationId: activeOrganization?.id || "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		includeBlocked: options.includeBlocked,
	});

	return {
		data,
		isLoading,
		error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch,
	};
}

// Hook to fetch a single counterparty by ID
export function useCounterpartyById(counterpartyId: string) {
	const { activeOrganization } = useActiveOrganization();

	const { data, isLoading, error, refetch } = useCounterpartyByIdQuery(
		activeOrganization?.id,
		counterpartyId,
	);

	return {
		counterparty: data,
		isLoading,
		error,
		refetch,
	};
}

// Hook to fetch contacts for a specific counterparty
export function useCounterpartyContacts(
	counterpartyId: string,
	includeInactive = true,
) {
	const { activeOrganization, loaded } = useActiveOrganization();

	const { data, isLoading, error, refetch } = useContactsQuery(
		activeOrganization?.id || "",
		counterpartyId,
	);

	return {
		contacts: data,
		isLoading,
		error,
		refetch,
	};
}

// Hook to fetch addresses for a specific counterparty
export function useCounterpartyAddresses(
	counterpartyId: string,
	options: { includeBlocked?: boolean } = {},
) {
	const { activeOrganization, loaded } = useActiveOrganization();

	const { data, isLoading, error, refetch } = useAddressesQuery(
		activeOrganization?.id || "",
		counterpartyId,
		options,
	);

	return {
		addresses: data,
		isLoading,
		error,
		refetch,
	};
}

// Hook to fetch a single address by ID
export function useAddressById(addressId: string) {
	const { activeOrganization } = useActiveOrganization();

	const { data, isLoading, error, refetch } = useAddressByIdQuery(
		activeOrganization?.id || "",
		addressId,
	);

	return {
		address: data,
		isLoading,
		error,
		refetch,
	};
}

// Mutations for counterparties
export function useCounterpartyMutations() {
	const { activeOrganization } = useActiveOrganization();

	// For creating counterparties
	const createCounterpartyMutation = useCreateCounterpartyMutation(
		activeOrganization?.id || "",
	);

	// For updating counterparties
	const updateCounterpartyMutation = useUpdateCounterpartyMutation(
		activeOrganization?.id || "",
	);

	// For deleting counterparties
	const deleteCounterpartyMutation = useDeleteCounterpartyMutation(
		activeOrganization?.id || "",
	);

	// For blocking counterparties
	const blockCounterpartyMutation = useBlockCounterpartyMutation(
		activeOrganization?.id || "",
	);

	// For unblocking counterparties
	const unblockCounterpartyMutation = useUnblockCounterpartyMutation(
		activeOrganization?.id || "",
	);

	const createCounterparty = async (data: CreateCounterpartyInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		// Use the mutation from api.ts
		return createCounterpartyMutation.mutateAsync(data);
	};

	const updateCounterparty = async (data: UpdateCounterpartyInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		// Use the mutation from api.ts
		return updateCounterpartyMutation.mutateAsync(data);
	};

	const deleteCounterparty = async (id: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		// Use the mutation from api.ts
		return deleteCounterpartyMutation.mutateAsync(id);
	};

	const blockCounterparty = async (id: string, reason?: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		// Use the mutation from api.ts
		return blockCounterpartyMutation.mutateAsync({ id, reason });
	};

	const unblockCounterparty = async (id: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		// Use the mutation from api.ts
		return unblockCounterpartyMutation.mutateAsync(id);
	};

	return {
		createCounterparty,
		updateCounterparty,
		deleteCounterparty,
		blockCounterparty,
		unblockCounterparty,
		isLoading:
			createCounterpartyMutation.isPending ||
			updateCounterpartyMutation.isPending ||
			deleteCounterpartyMutation.isPending ||
			blockCounterpartyMutation.isPending ||
			unblockCounterpartyMutation.isPending,
	};
}

// Mutations for contacts within a counterparty
export function useContactMutations(counterpartyId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Initialize mutations with organizationId
	const createMutation = useCreateContactMutation(
		organizationId,
		counterpartyId || "",
	);
	const updateMutation = useUpdateContactMutation(
		organizationId,
		counterpartyId || "",
	);
	const deleteMutation = useDeleteContactMutation(
		organizationId,
		counterpartyId || "",
	);

	const createContact = async (
		data: Omit<CreateContactInput, "organizationId">,
	) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!data.counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		// Use the actual counterpartyId to create a contact
		return createMutation.mutateAsync(data);
	};

	const updateContact = async (data: UpdateContactInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!data.counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		try {
			return await updateMutation.mutateAsync(data);
		} catch (error) {
			console.error("Failed to update contact:", error);
			throw error;
		}
	};

	const deleteContact = async (id: string, cpId?: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		const effectiveCounterpartyId = cpId || counterpartyId;
		if (!effectiveCounterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		return deleteMutation.mutateAsync(id);
	};

	return {
		createContact,
		updateContact,
		deleteContact,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Mutations for warnings within a counterparty
export function useWarningMutations(counterpartyId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Initialize mutations with organizationId
	const createMutation = useCreateWarningMutation(
		organizationId,
		counterpartyId || "",
	);
	const updateMutation = useUpdateWarningMutation(
		organizationId,
		counterpartyId || "",
	);
	const deleteMutation = useDeleteWarningMutation(
		organizationId,
		counterpartyId || "",
	);

	const createWarning = async (data: CreateCounterpartyWarningInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!data.counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		return createMutation.mutateAsync(data);
	};

	const updateWarning = async (data: UpdateCounterpartyWarningInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!data.counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		return updateMutation.mutateAsync(data);
	};

	const deleteWarning = async (id: string, cpId?: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		const effectiveCounterpartyId = cpId || counterpartyId;
		if (!effectiveCounterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		return deleteMutation.mutateAsync(id);
	};

	return {
		createWarning,
		updateWarning,
		deleteWarning,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Mutations for addresses within a counterparty
export function useAddressMutations(counterpartyId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Initialize mutations with organizationId
	const createMutation = useCreateAddressMutation(
		organizationId,
		counterpartyId || "",
	);
	const updateMutation = useUpdateAddressMutation(
		organizationId,
		counterpartyId || "",
	);
	const deleteMutation = useDeleteAddressMutation(
		organizationId,
		counterpartyId || "",
	);

	const createAddress = async (data: CreateAddressUsageInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		// The counterpartyId will be added by the mutation function
		return createMutation.mutateAsync(data);
	};

	const updateAddress = async (data: UpdateAddressUsageInput) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		if (!counterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		// The counterpartyId will be added by the mutation function
		return updateMutation.mutateAsync(data);
	};

	const deleteAddress = async (id: string, cpId?: string) => {
		if (!activeOrganization?.id) {
			throw new Error("No active organization");
		}

		const effectiveCounterpartyId = cpId || counterpartyId;
		if (!effectiveCounterpartyId) {
			throw new Error("No counterparty ID provided");
		}

		return deleteMutation.mutateAsync(id);
	};

	return {
		createAddress,
		updateAddress,
		deleteAddress,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

export type inferCounterpartyType = NonNullable<
	ReturnType<typeof useCounterparties>["data"]
>["items"][number];
