"use client";

import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { Crown } from "lucide-react";
import React from "react";
import type { Control } from "react-hook-form";

interface ExpenseBasicInfoSectionProps {
	control: Control<any>;
	ocrPrefilledFields: Set<string>;
}

export function ExpenseBasicInfoSection({
	control,
	ocrPrefilledFields,
}: ExpenseBasicInfoSectionProps) {
	return (
		<div className="space-y-6">
			{/* Supplier Information */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Supplier Information</h3>

				<FormField
					control={control}
					name="supplierId"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="flex items-center gap-2">
								Supplier
								{ocrPrefilledFields.has("supplierId") && (
									<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
								)}
							</FormLabel>
							<FormControl>
								<CounterpartySelector
									value={field.value}
									onChange={field.onChange}
									name={field.name}
									placeholder="Select supplier"
									className={
										ocrPrefilledFields.has("supplierId")
											? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
											: ""
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name="supplier_invoice_number"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="flex items-center gap-2">
								Invoice Number
								{ocrPrefilledFields.has(
									"supplier_invoice_number",
								) && (
									<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
								)}
							</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="Enter supplier invoice number"
									className={
										ocrPrefilledFields.has(
											"supplier_invoice_number",
										)
											? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
											: ""
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Date Information */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Date Information</h3>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<FormField
						control={control}
						name="expense_date"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="flex items-center gap-2">
									Expense Date
									{ocrPrefilledFields.has("expense_date") && (
										<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
									)}
								</FormLabel>
								<FormControl>
									<InputWithDate
										{...field}
										placeholder="Select expense date"
										className={
											ocrPrefilledFields.has(
												"expense_date",
											)
												? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
												: ""
										}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name="due_date"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="flex items-center gap-2">
									Due Date
									{ocrPrefilledFields.has("due_date") && (
										<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
									)}
								</FormLabel>
								<FormControl>
									<InputWithDate
										{...field}
										placeholder="Select due date"
										className={
											ocrPrefilledFields.has("due_date")
												? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
												: ""
										}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name="paid_date"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Paid Date</FormLabel>
								<FormControl>
									<InputWithDate
										{...field}
										placeholder="Select paid date"
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Financial Information */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">Financial Information</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<FormField
						control={control}
						name="currency"
						render={({ field }) => (
							<FormItem>
								<FormLabel className="flex items-center gap-2">
									Currency
									{ocrPrefilledFields.has("currency") && (
										<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
									)}
								</FormLabel>
								<FormControl>
									<Select
										value={field.value}
										onValueChange={field.onChange}
									>
										<SelectTrigger
											className={
												ocrPrefilledFields.has(
													"currency",
												)
													? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
													: ""
											}
										>
											<SelectValue placeholder="Select currency" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="EUR">
												EUR
											</SelectItem>
											<SelectItem value="USD">
												USD
											</SelectItem>
											<SelectItem value="GBP">
												GBP
											</SelectItem>
											<SelectItem value="CHF">
												CHF
											</SelectItem>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name="status"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Status</FormLabel>
								<FormControl>
									<Select
										value={field.value}
										onValueChange={field.onChange}
									>
										<SelectTrigger>
											<SelectValue placeholder="Select status" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="open">
												Open
											</SelectItem>
											<SelectItem value="paid">
												Paid
											</SelectItem>
											<SelectItem value="overdue">
												Overdue
											</SelectItem>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Description and Notes */}
			<div className="space-y-4">
				<h3 className="text-lg font-semibold">
					Additional Information
				</h3>

				<FormField
					control={control}
					name="description"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="flex items-center gap-2">
								Description
								{ocrPrefilledFields.has("description") && (
									<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
								)}
							</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="Enter expense description"
									className={
										ocrPrefilledFields.has("description")
											? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
											: ""
									}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name="notes"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Notes</FormLabel>
							<FormControl>
								<Textarea
									{...field}
									placeholder="Additional notes or comments"
									className="min-h-[80px]"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
}
