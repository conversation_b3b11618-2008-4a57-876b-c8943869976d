"use client";

import { useInvoicesUI } from "@saas/invoices/context/invoices-ui-context";
import {
	type LineItem,
	LineItemsTable,
} from "@saas/shared/components/line-items/line-items-table";
import { Button } from "@ui/components/button";
import { Dialog, DialogContent, DialogTitle } from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useInvoiceMutations } from "../hooks/use-invoice";

export function InvoiceEditDialog() {
	const { isEditDialogOpen, setEditDialogOpen, selectedInvoice } =
		useInvoicesUI();
	const t = useTranslations();
	const [lineItems, setLineItems] = useState<LineItem[]>([]);

	const { adminEditInvoiceLineItems, isLoading } = useInvoiceMutations({
		onSuccess: () => {
			handleSuccess();
		},
	});

	// Form for billing address
	const form = useForm({
		defaultValues: {
			billing_street: selectedInvoice?.billing_street || "",
			billing_addressSupplement:
				selectedInvoice?.billing_addressSupplement || "",
			billing_zipCode: selectedInvoice?.billing_zipCode || "",
			billing_city: selectedInvoice?.billing_city || "",
			billing_country: selectedInvoice?.billing_country || "",
		},
	});

	// Reset form when invoice changes
	useEffect(() => {
		if (selectedInvoice) {
			form.reset({
				billing_street: selectedInvoice.billing_street || "",
				billing_addressSupplement:
					selectedInvoice.billing_addressSupplement || "",
				billing_zipCode: selectedInvoice.billing_zipCode || "",
				billing_city: selectedInvoice.billing_city || "",
				billing_country: selectedInvoice.billing_country || "",
			});

			// Convert invoice line items to the format expected by LineItemsTable
			if (selectedInvoice && Array.isArray(selectedInvoice.lineItems)) {
				setLineItems(
					selectedInvoice.lineItems.map((item) => ({
						...item,
						// Add any other fields needed for the LineItemsTable component
					})),
				);
			}
		}
	}, [selectedInvoice, form]);

	const handleSuccess = () => {
		setEditDialogOpen(false);
	};

	const handleCancel = () => {
		setEditDialogOpen(false);
	};

	const handleSubmit = async (addressData: any) => {
		try {
			if (!selectedInvoice) {
				return;
			}

			// Prepare line items for update
			const lineItemsToUpdate = lineItems.map((item) => ({
				id: item.id,
				description: item.description,
				quantity: item.quantity,
				unit: item.unit,
				unitPrice: item.unitPrice,
				totalPrice: item.totalPrice,
				vatRate: item.vatRate,
				notes: item.notes,
			}));

			// Call the mutation to update the invoice
			await adminEditInvoiceLineItems({
				id: selectedInvoice.id,
				lineItemsToUpdate,
			});

			// Show success message
			toast.success("Invoice updated successfully");
		} catch (error) {
			console.error("Failed to update invoice:", error);
			toast.error("Failed to update invoice");
		}
	};

	// Update a line item
	const handleUpdateLineItem = async (updatedItem: any) => {
		setLineItems((prev) =>
			prev.map((item) =>
				item.id === updatedItem.id ? { ...item, ...updatedItem } : item,
			),
		);
		return Promise.resolve();
	};

	return (
		<Dialog open={isEditDialogOpen} onOpenChange={setEditDialogOpen}>
			<DialogContent className=" max-w-[80vw]">
				<DialogTitle>Edit Invoice</DialogTitle>
				{selectedInvoice && (
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleSubmit)}
							className="space-y-6"
						>
							<div>
								<h3 className="text-lg font-medium mb-4">
									Billing Address
								</h3>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="billing_street"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Street</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="billing_addressSupplement"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Address Supplement
												</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="billing_zipCode"
										render={({ field }) => (
											<FormItem>
												<FormLabel>ZIP Code</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="billing_city"
										render={({ field }) => (
											<FormItem>
												<FormLabel>City</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="billing_country"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Country</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<Separator />

							<div>
								<h3 className="text-lg font-medium mb-4">
									Line Items
								</h3>
								<LineItemsTable
									items={lineItems}
									onUpdateItem={handleUpdateLineItem}
									disabled={isLoading}
									hideTypeColumn={false}
								/>
							</div>

							<div className="flex justify-end space-x-2 pt-4">
								<Button
									type="button"
									variant="outline"
									onClick={handleCancel}
									disabled={isLoading}
								>
									Cancel
								</Button>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Saving..." : "Save Changes"}
								</Button>
							</div>
						</form>
					</Form>
				)}
			</DialogContent>
		</Dialog>
	);
}
