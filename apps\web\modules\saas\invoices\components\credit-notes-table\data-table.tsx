"use client";
import { useCreditNotesUI } from "@saas/invoices/context/credit-notes-ui-context";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import {
	useCustomerCreditNoteById,
	useCustomerCreditNotes,
} from "../../hooks/use-customer-credit-notes";
import { CreditNoteFormDialog } from "../credit-note-form-dialog";
import { useColumns } from "./columns";

// Delete dialog component
function DeleteCreditNoteDialog() {
	const t = useTranslations();
	const {
		deleteCreditNote,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = useCreditNotesUI();

	// Fetch the full credit note data with customer details
	const { creditNote: creditNoteDetails } = useCustomerCreditNoteById(
		deleteCreditNote?.id,
	);

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteCreditNote) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent data-shortcuts-scope="credit-notes-delete-dialog">
				<AlertDialogHeader>
					<AlertDialogTitle>Delete Credit Note</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							Are you sure you want to delete this credit note?
							This action cannot be undone.
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deleteCreditNote.credit_note_number ||
							deleteCreditNote.id}
						<span className="block text-sm text-muted-foreground">
							{creditNoteDetails?.customer?.nameLine1 ||
								`Customer ID: ${deleteCreditNote.customerId}` ||
								"Unknown customer"}
						</span>
					</div>
				</AlertDialogHeader>

				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							Confirm
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// PDF viewer dialog component
function CreditNotePdfDialog() {
	const {
		isViewPdfDialogOpen,
		setViewPdfDialogOpen,
		viewCreditNoteId,
		viewCreditNotePdfData,
		isLoadingPdf,
	} = useCreditNotesUI();

	// Use the new hook to fetch credit note with document
	const {
		creditNote: creditNoteWithDoc,
		document,
		isLoading,
	} = useCustomerCreditNoteById(
		viewCreditNoteId || undefined,
		true, // includeDocument=true
	);

	// Check if we have document data in the response
	const documentUrl = document?.url;
	const documentData = document?.data;

	return (
		<Dialog
			open={isViewPdfDialogOpen}
			onOpenChange={(open) => {
				if (!open) {
					setViewPdfDialogOpen(false);
				}
			}}
		>
			<DialogContent className="max-w-5xl max-h-[90vh]">
				<DialogHeader>
					<DialogTitle>Credit Note Details</DialogTitle>
				</DialogHeader>
				<div className="h-[80vh]">
					{isLoading ? (
						<div className="flex items-center justify-center h-full">
							<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
						</div>
					) : documentData ? (
						<iframe
							src={`data:application/pdf;base64,${documentData}`}
							width="100%"
							height="100%"
							title="Credit Note Details"
						/>
					) : documentUrl ? (
						<iframe
							src={documentUrl}
							width="100%"
							height="100%"
							title="Credit Note Details"
						/>
					) : (
						<div className="flex items-center justify-center h-full text-muted-foreground">
							PDF preview not available
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// Edit credit note dialog component
function EditCreditNoteDialog() {
	const { isEditDialogOpen, setEditDialogOpen, editCreditNoteId } =
		useCreditNotesUI();

	return (
		<CreditNoteFormDialog
			open={isEditDialogOpen}
			onOpenChange={setEditDialogOpen}
			editCreditNoteId={editCreditNoteId}
			onSuccess={() => {
				setEditDialogOpen(false);
			}}
		/>
	);
}

// Table component with the delete dialog
export function CreditNotesDataTable() {
	const {
		creditNotes,
		pagination,
		isLoading,
		refetch,
		search,
		setSearch,
		page,
		setPage,
		limit,
		setLimit,
	} = useCustomerCreditNotes();
	const { setSelectedCreditNote } = useCreditNotesUI();
	const columns = useColumns();

	// Convert date strings to Date objects with explicit type conversion
	const items = creditNotes.map((item: any) => ({
		...item,
		createdAt: new Date(item.createdAt),
		credit_note_date: item.credit_note_date
			? new Date(item.credit_note_date)
			: null,
	}));

	const t = useTranslations();

	return (
		<>
			<DataTable
				columns={columns}
				data={items}
				defaultColumnVisibility={{
					id: false,
					createdAt: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder="Search credit notes..."
				pagination={{
					page,
					setPage,
					pageSize: limit,
					setPageSize: setLimit,
					totalPages: pagination?.totalPages ?? 1,
					total: pagination?.total ?? 0,
				}}
				isLoading={isLoading}
				shortcutsScope="credit-notes-shortcuts"
			/>
			<DeleteCreditNoteDialog />
			<CreditNotePdfDialog />
			<EditCreditNoteDialog />
		</>
	);
}
