"use client";

import { OrderConfirmationEmailPreview } from "@saas/orders/components/order-confirmation-email-preview";
import {
	useOrderConfirmationPDFPreview,
	useOrderEmailLogsForMultipleOrders,
	useSendOrderConfirmationEmail,
} from "@saas/orders/hooks/use-orders";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import {
	useTourEmailLogs,
	useTransportOrderOperations,
} from "@saas/tours/hooks/use-tours";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import {
	CheckCircle2,
	FileText,
	Loader2,
	MoreVertical,
	SendHorizontal,
} from "lucide-react";
import { useEffect, useState } from "react";
import { TransportOrderEmailPreview } from "../transport-order-email-preview";

interface TransportOrderPanelProps {
	tourId: string;
}

export function TransportOrderPanel({ tourId }: TransportOrderPanelProps) {
	const [previewPdfOpen, setPreviewPdfOpen] = useState<boolean>(false);
	const [pdfPreview, setPdfPreview] = useState<string | null>(null);
	const [previewEmailId, setPreviewEmailId] = useState<string | null>(null);
	const [sendingEmailId, setSendingEmailId] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(false);

	// Order confirmation states
	const [sendingOrderId, setSendingOrderId] = useState<string | null>(null);
	const [orderPdfPreviewId, setOrderPdfPreviewId] = useState<string | null>(
		null,
	);
	const [orderEmailPreviewId, setOrderEmailPreviewId] = useState<
		string | null
	>(null);

	// Use the email logs hook to get transport order email status
	const { emailLogs } = useTourEmailLogs(tourId);

	// Use the custom hook for transport order operations
	const {
		generateTransportOrderPreview,
		sendTransportOrderEmail,
		isGeneratingPreview,
		isSendingEmail,
		refetchTour,
		tourData: fetchedTourData,
	} = useTransportOrderOperations(tourId);

	// Order confirmation hooks
	const { sendConfirmationEmail, isLoading: isSendingOrderConfirmation } =
		useSendOrderConfirmationEmail();
	const { data: orderPdfPreviewData, isLoading: isLoadingOrderPDF } =
		useOrderConfirmationPDFPreview(
			orderPdfPreviewId ? orderPdfPreviewId : undefined,
			orderPdfPreviewId !== null,
		);

	// Get tour builder context to access the stops data
	const { stops, updateTourData } = useTourBuilder();

	// Update tour data in context when it changes
	useEffect(() => {
		if (fetchedTourData) {
			// Update the tour number and status in the context
			updateTourData({
				tourNumber: fetchedTourData.tourNumber,
				status: fetchedTourData.status,
			});
		}
	}, [fetchedTourData, updateTourData]);

	// Extract unique orders from the stops
	const ordersInTour = stops
		.filter((stop) => stop.orderId)
		.reduce(
			(acc, stop) => {
				if (stop.orderId && !acc.some((o) => o.id === stop.orderId)) {
					const orderInfo = {
						id: stop.orderId,
						customerName:
							stop.order?.customer?.nameLine1 ||
							"Unknown Customer",
						orderNumber:
							stop.order?.order_number ||
							stop.order?.customer_order_number ||
							"No order number",
					};
					acc.push(orderInfo);
				}
				return acc;
			},
			[] as { id: string; customerName: string; orderNumber: string }[],
		);

	// Create a map of order IDs to track email logs
	const orderEmailLogsMap = {} as Record<string, any>;

	// Load email logs for each order
	// ISSUE: This is causing the "Rendered fewer hooks than expected" error
	// because hooks are being called in a loop, which violates React's rules of hooks
	// FIX: Use a hook specifically designed for this pattern instead
	const orderEmailLogsData = useOrderEmailLogsForMultipleOrders(
		ordersInTour.map((order) => order.id),
	);

	// Update the map with the fetched data
	if (orderEmailLogsData) {
		Object.keys(orderEmailLogsData).forEach((orderId) => {
			orderEmailLogsMap[orderId] = orderEmailLogsData[orderId];
		});
	}

	// Helper function to check if an order confirmation email has been sent
	const hasOrderConfirmationEmail = (orderId: string) => {
		const logs = orderEmailLogsMap[orderId];
		return (
			logs?.items?.length > 0 &&
			logs.items.some(
				(log: any) =>
					log.emailType === "ORDER_CONFIRMATION" &&
					log.status === "SENT",
			)
		);
	};

	// Helper function to get the date of the last sent confirmation email
	const getLastConfirmationDate = (orderId: string) => {
		const logs = orderEmailLogsMap[orderId];
		if (!logs?.items?.length) {
			return null;
		}

		const confirmationLogs = logs.items.filter(
			(log: any) =>
				log.emailType === "ORDER_CONFIRMATION" && log.status === "SENT",
		);

		if (confirmationLogs.length === 0) {
			return null;
		}

		// Sort by sentAt date descending and get the first one
		const sortedLogs = [...confirmationLogs].sort(
			(a, b) =>
				new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime(),
		);

		return new Date(sortedLogs[0].sentAt);
	};

	// Helper function to check if a transport order email has been sent
	const hasTransportOrderEmailSent = () => {
		return (
			emailLogs?.items &&
			emailLogs.items.length > 0 &&
			emailLogs.items.some(
				(log: any) =>
					log.emailType === "TRANSPORT_ORDER" &&
					log.status === "SENT",
			)
		);
	};

	// Helper function to get the date of the last sent transport order email
	const getLastTransportOrderEmailDate = () => {
		if (!emailLogs?.items?.length) {
			return null;
		}

		const transportOrderLogs = emailLogs.items.filter(
			(log: any) =>
				log.emailType === "TRANSPORT_ORDER" && log.status === "SENT",
		);

		if (transportOrderLogs.length === 0) {
			return null;
		}

		// Sort by sentAt date descending and get the first one
		const sortedLogs = [...transportOrderLogs].sort(
			(a, b) =>
				new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime(),
		);

		return new Date(sortedLogs[0].sentAt);
	};

	// Combined function to handle the complete flow
	const handleSendWithPreview = async () => {
		setIsLoading(true);
		try {
			// Step 1: Generate PDF preview
			const result = await generateTransportOrderPreview(tourId);
			if (result?.success && result?.pdfBase64) {
				setPdfPreview(result.pdfBase64);
				setPreviewPdfOpen(true);
			} else {
				console.error("Failed to generate PDF preview");
			}
		} catch (error) {
			console.error("Error in send with preview flow:", error);
		} finally {
			setIsLoading(false);
		}
	};

	// Move to email preview after PDF is confirmed
	const handleProceedToEmail = () => {
		setPreviewPdfOpen(false);
		setPreviewEmailId(tourId);
	};

	// Final step to send the email
	const handleConfirmSend = async (id: string, documentIds?: string[]) => {
		setSendingEmailId(id);
		try {
			await sendTransportOrderEmail({
				id,
				documentIds,
			});
			setPreviewEmailId(null);

			// Refetch tour data to update the context
			await refetchTour();
		} catch (error) {
			console.error("Failed to send transport order:", error);
		} finally {
			setSendingEmailId(null);
		}
	};

	// Order confirmation flow
	const handleStartOrderConfirmation = (orderId: string) => {
		// Start the PDF preview flow
		setOrderPdfPreviewId(orderId);
	};

	const handleProceedToOrderEmailPreview = () => {
		// Close PDF preview and open email preview
		const currentOrderId = orderPdfPreviewId;
		setOrderPdfPreviewId(null);
		setOrderEmailPreviewId(currentOrderId);
	};

	const handleSendOrderConfirmation = async (
		orderId: string,
		email?: string,
		documentIds?: string[],
	) => {
		setSendingOrderId(orderId);
		try {
			await sendConfirmationEmail({
				id: orderId,
				recipientEmail: email,
				documentIds,
			});

			// Close the email preview dialog
			setOrderEmailPreviewId(null);
		} catch (error) {
			console.error("Send order confirmation email error:", error);
		} finally {
			setSendingOrderId(null);
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Transport Order</h3>

				{/* Transport Order Email Status with Action Menu */}
				<div className="flex items-center gap-2">
					{hasTransportOrderEmailSent() ? (
						<>
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger>
										<div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
											<CheckCircle2 className="h-3 w-3" />
											<span>Sent</span>
										</div>
									</TooltipTrigger>
									<TooltipContent>
										Transport order email sent
										{getLastTransportOrderEmailDate() &&
											` on ${getLastTransportOrderEmailDate()?.toLocaleDateString()}`}
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>

							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										size="icon"
										className="h-8 w-8"
									>
										<MoreVertical className="h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end">
									<DropdownMenuItem
										onClick={handleSendWithPreview}
									>
										<FileText className="h-4 w-4 mr-2" />
										View/Resend
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					) : null}
				</div>
			</div>

			{/* Only show the primary button if no email has been sent yet */}
			{!hasTransportOrderEmailSent() && (
				<div className="flex items-center gap-2">
					<Button
						variant="primary"
						size="sm"
						onClick={handleSendWithPreview}
						disabled={
							isLoading ||
							isGeneratingPreview ||
							isSendingEmail ||
							sendingEmailId === tourId
						}
						className="flex-1"
					>
						{isLoading || isGeneratingPreview ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Preparing Transport Order...
							</>
						) : sendingEmailId === tourId ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Sending...
							</>
						) : (
							<>
								<SendHorizontal className="mr-2 h-4 w-4" />
								Send Transport Order
							</>
						)}
					</Button>
				</div>
			)}

			{/* Orders list section */}
			{ordersInTour.length > 0 && (
				<div className="mt-4">
					<h4 className="text-sm font-medium mb-2">
						Orders in this tour
					</h4>
					<div className="space-y-2">
						{ordersInTour.map((order) => {
							const hasEmailSent = hasOrderConfirmationEmail(
								order.id,
							);
							const lastSentDate = getLastConfirmationDate(
								order.id,
							);

							return (
								<div
									key={order.id}
									className="flex items-center gap-2 rounded-md border p-3"
								>
									<div className="overflow-hidden flex-grow">
										<div className="flex items-center gap-2">
											<p className="font-medium text-sm">
												{order.customerName}
											</p>
										</div>
										<p className="text-xs text-muted-foreground">
											Order: {order.orderNumber}
										</p>
									</div>

									{/* Email status badge */}
									{hasEmailSent ? (
										<TooltipProvider>
											<Tooltip>
												<TooltipTrigger>
													<div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full">
														<CheckCircle2 className="h-3 w-3" />
														<span>Sent</span>
													</div>
												</TooltipTrigger>
												<TooltipContent>
													Confirmation email sent
													{lastSentDate &&
														` on ${lastSentDate.toLocaleDateString()}`}
												</TooltipContent>
											</Tooltip>
										</TooltipProvider>
									) : (
										<div className="text-xs text-muted-foreground px-2">
											No email sent
										</div>
									)}

									{/* Dropdown menu */}
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button
												variant="ghost"
												size="icon"
												className="h-8 w-8"
												disabled={
													sendingOrderId === order.id
												}
											>
												{sendingOrderId === order.id ? (
													<Loader2 className="h-4 w-4 animate-spin" />
												) : (
													<MoreVertical className="h-4 w-4" />
												)}
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() =>
													handleStartOrderConfirmation(
														order.id,
													)
												}
											>
												<FileText className="h-4 w-4 mr-2" />
												{hasEmailSent
													? "View/Resend"
													: "Send Confirmation"}
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							);
						})}
					</div>
				</div>
			)}

			{/* Transport Order PDF Preview Dialog */}
			<Dialog open={previewPdfOpen} onOpenChange={setPreviewPdfOpen}>
				<DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
					<DialogHeader>
						<DialogTitle>Transport Order Preview</DialogTitle>
						<DialogDescription>
							Preview the transport order before sending to the
							carrier.
						</DialogDescription>
					</DialogHeader>
					<div className="flex-1 overflow-auto my-4">
						{pdfPreview ? (
							<iframe
								src={`data:application/pdf;base64,${pdfPreview}`}
								className="w-full h-[70vh]"
								title="Transport Order PDF Preview"
							/>
						) : (
							<div className="flex items-center justify-center h-96">
								<Loader2 className="h-6 w-6 text-primary animate-spin" />{" "}
								Loading preview...
							</div>
						)}
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setPreviewPdfOpen(false)}
						>
							Cancel
						</Button>
						<Button
							variant="primary"
							onClick={handleProceedToEmail}
							disabled={isSendingEmail}
						>
							Continue to Email
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Transport Order Email Preview component */}
			<TransportOrderEmailPreview
				tourId={previewEmailId}
				isOpen={previewEmailId !== null}
				onClose={() => setPreviewEmailId(null)}
				onSend={handleConfirmSend}
				isSending={sendingEmailId === previewEmailId}
			/>

			{/* Order Confirmation PDF Preview Dialog */}
			<Dialog
				open={orderPdfPreviewId !== null}
				onOpenChange={(open) => {
					if (!open) {
						setOrderPdfPreviewId(null);
					}
				}}
			>
				<DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
					<DialogHeader>
						<DialogTitle>Order Confirmation Preview</DialogTitle>
						<DialogDescription>
							Preview the order confirmation before sending to the
							customer.
						</DialogDescription>
					</DialogHeader>
					<div className="flex-1 overflow-auto my-4">
						{isLoadingOrderPDF ? (
							<div className="flex items-center justify-center h-96">
								<Loader2 className="h-6 w-6 text-primary animate-spin" />
								Loading preview...
							</div>
						) : orderPdfPreviewData?.pdfBase64 ? (
							<iframe
								src={`data:application/pdf;base64,${orderPdfPreviewData.pdfBase64}`}
								className="w-full h-[70vh]"
								title="Order Confirmation PDF Preview"
							/>
						) : (
							<div className="flex items-center justify-center h-96 text-muted-foreground">
								Failed to load PDF preview
							</div>
						)}
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setOrderPdfPreviewId(null)}
						>
							Cancel
						</Button>
						<Button
							variant="primary"
							onClick={handleProceedToOrderEmailPreview}
							disabled={!orderPdfPreviewData?.pdfBase64}
						>
							Continue to Email
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Order Confirmation Email Preview Dialog */}
			<OrderConfirmationEmailPreview
				orderId={orderEmailPreviewId}
				isOpen={orderEmailPreviewId !== null}
				onClose={() => setOrderEmailPreviewId(null)}
				onBack={() => {
					setOrderEmailPreviewId(null);
					setOrderPdfPreviewId(orderEmailPreviewId); // Go back to PDF preview
				}}
				onSend={handleSendOrderConfirmation}
				isSending={isSendingOrderConfirmation}
			/>
		</div>
	);
}
