"use client";

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { format } from "date-fns";
import { Edit, Loader2, TestTube, Trash } from "lucide-react";
import { useState } from "react";
import { useMemberEmailConfiguration } from "../../hooks/use-member-email-configuration";
import { useSmtpConfiguration } from "../../hooks/use-smtp-configuration";

interface MemberEmailConfigurationStatusProps {
	onEdit?: () => void;
}

export function MemberEmailConfigurationStatus({
	onEdit,
}: MemberEmailConfigurationStatusProps) {
	const {
		data: memberConfig,
		isLoading,
		testMemberEmailConfiguration,
		activateMemberEmailConfiguration,
		deleteMemberEmailConfiguration,
		isTestingConfiguration,
		isActivating,
		isDeleting,
	} = useMemberEmailConfiguration();

	const { data: orgSmtpConfig } = useSmtpConfiguration();

	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showTestDialog, setShowTestDialog] = useState(false);
	const [testEmail, setTestEmail] = useState("");

	if (isLoading) {
		return (
			<Card>
				<CardContent className="flex justify-center items-center p-8">
					<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
				</CardContent>
			</Card>
		);
	}

	if (!memberConfig) {
		return (
			<Card>
				<CardContent className="text-center p-8 text-muted-foreground">
					No member email configuration found. Set up your personal
					email settings above.
				</CardContent>
			</Card>
		);
	}

	const handleTestConnection = () => {
		// Set default test email and show dialog
		setTestEmail(memberConfig?.fromEmail || "");
		setShowTestDialog(true);
	};

	const handleSendTestEmail = () => {
		if (!testEmail) {
			return;
		}

		setShowTestDialog(false);
		testMemberEmailConfiguration({
			testEmail,
		});
	};

	const handleToggleActive = () => {
		activateMemberEmailConfiguration(!memberConfig.isActive);
	};

	const handleDelete = () => {
		deleteMemberEmailConfiguration();
		setShowDeleteDialog(false);
	};

	const getStatusBadge = () => {
		if (!memberConfig.isVerified) {
			return <Badge status="error">Not Verified</Badge>;
		}

		if (memberConfig.isActive) {
			return <Badge status="success">Active</Badge>;
		}

		return <Badge status="warning">Inactive</Badge>;
	};

	const getAuthBadge = () => {
		// Since useOAuth2 might not be available in the current type,
		// we'll determine based on organization settings
		if (orgSmtpConfig?.useOAuth2) {
			return <Badge status="info">OAuth2</Badge>;
		}
		return <Badge status="warning">Password</Badge>;
	};

	const getLastTestStatus = () => {
		if (!memberConfig.lastTestAt) {
			return "Never tested";
		}

		const testDate = format(
			new Date(memberConfig.lastTestAt),
			"MMM d, yyyy 'at' HH:mm",
		);
		const status =
			memberConfig.lastTestStatus === "SUCCESS" ? "Passed" : "Failed";

		return `${status} on ${testDate}`;
	};

	return (
		<>
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								My Email Configuration
								{getStatusBadge()}
								{getAuthBadge()}
							</CardTitle>
							<CardDescription>
								Your personal email settings for sending emails
							</CardDescription>
						</div>
						<div className="flex items-center space-x-2">
							<span className="text-sm text-muted-foreground">
								{memberConfig.isActive ? "Active" : "Inactive"}
							</span>
							<Switch
								checked={memberConfig.isActive}
								onCheckedChange={handleToggleActive}
								disabled={
									!memberConfig.isVerified || isActivating
								}
							/>
						</div>
					</div>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Personal Email Settings */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium">
							Personal Email Settings
						</h4>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									My Email Address
								</h5>
								<p className="text-sm">
									{memberConfig.fromEmail}
								</p>
							</div>
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									Display Name
								</h5>
								<p className="text-sm">
									{memberConfig.fromName || "Not set"}
								</p>
							</div>
						</div>
					</div>

					{/* Authentication Details */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium">Authentication</h4>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									Method
								</h5>
								<div className="flex items-center gap-2">
									<p className="text-sm">
										{orgSmtpConfig?.useOAuth2
											? "OAuth2"
											: "Username & Password"}
									</p>
									{orgSmtpConfig?.useOAuth2 && (
										<Badge status="info">
											Microsoft Exchange
										</Badge>
									)}
								</div>
							</div>
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									Username
								</h5>
								<p className="text-sm">
									{memberConfig.username}
								</p>
							</div>
						</div>
					</div>

					{/* Inherited Organization Settings */}
					{orgSmtpConfig && (
						<div className="space-y-4">
							<h4 className="text-sm font-medium">
								Inherited Organization Settings
							</h4>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-3 bg-muted/50 rounded-lg">
								<div>
									<h5 className="font-medium text-sm text-muted-foreground mb-1">
										SMTP Host
									</h5>
									<p className="text-sm">
										{orgSmtpConfig.host}
									</p>
								</div>
								<div>
									<h5 className="font-medium text-sm text-muted-foreground mb-1">
										Port
									</h5>
									<p className="text-sm">
										{orgSmtpConfig.port}
									</p>
								</div>
								<div>
									<h5 className="font-medium text-sm text-muted-foreground mb-1">
										Security
									</h5>
									<p className="text-sm">
										{orgSmtpConfig.secure
											? "SSL/TLS"
											: orgSmtpConfig.requireTLS
												? "STARTTLS"
												: "None"}
									</p>
								</div>
								<div>
									<h5 className="font-medium text-sm text-muted-foreground mb-1">
										Organization Method
									</h5>
									<p className="text-sm">
										{orgSmtpConfig.useOAuth2
											? "OAuth2"
											: "Username & Password"}
									</p>
								</div>
							</div>
						</div>
					)}

					{/* Status Information */}
					<div className="border-t pt-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									Last Test
								</h5>
								<p className="text-sm">{getLastTestStatus()}</p>
								{memberConfig.lastTestError && (
									<p className="text-sm text-red-600 mt-1">
										Error: {memberConfig.lastTestError}
									</p>
								)}
							</div>
							<div>
								<h5 className="font-medium text-sm text-muted-foreground mb-1">
									Created
								</h5>
								<p className="text-sm">
									{format(
										new Date(memberConfig.createdAt),
										"MMM d, yyyy",
									)}
								</p>
							</div>
						</div>
					</div>

					{/* Actions */}
					<div className="border-t pt-4 flex gap-2">
						{onEdit && (
							<Button
								variant="outline"
								size="sm"
								onClick={onEdit}
							>
								<Edit className="h-4 w-4 mr-1" />
								Edit My Configuration
							</Button>
						)}

						<Button
							variant="outline"
							size="sm"
							onClick={handleTestConnection}
							disabled={isTestingConfiguration}
						>
							{isTestingConfiguration ? (
								<Loader2 className="h-4 w-4 mr-1 animate-spin" />
							) : (
								<TestTube className="h-4 w-4 mr-1" />
							)}
							Send Test Email
						</Button>

						<AlertDialog
							open={showDeleteDialog}
							onOpenChange={setShowDeleteDialog}
						>
							<AlertDialogTrigger asChild>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setShowDeleteDialog(true)}
									disabled={isDeleting}
								>
									{isDeleting ? (
										<Loader2 className="h-4 w-4 mr-1 animate-spin" />
									) : (
										<Trash className="h-4 w-4 mr-1" />
									)}
									Remove My Configuration
								</Button>
							</AlertDialogTrigger>
							<AlertDialogContent>
								<AlertDialogHeader>
									<AlertDialogTitle>
										Remove Email Configuration
									</AlertDialogTitle>
									<AlertDialogDescription>
										Are you sure you want to remove your
										personal email configuration for{" "}
										<strong>
											{memberConfig.fromEmail}
										</strong>
										? You'll fall back to the organization's
										default email settings.
									</AlertDialogDescription>
								</AlertDialogHeader>
								<AlertDialogFooter>
									<AlertDialogCancel>
										Cancel
									</AlertDialogCancel>
									<AlertDialogAction
										onClick={(e) => {
											e.preventDefault();
											handleDelete();
										}}
										className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
									>
										Remove Configuration
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					</div>
				</CardContent>
			</Card>

			{/* Test Email Dialog */}
			<Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Send Test Email</DialogTitle>
						<DialogDescription>
							Enter an email address to send a test email and
							verify your personal email configuration is working
							correctly.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="test-email">
								Test Email Address
							</Label>
							<Input
								id="test-email"
								type="email"
								value={testEmail}
								onChange={(e) => setTestEmail(e.target.value)}
								placeholder="<EMAIL>"
								autoFocus
							/>
							<p className="text-sm text-muted-foreground">
								We'll send a test email to this address using
								your personal email configuration
							</p>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => setShowTestDialog(false)}
						>
							Cancel
						</Button>
						<Button
							type="button"
							onClick={handleSendTestEmail}
							disabled={!testEmail || isTestingConfiguration}
						>
							{isTestingConfiguration && (
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							)}
							<TestTube className="mr-2 h-4 w-4" />
							Send Test Email
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
