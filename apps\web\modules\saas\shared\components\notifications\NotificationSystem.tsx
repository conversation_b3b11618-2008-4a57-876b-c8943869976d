"use client";
import { useCounts, useNotifications, useNovu } from "@novu/react";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { ScrollArea } from "@ui/components/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import {
	Archive,
	BellIcon,
	CheckCheck,
	CheckCircle,
	ChevronDownIcon,
	Clock,
	Filter,
	Inbox,
	LoaderIcon,
	Tag,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { NotificationProvider } from "./NotificationProvider";
import type { NotificationSystemProps } from "./types";

// Define notification type locally as fallback
interface NovuNotification {
	id: string;
	subject?: string;
	body?: string;
	isRead: boolean;
	isArchived: boolean;
	createdAt: string;
	tags?: string[];
}

// Helper function to format timestamp
const formatTimestamp = (date: string): string => {
	const now = new Date();
	const notificationDate = new Date(date);
	const diffInSeconds = Math.floor(
		(now.getTime() - notificationDate.getTime()) / 1000,
	);

	if (diffInSeconds < 60) {
		return `${diffInSeconds} seconds ago`;
	}
	if (diffInSeconds < 3600) {
		return `${Math.floor(diffInSeconds / 60)} minutes ago`;
	}
	if (diffInSeconds < 86400) {
		return `${Math.floor(diffInSeconds / 3600)} hours ago`;
	}
	if (diffInSeconds < 604800) {
		return `${Math.floor(diffInSeconds / 86400)} days ago`;
	}

	return new Date(date).toLocaleDateString();
};

// Inner component that uses the hooks (must be inside NovuProvider)
function NotificationSystemInner({
	className,
}: Pick<NotificationSystemProps, "className">) {
	// Call all hooks unconditionally at the top level
	const [isOpen, setIsOpen] = useState(false);
	const [mounted, setMounted] = useState(false);
	const [realtimeCount, setRealtimeCount] = useState<number | null>(null);
	const [activeFilter, setActiveFilter] = useState<string>("all");
	const [realtimeActive, setRealtimeActive] = useState(false);
	const bellRef = useRef<HTMLButtonElement>(null);

	const { counts } = useCounts({
		filters: [{ read: false }],
	});
	const novu = useNovu();

	const {
		notifications: inboxNotifications,
		isLoading: inboxLoading,
		hasMore: inboxHasMore,
		fetchMore: inboxFetchMore,
		refetch: inboxRefetch,
		readAll,
		isFetching: inboxFetching,
	} = useNotifications({
		limit: 20,
		archived: false,
		read: undefined,
	});

	const {
		notifications: archivedNotifications,
		isLoading: archivedLoading,
		hasMore: archivedHasMore,
		fetchMore: archivedFetchMore,
		refetch: archivedRefetch,
		isFetching: archivedFetching,
	} = useNotifications({
		limit: 20,
		archived: true,
		read: undefined,
	});

	useEffect(() => {
		setMounted(true);
	}, []);

	// Main effect for Novu event listeners - MOVED TO TOP LEVEL
	useEffect(() => {
		// if (!novu || !mounted) return; // Guard against unmounted state

		console.log("🔗 Setting up Novu event listeners...");

		// Handler for new notifications
		const handleNewNotification = ({
			result,
		}: { result: NovuNotification }) => {
			console.log("🔔 New notification:", result.subject);
			setRealtimeActive(true);

			// Browser notification
			if (
				typeof window !== "undefined" &&
				"Notification" in window &&
				window.Notification.permission === "granted"
			) {
				new window.Notification(
					result.subject || "New Camion Notification",
					{
						body:
							result.body ||
							"You have a new notification from Camion.",
						icon: "/favicon.ico",
						tag: result.id,
					},
				);
			}

			// Bell animation
			if (bellRef.current) {
				bellRef.current.classList.add("animate-bounce");
				setTimeout(() => {
					bellRef.current?.classList.remove("animate-bounce");
				}, 1000);
			}

			// Refresh notifications
			setTimeout(() => {
				inboxRefetch();
			}, 500);
		};

		// Handler for unread count changes
		const handleUnreadCountChanged = ({ result }: { result: number }) => {
			console.log("📊 Unread count changed:", result);
			setRealtimeCount(result);
			setRealtimeActive(true);

			// Update document title
			const baseTitle = "Camion";
			document.title =
				result > 0 ? `(${result}) ${baseTitle}` : baseTitle;
		};

		// Subscribe to events
		novu.on("notifications.notification_received", handleNewNotification);
		novu.on("notifications.unread_count_changed", handleUnreadCountChanged);

		console.log("✅ Event listeners registered");

		// Cleanup function
		return () => {
			console.log("🧹 Cleaning up event listeners...");
			novu.off(
				"notifications.notification_received",
				handleNewNotification,
			);
			novu.off(
				"notifications.unread_count_changed",
				handleUnreadCountChanged,
			);
			document.title = "Camion"; // Reset title
		};
	}, [novu]);

	// Conditional rendering / early return *after* all hooks are called
	if (!mounted) {
		return null;
	}

	const unreadCount = realtimeCount ?? (counts?.[0]?.count || 0);

	// Filter notifications based on active filter
	const getFilteredNotifications = (notifications: NovuNotification[]) => {
		if (activeFilter === "all") {
			return notifications;
		}
		return notifications.filter((n) => n.tags?.includes(activeFilter));
	};

	// Get unique tags for filter dropdown
	const getUniqueTags = () => {
		const allNotifications = [
			...(inboxNotifications || []),
			...(archivedNotifications || []),
		];
		const tags = new Set<string>();
		allNotifications.forEach((n) => {
			n.tags?.forEach((tag) => tags.add(tag));
		});
		return Array.from(tags);
	};

	// Individual notification actions
	const markAsRead = async (id: string) => {
		try {
			await novu.notifications.read({ notificationId: id });
			// Refetch to update the UI since Novu doesn't handle optimistic updates
			inboxRefetch();
		} catch (error) {
			console.error("Failed to mark as read:", error);
			inboxRefetch();
		}
	};

	const markAsUnread = async (id: string) => {
		try {
			await novu.notifications.unread({ notificationId: id });
			// Refetch to update the UI since Novu doesn't handle optimistic updates
			inboxRefetch();
		} catch (error) {
			console.error("Failed to mark as unread:", error);
			inboxRefetch();
		}
	};

	const archiveNotification = async (id: string) => {
		try {
			await novu.notifications.archive({ notificationId: id });
			inboxRefetch();
			archivedRefetch();
		} catch (error) {
			console.error("Failed to archive:", error);
		}
	};

	const unarchiveNotification = async (id: string) => {
		try {
			await novu.notifications.unarchive({ notificationId: id });
			inboxRefetch();
			archivedRefetch();
		} catch (error) {
			console.error("Failed to unarchive:", error);
		}
	};

	// Bulk actions
	const handleReadAll = async () => {
		try {
			const result = await readAll();
			if (result.error) {
				console.error("Failed to mark all as read:", result.error);
			} else {
				// Refresh both notification lists to show updated read status
				inboxRefetch();
				archivedRefetch();
				// Reset realtime count since all are now read
				setRealtimeCount(0);
			}
		} catch (error) {
			console.error("Failed to mark all as read:", error);
		}
	};

	// Render notification item
	const renderNotificationItem = (
		notification: NovuNotification,
		archived: boolean,
	) => (
		<div
			key={notification.id}
			className={cn(
				"px-4 py-3 border-b transition-colors hover:bg-muted/50",
				notification.isRead ? "bg-background" : "bg-muted/30",
			)}
		>
			<div className="flex justify-between items-start">
				<div className="flex-1 min-w-0">
					<div className="flex items-center gap-2">
						<h4 className="text-sm font-medium truncate">
							{notification.subject}
						</h4>
						{!notification.isRead && (
							<span className="h-2 w-2 rounded-full bg-primary flex-shrink-0" />
						)}
					</div>
					{notification.body && (
						<p className="text-sm text-muted-foreground mt-1 line-clamp-2">
							{notification.body}
						</p>
					)}

					<div className="flex items-center gap-2 mt-2">
						<span className="text-xs text-muted-foreground flex items-center gap-1">
							<Clock className="h-3 w-3" />
							{formatTimestamp(notification.createdAt)}
						</span>
						{notification.tags && notification.tags.length > 0 && (
							<div className="flex items-center gap-1">
								{notification.tags.slice(0, 2).map((tag) => (
									<Badge
										key={tag}
										className="text-xs h-5 bg-muted text-muted-foreground"
									>
										{tag}
									</Badge>
								))}
								{notification.tags.length > 2 && (
									<Badge className="text-xs h-5 bg-muted text-muted-foreground">
										+{notification.tags.length - 2}
									</Badge>
								)}
							</div>
						)}
					</div>
				</div>

				<div className="flex items-center gap-1 ml-2 flex-shrink-0">
					{notification.isRead ? (
						<Button
							variant="ghost"
							size="icon"
							className="h-8 w-8"
							onClick={() => markAsUnread(notification.id)}
							title="Mark as unread"
						>
							<CheckCheck className="h-4 w-4" />
						</Button>
					) : (
						<Button
							variant="ghost"
							size="icon"
							className="h-8 w-8"
							onClick={() => markAsRead(notification.id)}
							title="Mark as read"
						>
							<CheckCircle className="h-4 w-4" />
						</Button>
					)}

					{!archived && (
						<Button
							variant="ghost"
							size="icon"
							className="h-8 w-8"
							onClick={() => archiveNotification(notification.id)}
							title="Archive"
						>
							<Archive className="h-4 w-4" />
						</Button>
					)}

					{archived && (
						<Button
							variant="ghost"
							size="icon"
							className="h-8 w-8"
							onClick={() =>
								unarchiveNotification(notification.id)
							}
							title="Move to inbox"
						>
							<Inbox className="h-4 w-4" />
						</Button>
					)}
				</div>
			</div>
		</div>
	);

	// Show a simple bell icon on server and during initial client render
	if (!mounted) {
		return (
			<Button
				variant="ghost"
				size="icon"
				className={cn(
					"relative transition-all duration-200",
					className,
				)}
				disabled
			>
				<BellIcon className="h-5 w-5" />
			</Button>
		);
	}

	return (
		<Popover open={isOpen} onOpenChange={setIsOpen}>
			<PopoverTrigger asChild>
				<Button
					ref={bellRef}
					variant="ghost"
					size="icon"
					className={cn(
						"relative transition-all duration-200",
						className,
					)}
				>
					<BellIcon className="h-5 w-5" />
					{unreadCount > 0 && (
						<Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500 text-white animate-pulse">
							{unreadCount > 9 ? "9+" : unreadCount}
						</Badge>
					)}
				</Button>
			</PopoverTrigger>

			<PopoverContent className="w-96 p-0 shadow-xl" align="end">
				<Card className="border-0 shadow-none">
					<CardHeader className="pb-2 pt-2 px-4">
						<div className="flex items-center justify-between">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant="outline"
										size="sm"
										className="h-7 gap-1"
									>
										<Filter className="h-3 w-3" />
										{activeFilter !== "all" && (
											<span className="capitalize text-xs">
												{activeFilter}
											</span>
										)}
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="start">
									<DropdownMenuItem
										onClick={() => setActiveFilter("all")}
									>
										All
									</DropdownMenuItem>
									{getUniqueTags().map((tag) => (
										<DropdownMenuItem
											key={tag}
											onClick={() => setActiveFilter(tag)}
										>
											<Tag className="h-4 w-4 mr-2" />
											{tag}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>

							<Button
								variant="ghost"
								size="sm"
								className="h-7 text-xs"
								onClick={handleReadAll}
								disabled={unreadCount === 0}
							>
								Mark all read
							</Button>
						</div>
					</CardHeader>

					<CardContent className="p-0">
						<Tabs defaultValue="inbox">
							<TabsList className="grid w-full grid-cols-2 mb-0">
								<TabsTrigger value="inbox">Inbox</TabsTrigger>
								<TabsTrigger value="archived">
									Archived
								</TabsTrigger>
							</TabsList>

							<TabsContent value="inbox" className="mt-0">
								<ScrollArea className="h-[400px]">
									{inboxLoading && !inboxNotifications ? (
										<div className="flex items-center justify-center h-[300px]">
											<LoaderIcon className="h-6 w-6 animate-spin" />
										</div>
									) : getFilteredNotifications(
											inboxNotifications || [],
										).length > 0 ? (
										<>
											{getFilteredNotifications(
												inboxNotifications || [],
											).map((notification) =>
												renderNotificationItem(
													notification,
													false,
												),
											)}
											{inboxHasMore && (
												<div className="flex items-center justify-center py-4">
													<Button
														variant="ghost"
														onClick={inboxFetchMore}
														disabled={inboxFetching}
													>
														{inboxFetching ? (
															<>
																<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
																Loading...
															</>
														) : (
															<>
																<ChevronDownIcon className="mr-2 h-4 w-4" />
																Load more
															</>
														)}
													</Button>
												</div>
											)}
										</>
									) : (
										<div className="flex flex-col items-center justify-center h-[300px] text-center">
											<BellIcon className="h-8 w-8 text-muted-foreground mb-2" />
											<p className="text-muted-foreground">
												No notifications to display
											</p>
											{activeFilter !== "all" && (
												<Button
													variant="link"
													onClick={() =>
														setActiveFilter("all")
													}
													className="mt-2"
												>
													Clear filter
												</Button>
											)}
										</div>
									)}
								</ScrollArea>
							</TabsContent>

							<TabsContent value="archived" className="mt-0">
								<ScrollArea className="h-[400px]">
									{archivedLoading &&
									!archivedNotifications ? (
										<div className="flex items-center justify-center h-[300px]">
											<LoaderIcon className="h-6 w-6 animate-spin" />
										</div>
									) : getFilteredNotifications(
											archivedNotifications || [],
										).length > 0 ? (
										<>
											{getFilteredNotifications(
												archivedNotifications || [],
											).map((notification) =>
												renderNotificationItem(
													notification,
													true,
												),
											)}
											{archivedHasMore && (
												<div className="flex items-center justify-center py-4">
													<Button
														variant="ghost"
														onClick={
															archivedFetchMore
														}
														disabled={
															archivedFetching
														}
													>
														{archivedFetching ? (
															<>
																<LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
																Loading...
															</>
														) : (
															<>
																<ChevronDownIcon className="mr-2 h-4 w-4" />
																Load more
															</>
														)}
													</Button>
												</div>
											)}
										</>
									) : (
										<div className="flex flex-col items-center justify-center h-[300px] text-center">
											<Archive className="h-8 w-8 text-muted-foreground mb-2" />
											<p className="text-muted-foreground">
												No archived notifications
											</p>
											{activeFilter !== "all" && (
												<Button
													variant="link"
													onClick={() =>
														setActiveFilter("all")
													}
													className="mt-2"
												>
													Clear filter
												</Button>
											)}
										</div>
									)}
								</ScrollArea>
							</TabsContent>
						</Tabs>
					</CardContent>
				</Card>
			</PopoverContent>
		</Popover>
	);
}

// Outer component that provides the context
export function NotificationSystem({
	subscriberId,
	applicationIdentifier,
	className,
}: NotificationSystemProps) {
	return (
		<NotificationProvider
			subscriberId={subscriberId}
			applicationIdentifier={applicationIdentifier}
		>
			<NotificationSystemInner className={className} />
		</NotificationProvider>
	);
}
