"use client";

import { OfferViewSheet } from "@saas/offers/components/offer-view-sheet";
import { useColumns } from "@saas/offers/components/offers-table/columns";
import { OffersUIProvider } from "@saas/offers/context/offers-ui-context";
import { useOffers } from "@saas/offers/hooks/use-offer";
import type { Offer } from "@saas/offers/lib/types";
import { DataTable } from "@saas/shared/components/data-table";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { FileEdit } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface OffersPanelProps {
	counterpartyId: string;
}

export function OffersPanel({ counterpartyId }: OffersPanelProps) {
	const t = useTranslations();
	const [pageSize, setPageSize] = useState(5);
	const [page, setPage] = useState(1);

	// Use the offers hook to fetch data filtered by counterpartyId
	const {
		data,
		isLoading,
		search,
		setSearch,
		sorting,
		setSorting,
		setCustomerId,
		refetch,
	} = useOffers(undefined);

	// Set the customerId to the counterpartyId prop
	useEffect(() => {
		setSearch("");
		setPage(1);
		setCustomerId(counterpartyId);
	}, [counterpartyId, setSearch, setPage, setCustomerId]);

	// We'll cast the data to Offer[] to handle any type mismatch
	const offers = (data?.items as unknown as Offer[]) ?? [];

	// Empty state component
	const EmptyState = () => (
		<div className="flex flex-col items-center justify-center py-12 text-center">
			<FileEdit className="h-12 w-12 text-muted-foreground mb-4" />
			<h3 className="text-lg font-medium mb-2">No Offers Yet</h3>
			<p className="text-muted-foreground mb-6 max-w-md">
				This contact doesn't have any offers yet. Create your first
				offer to start tracking potential business.
			</p>
			{/* <Button asChild>
				<Link href={`/app/offers/new?customerId=${counterpartyId}`}>
					<Plus className="h-4 w-4 mr-2" />
					Create Offer
				</Link>
			</Button> */}
		</div>
	);

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Offers</CardTitle>
				{/* <Button size="sm" asChild>
					<Link href={`/app/offers/new?customerId=${counterpartyId}`}>
						<Plus className="h-4 w-4 mr-2" />
						Create Offer
					</Link>
				</Button> */}
			</CardHeader>
			<CardContent>
				<OffersUIProvider onOfferDeleted={refetch}>
					{offers.length === 0 && !isLoading ? (
						<EmptyState />
					) : (
						<DataTableWrapper
							offers={offers}
							isLoading={isLoading}
							search={search}
							setSearch={setSearch}
							page={page}
							setPage={setPage}
							pageSize={pageSize}
							setPageSize={setPageSize}
							data={data}
							sorting={sorting}
							setSorting={setSorting}
						/>
					)}
					<OfferViewSheet />
				</OffersUIProvider>
			</CardContent>
		</Card>
	);
}

// Separate component inside which we can use the useColumns hook
interface DataTableWrapperProps {
	offers: Offer[];
	isLoading: boolean;
	search: string;
	setSearch: (search: string) => void;
	page: number;
	setPage: (page: number) => void;
	pageSize: number;
	setPageSize: (pageSize: number) => void;
	data: any;
	sorting: any;
	setSorting: (sorting: any) => void;
}

function DataTableWrapper({
	offers,
	isLoading,
	search,
	setSearch,
	page,
	setPage,
	pageSize,
	setPageSize,
	data,
	sorting,
	setSorting,
}: DataTableWrapperProps) {
	const columns = useColumns();

	return (
		<DataTable
			columns={columns}
			data={offers}
			defaultColumnVisibility={{
				id: false,
				createdAt: false,
				accepted_at: false,
				declined_at: false,
				customerName: false,
			}}
			onSearch={setSearch}
			searchValue={search}
			searchPlaceholder="Search offers..."
			pagination={{
				page,
				setPage,
				pageSize,
				setPageSize,
				totalPages: data?.totalPages ?? 1,
				total: data?.total ?? 0,
			}}
			isLoading={isLoading}
			sorting={sorting}
			onSortingChange={setSorting}
			manualSorting={true}
			shortcutsScope="customer-offers-shortcuts"
		/>
	);
}
