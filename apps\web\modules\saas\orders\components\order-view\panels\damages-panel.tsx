"use client";

import { useOrde<PERSON><PERSON>iew } from "@saas/orders/context/order-view-context";
import { useDamageMutations, useDamages } from "@saas/orders/hooks/use-damages";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON>alogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { Skeleton } from "@ui/components/skeleton";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import {
	Al<PERSON><PERSON>ir<PERSON>,
	AlertTriangle,
	Clock,
	Euro,
	FileText,
	MoreVertical,
	Pencil,
	Plus,
	Shield,
	Trash,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

// Define type for the damage item
interface DamageItem {
	id: string;
	damage_type: string | null;
	damage_date: string | Date | null;
	damage_incoming_claim_amount: number | null;
	damage_outgoing_claim_amount: number | null;
	internal_note: string | null;
	liability_note: string | null;
	liability_open: boolean | null;
	liability_sent: boolean | null;
	liability_entered: boolean | null;
	liability_billed: boolean | null;
	liability_accepted: boolean | null;
	liability_completed: boolean | null;
	createdAt?: string;
	updatedAt?: string;
}

interface DamagesPanelProps {
	orderId: string;
}

interface DamageDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSave: () => Promise<void>;
	editValues: Partial<DamageItem>;
	onChange: (field: string, value: any) => void;
	isEditing: boolean;
}

function DamageDialog({
	isOpen,
	onClose,
	onSave,
	editValues,
	onChange,
	isEditing,
}: DamageDialogProps) {
	const t = useTranslations();
	const [isSaving, setIsSaving] = useState(false);

	const handleSave = async () => {
		try {
			setIsSaving(true);
			await onSave();
			onClose();
		} catch (error) {
			// Error is handled in the parent component
		} finally {
			setIsSaving(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[550px]">
				<DialogHeader>
					<DialogTitle>
						{isEditing ? "Edit Damage" : "Add New Damage"}
					</DialogTitle>
					<DialogDescription>
						{isEditing
							? "Update the damage details below"
							: "Enter the damage details below"}
					</DialogDescription>
				</DialogHeader>

				<div className="grid gap-4 py-4">
					<div className="grid grid-cols-2 gap-4">
						<div className="space-y-2">
							<label
								htmlFor="damage_type"
								className="text-sm font-medium"
							>
								Damage Type
							</label>
							<Input
								id="damage_type"
								value={editValues.damage_type || ""}
								onChange={(e) =>
									onChange("damage_type", e.target.value)
								}
								placeholder="Type of damage"
							/>
						</div>
						<div className="space-y-2">
							<label
								htmlFor="damage_date"
								className="text-sm font-medium"
							>
								Damage Date
							</label>
							<InputWithDate
								inputProps={{
									id: "damage_date",
									placeholder: "DD.MM.YYYY",
									value: editValues.damage_date
										? typeof editValues.damage_date ===
											"string"
											? editValues.damage_date
											: format(
													editValues.damage_date as Date,
													"yyyy-MM-dd",
												)
										: "",
								}}
								onDateChange={(date: Date | string | null) =>
									onChange("damage_date", date)
								}
							/>
						</div>
					</div>

					<div className="grid grid-cols-2 gap-4">
						<div className="space-y-2">
							<label
								htmlFor="damage_incoming_claim_amount"
								className="text-sm font-medium"
							>
								Incoming Claim Amount
							</label>
							<Input
								id="damage_incoming_claim_amount"
								type="number"
								value={
									editValues.damage_incoming_claim_amount ||
									""
								}
								onChange={(e) =>
									onChange(
										"damage_incoming_claim_amount",
										Number(e.target.value) || 0,
									)
								}
								placeholder="0.00"
							/>
						</div>
						<div className="space-y-2">
							<label
								htmlFor="damage_outgoing_claim_amount"
								className="text-sm font-medium"
							>
								Outgoing Claim Amount
							</label>
							<Input
								id="damage_outgoing_claim_amount"
								type="number"
								value={
									editValues.damage_outgoing_claim_amount ||
									""
								}
								onChange={(e) =>
									onChange(
										"damage_outgoing_claim_amount",
										Number(e.target.value) || 0,
									)
								}
								placeholder="0.00"
							/>
						</div>
					</div>

					<div className="space-y-2">
						<label
							htmlFor="internal_note"
							className="text-sm font-medium"
						>
							Internal Notes
						</label>
						<Textarea
							id="internal_note"
							value={editValues.internal_note || ""}
							onChange={(e) =>
								onChange("internal_note", e.target.value)
							}
							placeholder="Internal notes (optional)"
							className="h-20"
						/>
					</div>

					<div className="space-y-2">
						<label
							htmlFor="liability_note"
							className="text-sm font-medium"
						>
							Liability Notes
						</label>
						<Textarea
							id="liability_note"
							value={editValues.liability_note || ""}
							onChange={(e) =>
								onChange("liability_note", e.target.value)
							}
							placeholder="Liability notes (optional)"
							className="h-20"
						/>
					</div>

					<div className="space-y-2">
						<p className="text-sm font-medium mb-2">
							Liability Status
						</p>
						<div className="grid grid-cols-2 gap-2">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_open"
									checked={editValues.liability_open || false}
									onCheckedChange={(checked) =>
										onChange("liability_open", checked)
									}
								/>
								<label
									htmlFor="liability_open"
									className="text-sm"
								>
									Open
								</label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_sent"
									checked={editValues.liability_sent || false}
									onCheckedChange={(checked) =>
										onChange("liability_sent", checked)
									}
								/>
								<label
									htmlFor="liability_sent"
									className="text-sm"
								>
									Sent
								</label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_entered"
									checked={
										editValues.liability_entered || false
									}
									onCheckedChange={(checked) =>
										onChange("liability_entered", checked)
									}
								/>
								<label
									htmlFor="liability_entered"
									className="text-sm"
								>
									Entered
								</label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_billed"
									checked={
										editValues.liability_billed || false
									}
									onCheckedChange={(checked) =>
										onChange("liability_billed", checked)
									}
								/>
								<label
									htmlFor="liability_billed"
									className="text-sm"
								>
									Billed
								</label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_accepted"
									checked={
										editValues.liability_accepted || false
									}
									onCheckedChange={(checked) =>
										onChange("liability_accepted", checked)
									}
								/>
								<label
									htmlFor="liability_accepted"
									className="text-sm"
								>
									Accepted
								</label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="liability_completed"
									checked={
										editValues.liability_completed || false
									}
									onCheckedChange={(checked) =>
										onChange("liability_completed", checked)
									}
								/>
								<label
									htmlFor="liability_completed"
									className="text-sm"
								>
									Completed
								</label>
							</div>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={onClose}
						disabled={isSaving}
					>
						Cancel
					</Button>
					<Button onClick={handleSave} disabled={isSaving}>
						{isSaving
							? "Saving..."
							: isEditing
								? "Update Damage"
								: "Create Damage"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

export function DamagesPanel({ orderId }: DamagesPanelProps) {
	const t = useTranslations();
	const { activeTab } = useOrderView();
	const { data: damagesData, isLoading: damagesLoading } =
		useDamages(orderId);
	const damages = damagesData?.items || [];

	const [editingId, setEditingId] = useState<string | null>(null);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
	const [damageToDelete, setDamageToDelete] = useState<string | null>(null);
	const [editValues, setEditValues] = useState<Partial<DamageItem>>({
		damage_type: "",
		damage_date: null,
		damage_incoming_claim_amount: 0,
		damage_outgoing_claim_amount: 0,
		internal_note: "",
		liability_note: "",
		liability_open: false,
		liability_sent: false,
		liability_entered: false,
		liability_billed: false,
		liability_accepted: false,
		liability_completed: false,
	});

	const { createDamage, updateDamage, deleteDamage } = useDamageMutations(
		orderId,
		{
			onSuccess: () => {
				setEditingId(null);
				setIsDialogOpen(false);
				setIsDeleteConfirmOpen(false);
				setDamageToDelete(null);
			},
		},
	);

	// Format currency
	const formatCurrency = (value: number | undefined | null) => {
		if (value === undefined || value === null) {
			return "—";
		}

		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "EUR",
		}).format(value);
	};

	// Format date for display
	const formatDate = (date: string | Date | null | undefined) => {
		if (!date) {
			return "—";
		}
		return format(new Date(date), "PP");
	};

	// Get status color for the indicator
	const getStatusColor = (damage: DamageItem) => {
		if (damage.liability_completed) {
			return "bg-green-500 dark:bg-green-600";
		}
		if (damage.liability_accepted) {
			return "bg-blue-500 dark:bg-blue-600";
		}
		if (damage.liability_sent) {
			return "bg-amber-500 dark:bg-amber-600";
		}
		if (damage.liability_open) {
			return "bg-orange-500 dark:bg-orange-600";
		}
		return "bg-red-500 dark:bg-red-600";
	};

	// Get status label
	const getStatusLabel = (damage: DamageItem) => {
		if (damage.liability_completed) {
			return "Completed";
		}
		if (damage.liability_accepted) {
			return "Accepted";
		}
		if (damage.liability_sent) {
			return "Sent";
		}
		if (damage.liability_open) {
			return "Open";
		}
		return "New";
	};

	const handleAddDamage = () => {
		setEditingId(null);
		setEditValues({
			damage_type: "",
			damage_date: null,
			damage_incoming_claim_amount: 0,
			damage_outgoing_claim_amount: 0,
			internal_note: "",
			liability_note: "",
			liability_open: false,
			liability_sent: false,
			liability_entered: false,
			liability_billed: false,
			liability_accepted: false,
			liability_completed: false,
		});
		setIsDialogOpen(true);
	};

	const handleEditDamage = (damage: DamageItem) => {
		setEditingId(damage.id);
		setEditValues({
			damage_type: damage.damage_type || "",
			damage_date: damage.damage_date
				? new Date(damage.damage_date)
				: null,
			damage_incoming_claim_amount:
				damage.damage_incoming_claim_amount || 0,
			damage_outgoing_claim_amount:
				damage.damage_outgoing_claim_amount || 0,
			internal_note: damage.internal_note || "",
			liability_note: damage.liability_note || "",
			liability_open: damage.liability_open || false,
			liability_sent: damage.liability_sent || false,
			liability_entered: damage.liability_entered || false,
			liability_billed: damage.liability_billed || false,
			liability_accepted: damage.liability_accepted || false,
			liability_completed: damage.liability_completed || false,
		});
		setIsDialogOpen(true);
	};

	const handleConfirmDelete = (id: string) => {
		setDamageToDelete(id);
		setIsDeleteConfirmOpen(true);
	};

	const handleDeleteDamage = async () => {
		if (!damageToDelete) {
			return;
		}

		try {
			await deleteDamage(damageToDelete);
			setIsDeleteConfirmOpen(false);
			setDamageToDelete(null);
		} catch (error) {
			console.error("Delete damage error:", error);
		}
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		setEditingId(null);
	};

	const handleChange = (field: string, value: any) => {
		setEditValues((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSaveEdit = async () => {
		try {
			if (editingId) {
				await updateDamage({
					id: editingId,
					...editValues,
					orderId,
					organizationId: "", // This will be filled by the hook
				});
			} else {
				await createDamage({
					...editValues,
					orderId,
					organizationId: "", // This will be filled by the hook
				});
			}
			return Promise.resolve();
		} catch (error) {
			console.error("Damage form error:", error);
			return Promise.reject(error);
		}
	};

	// Only render this component when active tab is "damages"
	if (activeTab !== "damages") {
		return null;
	}

	return (
		<>
			<Card>
				<CardHeader className="flex flex-row items-center justify-between">
					<div>
						<CardTitle>{t("app.orders.damages.title")}</CardTitle>
						<CardDescription>
							{t("app.orders.damages.description")}
						</CardDescription>
					</div>
					<Button
						onClick={handleAddDamage}
						size="sm"
						className="ml-auto"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Damage
					</Button>
				</CardHeader>
				<CardContent>
					{damagesLoading ? (
						<div className="space-y-4">
							{Array.from({ length: 3 }).map((_, index) => (
								<div
									key={index}
									className="border rounded-lg p-4 relative"
								>
									<div className="flex items-center mb-2">
										<Skeleton className="h-8 w-8 rounded-full mr-3" />
										<div className="w-full">
											<Skeleton className="h-5 w-1/3 mb-2" />
											<Skeleton className="h-4 w-2/3" />
										</div>
									</div>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
										<div>
											<Skeleton className="h-4 w-1/4 mb-2" />
											<Skeleton className="h-5 w-3/4" />
										</div>
										<div>
											<Skeleton className="h-4 w-1/4 mb-2" />
											<Skeleton className="h-5 w-1/2" />
										</div>
									</div>
								</div>
							))}
						</div>
					) : !damages || damages.length === 0 ? (
						<div className="text-center py-12 text-muted-foreground">
							<div className="flex flex-col items-center">
								<AlertTriangle className="w-10 h-10 mb-3 text-muted-foreground/50" />
								<p className="text-lg font-medium mb-2">
									No damages recorded
								</p>
								<p className="text-sm max-w-md mx-auto">
									There are no damage records for this order.
									Click "Add Damage" to record a new damage
									event.
								</p>
							</div>
						</div>
					) : (
						<div className="space-y-4">
							{damages.map((damage, index) => (
								<div
									key={damage.id}
									className="border rounded-lg p-4 relative overflow-hidden"
								>
									{/* Status indicator bar */}
									<div
										className={`absolute top-0 left-0 w-1 h-full ${getStatusColor(
											damage,
										)}`}
									/>

									{/* Action menu */}
									<div className="absolute top-2 right-2 flex items-center">
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button
													variant="ghost"
													size="icon"
													className="h-8 w-8"
												>
													<MoreVertical className="h-4 w-4" />
													<span className="sr-only">
														Actions
													</span>
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuItem
													onClick={() =>
														handleEditDamage(damage)
													}
												>
													<Pencil className="h-4 w-4 mr-2" />
													Edit
												</DropdownMenuItem>
												<DropdownMenuItem
													className="text-destructive"
													onClick={() =>
														handleConfirmDelete(
															damage.id,
														)
													}
												>
													<Trash className="h-4 w-4 mr-2" />
													Delete
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									</div>

									{/* Header with type and date */}
									<div className="flex items-center mb-4 pl-2">
										<div className="bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 p-2 rounded-full mr-3">
											<AlertTriangle className="h-5 w-5" />
										</div>
										<div className="flex-1">
											<h3 className="font-semibold text-base flex items-center">
												{damage.damage_type ||
													"Untitled Damage"}

												<span className="ml-2 text-sm inline-flex items-center px-2.5 py-0.5 rounded-full font-medium bg-muted">
													{getStatusLabel(damage)}
												</span>
											</h3>
											<p className="text-sm text-muted-foreground flex items-center">
												<Clock className="w-4 h-4 mr-1 text-muted-foreground" />
												{formatDate(damage.damage_date)}
											</p>
										</div>
									</div>

									{/* Main content grid */}
									<div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3 pl-2">
										{/* Claims Section */}
										<div className="space-y-1">
											<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
												<Euro className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
												Claims
											</h4>
											<div className="space-y-2">
												<div className="flex justify-between">
													<span className="text-sm">
														Incoming:
													</span>
													<span className="text-sm font-medium">
														{formatCurrency(
															damage.damage_incoming_claim_amount,
														)}
													</span>
												</div>
												<div className="flex justify-between">
													<span className="text-sm">
														Outgoing:
													</span>
													<span className="text-sm font-medium">
														{formatCurrency(
															damage.damage_outgoing_claim_amount,
														)}
													</span>
												</div>
											</div>
										</div>

										{/* Liability Status */}
										<div className="space-y-1">
											<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
												<Shield className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
												Liability Status
											</h4>
											<div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
												{damage.liability_open && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-orange-500 mr-2" />
														<span>Open</span>
													</div>
												)}
												{damage.liability_sent && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-amber-500 mr-2" />
														<span>Sent</span>
													</div>
												)}
												{damage.liability_entered && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-purple-500 mr-2" />
														<span>Entered</span>
													</div>
												)}
												{damage.liability_billed && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-indigo-500 mr-2" />
														<span>Billed</span>
													</div>
												)}
												{damage.liability_accepted && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-blue-500 mr-2" />
														<span>Accepted</span>
													</div>
												)}
												{damage.liability_completed && (
													<div className="flex items-center">
														<div className="w-2 h-2 rounded-full bg-green-500 mr-2" />
														<span>Completed</span>
													</div>
												)}
											</div>
										</div>
									</div>

									{/* Notes Section */}
									{(damage.internal_note ||
										damage.liability_note) && (
										<div className="mt-4 pl-2">
											<div className="space-y-3">
												{damage.internal_note && (
													<div className="space-y-1">
														<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
															<FileText className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
															Internal Notes
														</h4>
														<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
															{
																damage.internal_note
															}
														</p>
													</div>
												)}

												{damage.liability_note && (
													<div className="space-y-1">
														<h4 className="text-xs font-medium text-muted-foreground mb-1 flex items-center">
															<FileText className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
															Liability Notes
														</h4>
														<p className="text-sm text-muted-foreground bg-gray-50 p-2 rounded-md dark:bg-gray-800/50">
															{
																damage.liability_note
															}
														</p>
													</div>
												)}
											</div>
										</div>
									)}
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>

			{/* Damage Edit Dialog */}
			<DamageDialog
				isOpen={isDialogOpen}
				onClose={handleCloseDialog}
				onSave={handleSaveEdit}
				editValues={editValues}
				onChange={handleChange}
				isEditing={!!editingId}
			/>

			{/* Delete Confirmation Dialog */}
			<Dialog
				open={isDeleteConfirmOpen}
				onOpenChange={setIsDeleteConfirmOpen}
			>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Confirm Deletion</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete this damage record?
							This action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<div className="flex items-center gap-2 py-3">
						<AlertCircle className="h-6 w-6 text-destructive" />
						<p className="text-sm">
							All data associated with this damage record will be
							permanently removed.
						</p>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsDeleteConfirmOpen(false)}
						>
							Cancel
						</Button>
						<Button variant="error" onClick={handleDeleteDamage}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
