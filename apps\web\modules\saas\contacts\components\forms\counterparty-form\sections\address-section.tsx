"use client";

import type { CreateCounterpartyInput } from "@repo/api/src/routes/counterparties/types";
import { OperatingHoursField } from "@saas/contacts/components/counterparty-view/operating-hours-field";
import { CountrySelect } from "@saas/shared/components/CountrySelect";
import {
	AddressAutocomplete,
	type AddressType,
} from "@shared/components/address/autocomplete";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Separator } from "@ui/components/separator";
import { Switch } from "@ui/components/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { MapPin, PlusCircle, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import type { VerifiedAddressField } from "../counterparty-form-wizard";

interface AddressSectionProps {
	verifiedFields?: VerifiedAddressField[];
	verifiedValues?: Record<string, string>;
	addressesArray: {
		fields: any[];
		append: (value: any) => void;
		remove: (index: number) => void;
		update: (index: number, value: any) => void;
	};
}

export function AddressSection({
	verifiedFields = [],
	verifiedValues = {},
	addressesArray,
}: AddressSectionProps) {
	const t = useTranslations();
	const form = useFormContext<CreateCounterpartyInput>();
	const { control } = form;
	// Add a state variable to force re-renders
	const [, setForceUpdate] = useState<number>(0);
	// Track which address we're currently editing with the place search
	const [activeAddressIndex, setActiveAddressIndex] = useState<number | null>(
		null,
	);
	// Track active tab for each address
	const [activeTabs, setActiveTabs] = useState<Record<number, string>>({});

	// Check if counterparty already has a PRIMARY address
	const hasPrimaryAddress = () => {
		return addressesArray.fields.some((field) => field.type === "PRIMARY");
	};

	// Function to add a new address
	const addAddress = () => {
		const hasPrimary = hasPrimaryAddress();

		addressesArray.append({
			type: hasPrimary ? "LOADING" : "PRIMARY", // Default to LOADING if PRIMARY exists
			isDefault: addressesArray.fields.length === 0, // Make default if it's the first one
			isGlobal: false,
			address: {
				nameLine: "", // Empty string for controlled input
				street: "",
				zipCode: "",
				city: "",
				country: "",
				addressSupplement: "",
				isVerified: false,
				latitude: 0,
				longitude: 0,
			},
		});
	};

	// Helper function to clean address data before submission
	const cleanAddressForSubmission = (addressData: any) => {
		return {
			...addressData,
			address: {
				...addressData.address,
				// Convert empty strings to undefined for optional fields
				nameLine: addressData.address.nameLine?.trim() || undefined,
				addressSupplement:
					addressData.address.addressSupplement?.trim() || undefined,
			},
		};
	};

	// Function to set an address as default and update other addresses
	const setAsDefault = (index: number) => {
		// Create a copy of all fields with updated isDefault values
		const updatedAddresses = addressesArray.fields.map((field, i) => ({
			...field,
			isDefault: i === index,
		}));

		// Update all addresses at once to avoid partial state updates
		updatedAddresses.forEach((address, i) => {
			addressesArray.update(i, address);
		});
	};

	// Handle address updates from autocomplete for a specific address in the array
	const handleAddressChange = (address: AddressType, index: number) => {
		try {
			// Get the current addressUsage to preserve other fields
			const currentAddressUsage = addressesArray.fields[index];

			// Create a complete updated addressUsage with all fields explicitly defined
			const updatedAddressUsage = {
				...currentAddressUsage,
				address: {
					...currentAddressUsage.address,
					// Preserve the nameLine field (don't overwrite from autocomplete)
					nameLine: currentAddressUsage.address.nameLine || "",
					street: address.address1 || "",
					addressSupplement: address.address2 || "",
					city: address.city || "",
					zipCode: address.postalCode || "",
					country: address.country || "",
					latitude: address.lat || 0,
					longitude: address.lng || 0,
					isVerified: true,
				},
			};

			// Update the entire addressUsage object at once
			addressesArray.update(index, updatedAddressUsage);

			// No need to trigger validation manually as form will handle it
		} catch (error) {
			console.error("Error updating address:", error);
		}
	};

	// Set the active tab for a specific address
	const setAddressActiveTab = (index: number, tab: string) => {
		setActiveTabs((prev) => ({ ...prev, [index]: tab }));
		// Force re-render to ensure the UI updates
		setForceUpdate((prev) => prev + 1);
	};

	// Get active tab for a specific address with fallback to 'address'
	const getAddressActiveTab = (index: number) => {
		return activeTabs[index] || "address";
	};

	// Check if configuration tab should be shown based on address type
	const hasConfigTab = (addressUsage: any) => {
		return ["LOADING", "UNLOADING"].includes(addressUsage.type);
	};

	// Helper to ensure address type is updated immediately in the UI
	const updateAddressType = (
		index: number,
		value: "PRIMARY" | "LOADING" | "UNLOADING",
	) => {
		// Update the form value directly
		form.setValue(`addressUsages.${index}.type`, value);

		// Update the addressUsage object in the fields array directly
		const updatedAddressUsage = {
			...addressesArray.fields[index],
			type: value,
		};
		addressesArray.update(index, updatedAddressUsage);

		// Force re-render to update UI immediately
		setForceUpdate((prev) => prev + 1);

		// If changing to LOADING/UNLOADING, switch to config tab
		if (["LOADING", "UNLOADING"].includes(value)) {
			setAddressActiveTab(index, "config");
		}
	};

	return (
		<div className="space-y-4">
			<h3 className="text-lg font-semibold">Addresses</h3>

			{addressesArray.fields.map((addressUsage, index) => (
				<Card key={addressUsage.id} className="mb-4">
					<CardContent className="pt-4">
						<div className="flex justify-between items-center mb-4">
							<div className="flex items-center gap-2">
								<h4 className="font-medium">
									Address {index + 1}
								</h4>
								{addressUsage.isDefault && (
									<Badge className="bg-primary/10">
										Default
									</Badge>
								)}
							</div>
							<div className="flex items-center gap-4">
								{!addressUsage.isDefault && (
									<div className="flex items-center gap-2">
										<Switch
											checked={false}
											onCheckedChange={() =>
												setAsDefault(index)
											}
											id={`default-${index}`}
										/>
										<Label htmlFor={`default-${index}`}>
											Set as default
										</Label>
									</div>
								)}
								{addressesArray.fields.length > 1 && (
									<Button
										type="button"
										variant="ghost"
										size="sm"
										onClick={() =>
											addressesArray.remove(index)
										}
									>
										<Trash2 className="h-4 w-4 mr-1" />
										Remove
									</Button>
								)}
							</div>
						</div>

						{hasConfigTab(addressUsage) ? (
							<Tabs
								value={getAddressActiveTab(index)}
								onValueChange={(value) =>
									setAddressActiveTab(index, value)
								}
								className="mb-4"
							>
								<TabsList className="grid w-full grid-cols-2">
									<TabsTrigger value="address">
										Address
									</TabsTrigger>
									<TabsTrigger value="config">
										Configuration
									</TabsTrigger>
								</TabsList>

								<TabsContent
									value="address"
									className="space-y-4 pt-4"
								>
									{/* Address content */}
									<AddressContentSection
										addressUsage={addressUsage}
										index={index}
										control={control}
										handleAddressChange={
											handleAddressChange
										}
										onAddressTypeChange={(value) => {
											updateAddressType(index, value);
										}}
										hasPrimaryAddress={hasPrimaryAddress}
									/>
								</TabsContent>

								<TabsContent
									value="config"
									className="space-y-4 pt-4"
								>
									{/* Global Configuration toggle - Update to Global Address */}
									<FormField
										control={control}
										name={`addressUsages.${index}.isGlobal`}
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between space-x-2 space-y-0 p-4 border rounded-md">
												<div>
													<FormLabel>
														Global Address
													</FormLabel>
													<p className="text-sm text-muted-foreground">
														This address can be
														shared across other
														contacts
													</p>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={
															field.onChange
														}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Loading-specific configuration */}
									{addressUsage.type === "LOADING" && (
										<>
											{/* Loading Instructions */}
											<FormField
												control={control}
												name={`addressUsages.${index}.loadingConfig.instructions`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Loading Instructions
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Special instructions for loading"
																className="min-h-24"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Slot Booking */}
											<FormField
												control={control}
												name={`addressUsages.${index}.loadingConfig.slotBooking`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Slot Booking
														</FormLabel>
														<FormControl>
															<Switch
																checked={
																	field.value
																}
																onCheckedChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Operating Hours */}
											<FormField
												control={control}
												name={`addressUsages.${index}.loadingConfig.operatingHours`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Operating Hours
														</FormLabel>
														<FormControl>
															<OperatingHoursField
																value={
																	field.value ||
																	{}
																}
																onChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}

									{/* Unloading-specific configuration */}
									{addressUsage.type === "UNLOADING" && (
										<>
											{/* Unloading Instructions */}
											<FormField
												control={control}
												name={`addressUsages.${index}.unloadingConfig.instructions`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Unloading
															Instructions
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Special instructions for unloading"
																className="min-h-24"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Slot Booking */}
											<FormField
												control={control}
												name={`addressUsages.${index}.unloadingConfig.slotBooking`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Slot Booking
														</FormLabel>
														<FormControl>
															<Switch
																checked={
																	field.value
																}
																onCheckedChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Operating Hours */}
											<FormField
												control={control}
												name={`addressUsages.${index}.unloadingConfig.operatingHours`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Operating Hours
														</FormLabel>
														<FormControl>
															<OperatingHoursField
																value={
																	field.value ||
																	{}
																}
																onChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}
								</TabsContent>
							</Tabs>
						) : (
							// Just show address content for PRIMARY type
							<div className="space-y-4">
								<AddressContentSection
									addressUsage={addressUsage}
									index={index}
									control={control}
									handleAddressChange={handleAddressChange}
									onAddressTypeChange={(value) => {
										updateAddressType(index, value);
									}}
									hasPrimaryAddress={hasPrimaryAddress}
								/>
							</div>
						)}
					</CardContent>
				</Card>
			))}

			<Button
				type="button"
				variant="outline"
				onClick={addAddress}
				className="w-full"
			>
				<PlusCircle className="h-4 w-4 mr-2" />
				Add Another Address
			</Button>
		</div>
	);
}

// Separated component for address fields
function AddressContentSection({
	addressUsage,
	index,
	control,
	handleAddressChange,
	onAddressTypeChange,
	hasPrimaryAddress,
}: {
	addressUsage: any;
	index: number;
	control: any;
	handleAddressChange: (address: AddressType, index: number) => void;
	onAddressTypeChange: (value: "PRIMARY" | "LOADING" | "UNLOADING") => void;
	hasPrimaryAddress: () => boolean;
}) {
	return (
		<>
			{/* Address Lookup */}
			<div className="mb-4">
				<FormLabel className="text-sm text-muted-foreground">
					Search for address
				</FormLabel>

				<div className="relative mt-2">
					<AddressAutocomplete
						key={`address-autocomplete-${index}`}
						onAddressChange={(address) =>
							handleAddressChange(address, index)
						}
						placeholder="Enter address to search"
					/>
					<div className="absolute right-3 top-1/2 -translate-y-1/2 text-primary/70">
						<MapPin className="h-4 w-4" />
					</div>
				</div>

				<div className="mt-2 flex items-center gap-2">
					<p className="text-xs text-muted-foreground">
						Use the search to quickly find and verify addresses, or
						enter details manually below
					</p>
				</div>
			</div>

			<Separator className="my-4" />

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<FormField
					control={control}
					name={`addressUsages.${index}.type`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Type</FormLabel>
							<Select
								value={field.value}
								onValueChange={(
									value: "PRIMARY" | "LOADING" | "UNLOADING",
								) => {
									// Just pass the value to parent handler
									onAddressTypeChange(value);
								}}
							>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select address type" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem
										value="PRIMARY"
										disabled={
											hasPrimaryAddress() &&
											addressUsage.type !== "PRIMARY"
										}
									>
										Primary{" "}
										{hasPrimaryAddress() &&
											addressUsage.type !== "PRIMARY" &&
											"(Already exists)"}
									</SelectItem>
									<SelectItem value="LOADING">
										Loading
									</SelectItem>
									<SelectItem value="UNLOADING">
										Unloading
									</SelectItem>
								</SelectContent>
							</Select>
							{hasPrimaryAddress() &&
								addressUsage.type !== "PRIMARY" && (
									<p className="text-xs text-muted-foreground mt-1">
										Only one primary address is allowed per
										counterparty
									</p>
								)}
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name={`addressUsages.${index}.address.nameLine`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Name Line</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter name line"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name={`addressUsages.${index}.address.street`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Street *</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="Enter street address"
									className={cn(
										addressUsage.address?.isVerified &&
											"border-green-600",
									)}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={control}
					name={`addressUsages.${index}.address.addressSupplement`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Address Supplement</FormLabel>
							<FormControl>
								<Input
									{...field}
									placeholder="Floor, unit, etc. (optional)"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<div className="grid grid-cols-2 gap-4">
					<FormField
						control={control}
						name={`addressUsages.${index}.address.zipCode`}
						render={({ field }) => (
							<FormItem>
								<FormLabel>ZIP Code *</FormLabel>
								<FormControl>
									<Input
										{...field}
										placeholder="Enter ZIP code"
										className={cn(
											addressUsage.address?.isVerified &&
												"border-green-600",
										)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name={`addressUsages.${index}.address.city`}
						render={({ field }) => (
							<FormItem>
								<FormLabel>City *</FormLabel>
								<FormControl>
									<Input
										{...field}
										placeholder="Enter city"
										className={cn(
											addressUsage.address?.isVerified &&
												"border-green-600",
										)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<FormField
					control={control}
					name={`addressUsages.${index}.address.country`}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Country *</FormLabel>
							<FormControl>
								<CountrySelect
									value={field.value}
									onValueChange={field.onChange}
									name={`addressUsages.${index}.address.country`}
									placeholder="Select country"
									className={cn(
										"w-full",
										addressUsage.address?.isVerified &&
											"border-green-600",
									)}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</>
	);
}

export default AddressSection;
