"use client";
import { useAnalytics } from "@analytics";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { usePurchases } from "@saas/payments/hooks/purchases";
import posthog from "posthog-js";
import { useEffect } from "react";

export function AnalyticsUserIdentifier() {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const { identifyUser } = useAnalytics();

	const { activePlan, hasSubscription } = usePurchases(
		activeOrganization?.id, // Pass organization ID to get org-specific plan
	);

	useEffect(() => {
		if (user?.id) {
			// Identify the user with their properties
			identifyUser(user.id, {
				email: user.email,
				name: user.name,
				username: user.username,
				onboardingComplete: user.onboardingComplete,
				role: user.role,
				// Organization context
				organizationId: activeOrganization?.id,
				organizationName: activeOrganization?.name,
				// Subscription information
				subscriptionPlan: activePlan?.id,
			});

			// Group tracking by organization (for multi-tenant analytics)
			if (activeOrganization?.id) {
				posthog.group("organization", activeOrganization.id, {
					name: activeOrganization.name,
					plan: activePlan?.id || "free",
				});
			}
		}
	}, [user?.id, activeOrganization?.id, activePlan?.id, hasSubscription]);

	return null;
}
