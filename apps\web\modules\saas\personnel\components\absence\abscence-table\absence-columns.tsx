"use client";

import type { PersonnelAbsenceTableEntry } from "@repo/api/src/routes/personnel-absence/lib/absences/types";
import { useDepartments } from "@saas/organizations/hooks/use-config";
import { useAbsenceUI } from "@saas/personnel/context/absence-ui-context";

import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { createColumnHelper } from "@tanstack/react-table";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { MoreHorizontal } from "lucide-react";
import { useMemo } from "react";
import { AbsenceMonthCell } from "./absence-month-cell";

// Define the ColumnMeta interface with the correct types
declare module "@tanstack/react-table" {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
		className?: string; // Add className to the ColumnMeta interface
	}
}

type ActionsCellProps = {
	row: Row<PersonnelAbsenceTableEntry>;
	departmentName?: string;
};

function ActionsCell({ row, departmentName }: ActionsCellProps) {
	const employee = row.original;
	const { openHistorySheet, openEntitlementDialog } = useAbsenceUI();

	const handleEditEntitlements = () => {
		openEntitlementDialog({
			personnelId: employee.personnelId,
			firstName: employee.firstName,
			lastName: employee.lastName,
			departmentId: employee.departmentId,
			departmentName: departmentName,
			entitlement: employee.entitlement
				? employee.entitlement.holidayEntitlementDays
				: null,
		});
	};

	const handleViewAbsences = () => {
		openHistorySheet(
			employee.personnelId,
			`${employee.firstName} ${employee.lastName}`,
		);
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-7 w-7 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuItem onClick={handleViewAbsences}>
					View Absences
				</DropdownMenuItem>
				<DropdownMenuItem onClick={handleEditEntitlements}>
					Edit Entitlements
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

const columnHelper = createColumnHelper<PersonnelAbsenceTableEntry>();

// Custom month column header with minimal styling
function MonthColumnHeader({ column, title }: { column: any; title: string }) {
	return (
		<div className="text-sm text-center text-muted-foreground whitespace-nowrap font-medium">
			{title}
		</div>
	);
}

// PersonnelAvatar component to display initials
function PersonnelAvatar({
	firstName,
	lastName,
}: { firstName: string; lastName: string }) {
	const initials = firstName?.[0] + lastName?.[0];

	return (
		<Avatar className="h-8 w-8 mr-3 rounded-full">
			<AvatarFallback className="bg-primary/10 text-primary font-medium">
				{initials}
			</AvatarFallback>
		</Avatar>
	);
}

export function useColumns() {
	const months = [
		"Jan",
		"Feb",
		"Mar",
		"Apr",
		"May",
		"Jun",
		"Jul",
		"Aug",
		"Sep",
		"Oct",
		"Nov",
		"Dec",
	] as const;
	const { openHistorySheet, openEntitlementDialog } = useAbsenceUI();

	// Get departments to display names
	const { data: departmentsData } = useDepartments();

	// Create a map of department IDs to department names for quick lookup
	const departmentMap = useMemo(() => {
		const map = new Map<string, string>();
		departmentsData?.items?.forEach((dept) => {
			map.set(dept.id, dept.name);
		});
		return map;
	}, [departmentsData]);

	const columns: ColumnDef<PersonnelAbsenceTableEntry, any>[] = [
		columnHelper.accessor((row) => `${row.firstName} ${row.lastName}`, {
			id: "employee",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Employee" />
			),
			cell: ({ row }) => {
				const fullName = `${row.original.firstName} ${row.original.lastName}`;
				const departmentId = row.original.departmentId;
				const departmentName = departmentId
					? departmentMap.get(departmentId)
					: null;

				return (
					<div className="flex items-center">
						<PersonnelAvatar
							firstName={row.original.firstName}
							lastName={row.original.lastName}
						/>
						<div className="flex flex-col">
							<div>{fullName}</div>
							{departmentName && (
								<div className="inline-flex mt-1">
									<span className="text-xs px-2 py-0.5 rounded-full bg-muted text-muted-foreground">
										{departmentName}
									</span>
								</div>
							)}
						</div>
					</div>
				);
			},
		}),
		columnHelper.accessor(
			(row) => ({
				entitled: row.entitledDays,
				used: row.usedDays,
				open: row.openDays,
			}),
			{
				id: "leaveBalance",
				header: ({ column }) => (
					<DataTableColumnHeader
						column={column}
						title="Leave Balance"
					/>
				),
				cell: (info) => {
					const { entitled, used, open } = info.getValue();

					// Check if entitlement is not configured
					if (!entitled) {
						return (
							<TooltipProvider delayDuration={300}>
								<Tooltip>
									<TooltipTrigger asChild>
										<div className="flex items-center justify-start h-full py-2 cursor-default">
											<div className="flex items-center px-3 py-1 rounded-full bg-muted text-muted-foreground text-xs">
												<span>Not configured</span>
											</div>
										</div>
									</TooltipTrigger>
									<TooltipContent
										side="right"
										align="start"
										className="p-3 max-w-xs"
									>
										<div className="space-y-2">
											<div className="text-sm font-semibold mb-1">
												Missing Entitlement
											</div>
											<div className="text-sm text-muted-foreground">
												No leave entitlement has been
												configured for this employee.
											</div>
											<div className="text-xs font-medium">
												Use the "Edit Entitlements"
												action to set up leave
												entitlement.
											</div>
										</div>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						);
					}

					const usagePercentage = (used / entitled) * 100;
					const isOverused = used > entitled;
					const isWarning = usagePercentage >= 75 && !isOverused;

					const getProgressBarColor = () => {
						if (isOverused) {
							return "bg-destructive";
						}
						if (isWarning) {
							return "bg-warning";
						}
						return "bg-primary";
					};

					const getTextColor = () => {
						if (isOverused) {
							return "text-destructive font-semibold";
						}
						if (isWarning) {
							return "text-warning font-semibold";
						}
						return "text-muted-foreground";
					};

					return (
						<TooltipProvider delayDuration={300}>
							<Tooltip>
								<TooltipTrigger asChild>
									<div className="flex flex-col items-start justify-center h-full py-2 cursor-default">
										<div
											className={cn(
												"text-sm text-center w-24",
												isOverused &&
													"text-destructive font-semibold",
											)}
										>
											{open} days
										</div>
										<div className="relative h-2 w-24 rounded-full bg-muted mt-2 mb-6">
											<div
												className={cn(
													"h-full rounded-full",
													getProgressBarColor(),
												)}
												style={{
													width: `${Math.min(usagePercentage, 100)}%`,
												}}
											/>
											<div
												className={cn(
													"absolute top-3 left-0 right-0 text-xs text-center",
													getTextColor(),
												)}
											>
												{used}/{entitled}
											</div>
										</div>
									</div>
								</TooltipTrigger>
								<TooltipContent
									side="right"
									align="start"
									className="p-3 max-w-xs"
								>
									<div className="space-y-2">
										<div className="text-sm font-semibold mb-1">
											Leave Balance Details
										</div>
										<div className="flex justify-between gap-4">
											<span className="font-medium text-muted-foreground">
												Entitled days:
											</span>
											<span>{entitled} days</span>
										</div>
										<div className="flex justify-between gap-4">
											<span className="font-medium text-muted-foreground">
												Used days:
											</span>
											<span
												className={
													isOverused
														? "text-destructive font-semibold"
														: ""
												}
											>
												{used} days
											</span>
										</div>
										<div className="flex justify-between gap-4">
											<span className="font-medium text-muted-foreground">
												Remaining days:
											</span>
											<span
												className={
													isOverused
														? "text-destructive font-semibold"
														: "font-semibold"
												}
											>
												{open} days{" "}
												{isOverused && "(exceeded)"}
											</span>
										</div>
										{isOverused && (
											<div className="text-destructive text-xs mt-1 font-medium">
												Employee has exceeded their
												leave allowance!
											</div>
										)}
										{isWarning && (
											<div className="text-warning text-xs mt-1 font-medium">
												Employee is nearing their leave
												limit.
											</div>
										)}
									</div>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					);
				},
			},
		),
		columnHelper.accessor("sickLeaveDays", {
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Sick Leave" />
			),
			cell: (info) => {
				const sickLeaveDays = info.getValue();
				return (
					<TooltipProvider delayDuration={300}>
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="cursor-default">
									{sickLeaveDays} days
								</div>
							</TooltipTrigger>
							<TooltipContent
								side="top"
								align="center"
								className="p-3 max-w-xs"
							>
								<div className="space-y-2">
									<div className="text-sm font-semibold mb-1">
										Sick Leave Details
									</div>
									<div className="flex justify-between gap-4">
										<span className="font-medium text-muted-foreground">
											Used sick days:
										</span>
										<span className="font-semibold">
											{sickLeaveDays} days
										</span>
									</div>
									<div className="text-xs text-muted-foreground mt-1">
										Last updated:{" "}
										{new Date().toLocaleDateString()}
									</div>
								</div>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				);
			},
		}),
		// Month columns
		...months.map((month) =>
			columnHelper.accessor((row) => row.absencesByMonth[month], {
				id: month,
				header: ({ column }) => (
					<MonthColumnHeader column={column} title={month} />
				),
				cell: ({ row }) => (
					<div className="text-center">
						<AbsenceMonthCell
							absenceDays={row.original.absencesByMonth[month]}
							month={month}
							employeeId={row.original.personnelId}
						/>
					</div>
				),
				size: 55,
				meta: {
					className: "p-1 px-2", // Add more padding for breathing room
				},
			}),
		),
		columnHelper.display({
			id: "actions",
			cell: ({ row }) => {
				const departmentId = row.original.departmentId;
				const departmentName = departmentId
					? departmentMap.get(departmentId)
					: undefined;
				return (
					<ActionsCell row={row} departmentName={departmentName} />
				);
			},
			meta: {
				// Add getRowActions for keyboard shortcut support
				getRowActions: (row: Row<PersonnelAbsenceTableEntry>) => {
					const employee = row.original;
					const departmentId = employee.departmentId;
					const departmentName = departmentId
						? departmentMap.get(departmentId)
						: undefined;

					return {
						openView: () => {
							openHistorySheet(
								employee.personnelId,
								`${employee.firstName} ${employee.lastName}`,
							);
						},
						openEdit: () => {
							openEntitlementDialog({
								personnelId: employee.personnelId,
								firstName: employee.firstName,
								lastName: employee.lastName,
								departmentId: employee.departmentId,
								departmentName: departmentName,
								entitlement: employee.entitlement
									? employee.entitlement
											.holidayEntitlementDays
									: null,
							});
						},
						openDelete: () => {
							// Not implemented for this table
							console.log(
								"Delete action not implemented for absences table",
							);
						},
					};
				},
			},
		}),
	];

	return columns;
}
