"use client";

import type { CreateCounterpartyInput } from "@repo/api/src/routes/counterparties/types";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { HelpCircle, Plus, Trash, Truck } from "lucide-react";
import { useFieldArray, useFormContext } from "react-hook-form";

export function FreightExchangeSection() {
	const form = useFormContext<CreateCounterpartyInput>();
	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "freightExchangeProfile.freightExchanges",
	});

	return (
		<Card>
			<CardHeader>
				<CardTitle>Freight Exchanges</CardTitle>
			</CardHeader>
			<CardContent className="bg-gradient-to-r from-[#C6E6FA] via-[#E0F2FD] to-[#C6E6FA] dark:from-[#01A1E1]/5 dark:via-card/95 dark:to-[#01A1E1]/5 p-6 rounded-b-xl border-t border-border space-y-6">
				<div className="space-y-4">
					{/* Specialized Freight Exchange Platforms */}
					<div className="mt-4">
						<div className="flex items-center gap-2 mb-2">
							<Truck className="h-4 w-4 text-primary" />
							<h3 className="text-sm font-medium text-foreground">
								Specialized Freight Exchange Platforms
							</h3>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<FormField
								control={form.control}
								name="freightExchangeProfile.timocomNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Timocom
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Timocom freight
															exchange identifier
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Timocom number"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="freightExchangeProfile.teleroute"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Teleroute
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Teleroute freight
															exchange identifier
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Teleroute ID"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="freightExchangeProfile.transEu"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center gap-1">
											Trans.eu
											<TooltipProvider>
												<Tooltip>
													<TooltipTrigger asChild>
														<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
													</TooltipTrigger>
													<TooltipContent>
														<p>
															Trans.eu freight
															exchange identifier
														</p>
													</TooltipContent>
												</Tooltip>
											</TooltipProvider>
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Trans.eu ID"
												className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* Freight Exchanges List */}
					<div className="mt-6">
						<div className="flex items-center gap-1 mb-4">
							<div className="flex items-center gap-2 mb-2">
								<Truck className="h-4 w-4 text-primary" />
								<h3 className="text-sm font-medium text-foreground">
									Additional Freight Exchanges
								</h3>
							</div>
							<div className="flex-1" />
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={() => append({ name: "", number: "" })}
								className="h-8"
							>
								<Plus className="h-4 w-4 mr-2" />
								Add Exchange
							</Button>
						</div>

						<div className="space-y-4">
							{fields.map((field, index) => (
								<div
									key={field.id}
									className="grid grid-cols-[1fr_1fr_auto] gap-4 items-center"
								>
									<FormField
										control={form.control}
										name={`freightExchangeProfile.freightExchanges.${index}.name`}
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-xs text-muted-foreground">
													Exchange Name
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter exchange name"
														className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`freightExchangeProfile.freightExchanges.${index}.number`}
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-xs text-muted-foreground">
													Exchange Number
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter exchange number"
														className="transition-colors duration-200 focus:border-primary/50 focus:ring-1 focus:ring-primary/30"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<Button
										type="button"
										variant="ghost"
										size="icon"
										onClick={() => remove(index)}
										className="h-10 w-10 text-destructive hover:text-destructive/90 self-end mb-[2px]"
									>
										<Trash className="h-4 w-4" />
									</Button>
								</div>
							))}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

export default FreightExchangeSection;
