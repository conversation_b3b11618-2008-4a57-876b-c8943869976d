import type { UpdateCustomerConfigurationInput } from "@repo/api/src/routes/settings/contact-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	fetchCustomerConfiguration,
	useCustomerConfigurationQuery,
	useUpdateCustomerConfigurationMutation,
} from "@saas/organizations/lib/api-customer-settings";

// Type for frontend form values
export type CustomerSettingsFormValues = Omit<
	UpdateCustomerConfigurationInput,
	"organizationId"
>;

// Main hook for customer configuration
export function useCustomerSettings() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	// Query for customer configuration
	const query = useCustomerConfigurationQuery(organizationId, {
		enabled: !!organizationId,
	});

	// Mutation for updating customer configuration
	const updateMutation =
		useUpdateCustomerConfigurationMutation(organizationId);

	// Wrapped update function
	const updateCustomerSettings = async (data: CustomerSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		return await updateMutation.mutateAsync(data);
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
		refetch: query.refetch,
		updateCustomerSettings,
		isUpdating: updateMutation.isPending,
	};
}

// Hook for accessing raw fetch function
export function useCustomerSettingsRaw() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const updateMutation =
		useUpdateCustomerConfigurationMutation(organizationId);

	const fetchSettings = async () => {
		if (!organizationId) {
			throw new Error("No active organization");
		}
		return await fetchCustomerConfiguration(organizationId);
	};

	const updateSettings = async (data: CustomerSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		return await updateMutation.mutateAsync(data);
	};

	return {
		fetchSettings,
		updateSettings,
		isUpdating: updateMutation.isPending,
	};
}
