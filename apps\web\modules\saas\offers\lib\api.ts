import type {
	CreateOfferInput,
	OfferStatus,
} from "@repo/api/src/routes/offers/types";
import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

type FetchOffersParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	status?: OfferStatus;
	orderId?: string;
	customerId?: string;
	startDate?: Date;
	endDate?: Date;
};

export const offerKeys = {
	all: ["offers"] as const,
	list: (params: FetchOffersParams) =>
		[...offerKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...offerKeys.all, "detail", organizationId, id] as const,
};

export const fetchOffers = async (params: FetchOffersParams) => {
	const response = await apiClient.offers.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			status: params.status,
			orderId: params.orderId,
			customerId: params.customerId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch offers list");
	}

	return response.json();
};

export const useOffersQuery = (params: FetchOffersParams) => {
	return useQuery({
		queryKey: offerKeys.list(params),
		queryFn: () => fetchOffers(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchOfferById = async (organizationId: string, id: string) => {
	const response = await apiClient.offers[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch offer details");
	}

	return response.json();
};

export const useOfferByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: offerKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchOfferById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create offer mutation
export const useCreateOfferMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (data: CreateOfferInput) => {
			const response = await apiClient.offers.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create offer");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Offer created successfully");
			// Query invalidation is now handled in the useOfferMutations hook
		},
		onError: (error) => {
			toast.error("Failed to create offer");
			console.error("Create offer error:", error);
		},
	});
};

// Accept offer mutation
export const useAcceptOfferMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.offers[":id"].accept.$put({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to accept offer");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Offer accepted successfully");
			// Query invalidation is now handled in the useOfferMutations hook
		},
		onError: (error) => {
			toast.error("Failed to accept offer");
			console.error("Accept offer error:", error);
		},
	});
};

// Cancel offer mutation
export interface CancelOfferParams {
	id: string;
	decline_reason?: string | null;
}

export const useCancelOfferMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (params: string | CancelOfferParams) => {
			const id = typeof params === "string" ? params : params.id;
			const decline_reason =
				typeof params === "string" ? undefined : params.decline_reason;

			const response = await apiClient.offers[":id"].cancel.$put({
				param: { id },
				query: {
					organizationId,
					declineReason:
						decline_reason === null ? undefined : decline_reason,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to cancel offer");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Offer cancelled successfully");
			// Query invalidation is now handled in the useOfferMutations hook
		},
		onError: (error) => {
			toast.error("Failed to cancel offer");
			console.error("Cancel offer error:", error);
		},
	});
};

// Send offer email mutation
export interface SendOfferEmailParams {
	id: string;
	recipientEmail?: string;
	ccEmails?: string[];
	bccEmails?: string[];
}

export const useSendOfferEmailMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async ({
			id,
			recipientEmail,
			ccEmails,
			bccEmails,
		}: SendOfferEmailParams) => {
			const response = await apiClient.offers[":id"]["send-email"].$post({
				param: { id },
				query: { organizationId },
				json: { recipientEmail, ccEmails, bccEmails },
			});

			if (!response.ok) {
				// Try to parse error message from response, but handle parsing errors gracefully
				const errorData = await response.json().catch(() => null);
				// Extract error message from any potential response format
				const errorMessage = "Failed to send offer email";

				console.log(errorData);

				throw new Error(errorMessage);
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Offer email sent successfully");
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to send offer email",
			);
			console.error("Send offer email error:", error);
		},
	});
};

// Email preview query
export const useOfferEmailPreview = (
	organizationId: string,
	offerId: string,
	recipientEmail?: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: [
			...offerKeys.detail(offerId),
			"email-preview",
			recipientEmail,
		],
		queryFn: async () => {
			if (!offerId) {
				throw new Error("Offer ID is required");
			}

			const response = await apiClient.offers[":id"][
				"email-preview"
			].$get({
				param: { id: offerId },
				query: {
					organizationId,
					...(recipientEmail ? { recipientEmail } : {}),
				},
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => null);
				let errorMessage = "Failed to fetch email preview";

				// Check if the error response has the expected structure
				if (errorData && typeof errorData === "object") {
					if (
						"error" in errorData &&
						typeof errorData.error === "object" &&
						errorData.error &&
						"message" in errorData.error
					) {
						errorMessage = errorData.error.message as string;
					}
				}

				throw new Error(errorMessage);
			}

			return response.json();
		},
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!offerId && !!organizationId,
	});
};
