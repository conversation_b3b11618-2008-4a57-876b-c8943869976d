import type { OfferStatus } from "@repo/api/src/routes/offers/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useQueryClient } from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import {
	type CancelOfferParams,
	type SendOfferEmailParams,
	useAcceptOfferMutation,
	useCancelOfferMutation,
	useCreateOfferMutation,
	useOfferByIdQuery,
	useOffersQuery,
	useSendOfferEmailMutation,
} from "../lib/api";
import { offerKeys } from "../lib/api";

export function useOffers(initialOrderId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [status, setStatus] = useState<OfferStatus | undefined>(undefined);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});
	const [orderId, setOrderId] = useState<string | undefined>(initialOrderId);
	const [customerId, setCustomerId] = useState<string | undefined>(undefined);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useOffersQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		status,
		orderId,
		customerId,
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
	});

	const cancelMutation = useCancelOfferMutation(activeOrganization?.id ?? "");
	const acceptMutation = useAcceptOfferMutation(activeOrganization?.id ?? "");

	// Wrap the cancel function to refetch after cancellation
	const cancelOffer = async (idOrParams: string | CancelOfferParams) => {
		if (activeOrganization?.id) {
			try {
				await cancelMutation.mutateAsync(idOrParams);
			} catch (error) {
				console.error("Cancel offer error:", error);
			}
		}
	};

	// Wrap the accept function to refetch after acceptance
	const acceptOffer = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await acceptMutation.mutateAsync(id);
			} catch (error) {
				console.error("Accept offer error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange,
		orderId,
		setOrderId,
		customerId,
		setCustomerId,
		refetch: query.refetch,
		cancelOffer,
		acceptOffer,
	};
}

export function useOfferById(offerId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useOfferByIdQuery(activeOrganization?.id, offerId);
}

export function useOfferMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const queryClient = useQueryClient();

	// Helper function to handle refetching offers after mutations
	const refetchOffers = (orderId?: string) => {
		// Invalidate the general offers list
		queryClient.invalidateQueries({
			queryKey: offerKeys.all,
		});

		// Call the external onSuccess callback if provided, but don't rely on it for refetching
		if (options?.onSuccess) {
			options.onSuccess();
		}
	};

	const createMutation = useCreateOfferMutation(orgId);
	const acceptMutation = useAcceptOfferMutation(orgId);
	const cancelMutation = useCancelOfferMutation(orgId);
	const sendEmailMutation = useSendOfferEmailMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		try {
			const result = await createMutation.mutateAsync(data);
			// Handle refetching here
			refetchOffers(data.orderId);
			return result;
		} catch (error) {
			console.error("Create offer error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const acceptWithCallback = async (id: string) => {
		try {
			const result = await acceptMutation.mutateAsync(id);
			// Handle refetching here
			refetchOffers(result?.orderId);
			return result;
		} catch (error) {
			console.error("Accept offer error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const cancelWithCallback = async (
		idOrParams: string | CancelOfferParams,
	) => {
		try {
			const result = await cancelMutation.mutateAsync(idOrParams);
			// Handle refetching here
			refetchOffers(result?.orderId);
			return result;
		} catch (error) {
			console.error("Cancel offer error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const sendEmailWithCallback = async (params: SendOfferEmailParams) => {
		try {
			const result = await sendEmailMutation.mutateAsync(params);
			return result;
		} catch (error) {
			console.error("Send offer email error:", error);
			throw error;
		}
	};

	return {
		createOffer: createWithCallback,
		acceptOffer: acceptWithCallback,
		cancelOffer: cancelWithCallback,
		sendOfferEmail: sendEmailWithCallback,
		isLoading:
			createMutation.isPending ||
			acceptMutation.isPending ||
			cancelMutation.isPending ||
			sendEmailMutation.isPending,
	};
}
