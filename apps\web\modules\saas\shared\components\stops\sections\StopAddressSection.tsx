"use client";

import { CountrySelect } from "@saas/shared/components/CountrySelect";
import { AddressSelector } from "@saas/shared/components/stops/components/AddressSelector";
import {
	formatInstructions,
	formatOperatingHours,
	formatSlotBookingWarning,
	getAddressConfig,
} from "@saas/shared/lib/stops/address-utils";
import { getFieldName } from "@saas/shared/lib/stops/stop-form-utils";
import type { StopSectionProps } from "@saas/shared/lib/stops/stop-types";
import {
	AddressAutocomplete,
	type AddressType,
} from "@shared/components/address/autocomplete";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Separator } from "@ui/components/separator";
import { useTranslations } from "next-intl";
import React from "react";
import { useFormContext, useWatch } from "react-hook-form";

export function StopAddressSection({
	control,
	fieldPrefix = "stop",
	disabled = false,
	onlyView = false,
	counterpartyId,
}: StopSectionProps & { counterpartyId?: string }) {
	const t = useTranslations();
	const form = useFormContext(); // Access the form context to get setValue

	// Watch all required fields at the component top level
	const stopType = useWatch({
		control,
		name: getFieldName("stopType", fieldPrefix),
	});

	// Watch the selected address to display operating hours and instructions
	const selectedAddress = useWatch({
		control,
		name: getFieldName("selectedAddress", fieldPrefix),
	});

	// Also watch street to handle cases where address may be partially selected
	const street = useWatch({
		control,
		name: getFieldName("street", fieldPrefix),
	});

	// Watch coordinates for conditional display
	const latitude = useWatch({
		control,
		name: getFieldName("latitude", fieldPrefix),
	});

	const longitude = useWatch({
		control,
		name: getFieldName("longitude", fieldPrefix),
	});

	// Get config information
	const addressConfig = getAddressConfig(selectedAddress, stopType);

	// Check if coordinates exist
	const hasCoordinates = latitude && longitude;

	// Handle address selection from autocomplete
	const handleAddressChange = (address: AddressType) => {
		if (address && form) {
			// Use the form's setValue method instead of control.setValue
			form.setValue(
				getFieldName("street", fieldPrefix),
				address.address1,
				{ shouldValidate: true },
			);
			form.setValue(
				getFieldName("addressSupplement", fieldPrefix),
				address.address2,
				{ shouldValidate: true },
			);
			form.setValue(
				getFieldName("zipCode", fieldPrefix),
				address.postalCode,
				{ shouldValidate: true },
			);
			form.setValue(getFieldName("city", fieldPrefix), address.city, {
				shouldValidate: true,
			});
			form.setValue(
				getFieldName("country", fieldPrefix),
				address.country,
				{ shouldValidate: true },
			);

			// Set latitude and longitude - handling decimal separators properly
			if (address.lat) {
				// Convert to string using dot as decimal separator
				const latString = address.lat.toString().replace(",", ".");
				form.setValue(
					getFieldName("latitude", fieldPrefix),
					Number.parseFloat(latString),
					{ shouldValidate: true },
				);
			}

			if (address.lng) {
				// Convert to string using dot as decimal separator
				const lngString = address.lng.toString().replace(",", ".");
				form.setValue(
					getFieldName("longitude", fieldPrefix),
					Number.parseFloat(lngString),
					{ shouldValidate: true },
				);
			}
		}
	};

	if (onlyView) {
		// Implement view-only mode if needed
		return null;
	}

	return (
		<div className="border rounded-md p-4 space-y-4">
			<h3 className="text-sm font-medium">Address Information</h3>

			{/* Address Autocomplete */}
			{!disabled && (
				<div className="mb-4">
					<FormLabel>Search Address</FormLabel>
					<AddressAutocomplete
						onAddressChange={handleAddressChange}
						placeholder="Search for an address"
					/>
					<p className="text-xs text-muted-foreground mt-2">
						Search for an address or manually enter the details
						below
					</p>
				</div>
			)}

			{/* Address Selector */}
			{!disabled && (
				<>
					<AddressSelector
						fieldPrefix={fieldPrefix}
						stopType={stopType}
						counterpartyId={counterpartyId}
					/>

					<div className="flex items-center my-3">
						<div className="flex-grow border-t border-gray-200 dark:border-gray-700" />
						<span className="px-3 text-xs text-muted-foreground">
							or enter manually
						</span>
						<div className="flex-grow border-t border-gray-200 dark:border-gray-700" />
					</div>
				</>
			)}

			<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
				{/* Name Line */}
				<FormField
					control={control}
					name={getFieldName("nameLine", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Name Line</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter name line"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Street */}
				<FormField
					control={control}
					name={getFieldName("street", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Street</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter street"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Entrance Number */}
				<FormField
					control={control}
					name={getFieldName("entrance_number", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Entrance Number</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter entrance number"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Address Supplement */}
				<FormField
					control={control}
					name={getFieldName("addressSupplement", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Address Supplement</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter additional address info"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Zip Code */}
				<FormField
					control={control}
					name={getFieldName("zipCode", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Zip Code</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter zip code"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* City */}
				<FormField
					control={control}
					name={getFieldName("city", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>City</FormLabel>
							<FormControl>
								<Input
									{...field}
									value={field.value || ""}
									placeholder="Enter city"
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Country */}
				<FormField
					control={control}
					name={getFieldName("country", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Country</FormLabel>
							<FormControl>
								<CountrySelect
									{...field}
									name={field.name}
									placeholder="Select country"
									disabled={disabled}
									onValueChange={(value) => {
										field.onChange(value);
									}}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Coordinates Fields */}
			<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 mt-4">
				<FormField
					control={control}
					name={getFieldName("latitude", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Latitude</FormLabel>
							<FormControl>
								<Input
									{...field}
									type="text"
									inputMode="decimal"
									pattern="[0-9]*[.,]?[0-9]*"
									placeholder="Enter latitude (e.g., 52,5200)"
									value={
										field.value === undefined ||
										field.value === null
											? ""
											: String(field.value).replace(
													",",
													".",
												)
									}
									onChange={(e) => {
										// Replace commas with dots for consistency
										const value = e.target.value.replace(
											",",
											".",
										);
										const numericValue =
											value === ""
												? undefined
												: Number.parseFloat(value);
										field.onChange(numericValue);
									}}
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={control}
					name={getFieldName("longitude", fieldPrefix)}
					render={({ field }) => (
						<FormItem>
							<FormLabel>Longitude</FormLabel>
							<FormControl>
								<Input
									{...field}
									type="text"
									inputMode="decimal"
									pattern="[0-9]*[.,]?[0-9]*"
									placeholder="Enter longitude (e.g., 13,4050)"
									value={
										field.value === undefined ||
										field.value === null
											? ""
											: String(field.value).replace(
													",",
													".",
												)
									}
									onChange={(e) => {
										// Replace commas with dots for consistency
										const value = e.target.value.replace(
											",",
											".",
										);
										const numericValue =
											value === ""
												? undefined
												: Number.parseFloat(value);
										field.onChange(numericValue);
									}}
									disabled={disabled}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Coordinates info - only if they exist */}
			{hasCoordinates && (
				<div className="mt-2 text-xs text-muted-foreground">
					<p>
						These coordinates will be used for mapping and route
						optimization.
					</p>
				</div>
			)}

			{/* Display Operating Hours and Instructions if available */}
			{selectedAddress && street && (
				<>
					<Separator className="my-2" />

					{/* Slot Booking Warning Section */}
					{formatSlotBookingWarning(
						addressConfig?.slotBooking,
						stopType,
					)}

					{/* Operating Hours Section */}
					{addressConfig?.operatingHours &&
						formatOperatingHours(addressConfig.operatingHours)}

					{/* Instructions Section */}
					{addressConfig?.instructions &&
						formatInstructions(
							addressConfig.instructions,
							stopType,
						)}
				</>
			)}
		</div>
	);
}
