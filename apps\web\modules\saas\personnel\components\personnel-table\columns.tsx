"use client";

import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import { useRouter } from "@shared/hooks/router";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Eye, MoreHorizontal, Trash, User } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { usePersonnelUI } from "../../context/personnel-ui-context";
import type { PersonnelWithRelations } from "../../lib/types";

declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

type ActionsCellProps = {
	row: Row<PersonnelWithRelations>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const t = useTranslations();
	const personnel = row.original;
	const { handleDeletePersonnel } = usePersonnelUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	const navigateToPersonnelView = () => {
		router.push(
			`/app/${params.organizationSlug}/personnel/${personnel.id}`,
		);
	};

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>
					{t("app.personnel.table.actions.title")}
				</DropdownMenuLabel>
				<DropdownMenuItem onClick={navigateToPersonnelView}>
					<Eye className="mr-2 h-4 w-4" />
					{t("app.personnel.table.actions.view")}
				</DropdownMenuItem>
				<DropdownMenuSeparator />
				<DropdownMenuItem
					className="text-destructive focus:text-destructive"
					onClick={() => handleDeletePersonnel(personnel)}
					data-delete-action
				>
					<Trash className="mr-2 h-4 w-4" />
					{t("app.personnel.table.actions.delete")}
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<PersonnelWithRelations>[] {
	const t = useTranslations();
	const { handleDeletePersonnel } = usePersonnelUI();
	const router = useRouter();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "firstName",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.personnel.table.columns.firstName")}
				/>
			),
			cell: ({ row }) => {
				const firstName = row.getValue("firstName") as string;
				return (
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-muted-foreground" />
						<span>{firstName}</span>
					</div>
				);
			},
		},
		{
			accessorKey: "lastName",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.personnel.table.columns.lastName")}
				/>
			),
		},
		{
			accessorKey: "email",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.personnel.table.columns.email")}
				/>
			),
		},
		{
			accessorKey: "department",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={t("app.personnel.table.columns.department")}
				/>
			),
			cell: ({ row }) => {
				const department = row.original.department;
				return <div>{department ? department.name : "-"}</div>;
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<PersonnelWithRelations>) => {
					const personnel = row.original;
					return {
						openView: () => {
							router.push(
								`/app/${params.organizationSlug}/personnel/${personnel.id}`,
							);
						},
						openDelete: () => handleDeletePersonnel(row.original),
					};
				},
			},
		},
	];
}
