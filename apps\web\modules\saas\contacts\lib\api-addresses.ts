import type {
	CreateAddressUsageInput,
	UpdateAddressUsageInput,
} from "@repo/api/src/routes/counterparties/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define query keys for caching and invalidation
export const addressKeys = {
	all: ["addresses"] as const,
	list: (
		organizationId: string,
		counterpartyId: string,
		options?: { includeBlocked?: boolean },
	) =>
		[
			...addressKeys.all,
			"list",
			organizationId,
			counterpartyId,
			options,
		] as const,
	detail: (organizationId?: string, counterpartyId?: string, id?: string) =>
		[
			...addressKeys.all,
			"detail",
			organizationId,
			counterpartyId,
			id,
		] as const,
};

// Fetch addresses list for a counterparty
export const fetchAddresses = async (
	organizationId: string,
	counterpartyId: string,
	options: { includeBlocked?: boolean } = {},
) => {
	const response = await apiClient.counterparties[
		":counterpartyId"
	].addresses.$get({
		param: { counterpartyId },
		query: {
			organizationId,
			counterpartyId,
			includeBlocked: options.includeBlocked?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch addresses list");
	}

	return response.json();
};

// Hook for fetching addresses list
export const useAddressesQuery = (
	organizationId: string,
	counterpartyId: string,
	options: { includeBlocked?: boolean } = {},
) => {
	return useQuery({
		queryKey: addressKeys.list(organizationId, counterpartyId, options),
		queryFn: () => fetchAddresses(organizationId, counterpartyId, options),
		placeholderData: keepPreviousData,
		enabled: organizationId !== "" && counterpartyId !== "", // Only run query when we have valid IDs
	});
};

// Get single address
export const fetchAddress = async (
	organizationId: string,
	counterpartyId: string,
	id: string,
) => {
	const response = await apiClient.counterparties[
		":counterpartyId"
	].addresses[":id"].$get({
		param: { counterpartyId, id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch address");
	}

	return response.json();
};

// Hook for fetching a single address
export const useAddressQuery = (
	organizationId: string,
	counterpartyId: string,
	id: string,
) => {
	return useQuery({
		queryKey: addressKeys.detail(organizationId, counterpartyId, id),
		queryFn: () => fetchAddress(organizationId, counterpartyId, id),
		enabled: organizationId !== "" && counterpartyId !== "" && id !== "",
	});
};

// Hook for fetching a single address by ID - no counterparty needed
export const useAddressByIdQuery = (organizationId: string, id: string) => {
	return useQuery({
		queryKey: addressKeys.detail(organizationId, undefined, id),
		queryFn: () => fetchAddress(organizationId, "any", id), // Use a placeholder counterpartyId
		enabled: organizationId !== "" && id !== "",
	});
};

// Create address mutation
export const useCreateAddressMutation = (
	organizationId: string,
	counterpartyId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateAddressUsageInput) => {
			// Ensure organizationId is added to address if it exists
			const processedData = { ...data };

			if (processedData.address) {
				processedData.address = {
					...processedData.address,
				};
			}

			const response = await apiClient.counterparties[
				":counterpartyId"
			].addresses.$post({
				param: { counterpartyId },
				json: {
					...processedData,
					counterpartyId,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create address");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Address created successfully");
			queryClient.invalidateQueries({
				queryKey: addressKeys.list(organizationId, counterpartyId),
			});
		},
		onError: (error) => {
			toast.error("Failed to create address");
			console.error("Create address error:", error);
		},
	});
};

// Update address mutation
export const useUpdateAddressMutation = (
	organizationId: string,
	counterpartyId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateAddressUsageInput) => {
			// Ensure organizationId is added to address if it exists
			const processedData = { ...data };

			if (processedData.address) {
				processedData.address = {
					...processedData.address,
				};
			}

			const response = await apiClient.counterparties[
				":counterpartyId"
			].addresses[":id"].$put({
				param: { counterpartyId, id },
				json: {
					...processedData,
					id,
					counterpartyId,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update address");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Address updated successfully");
			queryClient.invalidateQueries({
				queryKey: addressKeys.list(organizationId, counterpartyId),
			});
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: addressKeys.detail(
						organizationId,
						counterpartyId,
						data.id,
					),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update address");
			console.error("Update address error:", error);
		},
	});
};

// Delete address mutation
export const useDeleteAddressMutation = (
	organizationId: string,
	counterpartyId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.counterparties[
				":counterpartyId"
			].addresses[":id"].$delete({
				param: { counterpartyId, id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete address");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Address deleted successfully");
			queryClient.invalidateQueries({
				queryKey: addressKeys.list(organizationId, counterpartyId),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete address");
			console.error("Delete address error:", error);
		},
	});
};
