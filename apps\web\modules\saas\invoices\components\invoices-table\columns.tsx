"use client";

import type { Invoice } from "@repo/database";
import { useInvoicesUI } from "@saas/invoices/context/invoices-ui-context";
import { DataTableColumnHeader } from "@saas/shared/components/data-table";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { format } from "date-fns";
import {
	Calendar,
	Check,
	CreditCard,
	Eye,
	Mail,
	MoreHorizontal,
	PenLine,
	Tag,
	Trash,
	User,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

declare module "@tanstack/react-table" {
	interface ColumnMeta<TData, TValue> {
		getRowActions?: (row: Row<TData>) => {
			openView: () => void;
			openEdit?: () => void;
			openDelete: () => void;
		};
	}
}

type ActionsCellProps = {
	row: Row<Invoice>;
};

function ActionsCell({ row }: ActionsCellProps) {
	const invoice = row.original;
	const {
		handleDeleteInvoice,
		handleViewInvoice,
		handleSendInvoiceEmail,
		handleMarkAsPaid,
		handleViewPdf,
		handleEditInvoice,
	} = useInvoicesUI();

	const isPaid = invoice.status === "paid";
	const isCancelled = invoice.status === "cancelled";

	return (
		<DropdownMenu>
			<DropdownMenuTrigger asChild>
				<Button variant="ghost" className="h-8 w-8 p-0">
					<span className="sr-only">Open menu</span>
					<MoreHorizontal className="h-4 w-4" />
				</Button>
			</DropdownMenuTrigger>
			<DropdownMenuContent align="end">
				<DropdownMenuLabel>Actions</DropdownMenuLabel>
				<DropdownMenuItem onClick={() => handleViewPdf(invoice)}>
					<Eye className="mr-2 h-4 w-4" />
					View
				</DropdownMenuItem>
				<DropdownMenuItem
					onClick={() => handleSendInvoiceEmail(invoice)}
					data-send-email-action
				>
					<Mail className="mr-2 h-4 w-4" />
					Send Email
				</DropdownMenuItem>

				{!isPaid && !isCancelled && (
					<>
						<DropdownMenuItem
							onClick={() => handleEditInvoice(invoice)}
							data-edit-action
						>
							<PenLine className="mr-2 h-4 w-4" />
							Edit
						</DropdownMenuItem>
						<DropdownMenuItem
							onClick={() => handleMarkAsPaid(invoice)}
							data-mark-paid-action
						>
							<Check className="mr-2 h-4 w-4" />
							Mark as Paid
						</DropdownMenuItem>
					</>
				)}

				<DropdownMenuSeparator />

				{!isPaid && !isCancelled && (
					<DropdownMenuItem
						className="text-destructive focus:text-destructive"
						onClick={() => handleDeleteInvoice(invoice)}
						data-delete-action
					>
						<Trash className="mr-2 h-4 w-4" />
						Cancel
					</DropdownMenuItem>
				)}
			</DropdownMenuContent>
		</DropdownMenu>
	);
}

export function useColumns(): ColumnDef<Invoice>[] {
	const {
		handleDeleteInvoice,
		handleViewInvoice,
		handleSendInvoiceEmail,
		handleMarkAsPaid,
		handleViewPdf,
		handleEditInvoice,
	} = useInvoicesUI();
	const params = useParams<{ organizationSlug: string }>();

	return [
		{
			accessorKey: "invoice_number",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Invoice Number" />
			),
			cell: ({ row }) => {
				const invoiceNumber = row.getValue("invoice_number") as
					| string
					| null
					| undefined;
				return (
					<div className="flex items-center gap-2">
						<Tag className="h-4 w-4 text-muted-foreground" />
						<span className="font-medium">
							{invoiceNumber || "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "order_number",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Order Number" />
			),
			cell: ({ row }) => {
				// Get order data from orderInvoices array
				const invoice = row.original;
				// Type assertion to fix the linter errors
				const orderInvoices = (invoice as any).orderInvoices || [];

				return (
					<div className="flex items-center gap-2">
						<Tag className="h-4 w-4 text-muted-foreground" />
						{orderInvoices.length > 0 ? (
							<div className="flex flex-col gap-1">
								{orderInvoices.map(
									(
										oi: {
											order?: {
												id: string;
												order_number: string | null;
											};
											order_number: string | null;
										},
										index: number,
									) => (
										<div key={index}>
											{oi.order?.id ? (
												<Link
													href={`/app/${params.organizationSlug}/orders/${oi.order.id}`}
													className="hover:underline cursor-pointer"
												>
													<span>
														{oi.order_number || "-"}
													</span>
												</Link>
											) : (
												<span>
													{oi.order_number || "-"}
												</span>
											)}
										</div>
									),
								)}
							</div>
						) : (
							<span>-</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "customer_name",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Customer" />
			),
			cell: ({ row }) => {
				const invoice = row.original;
				const customerName = row.getValue("customer_name") as
					| string
					| null
					| undefined;
				// Get customer ID from the invoice data
				const customerId = (invoice as any).customer?.id;

				return (
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-muted-foreground" />
						{customerId ? (
							<Link
								href={`/app/${params.organizationSlug}/contacts/${customerId}`}
								className="hover:underline cursor-pointer"
							>
								<span>{customerName || "-"}</span>
							</Link>
						) : (
							<span>{customerName || "-"}</span>
						)}
					</div>
				);
			},
		},
		{
			accessorKey: "status",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Status" />
			),
			cell: ({ row }) => {
				const status = row.getValue("status") as string | null;

				let statusClass = "bg-gray-100 text-gray-800";
				if (status === "paid") {
					statusClass = "bg-green-100 text-green-800";
				} else if (status === "cancelled") {
					statusClass = "bg-red-100 text-red-800";
				} else if (status === "open") {
					statusClass = "bg-blue-100 text-blue-800";
				} else if (status === "overdue") {
					statusClass = "bg-amber-100 text-amber-800";
				}

				return (
					<span
						className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}`}
					>
						{status || "Unknown"}
					</span>
				);
			},
		},
		{
			accessorKey: "invoice_date",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Invoice Date" />
			),
			cell: ({ row }) => {
				const invoiceDate = row.getValue("invoice_date") as Date | null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{invoiceDate
								? format(new Date(invoiceDate), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "due_date",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Due Date" />
			),
			cell: ({ row }) => {
				const dueDate = row.getValue("due_date") as Date | null;
				return (
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-muted-foreground" />
						<span>
							{dueDate ? format(new Date(dueDate), "PPP") : "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "payment_date",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Payment Date" />
			),
			cell: ({ row }) => {
				const paymentDate = row.getValue("payment_date") as Date | null;
				return (
					<div className="flex items-center gap-2">
						<CreditCard className="h-4 w-4 text-muted-foreground" />
						<span>
							{paymentDate
								? format(new Date(paymentDate), "PPP")
								: "-"}
						</span>
					</div>
				);
			},
		},
		{
			accessorKey: "financialData",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Amount" />
			),
			cell: ({ row }) => {
				const financialData = row.getValue("financialData") as Array<{
					currency: string;
					subtotal: number;
					taxAmount: number;
					totalAmount: number;
				}> | null;

				// Format the amount with currency from the financialData
				if (!financialData || financialData.length === 0) {
					return "-";
				}

				// Show multiple currencies if present
				return (
					<div className="space-y-1">
						{financialData.map((data, index) => {
							const formattedAmount = new Intl.NumberFormat(
								"de-DE",
								{
									style: "currency",
									currency: data.currency || "EUR",
								},
							).format(data.totalAmount);

							return <div key={index}>{formattedAmount}</div>;
						})}
					</div>
				);
			},
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="Created At" />
			),
			cell: ({ row }) => {
				const date = row.getValue("createdAt") as Date;
				return format(date, "PPP");
			},
		},
		{
			id: "actions",
			size: 50,
			cell: ({ row }) => <ActionsCell row={row} />,
			meta: {
				getRowActions: (row: Row<Invoice>) => {
					const invoice = row.original;
					return {
						openView: () => handleViewPdf(invoice),
						openEdit: () => handleEditInvoice(invoice),
						openDelete: () => handleDeleteInvoice(invoice),
						openMarkPaid: () => handleMarkAsPaid(invoice),
					};
				},
			},
		},
	];
}
