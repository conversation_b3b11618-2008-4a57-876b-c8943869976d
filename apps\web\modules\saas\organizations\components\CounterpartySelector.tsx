"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Check, ChevronDown, HelpCircle, MapPin, X } from "lucide-react";
import { useEffect, useId, useMemo, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

// Define counterparty type
type CounterpartyType = "customer" | "supplier" | "carrier" | "other";

// Query keys for counterparties
const counterpartyKeys = {
	all: ["counterparties"] as const,
	list: (params: any) => [...counterpartyKeys.all, "list", params] as const,
};

// Fetch counterparties with search and filtering
const fetchCounterparties = async ({
	organizationId,
	search = "",
	type = "",
	limit = 50,
	includeAddresses = false,
	groupIds,
	showSameGroupAs,
}: {
	organizationId: string;
	search?: string;
	type?: string;
	limit?: number;
	includeAddresses?: boolean;
	groupIds?: string[];
	showSameGroupAs?: string;
}) => {
	const response = await apiClient.counterparties.$get({
		query: {
			organizationId,
			limit: limit.toString(),
			search,
			...(includeAddresses ? { includeAddresses: "true" } : {}),
			...(type ? { filters: JSON.stringify({ type }) } : {}),
			...(groupIds?.length ? { groupIds: groupIds.join(",") } : {}),
			...(showSameGroupAs ? { showSameGroupAs } : {}),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparties");
	}

	return response.json();
};

export interface CounterpartySelectorProps {
	value?: string;
	onChange?: (value: string) => void;
	onDataFetched?: (data: any) => void;
	onSelectedCounterpartyChange?: (
		counterparty: any,
		addresses?: {
			primary: any[];
		},
	) => void;
	name: string;
	label?: string;
	tooltip?: string;
	placeholder?: string;
	type?: CounterpartyType | "";
	disabled?: boolean;
	error?: string;
	className?: string;
	isLoading?: boolean;
	isModal?: boolean;
	allowClear?: boolean;
	includeAddresses?: boolean;
	groupIds?: string[];
	showSameGroupAs?: string;
	excludeIds?: string[];
}

/**
 * A reusable Counterparty selector component that displays counterparty details
 */
export function CounterpartySelector({
	value,
	onChange,
	onDataFetched,
	onSelectedCounterpartyChange,
	name,
	label,
	tooltip,
	placeholder = "Select counterparty",
	type = "",
	disabled = false,
	error,
	className,
	isLoading: externalIsLoading = false,
	isModal = false,
	allowClear = false,
	includeAddresses = false,
	groupIds,
	showSameGroupAs,
	excludeIds,
}: CounterpartySelectorProps): JSX.Element {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";
	const [open, setOpen] = useState(false);
	const [search, setSearch] = useState("");
	const debouncedSearch = useDebounce(search, 300);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const searchInputRef = useRef<HTMLInputElement>(null);
	const dropdownContentRef = useRef<HTMLDivElement>(null);
	const shouldFocusRef = useRef(false);
	const uniqueId = useId();
	const searchInputId = `counterparty-search-${uniqueId}`;

	// Ref to track the previous counterparty ID to avoid infinite updates
	const prevCounterpartyIdRef = useRef<string | undefined>(undefined);

	// Reset search when closing the dropdown
	useEffect(() => {
		if (!open) {
			setSearch("");
		} else {
			// Flag that we should focus when the dropdown opens
			shouldFocusRef.current = true;
		}
	}, [open]);

	const { data, isLoading: isLoadingData } = useQuery({
		queryKey: counterpartyKeys.list({
			organizationId,
			search: debouncedSearch,
			type,
			groupIds,
			showSameGroupAs,
		}),
		queryFn: () =>
			fetchCounterparties({
				organizationId,
				search: debouncedSearch,
				type,
				limit: 50,
				includeAddresses,
				groupIds,
				showSameGroupAs,
			}),
		enabled: !!organizationId,
	});

	// Filter out excluded counterparties
	const filteredData = useMemo(() => {
		if (!data?.items) {
			return data;
		}

		if (!excludeIds?.length) {
			return data;
		}

		return {
			...data,
			items: data.items.filter(
				(item: any) => !excludeIds.includes(item.id),
			),
		};
	}, [data, excludeIds]);

	// Pass the fetched data to parent if needed
	useEffect(() => {
		if (filteredData && onDataFetched) {
			onDataFetched(filteredData);
		}
	}, [filteredData, onDataFetched]);

	// Find the selected counterparty
	const selectedCounterparty = data?.items?.find(
		(item: any) => item.id === value,
	);

	// Notify parent when selected counterparty changes - only when ID actually changes
	useEffect(() => {
		if (
			selectedCounterparty &&
			onSelectedCounterpartyChange &&
			value !== prevCounterpartyIdRef.current
		) {
			prevCounterpartyIdRef.current = value;

			// Extract addresses if includeAddresses is enabled and they're in structured format
			const addresses =
				includeAddresses &&
				selectedCounterparty.addresses &&
				typeof selectedCounterparty.addresses === "object" &&
				!Array.isArray(selectedCounterparty.addresses)
					? selectedCounterparty.addresses
					: undefined;

			onSelectedCounterpartyChange(selectedCounterparty, addresses);
		}
	}, [
		selectedCounterparty,
		onSelectedCounterpartyChange,
		value,
		includeAddresses,
	]);

	// Multi-approach focus strategy
	useEffect(() => {
		if (!open || !shouldFocusRef.current) {
			return;
		}

		// Schedule multiple focus attempts with increasing delays
		const attempts = [10, 50, 100, 250, 500];

		const focusAttempts = attempts.map((delay) =>
			setTimeout(() => {
				if (searchInputRef.current) {
					searchInputRef.current.focus();
					// Also try to select any existing text
					searchInputRef.current.select();
				} else {
					// As a fallback, try to focus by ID
					const inputEl = document.getElementById(searchInputId);
					inputEl?.focus();
				}
			}, delay),
		);

		shouldFocusRef.current = false;

		return () => {
			// Clean up all timeouts
			focusAttempts.forEach(clearTimeout);
		};
	}, [open, searchInputId]);

	// Combined loading state
	const isLoading = externalIsLoading || isLoadingData;

	// Helper to handle clearing selection (without using a button element)
	const handleClearSelection = (e: React.MouseEvent) => {
		e.stopPropagation();
		onChange?.("");
		// Reset the previous counterparty ID ref to allow reselection of the same counterparty
		prevCounterpartyIdRef.current = undefined;
		// Also notify that no counterparty is selected so billing address can be cleared
		if (onSelectedCounterpartyChange) {
			onSelectedCounterpartyChange(null, undefined);
		}
	};

	// Helper to get the counterparty type display value
	const getCounterpartyTypeDisplay = (counterparty: any) => {
		if (!counterparty?.types?.length) {
			return "unknown";
		}

		// If a specific type filter is provided, and the counterparty has that type,
		// prioritize showing that type instead of just the first one
		if (type && counterparty.types.some((t: any) => t.type === type)) {
			return type;
		}

		// If multiple types, show all types joined with "/"
		if (counterparty.types.length > 1) {
			return counterparty.types.map((t: any) => t.type).join("/");
		}

		// Otherwise, get the first type assignment from the types array
		return counterparty.types[0]?.type || "unknown";
	};

	// Helper to get primary address from counterparty
	const getPrimaryAddress = (counterparty: any) => {
		// Handle structured addresses format (when includeAddresses=true)
		if (
			includeAddresses &&
			counterparty?.addresses &&
			typeof counterparty.addresses === "object" &&
			!Array.isArray(counterparty.addresses)
		) {
			const { primary = [] } = counterparty.addresses;

			// Get default primary address or first primary address
			const defaultPrimaryAddress = primary.find(
				(addr: any) => addr.isDefault,
			);
			if (defaultPrimaryAddress) {
				return defaultPrimaryAddress;
			}

			const firstPrimaryAddress = primary[0];
			if (firstPrimaryAddress) {
				return firstPrimaryAddress;
			}

			return null;
		}

		// Handle legacy format (array of addresses directly on counterparty)
		if (!counterparty?.addresses?.length) {
			return null;
		}

		// First try to find the default address
		const defaultAddress = counterparty.addresses.find(
			(addr: any) => addr.isDefault && addr.address,
		);

		// If no default, just use the first one with an address object
		const firstAddress = counterparty.addresses.find(
			(addr: any) => addr.address,
		);

		return defaultAddress || firstAddress || null;
	};

	// Helper to format address as a string
	const formatAddress = (addressUsage: any) => {
		// Handle direct address object (new structured format)
		if (addressUsage && !addressUsage.address && addressUsage.street) {
			const parts = [];
			if (addressUsage.street) {
				parts.push(addressUsage.street);
			}
			if (addressUsage.city) {
				parts.push(addressUsage.city);
			}
			if (addressUsage.zipCode) {
				parts.push(addressUsage.zipCode);
			}
			if (addressUsage.country) {
				parts.push(addressUsage.country);
			}
			return parts.join(", ") || "No address details";
		}

		// Handle legacy format (with addressUsage.address wrapper)
		if (!addressUsage?.address) {
			return "No address";
		}

		const { address } = addressUsage;
		const parts = [];

		if (address.street) {
			parts.push(address.street);
		}
		if (address.city) {
			parts.push(address.city);
		}
		if (address.zipCode) {
			parts.push(address.zipCode);
		}
		if (address.country) {
			parts.push(address.country);
		}

		return parts.join(", ") || "No address details";
	};

	// Handle keyboard navigation
	const handleKeyDown = (e: React.KeyboardEvent) => {
		// Close the dropdown menu when pressing Escape
		if (e.key === "Escape") {
			e.preventDefault();
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle search input
	const handleSearchInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
	) => {
		setSearch(e.target.value);
	};

	// Prevent dropdown from stealing focus from search input
	const handleSearchInputKeyDown = (
		e: React.KeyboardEvent<HTMLInputElement>,
	) => {
		// Prevent propagation to stop the dropdown from handling these events
		e.stopPropagation();

		// Still close on Escape
		if (e.key === "Escape") {
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle dropdown trigger click, to set focus flag
	const handleTriggerClick = () => {
		shouldFocusRef.current = true;
	};

	// Handle wheel events in the dropdown content to ensure scrolling works
	const handleWheel = (e: React.WheelEvent) => {
		// Don't block the wheel event, let it propagate naturally
		e.stopPropagation();
	};

	// Render the selected counterparty card when we have a value and the data is loaded
	if (value && selectedCounterparty && !isLoading) {
		return (
			<div className={cn("space-y-2", className)}>
				<div className="relative">
					<Card className="w-full">
						<CardContent className="p-4">
							<div className="flex flex-col items-start">
								<div className="flex justify-between w-full items-center">
									<div className="font-medium text-base">
										{selectedCounterparty.nameLine1 ||
											selectedCounterparty.id}
									</div>
									{allowClear && (
										<button
											type="button"
											className="h-6 w-6 rounded-md inline-flex items-center justify-center text-muted-foreground hover:bg-accent hover:text-accent-foreground"
											onClick={handleClearSelection}
											aria-label="Clear selection"
										>
											<X className="h-4 w-4" />
										</button>
									)}
								</div>
								<div className="mt-1 text-sm text-muted-foreground flex items-center gap-1">
									{getPrimaryAddress(selectedCounterparty) ? (
										<>
											<MapPin className="h-3.5 w-3.5 text-muted-foreground" />
											<span>
												{formatAddress(
													getPrimaryAddress(
														selectedCounterparty,
													),
												)}
											</span>
										</>
									) : (
										<span className="text-muted-foreground/70">
											No address available
										</span>
									)}
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{error && (
					<p className="text-sm font-medium text-destructive">
						{error}
					</p>
				)}
			</div>
		);
	}

	// Otherwise, render the dropdown selector
	return (
		<div className={cn("space-y-2", className)}>
			<DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
				<DropdownMenuTrigger asChild disabled={disabled || isLoading}>
					<Button
						ref={buttonRef}
						variant="outline"
						className={cn(
							"w-full justify-between p-4 h-auto text-left",
							!selectedCounterparty && "text-muted-foreground",
						)}
						onClick={handleTriggerClick}
					>
						{isLoading ? (
							<div>Loading...</div>
						) : (
							<div className="flex justify-between w-full items-center">
								<span>{placeholder}</span>
								<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
							</div>
						)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					ref={dropdownContentRef}
					className="p-0 max-h-[300px] overflow-auto"
					align="start"
					sideOffset={4}
					style={{
						width: "var(--radix-dropdown-menu-trigger-width)",
					}}
					role="menu"
					aria-orientation="vertical"
					aria-label="Counterparties list"
					onKeyDown={handleKeyDown}
					onWheel={handleWheel}
				>
					<div className="p-2 border-b">
						<Input
							id={searchInputId}
							ref={searchInputRef}
							placeholder="Search counterparties..."
							value={search}
							onChange={handleSearchInputChange}
							onKeyDown={handleSearchInputKeyDown}
							onClick={(e) => e.stopPropagation()}
							className="h-8"
							autoFocus
							// Force autofocus
							onFocus={(e) => e.currentTarget.select()}
						/>
					</div>
					<div className="overflow-y-auto max-h-[250px]">
						{!Array.isArray(data?.items) ||
						data?.items.length === 0 ? (
							<div className="py-6 text-center">
								<p className="text-sm text-muted-foreground">
									No counterparties found.
								</p>
							</div>
						) : (
							<>
								{data.items.map((counterparty: any) => (
									<DropdownMenuItem
										key={counterparty.id}
										className={cn(
											"flex flex-col w-full items-start text-left px-3 py-2 cursor-pointer",
											"text-foreground", // Ensure proper text color
											value === counterparty.id
												? "bg-accent text-accent-foreground"
												: "",
											"hover:bg-accent/20 hover:text-foreground focus:bg-accent/20 focus:text-foreground data-[highlighted]:bg-accent/20 data-[highlighted]:text-foreground",
										)}
										onSelect={() => {
											onChange?.(counterparty.id);
											setOpen(false); // Close dropdown after selection
										}}
										role="menuitemradio"
										aria-checked={value === counterparty.id}
									>
										<div className="flex w-full items-center justify-between">
											<div className="font-medium text-foreground">
												{counterparty.nameLine1 ||
													counterparty.id}
											</div>
											<div className="flex items-center gap-2">
												<Badge className="text-xs capitalize">
													{getCounterpartyTypeDisplay(
														counterparty,
													)}
												</Badge>
												{value === counterparty.id && (
													<Check className="h-4 w-4 text-primary" />
												)}
											</div>
										</div>
										<div className="mt-1 text-xs text-muted-foreground flex flex-wrap gap-2">
											{getPrimaryAddress(
												counterparty,
											) && (
												<div className="flex items-center gap-1">
													<MapPin className="h-3 w-3" />
													<span>
														{formatAddress(
															getPrimaryAddress(
																counterparty,
															),
														)}
													</span>
												</div>
											)}
											{counterparty.uidNumber && (
												<div className="flex items-center gap-1 text-muted-foreground">
													<span className="font-medium">
														VAT:
													</span>
													<span>
														{counterparty.uidNumber}
													</span>
												</div>
											)}
											{counterparty.financialProfile
												?.taxNotes && (
												<div className="flex items-center gap-1 text-muted-foreground">
													<span className="font-medium">
														Tax ID:
													</span>
													<span>
														{
															counterparty
																.financialProfile
																.taxNotes
														}
													</span>
												</div>
											)}
										</div>
									</DropdownMenuItem>
								))}
							</>
						)}
					</div>
				</DropdownMenuContent>
			</DropdownMenu>

			{error && (
				<p className="text-sm font-medium text-destructive">{error}</p>
			)}
		</div>
	);
}

/**
 * A form-connected version of the Counterparty selector for use with react-hook-form
 */
export function FormCounterpartySelector({
	name,
	label,
	tooltip,
	placeholder = "Select counterparty",
	type = "",
	disabled = false,
	className,
	isLoading = false,
	isModal = false,
	allowClear = false,
	includeAddresses = false,
	groupIds,
	showSameGroupAs,
	onSelectedCounterpartyChange,
}: Omit<
	CounterpartySelectorProps,
	"onChange" | "value" | "error"
>): JSX.Element {
	const form = useFormContext();

	if (!form) {
		throw new Error(
			"FormCounterpartySelector must be used within a FormProvider",
		);
	}

	return (
		<FormField
			control={form.control}
			name={name}
			render={({ field }) => (
				<FormItem className={className}>
					{label && (
						<FormLabel
							htmlFor={name}
							className="flex items-center gap-1"
						>
							{label}
							{tooltip && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
										</TooltipTrigger>
										<TooltipContent>
											<p>{tooltip}</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}
						</FormLabel>
					)}
					<FormControl>
						<CounterpartySelector
							onChange={field.onChange}
							value={field.value}
							name={name}
							placeholder={placeholder}
							type={type}
							disabled={disabled}
							isLoading={isLoading}
							isModal={isModal}
							allowClear={allowClear}
							includeAddresses={includeAddresses}
							groupIds={groupIds}
							showSameGroupAs={showSameGroupAs}
							onSelectedCounterpartyChange={
								onSelectedCounterpartyChange
							}
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
