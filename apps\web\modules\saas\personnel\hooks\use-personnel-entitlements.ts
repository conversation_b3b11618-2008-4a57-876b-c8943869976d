import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	useCreateEntitlementMutation,
	useUpdateEntitlementMutation,
} from "../lib/api-entitlements";
import { usePersonnelAbsenceTable } from "./use-personnel-absence";

/**
 * Hook for managing personnel entitlements
 * Provides mutations for creating and updating entitlements
 */
export function usePersonnelEntitlementMutations(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	// Get the mutations
	const createMutation = useCreateEntitlementMutation(orgId);
	const updateMutation = useUpdateEntitlementMutation(orgId);

	// Create with callback
	const createEntitlement = async (data: {
		personnelId: string;
		year: number;
		holidayEntitlementDays: number;
		usedDays?: number;
	}) => {
		const result = await createMutation.mutateAsync(data);

		if (options?.onSuccess) {
			options.onSuccess();
		}

		return result;
	};

	// Update with callback
	const updateEntitlement = async (
		id: string,
		data: {
			personnelId?: string;
			year?: number;
			holidayEntitlementDays?: number;
			usedDays?: number;
		},
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			organizationId: orgId,
			...data,
		});

		if (options?.onSuccess) {
			options.onSuccess();
		}

		return result;
	};

	return {
		createEntitlement,
		updateEntitlement,
		isLoading: createMutation.isPending || updateMutation.isPending,
	};
}

/**
 * Hook for accessing personnel entitlement data from the absence table
 * Makes it easy to create/update entitlements based on the table data
 */
export function useAbsenceTableEntitlements() {
	const { data, isLoading, error, refetch } = usePersonnelAbsenceTable();
	const {
		createEntitlement,
		updateEntitlement,
		isLoading: isMutating,
	} = usePersonnelEntitlementMutations({
		onSuccess: () => refetch(),
	});

	// Get all entitlements from the table items
	const entitlements =
		data?.items.map((item) => ({
			personnelId: item.personnelId,
			firstName: item.firstName,
			lastName: item.lastName,
			departmentId: item.departmentId,
			entitlement: item.entitlement,
			entitledDays: item.entitledDays,
			usedDays: item.usedDays,
			openDays: item.openDays,
		})) || [];

	// Create or update entitlement for a personnel
	const updatePersonnelEntitlement = async (
		personnelId: string,
		entitlementData: {
			year: number;
			holidayEntitlementDays: number;
			usedDays?: number;
		},
	) => {
		// Find if there's an existing entitlement
		const personnel = entitlements.find(
			(e) => e.personnelId === personnelId,
		);

		// If there's an existing entitlement, update it; otherwise create a new one
		if (personnel?.entitlement?.id) {
			return updateEntitlement(personnel.entitlement.id, entitlementData);
		}

		// Create new entitlement
		return createEntitlement({
			personnelId,
			...entitlementData,
		});
	};

	return {
		entitlements,
		updatePersonnelEntitlement,
		isLoading: isLoading || isMutating,
		error,
		refetch,
	};
}
