import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useState } from "react";
import {
	useDeleteDocumentMutation,
	useTourDocumentsQuery,
	useUploadDocumentMutation,
} from "../lib/api-documents";

export function useDocuments(tourId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sortBy, setSortBy] = useState<string | undefined>(undefined);
	const [sortDirection, setSortDirection] = useState<
		"asc" | "desc" | undefined
	>(undefined);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	// If tourId is not provided, disable the query
	const enabled = !!activeOrganization?.id && !!tourId;

	const query = useTourDocumentsQuery({
		organizationId: activeOrganization?.id ?? "",
		tourId: tourId ?? "",
		page,
		limit: pageSize,
		sortBy,
		sortDirection,
	});

	const deleteMutation = useDeleteDocumentMutation(
		activeOrganization?.id ?? "",
		tourId ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deleteDocument = async (id: string) => {
		if (activeOrganization?.id && tourId) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete document error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sortBy,
		setSortBy,
		sortDirection,
		setSortDirection,
		refetch: query.refetch,
		deleteDocument,
		enabled,
	};
}

export function useDocumentMutations(
	tourId: string,
	options?: { onSuccess?: () => void },
) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const uploadMutation = useUploadDocumentMutation(orgId, tourId);
	const deleteMutation = useDeleteDocumentMutation(orgId, tourId);

	// Upload document function - takes a file and optional metadata
	const uploadDocument = async (
		file: File,
		description?: string,
		tags?: string[],
	) => {
		try {
			// Create FormData instead of trying to use Buffer
			const formData = new FormData();
			formData.append("file", file);
			formData.append("fileName", file.name);
			formData.append("fileType", file.type);
			formData.append("fileSize", file.size.toString());

			if (description) {
				formData.append("description", description);
			}

			if (tags && tags.length > 0) {
				formData.append("tags", JSON.stringify(tags));
			}

			// Use direct fetch to avoid API client limitations
			const url = new URL(
				`/api/tours/${tourId}/documents`,
				window.location.origin,
			);
			url.searchParams.append("organizationId", orgId);

			const response = await fetch(url.toString(), {
				method: "POST",
				body: formData,
				credentials: "include",
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(
					errorData.error?.message || "Failed to upload document",
				);
			}

			const result = await response.json();

			if (options?.onSuccess) {
				options.onSuccess();
			}

			return result;
		} catch (error) {
			console.error("Upload document error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	// Delete document function
	const deleteDocument = async (documentId: string) => {
		try {
			const result = await deleteMutation.mutateAsync(documentId);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Delete document error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	return {
		uploadDocument,
		deleteDocument,
		isLoading: uploadMutation.isPending || deleteMutation.isPending,
	};
}
