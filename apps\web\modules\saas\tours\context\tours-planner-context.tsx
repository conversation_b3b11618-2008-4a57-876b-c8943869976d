"use client";
import type {
	StopOrder,
	TourStopInput,
} from "@repo/api/src/routes/tours/types";
import { useVehicleLocations } from "@saas/tours/hooks/use-vehicle-locations";
import { fetchVehicles } from "@saas/vehicles/lib/api";
import {
	type ReactNode,
	createContext,
	useCallback,
	useContext,
	useEffect,
	useState,
} from "react";
import type { TourResponse } from "../lib/api";

// Make the type more permissive to handle API responses
export type Stop = {
	id: string;
	orderId?: string | null;
	tourId?: string | null;
	stopType: "loading" | "unloading" | "stopover";
	entityType?: "order" | "tour";
	nameLine?: string | null;
	street?: string | null;
	city?: string | null;
	datetime_start?: Date | string | null;
	datetime_end?: Date | string | null;
	position: number;
	organizationId: string;

	// Add new fields for map usage
	latitude?: number | null;
	longitude?: number | null;

	// Optional fields from the API response
	dangerous_goods_nr?: string | null;
	special_transports_height?: number | null;
	special_transports_width?: number | null;
	special_transports_length?: number | null;
	special_transports_heavy?: boolean | null;
	crane_loading?: boolean | null;
	crane_unloading?: boolean | null;
	pallet_exchange_count?: number | null;
	pallet_exchange_type?: string | null;
	driver_notes?: string | null;
	information_text?: string | null;
	goods_information?: string | null;
	length?: number | null;
	width?: number | null;
	height?: number | null;
	units?: string | null;
	weight?: number | null;
	cubic_meters?: number | null;
	loading_meter?: number | null;
	measurement_text?: string | null;
	reference_number?: string | null;
	neutrality_text?: string | null;
	loading_equipment_exchange?: boolean | null;
	positionTour?: number | null;

	// Include order data
	order?: {
		customer?: {
			nameLine1?: string;
		};
		order_number?: string;
		customer_order_number?: string;
	} | null;

	// Allow for other fields from the API
	[key: string]: any;
};

// Define a type for map bounds
export interface MapBounds {
	minLat: number;
	maxLat: number;
	minLng: number;
	maxLng: number;
}

// Type for a new tour stop that hasn't been created yet
export type NewTourStop = Omit<Stop, "id" | "position" | "entityType"> & {
	isNew: true;
	position?: number;
};

export type TourBuilderMode = "create" | "edit";

interface TourBuilderContextValue {
	// Tour data - now using the inferred response type
	tourData: Partial<TourResponse>;
	updateTourData: (data: Partial<TourResponse>) => void;

	// Stops management
	stops: Stop[];
	addStop: (stop: Omit<Stop, "position"> & { position?: number }) => void;
	removeStop: (stopId: string) => void;
	reorderStops: (newOrder: Stop[]) => void;

	// Tour-specific stops management
	addTourStop: (stop: NewTourStop) => void;
	newTourStops: NewTourStop[];

	// Vehicle selection with auto-selection of carrier and trailer
	selectVehicle: (vehicleId: string, organizationId: string) => Promise<void>;

	// Vehicle dimensions from selected vehicle
	vehicleDimensions: {
		length?: number | null;
		width?: number | null;
		height?: number | null;
		loadingWeight?: number | null;
		euroPalletsAmount?: number | null;
	};

	// Calculate cargo fill percentage and metrics
	calculateCargoFill: () => number;
	getCurrentLoadingMeters: () => number;

	// Tour builder state
	isSaving: boolean;
	setIsSaving: (value: boolean) => void;

	// Mode
	mode: TourBuilderMode;
	tourId?: string;

	// Get data formatted for API calls
	getStopOrders: () => StopOrder[];
	getTourStops: () => TourStopInput[];

	// Vehicle locations display
	vehicleLocations: any[];
	isLoadingVehicleLocations: boolean;
	showVehicleLocations: boolean;
	setShowVehicleLocations: (value: boolean) => void;

	// Map bounds for filtering
	mapBounds: MapBounds | null;
	setMapBounds: (bounds: MapBounds | null) => void;
	filterStopsByMap: boolean;
	setFilterStopsByMap: (value: boolean) => void;
}

const TourBuilderContext = createContext<TourBuilderContextValue | undefined>(
	undefined,
);

interface TourBuilderProviderProps {
	children: ReactNode;
	initialTourData?: Partial<TourResponse>;
	initialStops?: Stop[];
	mode: TourBuilderMode;
	tourId?: string;
}

export function TourBuilderProvider({
	children,
	initialTourData = {},
	initialStops = [],
	mode,
	tourId,
}: TourBuilderProviderProps) {
	const [tourData, setTourData] =
		useState<Partial<TourResponse>>(initialTourData);
	const [stops, setStops] = useState<Stop[]>(initialStops);
	const [newTourStops, setNewTourStops] = useState<NewTourStop[]>([]);
	const [isSaving, setIsSaving] = useState(false);
	const [showVehicleLocations, setShowVehicleLocations] = useState(true);

	// Add state for vehicle dimensions
	const [vehicleDimensions, setVehicleDimensions] = useState<{
		length?: number | null;
		width?: number | null;
		height?: number | null;
		loadingWeight?: number | null;
		euroPalletsAmount?: number | null;
	}>({});

	// Add state for map bounds and filtering
	const [mapBounds, setMapBounds] = useState<MapBounds | null>(null);
	const [filterStopsByMap, setFilterStopsByMap] = useState(false);

	// Fetch vehicle locations data
	const { vehicleLocations, isLoading: isLoadingVehicleLocations } =
		useVehicleLocations(true); // Include route calculations by default

	// Effect to load vehicle dimensions when editing a tour with preselected vehicle
	useEffect(() => {
		const loadVehicleDimensions = async () => {
			// Only run if we have a vehicleId but no dimensions loaded yet
			if (
				tourData.vehicleId &&
				tourData.organizationId &&
				!vehicleDimensions.length // Check if dimensions are not loaded
			) {
				try {
					const vehiclesData = await fetchVehicles({
						organizationId: tourData.organizationId,
						limit: 100,
					});

					const selectedVehicle = vehiclesData.items.find(
						(v) => v.id === tourData.vehicleId,
					);

					if (selectedVehicle) {
						setVehicleDimensions({
							length: selectedVehicle.length,
							width: selectedVehicle.width,
							height: selectedVehicle.height,
							loadingWeight: selectedVehicle.loadingWeight,
							euroPalletsAmount:
								selectedVehicle.euroPalletsAmount,
						});
					}
				} catch (error) {
					console.error("Error loading vehicle dimensions:", error);
				}
			}
		};

		loadVehicleDimensions();
	}, [tourData.vehicleId, tourData.organizationId, vehicleDimensions.length]);

	// Function to update tour data
	const updateTourData = useCallback((data: Partial<TourResponse>) => {
		setTourData((prev) => ({ ...prev, ...data }));
	}, []);

	const addStop = useCallback(
		(stop: Omit<Stop, "position"> & { position?: number }) => {
			setStops((prev) => {
				// Find the next position (max + 1 or 0 if empty)
				const nextPosition =
					prev.length > 0
						? Math.max(...prev.map((s) => s.position)) + 1
						: 0;

				return [
					...prev,
					{ ...stop, position: stop.position || nextPosition },
				] as Stop[];
			});
		},
		[],
	);

	// Add a new tour-specific stop (not yet created in the database)
	const addTourStop = useCallback(
		(stop: NewTourStop) => {
			setNewTourStops((prev) => {
				// Find the next position (max + 1 or 0 if empty)
				const allStopsPositions = [
					...stops.map((s) => s.position),
					...prev.map((s) => s.position || 0),
				];

				const nextPosition =
					allStopsPositions.length > 0
						? Math.max(...allStopsPositions) + 1
						: 0;

				return [
					...prev,
					{
						...stop,
						position:
							stop.position !== undefined
								? stop.position
								: nextPosition,
						isNew: true,
					},
				];
			});
		},
		[stops],
	);

	const removeStop = useCallback((stopId: string) => {
		// First check if it's in the regular stops
		setStops((prev) => prev.filter((stop) => stop.id !== stopId));

		// Then check if it's in the new tour stops (they don't have real IDs yet, so we need a different approach)
		setNewTourStops((prev) => {
			// For new tour stops, we're using client-side IDs, so this might be different
			// For simplicity, we'll just filter based on any matching "id" property
			return prev.filter((stop) => stop.id !== stopId);
		});
	}, []);

	const reorderStops = useCallback((newOrder: Stop[]) => {
		setStops(newOrder);
	}, []);

	const getStopOrders = useCallback((): StopOrder[] => {
		return stops.map((stop) => ({
			stopId: stop.id,
			position: stop.position,
		}));
	}, [stops]);

	// Convert new tour stops to the format expected by the API
	const getTourStops = useCallback((): TourStopInput[] => {
		return newTourStops.map((stop) => {
			// Remove fields that shouldn't be sent to the API
			const { isNew, id, order, coordinates, positionTour, ...stopData } =
				stop;

			// Return all stop data with proper formatting
			return {
				...stopData,
				// Ensure position is set
				position: stop.position || 0,
				// Convert coordinates if they exist
				latitude: coordinates?.latitude || stop.latitude || undefined,
				longitude:
					coordinates?.longitude || stop.longitude || undefined,
				// Convert null values to undefined for optional fields
				...Object.fromEntries(
					Object.entries(stopData).map(([key, value]) => [
						key,
						value === null ? undefined : value,
					]),
				),
			} as unknown as TourStopInput;
		});
	}, [newTourStops]);

	// Centralized function to select a vehicle and automatically set related data
	const selectVehicle = useCallback(
		async (vehicleId: string, organizationId: string) => {
			// If no vehicle ID, clear all vehicle-related data
			if (!vehicleId) {
				updateTourData({
					vehicleId: null,
					truckLicensePlate: "",
					carrierId: null,
					trailerVehicleId: null,
					trailerLicensePlate: "",
				});
				// Clear vehicle dimensions
				setVehicleDimensions({});
				return;
			}

			// Fetch vehicles to get details about selected vehicle
			try {
				// Use the fetchVehicles function from the vehicles lib
				const vehiclesData = await fetchVehicles({
					organizationId,
					limit: 100,
				});

				const selectedVehicle = vehiclesData.items.find(
					(v) => v.id === vehicleId,
				);

				if (selectedVehicle) {
					// Update vehicle ID
					updateTourData({ vehicleId });

					// Auto-fill truck license plate if vehicle is selected
					if (selectedVehicle.licensePlate) {
						updateTourData({
							truckLicensePlate: selectedVehicle.licensePlate,
						});
					}

					// Auto-set carrier if the vehicle has an owner
					if (selectedVehicle.ownerId) {
						updateTourData({ carrierId: selectedVehicle.ownerId });
					} else {
						// Clear the carrier if vehicle doesn't have an owner
						updateTourData({ carrierId: null });
					}

					// Find trailer if one is attached to this vehicle
					const attachedTrailer = vehiclesData.items.find(
						(item) =>
							item.vehicleType === "TRAILER" &&
							item.attachedToId === vehicleId,
					);

					if (attachedTrailer) {
						updateTourData({
							trailerVehicleId: attachedTrailer.id,
							trailerLicensePlate:
								attachedTrailer.licensePlate || "",
						});
					} else {
						// Clear trailer data if no trailer is attached to this vehicle
						updateTourData({
							trailerVehicleId: null,
							trailerLicensePlate: "",
						});
					}

					// Update vehicle dimensions
					setVehicleDimensions({
						length: selectedVehicle.length,
						width: selectedVehicle.width,
						height: selectedVehicle.height,
						loadingWeight: selectedVehicle.loadingWeight,
						euroPalletsAmount: selectedVehicle.euroPalletsAmount,
					});
				}
			} catch (error) {
				console.error("Error fetching vehicle data:", error);
			}
		},
		[updateTourData, setVehicleDimensions],
	);

	const calculateCargoFill = useCallback((): number => {
		// If no vehicle length available, return 0
		if (!vehicleDimensions.length || vehicleDimensions.length <= 0) {
			return 0;
		}

		// Combine all stops (existing and new tour stops)
		const allStops = [...stops, ...newTourStops];

		// Find the last loading stop position
		const loadingStops = allStops.filter(
			(stop) => stop.stopType === "loading",
		);
		if (loadingStops.length === 0) {
			return 0;
		}

		const lastLoadingPosition = Math.max(
			...loadingStops.map((stop) => stop.position || 0),
		);

		// Only consider stops up to and including the last loading stop
		const relevantStops = allStops
			.filter((stop) => (stop.position || 0) <= lastLoadingPosition)
			.sort((a, b) => (a.position || 0) - (b.position || 0));

		// Simulate cargo flow through the relevant stops
		let currentCargoMeters = 0;

		for (const stop of relevantStops) {
			if (stop.stopType === "loading") {
				// Add cargo from loading stops
				const loadingMeter = Number(stop.loading_meter) || 0;
				currentCargoMeters += loadingMeter;
			} else if (stop.stopType === "unloading") {
				// Calculate unloading amount
				let unloadingMeter = 0;

				// Check if stop has explicit loading_meter (explicit unloading amount)
				if (stop.loading_meter && Number(stop.loading_meter) > 0) {
					unloadingMeter = Number(stop.loading_meter);
				} else {
					// Implicit unloading - find related loading stops by order ID
					const orderId = stop.orderId;

					if (orderId) {
						// Find all loading stops for this order within our relevant stops
						const relatedLoadingStops = relevantStops.filter(
							(relatedStop) => {
								return (
									relatedStop.stopType === "loading" &&
									relatedStop.orderId === orderId
								);
							},
						);

						// Sum up loading meters from related loading stops
						unloadingMeter = relatedLoadingStops.reduce(
							(total, loadingStop) => {
								return (
									total +
									(Number(loadingStop.loading_meter) || 0)
								);
							},
							0,
						);
					}
				}

				// Subtract unloaded cargo (don't go below 0)
				currentCargoMeters = Math.max(
					0,
					currentCargoMeters - unloadingMeter,
				);
			}
			// Ignore stopover stops for cargo calculation
		}

		// Calculate percentage based on vehicle length capacity
		const vehicleCapacityMeters = vehicleDimensions.length;

		if (vehicleCapacityMeters <= 0) {
			return 0;
		}

		const fillPercentage =
			(currentCargoMeters / vehicleCapacityMeters) * 100;

		// Cap at 100% to avoid overflow display
		return Math.min(100, Math.max(0, fillPercentage));
	}, [vehicleDimensions.length, stops, newTourStops]);

	const getCurrentLoadingMeters = useCallback((): number => {
		// Combine all stops (existing and new tour stops)
		const allStops = [...stops, ...newTourStops];

		// Find the last loading stop position
		const loadingStops = allStops.filter(
			(stop) => stop.stopType === "loading",
		);
		if (loadingStops.length === 0) {
			return 0;
		}

		const lastLoadingPosition = Math.max(
			...loadingStops.map((stop) => stop.position || 0),
		);

		// Only consider stops up to and including the last loading stop
		const relevantStops = allStops
			.filter((stop) => (stop.position || 0) <= lastLoadingPosition)
			.sort((a, b) => (a.position || 0) - (b.position || 0));

		// Simulate cargo flow through the relevant stops
		let currentCargoMeters = 0;

		for (const stop of relevantStops) {
			if (stop.stopType === "loading") {
				// Add cargo from loading stops
				const loadingMeter = Number(stop.loading_meter) || 0;
				currentCargoMeters += loadingMeter;
			} else if (stop.stopType === "unloading") {
				// Calculate unloading amount
				let unloadingMeter = 0;

				// Check if stop has explicit loading_meter (explicit unloading amount)
				if (stop.loading_meter && Number(stop.loading_meter) > 0) {
					unloadingMeter = Number(stop.loading_meter);
				} else {
					// Implicit unloading - find related loading stops by order ID
					const orderId = stop.orderId;

					if (orderId) {
						// Find all loading stops for this order within our relevant stops
						const relatedLoadingStops = relevantStops.filter(
							(relatedStop) => {
								return (
									relatedStop.stopType === "loading" &&
									relatedStop.orderId === orderId
								);
							},
						);

						// Sum up loading meters from related loading stops
						unloadingMeter = relatedLoadingStops.reduce(
							(total, loadingStop) => {
								return (
									total +
									(Number(loadingStop.loading_meter) || 0)
								);
							},
							0,
						);
					}
				}

				// Subtract unloaded cargo (don't go below 0)
				currentCargoMeters = Math.max(
					0,
					currentCargoMeters - unloadingMeter,
				);
			}
			// Ignore stopover stops for cargo calculation
		}

		return currentCargoMeters;
	}, [stops, newTourStops]);

	const value: TourBuilderContextValue = {
		tourData,
		updateTourData,
		stops,
		addStop,
		removeStop,
		reorderStops,
		newTourStops,
		addTourStop,
		selectVehicle,
		isSaving,
		setIsSaving,
		mode,
		tourId,
		getStopOrders,
		getTourStops,
		// Add vehicle locations data
		vehicleLocations,
		isLoadingVehicleLocations,
		showVehicleLocations,
		setShowVehicleLocations,
		// Add map bounds data
		mapBounds,
		setMapBounds,
		filterStopsByMap,
		setFilterStopsByMap,
		// Add vehicle dimensions data
		vehicleDimensions,
		// Add calculateCargoFill function
		calculateCargoFill,
		getCurrentLoadingMeters,
	};

	return (
		<TourBuilderContext.Provider value={value}>
			{children}
		</TourBuilderContext.Provider>
	);
}

export function useTourBuilder() {
	const context = useContext(TourBuilderContext);

	if (context === undefined) {
		throw new Error(
			"useTourBuilder must be used within a TourBuilderProvider",
		);
	}

	return context;
}
