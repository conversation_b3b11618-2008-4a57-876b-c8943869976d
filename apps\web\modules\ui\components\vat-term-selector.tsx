"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	useCreateVatTermMutation,
	useVatTermsQuery,
} from "@saas/organizations/lib/api-vat-terms";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import {} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Check, ChevronDown, Plus } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

export interface VatTermType {
	id: string;
	name: string;
	code: string;
	rate: number | string;
	isDefault?: boolean;
	description?: string | null;
	country_code?: string;
}

export interface VatTermSelectorProps {
	vatTerms?: VatTermType[];
	onChange?: (value: string) => void;
	value?: string;
	name: string;
	label?: string;
	tooltip?: string;
	isLoading?: boolean;
	placeholder?: string;
	className?: string;
	error?: string;
	showCreateOption?: boolean;
	isModal?: boolean;
	disableAutoDefault?: boolean;
}

// Form schema for creating VAT terms
const createVatTermSchema = z.object({
	name: z.string().min(1, "Name is required"),
	description: z.string().optional(),
	rate: z.coerce.number().min(0, "Rate cannot be negative"),
	isDefault: z.boolean().default(false),
	code: z.string().optional().default(""),
	country_code: z
		.string()
		.min(2, "Country code is required")
		.max(2, "Use ISO 2-letter code"),
});

type CreateVatTermValues = z.infer<typeof createVatTermSchema>;

/**
 * A reusable VAT Term selector component that displays VAT term details in a card
 */
export function VatTermSelector({
	vatTerms = [],
	onChange,
	value,
	name,
	tooltip,
	isLoading = false,
	placeholder = "Select VAT term",
	className,
	error,
	showCreateOption = true,
	isModal = false,
	disableAutoDefault = false,
}: VatTermSelectorProps) {
	const { activeOrganization } = useActiveOrganization();
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [open, setOpen] = useState(false);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const createButtonRef = useRef<HTMLButtonElement>(null);

	const createForm = useForm<CreateVatTermValues>({
		resolver: zodResolver(createVatTermSchema),
		defaultValues: {
			name: "",
			description: "",
			rate: 0,
			isDefault: false,
			code: "",
			country_code: "",
		},
	});

	const createMutation = useCreateVatTermMutation(
		activeOrganization?.id || "",
	);

	// Get VAT terms from API if not provided
	const { data: apiVatTerms, isLoading: isLoadingApi } = useVatTermsQuery(
		{ organizationId: activeOrganization?.id || "" },
		{ enabled: !vatTerms?.length && !!activeOrganization?.id },
	);

	// Combine provided VAT terms with those from API
	const allVatTerms = vatTerms?.length
		? vatTerms
		: (apiVatTerms?.items ?? []);

	// Find the default VAT term
	const defaultVatTerm = allVatTerms?.find((term) => term.isDefault);

	// Set the default value if no value is provided and a default term exists
	useEffect(() => {
		if (!value && defaultVatTerm && onChange && !disableAutoDefault) {
			onChange(defaultVatTerm.id);
		}
	}, [defaultVatTerm, value, onChange, disableAutoDefault]);

	// Handle creating a new VAT term
	const handleCreateVatTerm = async (values: CreateVatTermValues) => {
		try {
			if (!activeOrganization?.id) {
				toast.error("No active organization");
				return;
			}

			const result = await createMutation.mutateAsync({
				...values,
				code: values.code || values.name.substring(0, 10).toUpperCase(),
				organizationId: activeOrganization?.id,
				isActive: true,
			});

			// Select the newly created VAT term
			if (result && onChange) {
				onChange(result.id);
			}

			setIsDialogOpen(false);
			createForm.reset();
		} catch (error) {
			console.error("Error creating VAT term:", error);
		}
	};

	// Find the selected VAT term
	const selectedTerm = allVatTerms?.find((term) => term.id === value);

	// Handle keyboard navigation
	const handleKeyDown = (e: React.KeyboardEvent) => {
		// Focus on the "Create new VAT term" button when pressing End key
		if (e.key === "Tab" && createButtonRef.current) {
			e.preventDefault();
			createButtonRef.current.focus();
		}

		// Close the dropdown menu when pressing Escape
		if (e.key === "Escape") {
			e.preventDefault();
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	return (
		<div className={cn("space-y-2", className)}>
			<DropdownMenu open={open} onOpenChange={setOpen} modal={isModal}>
				<DropdownMenuTrigger
					asChild
					disabled={isLoading || isLoadingApi}
				>
					<Button
						ref={buttonRef}
						variant="outline"
						className={cn(
							"w-full justify-between p-0 overflow-hidden h-auto text-left",
							!selectedTerm && "text-muted-foreground",
						)}
					>
						{isLoading || isLoadingApi ? (
							<div className="p-4">Loading...</div>
						) : selectedTerm ? (
							<Card className="w-full border-0 shadow-none">
								<CardContent className="p-4">
									<div className="flex flex-col items-start">
										<div className="flex justify-between w-full items-center">
											<div className="font-medium text-base">
												{selectedTerm.name}
												{selectedTerm.country_code && (
													<span className="ml-2 text-xs px-1.5 py-0.5 rounded-sm bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 font-medium">
														{
															selectedTerm.country_code
														}
													</span>
												)}
											</div>
											<div className="flex items-center gap-2">
												{selectedTerm.isDefault && (
													<span className="text-xs px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 font-medium">
														Default
													</span>
												)}
												<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
											</div>
										</div>
										<div className="mt-1 text-sm text-muted-foreground flex items-center flex-wrap gap-2">
											<div className="flex items-center gap-1">
												<span className="font-medium">
													Rate:
												</span>
												<span>
													{typeof selectedTerm.rate ===
													"number"
														? `${selectedTerm.rate}%`
														: selectedTerm.rate}
												</span>
											</div>
										</div>
										{selectedTerm.description && (
											<p className="text-xs text-muted-foreground mt-1 line-clamp-2">
												{selectedTerm.description}
											</p>
										)}
									</div>
								</CardContent>
							</Card>
						) : (
							<div className="p-4 text-left">{placeholder}</div>
						)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					className="p-0 max-h-[300px] overflow-auto"
					align="start"
					sideOffset={4}
					style={{
						width: "var(--radix-dropdown-menu-trigger-width)",
					}}
					role="menu"
					aria-orientation="vertical"
					aria-label="VAT terms list"
					onKeyDown={handleKeyDown}
				>
					{!Array.isArray(allVatTerms) || allVatTerms.length === 0 ? (
						<div className="py-6 text-center">
							<p className="text-sm text-muted-foreground">
								No VAT term found.
							</p>
							{showCreateOption && (
								<div className="px-2 pt-2">
									<Dialog
										open={isDialogOpen}
										onOpenChange={setIsDialogOpen}
									>
										<DialogTrigger asChild>
											<Button
												ref={createButtonRef}
												variant="outline"
												size="sm"
												className="w-full"
												tabIndex={0}
												role="menuitem"
												aria-label="Create new VAT term"
											>
												<Plus className="mr-2 h-4 w-4" />
												Create new VAT term
											</Button>
										</DialogTrigger>
										<DialogContent className="sm:max-w-[425px]">
											<DialogHeader>
												<DialogTitle>
													Create VAT Term
												</DialogTitle>
												<DialogDescription>
													Add a new VAT term to your
													organization.
												</DialogDescription>
											</DialogHeader>
											<Form {...createForm}>
												<form
													onSubmit={(e) => {
														e.preventDefault();
														e.stopPropagation();
														createForm.handleSubmit(
															handleCreateVatTerm,
														)(e);
													}}
													className="space-y-4"
													onClick={(e) =>
														e.stopPropagation()
													}
													onKeyDown={(e) =>
														e.stopPropagation()
													}
												>
													<FormField
														control={
															createForm.control
														}
														name="name"
														render={({ field }) => (
															<FormItem>
																<FormLabel htmlFor="name">
																	Name
																</FormLabel>
																<FormControl>
																	<Input
																		id="name"
																		placeholder="Enter name"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={
															createForm.control
														}
														name="code"
														render={({ field }) => (
															<FormItem>
																<FormLabel htmlFor="code">
																	Code
																</FormLabel>
																<FormControl>
																	<Input
																		id="code"
																		placeholder="Enter code (optional)"
																		{...field}
																		value={
																			field.value ||
																			""
																		}
																	/>
																</FormControl>
																<FormDescription>
																	A short code
																	for the VAT
																	term (e.g.
																	VAT19)
																</FormDescription>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={
															createForm.control
														}
														name="rate"
														render={({ field }) => (
															<FormItem>
																<FormLabel htmlFor="rate">
																	Rate (%)
																</FormLabel>
																<FormControl>
																	<Input
																		id="rate"
																		type="number"
																		step="0.01"
																		placeholder="19"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={
															createForm.control
														}
														name="country_code"
														render={({ field }) => (
															<FormItem>
																<FormLabel htmlFor="country_code">
																	Country Code
																</FormLabel>
																<FormControl>
																	<Input
																		id="country_code"
																		placeholder="DE"
																		maxLength={
																			2
																		}
																		{...field}
																	/>
																</FormControl>
																<FormDescription>
																	ISO 2-letter
																	country code
																	(e.g. DE,
																	FR, IT)
																</FormDescription>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={
															createForm.control
														}
														name="description"
														render={({ field }) => (
															<FormItem>
																<FormLabel htmlFor="description">
																	Description
																</FormLabel>
																<FormControl>
																	<Input
																		id="description"
																		placeholder="Enter description (optional)"
																		{...field}
																		value={
																			field.value ||
																			""
																		}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
													<FormField
														control={
															createForm.control
														}
														name="isDefault"
														render={({ field }) => (
															<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
																<div className="space-y-0.5">
																	<FormLabel htmlFor="isDefault">
																		Default
																		Term
																	</FormLabel>
																	<FormDescription>
																		Make
																		this the
																		default
																		VAT term
																	</FormDescription>
																</div>
																<FormControl>
																	<Switch
																		id="isDefault"
																		checked={
																			field.value
																		}
																		onCheckedChange={
																			field.onChange
																		}
																	/>
																</FormControl>
															</FormItem>
														)}
													/>
													<DialogFooter>
														<Button
															type="button"
															variant="outline"
															onClick={() =>
																setIsDialogOpen(
																	false,
																)
															}
														>
															Cancel
														</Button>
														<Button
															type="submit"
															disabled={
																createMutation.isPending
															}
														>
															{createMutation.isPending
																? "Creating..."
																: "Create"}
														</Button>
													</DialogFooter>
												</form>
											</Form>
										</DialogContent>
									</Dialog>
								</div>
							)}
						</div>
					) : (
						<>
							{allVatTerms.map((term) => (
								<DropdownMenuItem
									key={term.id}
									className={cn(
										"flex flex-col w-full items-start text-left px-3 py-2 cursor-pointer",
										value === term.id
											? "bg-accent/5 text-accent-foreground"
											: "",
										"focus:bg-accent/10 focus:text-accent-foreground/90",
									)}
									onSelect={() => {
										onChange?.(term.id);
									}}
									role="menuitemradio"
									aria-checked={value === term.id}
								>
									<div className="flex w-full items-center justify-between">
										<div className="font-medium">
											{term.name}
											{term.country_code && (
												<span className="ml-2 text-xs px-1.5 py-0.5 rounded-sm bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 font-medium">
													{term.country_code}
												</span>
											)}
										</div>
										<div className="flex items-center gap-2">
											{term.isDefault && (
												<span className="text-xs px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 font-medium">
													Default
												</span>
											)}
											{value === term.id && (
												<Check className="h-4 w-4 text-primary" />
											)}
										</div>
									</div>
									<div className="mt-1 text-xs text-muted-foreground flex items-center flex-wrap gap-2">
										<div className="flex items-center gap-1">
											<span className="font-medium">
												Rate:
											</span>
											<span>
												{typeof term.rate === "number"
													? `${term.rate}%`
													: term.rate}
											</span>
										</div>
									</div>
									{term.description && (
										<p className="text-xs text-muted-foreground mt-1 line-clamp-2">
											{term.description}
										</p>
									)}
								</DropdownMenuItem>
							))}
						</>
					)}
					{showCreateOption &&
						allVatTerms &&
						allVatTerms.length > 0 && (
							<div className="p-2 border-t">
								<Dialog
									open={isDialogOpen}
									onOpenChange={setIsDialogOpen}
								>
									<DialogTrigger asChild>
										<Button
											ref={createButtonRef}
											variant="outline"
											size="sm"
											className="w-full"
											tabIndex={0}
											role="menuitem"
											aria-label="Create new VAT term"
										>
											<Plus className="mr-2 h-4 w-4" />
											Create new VAT term
										</Button>
									</DialogTrigger>
									<DialogContent
										className="sm:max-w-[425px]"
										onOpenAutoFocus={(e) => {
											// Prevent the default focus behavior
											e.preventDefault();
											// Focus on the first form field
											const nameInput =
												document.querySelector(
													'input[name="name"]',
												);
											if (
												nameInput instanceof HTMLElement
											) {
												nameInput.focus();
											}
										}}
									>
										<DialogHeader>
											<DialogTitle>
												Create VAT Term
											</DialogTitle>
											<DialogDescription>
												Add a new VAT term to your
												organization.
											</DialogDescription>
										</DialogHeader>
										<Form {...createForm}>
											<form
												onSubmit={(e) => {
													e.preventDefault();
													e.stopPropagation();
													createForm.handleSubmit(
														handleCreateVatTerm,
													)(e);
												}}
												className="space-y-4"
												onClick={(e) =>
													e.stopPropagation()
												}
												onKeyDown={(e) =>
													e.stopPropagation()
												}
											>
												<FormField
													control={createForm.control}
													name="name"
													render={({ field }) => (
														<FormItem>
															<FormLabel htmlFor="name">
																Name
															</FormLabel>
															<FormControl>
																<Input
																	id="name"
																	placeholder="Enter name"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<FormField
													control={createForm.control}
													name="code"
													render={({ field }) => (
														<FormItem>
															<FormLabel htmlFor="code">
																Code
															</FormLabel>
															<FormControl>
																<Input
																	id="code"
																	placeholder="Enter code (optional)"
																	{...field}
																	value={
																		field.value ||
																		""
																	}
																/>
															</FormControl>
															<FormDescription>
																A short code for
																the VAT term
																(e.g. VAT19)
															</FormDescription>
															<FormMessage />
														</FormItem>
													)}
												/>
												<FormField
													control={createForm.control}
													name="rate"
													render={({ field }) => (
														<FormItem>
															<FormLabel htmlFor="rate">
																Rate (%)
															</FormLabel>
															<FormControl>
																<Input
																	id="rate"
																	type="number"
																	step="0.01"
																	placeholder="19"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<FormField
													control={createForm.control}
													name="country_code"
													render={({ field }) => (
														<FormItem>
															<FormLabel htmlFor="country_code">
																Country Code
															</FormLabel>
															<FormControl>
																<Input
																	id="country_code"
																	placeholder="DE"
																	maxLength={
																		2
																	}
																	{...field}
																/>
															</FormControl>
															<FormDescription>
																ISO 2-letter
																country code
																(e.g. DE, FR,
																IT)
															</FormDescription>
															<FormMessage />
														</FormItem>
													)}
												/>
												<FormField
													control={createForm.control}
													name="description"
													render={({ field }) => (
														<FormItem>
															<FormLabel htmlFor="description">
																Description
															</FormLabel>
															<FormControl>
																<Input
																	id="description"
																	placeholder="Enter description (optional)"
																	{...field}
																	value={
																		field.value ||
																		""
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<FormField
													control={createForm.control}
													name="isDefault"
													render={({ field }) => (
														<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
															<div className="space-y-0.5">
																<FormLabel htmlFor="isDefault">
																	Default Term
																</FormLabel>
																<FormDescription>
																	Make this
																	the default
																	VAT term
																</FormDescription>
															</div>
															<FormControl>
																<Switch
																	id="isDefault"
																	checked={
																		field.value
																	}
																	onCheckedChange={
																		field.onChange
																	}
																/>
															</FormControl>
														</FormItem>
													)}
												/>
												<DialogFooter>
													<Button
														type="button"
														variant="outline"
														onClick={() =>
															setIsDialogOpen(
																false,
															)
														}
													>
														Cancel
													</Button>
													<Button
														type="submit"
														disabled={
															createMutation.isPending
														}
													>
														{createMutation.isPending
															? "Creating..."
															: "Create"}
													</Button>
												</DialogFooter>
											</form>
										</Form>
									</DialogContent>
								</Dialog>
							</div>
						)}
				</DropdownMenuContent>
			</DropdownMenu>

			{error && (
				<p className="text-sm font-medium text-destructive">{error}</p>
			)}
		</div>
	);
}
