"use client";
import { AvailableOrders } from "./available-orders";
import { AvailableVehicles } from "./available-vehicles";
import { FinanceRevenue } from "./finance-revenue";

/**
 * Dashboard component that displays vehicle availability, available orders, and revenue data
 */
export const Dashboard = () => {
	return (
		<div className="space-y-4 py-4 sm:space-y-6 sm:py-6">
			{/* Grid layout for top widgets */}
			<div className="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-2">
				<AvailableVehicles />
				<AvailableOrders />
			</div>

			{/* Revenue chart with full width */}
			<div>
				<FinanceRevenue />
			</div>
		</div>
	);
};

export default Dashboard;
