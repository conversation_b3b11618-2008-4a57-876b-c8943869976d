"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import { FileText, FileX, Receipt } from "lucide-react";
import { CreditNotesPanel } from "./credit-notes-panel";
import { InvoicesPanel } from "./invoice-panel";
import { OffersPanel } from "./offer-panel";

interface OrderTabsProps {
	orderId: string;
	onCreateOffer?: () => void;
	onCreateInvoice?: () => void;
	onCreateCreditNote?: () => void;
}

export function OrderTabs({
	orderId,
	onCreateOffer,
	onCreateInvoice,
	onCreateCreditNote,
}: OrderTabsProps) {
	return (
		<Tabs defaultValue="offers" className="w-full">
			<TabsList className="grid w-full grid-cols-3 mb-4">
				<TabsTrigger value="offers" className="flex items-center gap-2">
					<FileText className="h-4 w-4" />
					Offers
				</TabsTrigger>
				<TabsTrigger
					value="invoices"
					className="flex items-center gap-2"
				>
					<Receipt className="h-4 w-4" />
					Invoices
				</TabsTrigger>
				<TabsTrigger
					value="credit-notes"
					className="flex items-center gap-2"
				>
					<FileX className="h-4 w-4" />
					Credit Notes
				</TabsTrigger>
			</TabsList>

			<TabsContent value="offers" className="mt-0">
				<OffersPanel orderId={orderId} onCreateOffer={onCreateOffer} />
			</TabsContent>

			<TabsContent value="invoices" className="mt-0">
				<InvoicesPanel
					orderId={orderId}
					onCreateInvoice={onCreateInvoice}
				/>
			</TabsContent>

			<TabsContent value="credit-notes" className="mt-0">
				<CreditNotesPanel
					orderId={orderId}
					onCreateCreditNote={onCreateCreditNote}
				/>
			</TabsContent>
		</Tabs>
	);
}
