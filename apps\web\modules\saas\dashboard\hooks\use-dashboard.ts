import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useState } from "react";
import {
	formatStopAddress,
	formatTime,
	getStopTimeDisplay,
	useAvailableOrdersQuery,
	useAvailableVehiclesQuery,
	useVehiclesWithToursQuery,
} from "../lib/api";

/**
 * Main dashboard hook for fetching vehicle and tour data
 * Returns vehicles with their scheduled tours
 */
export function useDashboard() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(50);

	// For available vehicles pagination (separate from regular vehicles)
	const [availablePage, setAvailablePage] = useState(1);
	const [availablePageSize, setAvailablePageSize] = useState(50);

	// For available orders pagination
	const [availableOrdersPage, setAvailableOrdersPage] = useState(1);
	const [availableOrdersPageSize, setAvailableOrdersPageSize] = useState(50);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const handleAvailablePageSizeChange = (size: number) => {
		setAvailablePageSize(size);
		setAvailablePage(1);
	};

	const handleAvailableOrdersPageSizeChange = (size: number) => {
		setAvailableOrdersPageSize(size);
		setAvailableOrdersPage(1);
	};

	// Fetch vehicles with tours
	const vehiclesQuery = useVehiclesWithToursQuery({
		organizationId: activeOrganization?.id ?? "",
		page,
		limit: pageSize,
	});

	// Fetch available vehicles
	const availableVehiclesQuery = useAvailableVehiclesQuery({
		organizationId: activeOrganization?.id ?? "",
		page: availablePage,
		limit: availablePageSize,
	});

	// Fetch available orders
	const availableOrdersQuery = useAvailableOrdersQuery({
		organizationId: activeOrganization?.id ?? "",
		page: availableOrdersPage,
		limit: availableOrdersPageSize,
	});

	return {
		// Vehicles with tours data
		vehicles: vehiclesQuery.data?.items ?? [],
		total: vehiclesQuery.data?.total,
		totalPages: vehiclesQuery.data?.totalPages,
		isLoading: vehiclesQuery.isLoading || !loaded,
		error: vehiclesQuery.error,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		refetch: vehiclesQuery.refetch,

		// Available vehicles data
		availableVehicles: availableVehiclesQuery.data?.items ?? [],
		availableTotal: availableVehiclesQuery.data?.total,
		availableTotalPages: availableVehiclesQuery.data?.totalPages,
		isAvailableLoading: availableVehiclesQuery.isLoading || !loaded,
		availableError: availableVehiclesQuery.error,
		availablePage,
		setAvailablePage,
		availablePageSize,
		setAvailablePageSize: handleAvailablePageSizeChange,
		refetchAvailable: availableVehiclesQuery.refetch,

		// Available orders data
		availableOrders: availableOrdersQuery.data?.items ?? [],
		availableOrdersTotal: availableOrdersQuery.data?.total,
		availableOrdersTotalPages: availableOrdersQuery.data?.totalPages,
		isAvailableOrdersLoading: availableOrdersQuery.isLoading || !loaded,
		availableOrdersError: availableOrdersQuery.error,
		availableOrdersPage,
		setAvailableOrdersPage,
		availableOrdersPageSize,
		setAvailableOrdersPageSize: handleAvailableOrdersPageSizeChange,
		refetchAvailableOrders: availableOrdersQuery.refetch,

		// Export utility functions for easy use in components
		utils: {
			formatTime,
			formatStopAddress,
			getStopTimeDisplay,
		},
	};
}

/**
 * Utility hook for parsing work hours from a vehicle
 */
export function useVehicleWorkHours(workHours: { start: string; end: string }) {
	// Convert string work hours to Date objects for today
	const getWorkHourAsDate = (timeString: string): Date => {
		const [hours, minutes] = timeString
			.split(":")
			.map((num) => Number.parseInt(num, 10));
		const date = new Date();
		date.setHours(hours, minutes, 0, 0);
		return date;
	};

	const startTime = getWorkHourAsDate(workHours.start);
	const endTime = getWorkHourAsDate(workHours.end);

	// Calculate total work hours in decimal
	const totalHours =
		(endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

	return {
		startTime,
		endTime,
		totalHours,
		formattedStartTime: formatTime(startTime),
		formattedEndTime: formatTime(endTime),
	};
}
