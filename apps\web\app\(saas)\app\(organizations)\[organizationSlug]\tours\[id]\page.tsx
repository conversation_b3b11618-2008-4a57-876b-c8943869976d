"use client";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import { Loader2 } from "lucide-react";
import dynamic from "next/dynamic";
import { usePathname, useRouter } from "next/navigation";
import { Suspense, use, useRef } from "react";

// Dynamically import TourBuilder to avoid hydration mismatch
const TourBuilder = dynamic(
	() =>
		import("@saas/tours/components/tour-builder").then(
			(mod) => mod.TourBuilder,
		),
	{ ssr: false },
);

interface TourPageProps {
	params: Promise<{
		id: string;
		organizationSlug: string;
	}>;
}

export default function TourPage({ params }: TourPageProps) {
	const router = useRouter();
	const pathname = usePathname();
	const { activeOrganization } = useActiveOrganization();
	const tourBuilderRef = useRef<any>(null);

	// Properly unwrap params with use()
	const unwrappedParams = use(params);
	const { id, organizationSlug } = unwrappedParams;
	const isEditMode = id !== "new";
	const tourId = isEditMode ? id : undefined;

	// Handle successful save by navigating to edit mode with the new tour ID
	const handleSuccess = (tourId: string) => {
		if (tourId && !isEditMode) {
			// Create the correct path by replacing "new" with the actual tour ID
			const newPath = pathname.replace("/new", `/${tourId}`);
			router.push(newPath);
		}
	};

	// Handle save button click
	const handleSave = () => {
		if (tourBuilderRef.current?.handleSave) {
			tourBuilderRef.current.handleSave();
		}
	};

	return (
		<div className="flex flex-col h-screen overflow-hidden">
			<div className="flex justify-between items-center mb-6 shrink-0">
				<h1 className="text-3xl font-bold tracking-tight">
					{isEditMode ? "Edit Tour" : "Create Tour"}
				</h1>
				<Button size="lg" onClick={handleSave}>
					{isEditMode ? "Update Tour" : "Create Tour"}
				</Button>
			</div>
			<Suspense
				fallback={
					<div className="flex-1 flex items-center justify-center min-h-0">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				}
			>
				<div className="flex-1 flex flex-col min-h-0 overflow-hidden">
					<TourBuilder
						mode={isEditMode ? "edit" : "create"}
						tourId={tourId}
						onSuccess={handleSuccess}
						ref={tourBuilderRef}
						showSaveButton={false}
					/>
				</div>
			</Suspense>
		</div>
	);
}
