import type {
	CreateOrderInput,
	OrderStatus,
	SendOrderConfirmationEmailInput,
	UpdateOrderInput,
} from "@repo/api/src/routes/orders/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

export interface FetchOrdersParams {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	status?: OrderStatus;
	customerId?: string;
	startDate?: Date;
	endDate?: Date;
}

export const orderKeys = {
	all: ["orders"] as const,
	list: (params: FetchOrdersParams) =>
		[...orderKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...orderKeys.all, "detail", organizationId, id] as const,
};

export const fetchOrders = async (params: FetchOrdersParams) => {
	const response = await apiClient.orders.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			status: params.status,
			customerId: params.customerId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch orders list");
	}

	return response.json();
};

export const useOrdersQuery = (params: FetchOrdersParams) => {
	return useQuery({
		queryKey: orderKeys.list(params),
		queryFn: () => fetchOrders(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchOrderById = async (organizationId: string, id: string) => {
	const response = await apiClient.orders[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch order details");
	}

	return response.json();
};

export const useOrderByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: orderKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchOrderById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create order mutation
export const useCreateOrderMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateOrderInput) => {
			const response = await apiClient.orders.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create order");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Order created successfully");
			queryClient.invalidateQueries({
				queryKey: orderKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create order");
			console.error("Create order error:", error);
		},
	});
};

// Update order mutation
export const useUpdateOrderMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateOrderInput) => {
			const response = await apiClient.orders[":id"].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update order");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Order updated successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: orderKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data?.id) {
				queryClient.invalidateQueries({
					queryKey: orderKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update order");
			console.error("Update order error:", error);
		},
	});
};

// Delete order mutation
export const useDeleteOrderMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.orders[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete order");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Order deleted successfully");
			queryClient.invalidateQueries({
				queryKey: orderKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete order");
			console.error("Delete order error:", error);
		},
	});
};

// Preview order confirmation PDF
export const usePreviewOrderConfirmationPDF = (
	organizationId?: string,
	id?: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: [...orderKeys.detail(organizationId, id), "preview-pdf"],
		queryFn: async () => {
			if (!organizationId || !id) {
				return null;
			}

			const response = await apiClient.orders[":id"][
				"confirmation-preview"
			].$get({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to preview order confirmation PDF");
			}

			return response.json();
		},
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!organizationId && !!id,
	});
};

// Preview order confirmation email
export const usePreviewOrderConfirmationEmail = (
	organizationId?: string,
	id?: string,
	recipientEmail?: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: [
			...orderKeys.detail(organizationId, id),
			"preview-email",
			recipientEmail,
		],
		queryFn: async () => {
			if (!organizationId || !id) {
				return null;
			}

			const response = await apiClient.orders[":id"][
				"email-preview"
			].$get({
				param: { id },
				query: {
					organizationId,
					recipientEmail,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to preview order confirmation email");
			}

			return response.json();
		},
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!organizationId && !!id,
	});
};

// Send order confirmation email
export const useSendOrderConfirmationEmailMutation = (
	organizationId: string,
) => {
	return useMutation({
		mutationFn: async ({
			id,
			recipientEmail,
			ccEmails,
			bccEmails,
			documentIds,
			documentFilenames,
		}: { id: string } & SendOrderConfirmationEmailInput) => {
			const response = await apiClient.orders[":id"]["send-email"].$post({
				param: { id },
				query: { organizationId },
				json: {
					recipientEmail,
					ccEmails,
					bccEmails,
					documentIds,
					documentFilenames,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to send order confirmation email");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Order confirmation email sent successfully");
		},
		onError: (error) => {
			toast.error("Failed to send order confirmation email");
			console.error("Send order confirmation email error:", error);
		},
	});
};

// Fetch email logs for an order
export const fetchOrderEmailLogs = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.orders[":id"]["email-logs"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch order email logs");
	}

	return response.json();
};

// Hook for fetching email logs for an order
export const useOrderEmailLogsQuery = (
	organizationId?: string,
	id?: string,
) => {
	return useQuery({
		queryKey: [...orderKeys.detail(organizationId, id), "email-logs"],
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchOrderEmailLogs(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Fetch orders with uninvoiced line items
type FetchUninvoicedOrdersParams = {
	organizationId: string;
	search?: string;
	customerId?: string;
	status?: OrderStatus;
	startDate?: Date;
	endDate?: Date;
	limit?: number;
	page?: number;
	onlyWithUninvoicedItems?: boolean;
	excludeCreditNoteId?: string;
};

export const fetchOrdersWithUninvoicedItems = async (
	params: FetchUninvoicedOrdersParams,
) => {
	const response = await apiClient["order-reports"]["uninvoiced-items"].$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			customerId: params.customerId,
			status: params.status,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
			limit: params.limit?.toString(),
			page: params.page?.toString(),
			onlyWithUninvoicedItems:
				params.onlyWithUninvoicedItems !== false ? "true" : "false",
			excludeCreditNoteId: params.excludeCreditNoteId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch uninvoiced orders");
	}

	return response.json();
};

export const useOrdersWithUninvoicedItemsQuery = (
	params: FetchUninvoicedOrdersParams,
) => {
	return useQuery({
		queryKey: [...orderKeys.all, "uninvoiced", params],
		queryFn: () => fetchOrdersWithUninvoicedItems(params),
		placeholderData: keepPreviousData,
		enabled: !!params.organizationId,
	});
};
