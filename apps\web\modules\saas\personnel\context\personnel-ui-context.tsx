"use client";

import { usePersonnelMutations } from "@saas/personnel/hooks/use-personnel";
import { personnelKeys } from "@saas/personnel/lib/api";
import type { PersonnelWithRelations } from "@saas/personnel/lib/types";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { DELETE_DIALOG_SHORTCUTS } from "@saas/shared/components/shortcuts/registry/shortcut-registry";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
	type ReactNode,
	createContext,
	useCallback,
	useContext,
	useRef,
	useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import { usePersonnel } from "../hooks/use-personnel";

interface PersonnelUIContextValue {
	// State
	deletePersonnel: PersonnelWithRelations | null;
	selectedPersonnel: PersonnelWithRelations | null;
	isDeleteDialogOpen: boolean;
	focusedDeleteButton: "cancel" | "confirm";
	cancelRef: React.RefObject<HTMLButtonElement | null>;
	confirmRef: React.RefObject<HTMLButtonElement | null>;

	// Actions
	handleDeletePersonnel: (personnel: PersonnelWithRelations) => void;
	handleCancelDelete: () => void;
	handleConfirmDelete: () => Promise<void>;
	handleSetDeleteButtonFocus: (button: "cancel" | "confirm") => void;
	setSelectedPersonnel: (personnel: PersonnelWithRelations | null) => void;
	closeAllDialogs: () => void;

	// Dialog management
	setDeleteDialogOpen: (open: boolean) => void;
}

const PersonnelUIContext = createContext<PersonnelUIContextValue | null>(null);

export function PersonnelUIProvider({
	children,
	onPersonnelDeleted,
}: {
	children: ReactNode;
	onPersonnelDeleted?: () => void;
}) {
	const t = useTranslations();
	const { deletePersonnel: deletePersonnelMutation } =
		usePersonnelMutations();
	const { data } = usePersonnel();
	const queryClient = useQueryClient();
	const {
		enableScope,
		disableScope,
		addShortcuts,
		removeShortcuts,
		activeScopes,
	} = useShortcuts();

	// State
	const [deletePersonnel, setDeletePersonnel] =
		useState<PersonnelWithRelations | null>(null);
	const [selectedPersonnel, setSelectedPersonnel] =
		useState<PersonnelWithRelations | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [focusedDeleteButton, setFocusedDeleteButton] = useState<
		"cancel" | "confirm"
	>("cancel");

	// Refs for focus management
	const cancelRef = useRef<HTMLButtonElement>(null);
	const confirmRef = useRef<HTMLButtonElement>(null);

	// Actions
	const handleDeletePersonnel = useCallback(
		(personnel: PersonnelWithRelations) => {
			setDeletePersonnel(personnel);
			setFocusedDeleteButton("cancel");
			setIsDeleteDialogOpen(true);

			// When delete dialog opens, disable table scope and enable dialog scope
			disableScope("personnel-shortcuts");
			enableScope("personnel-delete-dialog");

			// Register shortcuts for the delete dialog
			addShortcuts(DELETE_DIALOG_SHORTCUTS);

			return () => {
				removeShortcuts(DELETE_DIALOG_SHORTCUTS.map((s) => s.id));
				disableScope("personnel-delete-dialog");
				enableScope("personnel-shortcuts");
			};
		},
		[addShortcuts, disableScope, enableScope, removeShortcuts],
	);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setDeletePersonnel(null);
		setFocusedDeleteButton("cancel");

		// Re-enable table scope when dialog closes
		disableScope("personnel-delete-dialog");
		enableScope("personnel-shortcuts");
	}, [disableScope, enableScope]);

	const handleConfirmDelete = useCallback(async () => {
		if (!deletePersonnel) {
			return;
		}

		try {
			await deletePersonnelMutation(deletePersonnel.id);
			toast.success(t("app.personnel.delete.success"));

			// Invalidate queries to refresh the data
			await queryClient.invalidateQueries({
				queryKey: personnelKeys.all,
			});

			// Close dialog and reset state
			setIsDeleteDialogOpen(false);
			setDeletePersonnel(null);
			setFocusedDeleteButton("cancel");

			// Re-enable table scope when dialog closes
			disableScope("personnel-delete-dialog");
			enableScope("personnel-shortcuts");

			// Notify parent component
			onPersonnelDeleted?.();
		} catch (error) {
			toast.error(t("app.personnel.delete.error"));
		}
	}, [
		deletePersonnel,
		deletePersonnelMutation,
		disableScope,
		enableScope,
		onPersonnelDeleted,
		queryClient,
		t,
	]);

	const handleSetDeleteButtonFocus = useCallback(
		(button: "cancel" | "confirm") => {
			setFocusedDeleteButton(button);
		},
		[],
	);

	const closeAllDialogs = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setDeletePersonnel(null);
		setFocusedDeleteButton("cancel");
	}, []);

	// Add keyboard handlers for arrow keys in delete dialog
	useHotkeys(
		"left",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("cancel");
			cancelRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("personnel-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"right",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("confirm");
			confirmRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("personnel-delete-dialog"),
			preventDefault: true,
		},
	);

	// Add keyboard handlers for enter/esc in delete dialog
	useHotkeys(
		"enter",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				if (focusedDeleteButton === "cancel") {
					handleCancelDelete();
				} else {
					void handleConfirmDelete();
				}
			}
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("personnel-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"esc",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				handleCancelDelete();
			}
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("personnel-delete-dialog"),
			preventDefault: true,
		},
	);

	// Add keyboard handler for Ctrl+D to trigger delete
	useHotkeys(
		"ctrl+d",
		(e) => {
			e.preventDefault();
			const highlightedRow = document.querySelector(
				"[data-row-id].bg-muted",
			);
			if (!isDeleteDialogOpen && highlightedRow) {
				const rowId = highlightedRow.getAttribute("data-row-id");
				const personnel = data?.items?.find(
					(p: PersonnelWithRelations) => p.id === rowId,
				);
				if (personnel) {
					handleDeletePersonnel(personnel);
				}
			}
		},
		{
			enabled: activeScopes.includes("personnel-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	return (
		<PersonnelUIContext.Provider
			value={{
				// State
				deletePersonnel,
				selectedPersonnel,
				isDeleteDialogOpen,
				focusedDeleteButton,
				cancelRef,
				confirmRef,

				// Actions
				handleDeletePersonnel,
				handleCancelDelete,
				handleConfirmDelete,
				handleSetDeleteButtonFocus,
				setSelectedPersonnel,
				closeAllDialogs,

				// Dialog management
				setDeleteDialogOpen: setIsDeleteDialogOpen,
			}}
		>
			{children}
		</PersonnelUIContext.Provider>
	);
}

export function usePersonnelUI() {
	const context = useContext(PersonnelUIContext);
	if (!context) {
		throw new Error(
			"usePersonnelUI must be used within a PersonnelUIProvider",
		);
	}
	return context;
}
