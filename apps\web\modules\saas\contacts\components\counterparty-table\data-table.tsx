"use client";

import { useColumns } from "@saas/contacts/components/counterparty-table/columns";
import { useContactUI } from "@saas/contacts/context/counterparty-ui-context";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { useCounterparties } from "../../hooks/use-counterparty";

// Delete dialog component
function DeleteCounterpartyDialog() {
	const t = useTranslations();

	const {
		deleteContact,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = useContactUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteContact) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>
						{t("app.contacts.delete.title")}
					</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							{t("app.contacts.delete.description")}
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deleteContact.nameLine1}
						{deleteContact.nameLine2 && (
							<span className="block text-sm text-muted-foreground">
								{deleteContact.nameLine2}
							</span>
						)}
					</div>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							{t("common.confirmation.cancel")}
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							{t("common.confirmation.delete")}
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// Table component with the delete dialog
export function ContactsDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
	} = useCounterparties({ includeBlocked: true });
	const columns = useColumns();
	const counterparties = data?.items ?? [];
	const t = useTranslations();

	return (
		<>
			<DataTable
				columns={columns}
				data={counterparties}
				defaultColumnVisibility={{
					id: false,
					createdAt: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder={t("app.contacts.table.searchPlaceholder")}
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="contact-shortcuts"
			/>
			<DeleteCounterpartyDialog />
		</>
	);
}
