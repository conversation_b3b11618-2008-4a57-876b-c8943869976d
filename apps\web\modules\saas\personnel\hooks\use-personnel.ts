import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import {
	useCreatePersonnelMutation,
	useDeletePersonnelMutation,
	usePersonnelByIdQuery,
	usePersonnelQuery,
	useUpdatePersonnelMutation,
} from "../lib/api";

export function usePersonnel() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [departmentId, setDepartmentId] = useState<string | undefined>(
		undefined,
	);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = usePersonnelQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		departmentId,
	});

	const deleteMutation = useDeletePersonnelMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deletePersonnel = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete personnel error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		departmentId,
		setDepartmentId,
		refetch: query.refetch,
		deletePersonnel,
	};
}

export function usePersonnelById(personnelId: string) {
	const { activeOrganization } = useActiveOrganization();

	return usePersonnelByIdQuery(activeOrganization?.id, personnelId);
}

export function usePersonnelMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreatePersonnelMutation(orgId);
	const updateMutation = useUpdatePersonnelMutation(orgId);
	const deleteMutation = useDeletePersonnelMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		const result = await createMutation.mutateAsync(data);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (data: any) => {
		const result = await updateMutation.mutateAsync(data);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createPersonnel: createWithCallback,
		updatePersonnel: updateWithCallback,
		deletePersonnel: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}
