import { OrderSettingsForm } from "@saas/organizations/order-settings/components/order-settings-form";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";

import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import React from "react";

export const metadata: Metadata = {
	title: "Order Settings | Settings",
	description: "Configure order numbering for your organization",
};

export default async function OrderSettingsPage() {
	const t = await getTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium">Order Settings</h3>
				<p className="text-sm text-muted-foreground">
					Configure how order numbers are generated
				</p>
			</div>
			<Separator />

			<div className="grid gap-6">
				<div className="col-span-1">
					<OrderSettingsForm />
				</div>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>About Order Numbering</CardTitle>
					<CardDescription>
						How the order numbering system works
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<p>
						Order numbers can be customized using a combination of a
						prefix, date components, and a sequential counter. This
						allows you to generate order numbers that match your
						business requirements.
					</p>

					<div className="space-y-2">
						<h4 className="font-medium">Available Placeholders:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								<code>{"{YYYY}"}</code> - Current year (e.g.,
								"2023")
							</li>
							<li>
								<code>{"{YY}"}</code> - Current year, 2 digits
								(e.g., "23")
							</li>
							<li>
								<code>{"{MM}"}</code> - Current month, 2 digits
								(e.g., "07")
							</li>
							<li>
								<code>{"{SEQ}"}</code> - Sequential number with
								leading zeros
							</li>
						</ul>
					</div>

					<div className="space-y-2">
						<h4 className="font-medium">Example Formats:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								<code>{"{YYYY}{MM}{SEQ}"}</code> →
								ORD20230700001
							</li>
							<li>
								<code>{"-{YYYY}/{MM}-{SEQ}"}</code> →
								ORD-2023/07-0001
							</li>
							<li>
								<code>{"{YY}{SEQ}"}</code> → ORD230001
							</li>
						</ul>
					</div>

					<div className="space-y-2">
						<h4 className="font-medium">Counter Reset Options:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								<strong>Yearly:</strong> The sequential counter
								resets to 1 at the beginning of each year
							</li>
							<li>
								<strong>Monthly:</strong> The sequential counter
								resets to 1 at the beginning of each month
							</li>
							<li>
								<strong>Never:</strong> The sequential counter
								never resets, increasing continuously
							</li>
						</ul>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
