"use client";
import type { LineItemFormValues } from "@repo/api/src/routes/line-items/types";
import type {
	fetchLineItemById,
	fetchLineItems,
} from "@saas/orders/lib/api-line-items";
import { Button } from "@ui/components/button";
import { CurrencySelect } from "@ui/components/currency-select";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { Textarea } from "@ui/components/textarea";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { Check, Pencil, Plus, Trash, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import React from "react";
import { toast } from "sonner";

// Infer the LineItem type from the API response
type ApiResponse = Awaited<ReturnType<typeof fetchLineItems>>;
type ApiLineItem = ApiResponse["items"][number];
type ApiSingleLineItem = Awaited<ReturnType<typeof fetchLineItemById>>;

// Combine both possible response types and add frontend-specific fields
export interface LineItem extends ApiLineItem {
	// Frontend-only field to store references for percentage-based line items
	_references?: string[];
	// Fields for allocations from tour line items
	isFromAllocation?: boolean;
	originalTourLineItemId?: string;
	allocationInfo?: {
		id: string;
		type: string;
		percentageValue?: number;
		fixedValue?: number;
	};
	// Invoice status fields (only present for revenue line items)
	invoicedAmount?: number;
	creditedAmount?: number;
	remainingAmount?: number;
	isFullyInvoiced?: boolean;
}

export interface LineItemsTableProps {
	items: LineItem[];
	isLoading?: boolean;
	onCreateItem?: (
		item: LineItemFormValues & { _references?: string[] },
	) => Promise<void>;
	onUpdateItem?: (
		item: LineItemFormValues & { id: string; _references?: string[] },
	) => Promise<void>;
	onDeleteItem?: (itemId: string) => Promise<void>;
	disabled?: boolean;
	hideTypeColumn?: boolean;
	hideCurrencyColumn?: boolean;
	hideTypeTotals?: boolean;
	hideVatColumn?: boolean;
	defaultType?: string;
	defaultVat?: number;
}

export function LineItemsTable({
	items = [],
	isLoading = false,
	onCreateItem,
	onUpdateItem,
	onDeleteItem,
	disabled = false,
	hideTypeColumn = false,
	hideCurrencyColumn = false,
	hideTypeTotals = false,
	hideVatColumn = false,

	defaultVat = 0,
}: LineItemsTableProps) {
	const t = useTranslations();
	const [editingId, setEditingId] = useState<string | null>(null);
	const [newItem, setNewItem] = useState<boolean>(false);
	const [editValues, setEditValues] = useState<
		LineItemFormValues & { _references?: string[] }
	>({
		description: "",
		quantity: 0,
		unit: "",
		unitPrice: 0,
		totalPrice: 0,
		currency: "EUR",
		notes: "",
		vatRate: defaultVat,
	});

	// State to track percentage references (frontend only)
	const [percentageReferences, setPercentageReferences] = useState<
		Record<string, string[]>
	>({});
	// State to track if we're in percentage selection mode
	const [selectingReferences, setSelectingReferences] =
		useState<boolean>(false);

	// Format currency
	const formatCurrency = (
		value: number | undefined | null,
		currency: string | undefined | null,
	) => {
		if (value === undefined || value === null) {
			return "-";
		}

		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency || "EUR",
		}).format(value);
	};

	// Check if a line item can be edited/deleted (not invoiced or credited)
	const canEditOrDelete = (item: LineItem) => {
		// Don't allow editing/deleting of allocated items
		if (item.isFromAllocation) {
			return false;
		}

		// For revenue line items, check if they have been invoiced or credited
		return (
			(item.invoicedAmount || 0) === 0 && (item.creditedAmount || 0) === 0
		);
	};

	// Get invoice status badge for revenue line items
	const getInvoiceStatusBadge = (item: LineItem) => {
		if (typeof item.invoicedAmount === "undefined") {
			return <span className="text-xs text-muted-foreground">-</span>;
		}

		const invoicedAmount = item.invoicedAmount || 0;
		const creditedAmount = item.creditedAmount || 0;
		const remainingAmount = item.remainingAmount || 0;

		if (item.isFullyInvoiced) {
			return (
				<div className="text-center">
					<span className="text-xs rounded-full px-2 py-1 bg-green-100 text-green-800">
						Full
					</span>
				</div>
			);
		}

		if (invoicedAmount > 0 || creditedAmount > 0) {
			const tooltipContent = (
				<div className="text-xs">
					{invoicedAmount > 0 && (
						<div>
							Invoiced:{" "}
							{formatCurrency(invoicedAmount, item.currency)}
						</div>
					)}
					{creditedAmount > 0 && (
						<div>
							Credited:{" "}
							{formatCurrency(creditedAmount, item.currency)}
						</div>
					)}
					{remainingAmount > 0 && (
						<div>
							Remaining:{" "}
							{formatCurrency(remainingAmount, item.currency)}
						</div>
					)}
				</div>
			);

			return (
				<div className="text-center">
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<span className="text-xs rounded-full px-2 py-1 bg-yellow-100 text-yellow-800 cursor-help">
									Partial
								</span>
							</TooltipTrigger>
							<TooltipContent>{tooltipContent}</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			);
		}

		return (
			<div className="text-center">
				<span className="text-xs rounded-full px-2 py-1 bg-gray-100 text-gray-600">
					None
				</span>
			</div>
		);
	};

	const handleAddLineItem = () => {
		setNewItem(true);
		setEditValues({
			description: "",
			quantity: 0,
			unit: "",
			unitPrice: 0,
			totalPrice: 0,
			currency: "EUR",
			notes: "",
			vatRate: defaultVat,
		});
	};

	const handleEditLineItem = (item: LineItem) => {
		setEditingId(item.id || null);
		setEditValues({
			description: item.description || "",
			quantity: item.quantity ?? 0,
			unit: item.unit || "",
			unitPrice: item.unitPrice ?? 0,
			totalPrice: item.totalPrice || 0,
			currency: item.currency || "EUR",
			notes: item.notes || "",
			vatRate: item.vatRate ?? defaultVat,
			_references: item._references || [],
		});
	};

	const handleCancelEdit = () => {
		setEditingId(null);
		setNewItem(false);
		setSelectingReferences(false);
	};

	const handleSaveEdit = async (e?: React.MouseEvent) => {
		// Prevent any event bubbling that might trigger form submission
		if (e) {
			e.preventDefault();
			e.stopPropagation();
		}

		// Reset the reference selection mode
		setSelectingReferences(false);

		// If it's a percentage item, make sure total price is calculated
		if (editValues.unit === "%") {
			const currentId = editingId || "new-item";
			const referenceIds = percentageReferences[currentId] || [];

			if (referenceIds.length === 0) {
				toast.error(
					"Please select at least one reference item for percentage calculation",
				);
				return;
			}

			const referenceItems = items.filter((item) =>
				referenceIds.includes(item.id || ""),
			);
			const totalValue =
				referenceItems.reduce(
					(sum, item) => sum + (item.totalPrice || 0),
					0,
				) *
				((editValues.quantity || 0) / 100);

			// Update total price and store references before saving
			const updatedValues = {
				...editValues,
				totalPrice: totalValue,
				// Store references in frontend-only field
				_references: referenceIds,
			};

			// Continue with save using updated values
			try {
				if (editingId && onUpdateItem) {
					await onUpdateItem({
						...updatedValues,
						id: editingId,
					});
					toast.success("Item updated successfully");

					// Note: If parent component allows, updated item with references
					// should be reflected in the items array
				} else if (newItem && onCreateItem) {
					await onCreateItem({
						...updatedValues,
						position: items?.length ? items.length + 1 : 1,
					});
					toast.success("Item created successfully");
				}
				setEditingId(null);
				setNewItem(false);
			} catch (error) {
				toast.error(
					editingId
						? "Failed to update item"
						: "Failed to create item",
				);
				console.error("Line item form error:", error);
			}
			return;
		}

		try {
			if (editingId && onUpdateItem) {
				await onUpdateItem({
					...editValues,
					id: editingId,
				});
				toast.success("Item updated successfully");
			} else if (newItem && onCreateItem) {
				await onCreateItem({
					...editValues,
					position: items?.length ? items.length + 1 : 1,
				});
				toast.success("Item created successfully");
			}
			setEditingId(null);
			setNewItem(false);
		} catch (error) {
			toast.error(
				editingId ? "Failed to update item" : "Failed to create item",
			);
			console.error("Line item form error:", error);
		}
	};

	const handleChange = (field: string, value: any) => {
		setEditValues((prev) => {
			const newValues = { ...prev, [field]: value };

			// Auto-calculate total price when both quantity and unitPrice are set
			if (field === "quantity" || field === "unitPrice") {
				const quantity = field === "quantity" ? value : prev.quantity;
				const unitPrice =
					field === "unitPrice" ? value : prev.unitPrice;

				if (quantity && unitPrice) {
					newValues.totalPrice = quantity * unitPrice;
				}
			}

			// Handle unit change to percentage
			if (field === "unit") {
				if (value === "%") {
					setSelectingReferences(true);

					// Clear any existing references when switching to % unit
					const currentId = editingId || "new-item";
					setPercentageReferences((prev) => ({
						...prev,
						[currentId]: [],
					}));
				} else if (prev.unit === "%") {
					// If changing from % to something else, clear reference tracking
					setSelectingReferences(false);
					newValues._references = undefined;
				}
			}

			return newValues;
		});
	};

	// Function to handle reference item selection for percentage calculations
	const handleReferenceChange = (referenceIds: string[]) => {
		if (!editingId && !newItem) {
			return;
		}

		const currentId = editingId || "new-item";
		setPercentageReferences((prev) => ({
			...prev,
			[currentId]: referenceIds,
		}));

		// Calculate total price based on references if unit is %
		if (editValues.unit === "%") {
			const referenceItems = items.filter((item) =>
				referenceIds.includes(item.id || ""),
			);
			const totalValue =
				referenceItems.reduce(
					(sum, item) => sum + (item.totalPrice || 0),
					0,
				) *
				((editValues.quantity || 0) / 100);

			setEditValues((prev) => ({
				...prev,
				totalPrice: totalValue,
			}));
		}
	};

	// Helper to determine if total price should be disabled
	const shouldDisableTotalPrice = (
		values: LineItemFormValues & { _references?: string[] },
	) => {
		// Disable manual entry if unit is % or quantity and unitPrice are set
		return values.unit === "%" || !!(values.quantity && values.unitPrice);
	};

	const handleDeleteLineItem = async (id: string) => {
		if (onDeleteItem) {
			try {
				await onDeleteItem(id);
				toast.success("Item deleted successfully");
			} catch (error) {
				toast.error("Failed to delete item");
				console.error("Delete line item error:", error);
			}
		}
	};

	// Calculate grand total across all line items
	const calculateGrandTotal = () => {
		if (!items || items.length === 0) {
			return { total: 0, currency: "EUR" };
		}

		const total = items.reduce(
			(sum, item) => sum + (item.totalPrice || 0),
			0,
		);
		// Use currency from the first item or default to EUR
		const currency = items[0]?.currency || "EUR";

		return { total, currency };
	};

	// Component for selecting reference items
	const ReferenceSelector = () => {
		const currentId = editingId || "new-item";
		// Get references from state
		const selectedRefs = percentageReferences[currentId] || [];

		// If we're editing an existing item with _references AND we don't have references in state yet,
		// initialize from the item's _references ONE TIME
		React.useEffect(() => {
			if (editingId && !percentageReferences[currentId]) {
				const editingItem = items.find((item) => item.id === editingId);
				if (
					editingItem?._references &&
					editingItem._references.length > 0
				) {
					setPercentageReferences((prev) => ({
						...prev,
						[currentId]: editingItem._references || [],
					}));
				}
			}
		}, [editingId, currentId]);

		const eligibleItems = items.filter(
			(item) =>
				// Don't include self or already selected items
				item.id !== editingId && item.unit !== "%", // Don't allow percentage items to reference other percentage items
		);

		return (
			<div className="mt-4 p-4 border rounded-md bg-muted/20">
				<div className="font-medium mb-2">
					Select items to calculate percentage from:
				</div>

				{/* Display selected references */}
				{selectedRefs.length > 0 && (
					<div className="flex flex-wrap gap-2 mb-4">
						{selectedRefs.map((id) => {
							const refItem = items.find(
								(item) => item.id === id,
							);
							return (
								<div
									key={id}
									className="flex items-center bg-muted rounded-md px-2 py-1 text-sm"
								>
									<span className="mr-1">
										{refItem?.description || id}
									</span>
									<Button
										variant="ghost"
										size="icon"
										className="h-5 w-5"
										onClick={() => {
											const newRefs = selectedRefs.filter(
												(refId) => refId !== id,
											);
											handleReferenceChange(newRefs);
										}}
									>
										<X className="h-3 w-3" />
									</Button>
								</div>
							);
						})}
					</div>
				)}

				{/* Dropdown to add more references */}
				{eligibleItems.length > 0 ? (
					<Select
						onValueChange={(value) => {
							if (!selectedRefs.includes(value)) {
								handleReferenceChange([...selectedRefs, value]);
							}
						}}
					>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="Add reference item" />
						</SelectTrigger>
						<SelectContent>
							{eligibleItems
								.filter(
									(item) =>
										!selectedRefs.includes(item.id || ""),
								)
								.map((item) => (
									<SelectItem
										key={item.id}
										value={item.id || ""}
									>
										{item.description} (
										{formatCurrency(
											item.totalPrice,
											item.currency,
										)}
										)
									</SelectItem>
								))}
						</SelectContent>
					</Select>
				) : (
					<div className="text-muted-foreground text-sm">
						No eligible items to reference
					</div>
				)}
			</div>
		);
	};

	return (
		<div>
			<div className="flex justify-end mb-4">
				<Button
					onClick={handleAddLineItem}
					size="sm"
					disabled={disabled || newItem || !!editingId}
				>
					<Plus className="mr-2 h-4 w-4" />
					Add Item
				</Button>
			</div>
			{isLoading ? (
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[40%]">
								Description
							</TableHead>
							{/* Removed Type column - all line items are now revenue-only */}
							<TableHead className="w-[10%] text-right">
								Quantity
							</TableHead>
							<TableHead className="w-[10%] text-right">
								Unit
							</TableHead>
							<TableHead className="w-[15%] text-right">
								Unit Price
							</TableHead>
							<TableHead
								className={
									hideCurrencyColumn
										? "w-[15%] text-right"
										: "w-[20%] text-right"
								}
							>
								Total Price
							</TableHead>
							<TableHead className="w-[15%] text-center">
								Invoice Status
							</TableHead>
							<TableHead className="w-[10%]">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{Array.from({ length: 3 }).map((_, index) => (
							<TableRow key={index}>
								<TableCell>
									<Skeleton
										className={
											hideTypeColumn
												? "h-5 w-[250px]"
												: "h-5 w-[200px]"
										}
									/>
								</TableCell>
								{!hideTypeColumn && (
									<TableCell className="text-right">
										<Skeleton className="h-5 w-16 ml-auto" />
									</TableCell>
								)}
								<TableCell className="text-right">
									<Skeleton className="h-5 w-12 ml-auto" />
								</TableCell>
								<TableCell className="text-right">
									<Skeleton className="h-5 w-12 ml-auto" />
								</TableCell>
								<TableCell className="text-right">
									<Skeleton className="h-5 w-24 ml-auto" />
								</TableCell>
								<TableCell className="text-right">
									<Skeleton className="h-5 w-28 ml-auto" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-8 w-8" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-8 w-8" />
								</TableCell>
								<TableCell>
									<Skeleton className="h-8 w-8" />
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</Table>
			) : !items || (items.length === 0 && !newItem) ? (
				<div className="text-center py-6 text-muted-foreground">
					No items added yet
				</div>
			) : (
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead className="w-[35%]">
								Description
							</TableHead>
							{/* Removed Type column - all line items are now revenue-only */}
							<TableHead className="w-[10%] text-right">
								Quantity
							</TableHead>
							<TableHead className="w-[10%] text-right">
								Unit
							</TableHead>
							<TableHead className="w-[10%] text-right">
								Unit Price
							</TableHead>
							{!hideVatColumn && (
								<TableHead className="w-[10%] text-right">
									VAT %
								</TableHead>
							)}
							<TableHead
								className={
									hideCurrencyColumn
										? "w-[15%] text-right"
										: "w-[15%] text-right"
								}
							>
								Total Price
							</TableHead>
							<TableHead className="w-[15%] text-center">
								Invoice Status
							</TableHead>
							<TableHead className="w-[10%]">Actions</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{newItem && (
							<>
								<TableRow key="new-item-form">
									<TableCell>
										<Input
											value={editValues.description ?? ""}
											onChange={(e) =>
												handleChange(
													"description",
													e.target.value,
												)
											}
											className="w-full"
											placeholder="Description"
										/>
									</TableCell>
									{/* Removed Type column - all line items are now revenue-only */}
									<TableCell className="text-right">
										<Input
											type="number"
											value={editValues.quantity ?? 0}
											onChange={(e) =>
												handleChange(
													"quantity",
													Number.parseFloat(
														e.target.value,
													) || 0,
												)
											}
											className="w-full text-right"
										/>
									</TableCell>
									<TableCell className="text-right">
										<Input
											value={editValues.unit ?? ""}
											onChange={(e) =>
												handleChange(
													"unit",
													e.target.value,
												)
											}
											className="w-full text-right"
										/>
									</TableCell>
									<TableCell className="text-right">
										<Input
											type="number"
											value={editValues.unitPrice ?? 0}
											onChange={(e) =>
												handleChange(
													"unitPrice",
													Number.parseFloat(
														e.target.value,
													) || 0,
												)
											}
											className="w-full text-right"
										/>
									</TableCell>
									{!hideVatColumn && (
										<TableCell className="text-right">
											<Input
												type="number"
												value={
													editValues.vatRate ??
													defaultVat
												}
												onChange={(e) =>
													handleChange(
														"vatRate",
														Number.parseFloat(
															e.target.value,
														) || 0,
													)
												}
												className="w-full text-right"
											/>
										</TableCell>
									)}
									<TableCell className="text-right">
										<div className="flex items-center gap-2">
											{!hideCurrencyColumn && (
												<div className="w-1/3">
													<CurrencySelect
														name="currency"
														value={
															editValues.currency
														}
														onValueChange={(
															value,
														) =>
															handleChange(
																"currency",
																value,
															)
														}
														variant="small"
													/>
												</div>
											)}
											<div
												className={
													hideCurrencyColumn
														? "w-full"
														: "w-2/3"
												}
											>
												<Input
													type="number"
													value={
														editValues.totalPrice ??
														0
													}
													onChange={(e) =>
														handleChange(
															"totalPrice",
															Number.parseFloat(
																e.target.value,
															) || 0,
														)
													}
													className="w-full text-right"
													disabled={shouldDisableTotalPrice(
														editValues,
													)}
												/>
											</div>
										</div>
									</TableCell>
									<TableCell className="text-center">
										{/* Hide invoice status for new items */}
									</TableCell>
									<TableCell>
										<div className="flex space-x-1">
											<Button
												variant="ghost"
												size="icon"
												type="button"
												onClick={(e) =>
													handleSaveEdit(e)
												}
											>
												<Check className="h-4 w-4" />
												<span className="sr-only">
													Save
												</span>
											</Button>
											<Button
												variant="ghost"
												size="icon"
												onClick={handleCancelEdit}
											>
												<X className="h-4 w-4" />
												<span className="sr-only">
													Cancel
												</span>
											</Button>
										</div>
									</TableCell>
								</TableRow>
								<TableRow
									key="new-item-notes"
									className="border-0"
								>
									<TableCell
										colSpan={7 - (hideVatColumn ? 1 : 0)}
										className="pt-0 pb-4"
									>
										<Textarea
											value={editValues.notes || ""}
											onChange={(e) =>
												handleChange(
													"notes",
													e.target.value,
												)
											}
											placeholder="Notes (optional)"
											className="text-sm w-full h-20"
										/>

										{/* Only show reference selector for new items here */}
										{editValues.unit === "%" && (
											<ReferenceSelector />
										)}
									</TableCell>
								</TableRow>
							</>
						)}
						{items.map((item) => (
							<React.Fragment
								key={item.id || `item-${item.position}`}
							>
								<TableRow
									className={
										item.isFromAllocation
											? "bg-muted/30"
											: ""
									}
								>
									{editingId === item.id ? (
										<>
											<TableCell>
												<Input
													value={
														editValues.description ??
														""
													}
													onChange={(e) =>
														handleChange(
															"description",
															e.target.value,
														)
													}
													className="w-full"
												/>
											</TableCell>
											{/* Removed Type column - all line items are now revenue-only */}
											<TableCell className="text-right">
												<Input
													type="number"
													value={
														editValues.quantity ?? 0
													}
													onChange={(e) =>
														handleChange(
															"quantity",
															Number.parseFloat(
																e.target.value,
															) || 0,
														)
													}
													className="w-full text-right"
												/>
											</TableCell>
											<TableCell className="text-right">
												<Input
													value={
														editValues.unit ?? ""
													}
													onChange={(e) =>
														handleChange(
															"unit",
															e.target.value,
														)
													}
													className="w-full text-right"
												/>
											</TableCell>
											<TableCell className="text-right">
												<Input
													type="number"
													value={
														editValues.unitPrice ??
														0
													}
													onChange={(e) =>
														handleChange(
															"unitPrice",
															Number.parseFloat(
																e.target.value,
															) || 0,
														)
													}
													className="w-full text-right"
												/>
											</TableCell>
											{!hideVatColumn && (
												<TableCell className="text-right">
													<Input
														type="number"
														value={
															editValues.vatRate ??
															defaultVat
														}
														onChange={(e) =>
															handleChange(
																"vatRate",
																Number.parseFloat(
																	e.target
																		.value,
																) || 0,
															)
														}
														className="w-full text-right"
													/>
												</TableCell>
											)}
											<TableCell className="text-right">
												<div className="flex items-center gap-2">
													{!hideCurrencyColumn && (
														<div className="w-1/3">
															<CurrencySelect
																name="currency"
																value={
																	editValues.currency
																}
																onValueChange={(
																	value,
																) =>
																	handleChange(
																		"currency",
																		value,
																	)
																}
																variant="small"
															/>
														</div>
													)}
													<div
														className={
															hideCurrencyColumn
																? "w-full"
																: "w-2/3"
														}
													>
														<Input
															type="number"
															value={
																editValues.totalPrice ??
																0
															}
															onChange={(e) =>
																handleChange(
																	"totalPrice",
																	Number.parseFloat(
																		e.target
																			.value,
																	) || 0,
																)
															}
															className="w-full text-right"
															disabled={shouldDisableTotalPrice(
																editValues,
															)}
														/>
													</div>
												</div>
											</TableCell>
											<TableCell>
												{getInvoiceStatusBadge(item)}
											</TableCell>
											<TableCell>
												<div className="flex space-x-1">
													<Button
														variant="ghost"
														size="icon"
														type="button"
														onClick={(e) =>
															handleSaveEdit(e)
														}
													>
														<Check className="h-4 w-4" />
														<span className="sr-only">
															Save
														</span>
													</Button>
													<Button
														variant="ghost"
														size="icon"
														onClick={
															handleCancelEdit
														}
													>
														<X className="h-4 w-4" />
														<span className="sr-only">
															Cancel
														</span>
													</Button>
												</div>
											</TableCell>
										</>
									) : (
										<>
											<TableCell className="font-medium">
												{item.description || "-"}
												{item.isFromAllocation && (
													<span className="ml-2 text-xs rounded-full px-2 py-1 bg-muted-foreground/10">
														Allocated
													</span>
												)}
											</TableCell>
											{/* Removed Type column - all line items are now revenue-only */}
											<TableCell className="text-right">
												{item.quantity || ""}
											</TableCell>
											<TableCell className="text-right">
												{item.unit || ""}
											</TableCell>
											<TableCell className="text-right">
												{item.unitPrice
													? formatCurrency(
															item.unitPrice,
															item.currency,
														)
													: ""}
											</TableCell>
											{!hideVatColumn && (
												<TableCell className="text-right">
													{typeof item.vatRate !==
													"undefined"
														? `${item.vatRate}%`
														: defaultVat
															? `${defaultVat}%`
															: "0%"}
												</TableCell>
											)}
											<TableCell className="text-right">
												{item.totalPrice
													? formatCurrency(
															item.totalPrice,
															item.currency,
														)
													: ""}
											</TableCell>
											<TableCell>
												{getInvoiceStatusBadge(item)}
											</TableCell>
											<TableCell>
												<div className="flex space-x-1">
													<Button
														size="icon"
														variant="ghost"
														onClick={() =>
															handleEditLineItem(
																item,
															)
														}
														disabled={
															!!editingId ||
															newItem ||
															disabled ||
															!canEditOrDelete(
																item,
															)
														}
													>
														<Pencil className="h-4 w-4" />
														<span className="sr-only">
															Edit
														</span>
													</Button>
													<Button
														size="icon"
														variant="ghost"
														onClick={() =>
															item.id &&
															handleDeleteLineItem(
																item.id,
															)
														}
														disabled={
															!!editingId ||
															newItem ||
															disabled ||
															!item.id ||
															!canEditOrDelete(
																item,
															)
														}
													>
														<Trash className="h-4 w-4" />
														<span className="sr-only">
															Delete
														</span>
													</Button>
												</div>
											</TableCell>
										</>
									)}
								</TableRow>
								{/* Notes row for existing items */}
								{(editingId === item.id || item.notes) && (
									<TableRow
										key={`${item.id || `item-${item.position}`}-notes`}
										className="border-0"
									>
										<TableCell
											colSpan={
												7 - (hideVatColumn ? 1 : 0)
											}
											className={
												editingId === item.id
													? "pt-2 pb-4"
													: "pt-2 pb-2 text-xs text-muted-foreground"
											}
										>
											{editingId === item.id ? (
												<>
													<Textarea
														value={
															editValues.notes ||
															""
														}
														onChange={(e) =>
															handleChange(
																"notes",
																e.target.value,
															)
														}
														placeholder="Notes (optional)"
														className="text-sm w-full h-20"
													/>

													{/* Only show reference selector for edited items here */}
													{editValues.unit ===
														"%" && (
														<ReferenceSelector />
													)}
												</>
											) : (
												<>
													<span className="font-medium">
														Notes:{" "}
													</span>
													{item.notes}
												</>
											)}
										</TableCell>
									</TableRow>
								)}
							</React.Fragment>
						))}
						{/* Grand total */}
						{!hideTypeTotals && items.length > 1 && (
							<TableRow className="font-bold text-lg">
								<TableCell
									colSpan={5 - (hideVatColumn ? 1 : 0)}
									className="text-right"
								>
									Grand Total
								</TableCell>
								<TableCell className="text-right">
									{formatCurrency(
										calculateGrandTotal().total,
										calculateGrandTotal().currency,
									)}
								</TableCell>
								<TableCell />
								<TableCell />
							</TableRow>
						)}
					</TableBody>
				</Table>
			)}
		</div>
	);
}
