"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import {
	Bar,
	BarChart,
	CartesianGrid,
	Cell,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";
import { chartStyles, getDepartmentColor } from "../utils/chart-colors";
import { semanticColors } from "../utils/chart-colors";
import {
	animationSettings,
	formatNumber,
	formatPercentage,
} from "../utils/chart-helpers";
import type { DepartmentData } from "../utils/statistics-types";

interface DepartmentChartProps {
	data: DepartmentData[];
	isLoading?: boolean;
	title?: string;
	height?: number;
}

/**
 * A horizontal bar chart showing absence by department
 */
export function DepartmentChart({
	data,
	isLoading = false,
	title = "Absence by Department",
	height = 300,
}: DepartmentChartProps) {
	if (isLoading) {
		return <DepartmentChartSkeleton height={height} />;
	}

	// Sort data by absenceDays in descending order
	const sortedData = [...data].sort((a, b) => b.absenceDays - a.absenceDays);

	// Calculate average absence rate for reference line
	const avgAbsenceRate =
		sortedData.reduce((sum, dept) => sum + dept.absenceRate, 0) /
		sortedData.length;

	// Format the data for the chart with semantic colors
	const chartData = sortedData.map((dept) => ({
		departmentId: dept.departmentId,
		departmentName: dept.departmentName,
		absenceDays: dept.absenceDays,
		absenceRate: dept.absenceRate,
		employeeCount: dept.employeeCount,
		// Color based on absence rate - higher is more concerning
		color: getDepartmentColor(dept.absenceRate),
	}));

	// Custom tooltip to show count and percentage
	const CustomTooltip = ({ active, payload }: any) => {
		if (active && payload && payload.length) {
			const data = payload[0].payload;
			return (
				<div
					style={chartStyles.tooltipStyles}
					className="text-card-foreground"
				>
					<div className="font-medium">{data.departmentName}</div>
					<div className="flex justify-between gap-4 text-xs pt-1">
						<span>Absence Days:</span>
						<span className="font-medium">
							{formatNumber(data.absenceDays)}
						</span>
					</div>
					<div className="flex justify-between gap-4 text-xs">
						<span>Absence Rate:</span>
						<span className="font-medium">
							{formatPercentage(data.absenceRate)}
						</span>
					</div>
					<div className="flex justify-between gap-4 text-xs">
						<span>Employees:</span>
						<span className="font-medium">
							{formatNumber(data.employeeCount)}
						</span>
					</div>
				</div>
			);
		}
		return null;
	};

	return (
		<Card className="w-full">
			<CardHeader className="pb-2">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
			</CardHeader>
			<CardContent>
				<div style={{ height: `${height}px` }} className="w-full">
					<ResponsiveContainer width="100%" height="100%">
						<BarChart
							data={chartData}
							layout="vertical"
							margin={chartStyles.barChartMargins}
						>
							<CartesianGrid
								strokeDasharray={
									chartStyles.gridStrokeDasharray
								}
								stroke={chartStyles.gridColor}
								horizontal={true}
								vertical={true}
							/>
							<XAxis
								type="number"
								tick={{ fontSize: chartStyles.axisFontSize }}
								axisLine={false}
								tickLine={false}
							/>
							<YAxis
								dataKey="departmentName"
								type="category"
								tick={{ fontSize: chartStyles.axisFontSize }}
								width={75}
								axisLine={true}
								tickLine={false}
							/>
							<Tooltip content={<CustomTooltip />} />
							<Bar
								name="Absence Days"
								dataKey="absenceDays"
								radius={chartStyles.barRadius}
								minPointSize={3}
								animationDuration={
									animationSettings.duration.bar
								}
								animationEasing="ease-out"
							>
								{chartData.map((entry, index) => (
									<Cell
										key={`cell-${index}`}
										fill={entry.color}
									/>
								))}
							</Bar>
						</BarChart>
					</ResponsiveContainer>
				</div>

				{/* Color legend */}
				<div className="flex items-center justify-center mt-4 text-xs space-x-4">
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.positive }}
						/>
						<span>Low (&lt;5%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.neutral }}
						/>
						<span>Medium (5-8%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.caution }}
						/>
						<span>High (8-12%)</span>
					</div>
					<div className="flex items-center">
						<div
							className="w-3 h-3 rounded-sm mr-1"
							style={{ backgroundColor: semanticColors.negative }}
						/>
						<span>Critical (≥12%)</span>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

/**
 * Skeleton loader for the department chart
 */
function DepartmentChartSkeleton({ height }: { height: number }) {
	return (
		<Card className="w-full">
			<CardHeader className="pb-2">
				<Skeleton className="h-4 w-32" />
			</CardHeader>
			<CardContent>
				<div style={{ height: `${height}px` }} className="w-full">
					<Skeleton className="h-full w-full" />
				</div>
			</CardContent>
		</Card>
	);
}
