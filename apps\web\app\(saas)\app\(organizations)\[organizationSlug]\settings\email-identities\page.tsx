import { SmtpConfigurationManager } from "@saas/organizations/smtp-configuration/components/smtp-configuration-manager";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";

import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import React from "react";

export const metadata: Metadata = {
	title: "Email Configuration | Settings",
	description: "Configure email settings for your organization",
};

export default async function EmailConfigurationPage() {
	const t = await getTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium">Email Configuration</h3>
				<p className="text-sm text-muted-foreground">
					Configure SMTP settings for sending emails from your
					organization
				</p>
			</div>
			<Separator />

			<div className="grid gap-6">
				<div className="col-span-1">
					<SmtpConfigurationManager />
				</div>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>About SMTP Configuration</CardTitle>
					<CardDescription>
						How SMTP email sending works
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<p>
						SMTP configuration allows you to send emails from your
						organization using your own email server or a
						third-party email service provider. This gives you
						complete control over your email delivery.
					</p>

					<div className="space-y-2">
						<h4 className="font-medium">Setup Process:</h4>
						<ol className="list-decimal pl-5 space-y-1">
							<li>
								Enter your SMTP server details in the form above
							</li>
							<li>Test the connection to verify your settings</li>
							<li>Save the configuration once the test passes</li>
							<li>
								Activate the configuration to start sending
								emails
							</li>
						</ol>
					</div>

					<div className="space-y-2">
						<h4 className="font-medium">Popular SMTP Providers:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								Gmail: smtp.gmail.com (port 587, use app
								password)
							</li>
							<li>Outlook: smtp-mail.outlook.com (port 587)</li>
							<li>SendGrid: smtp.sendgrid.net (port 587)</li>
							<li>Mailgun: smtp.mailgun.org (port 587)</li>
						</ul>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
