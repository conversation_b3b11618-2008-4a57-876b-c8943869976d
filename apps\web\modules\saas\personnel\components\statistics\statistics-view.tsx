"use client";

import { useDepartments } from "@saas/organizations/hooks/use-config";
import { Skeleton } from "@ui/components/skeleton";
import { usePersonnelStatistics } from "../../hooks/use-personnel-statistics";
import { AbsenceTrendChart } from "./charts/absence-trend-chart";
import { AbsenceTypeChart } from "./charts/absence-type-chart";
import { DepartmentChart } from "./charts/department-chart";
import { TopPersonnelChart } from "./charts/top-personnel-chart";
import { WeekdayDistributionChart } from "./charts/weekday-distribution-chart";
import { StatisticsFilters } from "./filters/statistics-filters";
import { KpiRow } from "./kpi/kpi-row";

/**
 * Main component for the statistics tab
 */
export function StatisticsView() {
	// Get statistics data from the hook
	const {
		data,
		filters,
		isLoading: statsLoading,
		isError,
		error,
		setDateRange,
		setDepartmentIds,
		setAbsenceTypes,
		resetFilters,
	} = usePersonnelStatistics();

	// Get departments data
	const { data: departmentsData, isLoading: deptsLoading } = useDepartments();

	// Create a formatted list of departments with id and name
	const formattedDepartments =
		departmentsData?.items?.map((dept) => ({
			id: dept.id,
			name: dept.name,
		})) || [];

	// Show loading state
	const isLoading = statsLoading || deptsLoading;
	if (isLoading) {
		return <StatisticsViewSkeleton />;
	}

	// Show error state
	if (isError) {
		return (
			<div className="p-8 text-center">
				<h3 className="text-lg font-medium mb-2">
					Error loading statistics
				</h3>
				<p className="text-muted-foreground mb-4">
					{error instanceof Error
						? error.message
						: "Something went wrong. Please try again."}
				</p>
			</div>
		);
	}

	// Show empty state if no data available yet
	if (!data) {
		return (
			<div className="p-8 text-center">
				<h3 className="text-lg font-medium mb-2">
					No statistics data available
				</h3>
				<p className="text-muted-foreground mb-4">
					Please check your filters or try again later.
				</p>
				<StatisticsFilters
					filters={filters}
					departments={formattedDepartments}
					onDateRangeChange={setDateRange}
					onDepartmentIdsChange={setDepartmentIds}
					onAbsenceTypesChange={setAbsenceTypes}
					onReset={resetFilters}
				/>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Filter section */}
			<StatisticsFilters
				filters={filters}
				departments={formattedDepartments}
				onDateRangeChange={setDateRange}
				onDepartmentIdsChange={setDepartmentIds}
				onAbsenceTypesChange={setAbsenceTypes}
				onReset={resetFilters}
			/>

			{/* KPI cards row */}
			<KpiRow kpiData={data.kpi} />

			{/* Charts grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Absence Trend Chart */}
				<AbsenceTrendChart
					data={data.absenceTrend}
					title="Absence Trend"
					height={300}
				/>

				{/* Absence Type Breakdown Chart */}
				<AbsenceTypeChart
					data={data.absenceByType}
					title="Absence by Type"
					height={300}
				/>

				{/* Department Comparison Chart */}
				<DepartmentChart
					data={data.absenceByDepartment}
					title="Absence by Department"
					height={300}
				/>

				{/* Weekday Distribution Chart */}
				<WeekdayDistributionChart
					data={data.absenceByWeekday}
					title="Absence by Day of Week"
					height={300}
				/>

				{/* Top Personnel with Absences Chart */}
				<TopPersonnelChart
					data={data.topPersonnelAbsences}
					title="Top 5 Personnel Absences"
					height={300}
				/>

				{/* Bradford Factor Chart Placeholder (if time permits) */}
				{data.bradfordFactor && (
					<div className="p-4 border rounded-lg bg-card md:col-span-2">
						<h3 className="text-sm font-medium mb-4">
							Bradford Factor
						</h3>
						<div className="h-64 flex items-center justify-center bg-muted/30 rounded">
							<p className="text-muted-foreground text-sm">
								Bradford Factor Visualization Coming Soon
							</p>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}

/**
 * Skeleton loading state for the statistics view
 */
function StatisticsViewSkeleton() {
	return (
		<div className="space-y-6">
			{/* Filters skeleton */}
			<Skeleton className="h-20 w-full" />

			{/* KPI cards skeleton */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
				{Array.from({ length: 4 }).map((_, i) => (
					<div key={i} className="p-4 border rounded-lg">
						<Skeleton className="h-4 w-24 mb-2" />
						<Skeleton className="h-8 w-16 mb-2" />
						<Skeleton className="h-3 w-32" />
					</div>
				))}
			</div>

			{/* Charts skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{Array.from({ length: 4 }).map((_, i) => (
					<div key={i} className="p-4 border rounded-lg">
						<Skeleton className="h-4 w-32 mb-4" />
						<Skeleton className="h-64 w-full" />
					</div>
				))}
			</div>

			{/* Bradford factor skeleton */}
			<div className="p-4 border rounded-lg">
				<Skeleton className="h-4 w-40 mb-4" />
				<Skeleton className="h-48 w-full" />
			</div>
		</div>
	);
}
