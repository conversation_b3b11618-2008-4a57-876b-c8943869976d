"use client";
import { ExpenseCreateDialog } from "@saas/expenses/components/expense-create-dialog";
import { ExpensesDataTable } from "@saas/expenses/components/expenses-table/data-table";
import {
	ExpensesUIProvider,
	useExpensesUI,
} from "@saas/expenses/context/expenses-ui-context";
import { useExpenses } from "@saas/expenses/hooks/use-expenses";
import { useExpenseMutations } from "@saas/expenses/hooks/use-expenses";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { AlertCircle, Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";

// Delete confirmation dialog component
function DeleteExpenseDialog() {
	const t = useTranslations();
	const {
		deleteExpense,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = useExpensesUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteExpense) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Delete Expense</AlertDialogTitle>
					<AlertDialogDescription>
						Are you sure you want to delete this expense?
					</AlertDialogDescription>
				</AlertDialogHeader>

				<div className="flex items-start gap-3 py-3">
					<AlertCircle className="h-5 w-5 text-destructive flex-shrink-0 mt-0.5" />
					<div className="space-y-1">
						<p className="text-sm font-medium">
							{deleteExpense.description}
						</p>
						<p className="text-sm text-muted-foreground">
							Amount:{" "}
							{new Intl.NumberFormat("de-DE", {
								style: "currency",
								currency: deleteExpense.currency || "EUR",
							}).format(deleteExpense.totalAmount)}
						</p>
						<p className="text-xs text-muted-foreground">
							This action cannot be undone. All allocations and
							related data will be permanently removed.
						</p>
					</div>
				</div>

				<AlertDialogFooter>
					<AlertDialogCancel
						ref={cancelRef}
						onClick={() => {
							handleSetDeleteButtonFocus("cancel");
							handleCancelDelete();
						}}
						onKeyDown={(e) => {
							if (e.key === "ArrowRight") {
								e.preventDefault();
								handleSetDeleteButtonFocus("confirm");
							}
						}}
					>
						Cancel
					</AlertDialogCancel>
					<AlertDialogAction
						ref={confirmRef}
						onClick={() => {
							handleSetDeleteButtonFocus("confirm");
							handleConfirmDelete();
						}}
						onKeyDown={(e) => {
							if (e.key === "ArrowLeft") {
								e.preventDefault();
								handleSetDeleteButtonFocus("cancel");
							}
						}}
						className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
					>
						Delete Expense
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

function ExpensePageContent() {
	const {
		isCreateDialogOpen,
		setCreateDialogOpen,
		isEditDialogOpen,
		setEditDialogOpen,
		selectedExpense,
		setSelectedExpense,
	} = useExpensesUI();
	const { refetch: refetchExpenses } = useExpenses();
	const { createExpense, updateExpense } = useExpenseMutations({
		onSuccess: () => {
			refetchExpenses();
		},
	});
	const t = useTranslations();

	const handleExpenseSubmit = async (data: any) => {
		console.log("🎯 PARENT: handleExpenseSubmit called");
		console.log("📥 PARENT: Received expense data:", data);
		console.log("📊 PARENT: Selected expense:", selectedExpense?.id);

		try {
			if (selectedExpense?.id) {
				console.log("📝 PARENT: Updating expense:", selectedExpense.id);
				// Extract document file from data for update
				const { document, removeDocument, ...expenseData } = data;
				console.log("📤 PARENT: Calling updateExpense with:", {
					expenseData,
					document,
					removeDocument,
				});
				await updateExpense(selectedExpense.id, {
					...expenseData,
					...(document && { documentFile: document }),
					...(removeDocument && { removeDocument }),
				});
			} else {
				console.log("🆕 PARENT: Creating new expense");
				// Extract document file from data for create
				const { document, ...expenseData } = data;
				console.log("📤 PARENT: Calling createExpense with:", {
					expenseData,
					document,
				});
				await createExpense({
					...expenseData,
					...(document && { documentFile: document }),
				});
			}

			console.log("✅ PARENT: Expense operation completed successfully!");
			setCreateDialogOpen(false);
			setEditDialogOpen(false);
		} catch (error) {
			console.error("❌ PARENT: Expense operation failed:", error);
			throw error; // Re-throw so the dialog can handle it
		}
	};

	return (
		<>
			<div className="flex items-center justify-between pb-3">
				<h1 className="text-3xl font-bold tracking-tight">Expenses</h1>
				<Button
					onClick={() => {
						setSelectedExpense(null); // Clear selected expense for create mode
						setCreateDialogOpen(true);
					}}
				>
					<Plus className="mr-2 h-4 w-4" />
					Create Expense
				</Button>
			</div>

			<ExpensesDataTable />

			{/* Unified Expense Form Dialog */}
			<ExpenseCreateDialog
				isOpen={isCreateDialogOpen || isEditDialogOpen}
				onClose={() => {
					setCreateDialogOpen(false);
					setEditDialogOpen(false);
					setSelectedExpense(null); // Clear selected expense when closing
				}}
				onSubmit={handleExpenseSubmit}
				expense={selectedExpense || undefined}
				mode={isEditDialogOpen ? "edit" : "create"}
			/>

			{/* Delete Confirmation Dialog */}
			<DeleteExpenseDialog />
		</>
	);
}

export default function ExpensesPage() {
	const { refetch } = useExpenses();
	const { registerScopeWithShortcuts } = useShortcuts();

	// Register the expenses shortcuts scope
	useEffect(() => {
		const cleanupShortcuts =
			registerScopeWithShortcuts("expenses-shortcuts");
		return cleanupShortcuts;
	}, [registerScopeWithShortcuts]);

	return (
		<ExpensesUIProvider onExpenseDeleted={refetch}>
			<ExpensePageContent />
		</ExpensesUIProvider>
	);
}
