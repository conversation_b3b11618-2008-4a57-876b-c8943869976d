"use client";

import { Alert, AlertDescription } from "@ui/components/alert";
import { AlertTriangle, Clock, Info } from "lucide-react";
import React, { type ReactNode } from "react";

/**
 * Gets the appropriate address config based on stop type
 */
export function getAddressConfig(selectedAddress: any, stopType?: string): any {
	if (!selectedAddress) {
		return null;
	}

	// Try to get the config from the selectedAddress
	if (stopType?.toLowerCase() === "loading") {
		return selectedAddress.loadingConfig;
	}
	if (stopType?.toLowerCase() === "unloading") {
		return selectedAddress.unloadingConfig;
	}
	return null;
}

/**
 * Creates a hint text based on stop type
 */
export function getAddressHint(stopType?: string): string {
	if (!stopType) {
		return "Select from saved addresses";
	}

	return stopType.toLowerCase() === "loading"
		? "Select from loading addresses"
		: "Select from unloading addresses";
}

/**
 * Format slot booking warning for loading/unloading addresses
 */
export function formatSlotBookingWarning(
	slotBooking: boolean | undefined,
	stopType?: string,
): ReactNode | null {
	if (!slotBooking) {
		return null;
	}

	return (
		<Alert variant="error" className="mt-4">
			<AlertTriangle className="h-4 w-4" />
			<AlertDescription>
				Slot booking is required for this{" "}
				{stopType?.toLowerCase() === "loading"
					? "loading"
					: "unloading"}{" "}
				address. Please ensure you have booked a time slot.
			</AlertDescription>
		</Alert>
	);
}

/**
 * Format operating hours for display
 */
export function formatOperatingHours(
	operatingHours: Record<string, string[]> | undefined,
): ReactNode | null {
	if (!operatingHours) {
		return null;
	}

	const days = Object.keys(operatingHours);
	if (days.length === 0) {
		return null;
	}

	return (
		<div className="mt-4 space-y-2">
			<div className="flex items-center gap-2 text-sm font-medium">
				<Clock className="h-4 w-4 text-muted-foreground" />
				<h4>Operating Hours</h4>
			</div>
			<div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-xs">
				{days.map((day) => (
					<div key={day} className="flex justify-between">
						<span className="capitalize">{day}:</span>
						<span>{operatingHours[day].join(", ")}</span>
					</div>
				))}
			</div>
		</div>
	);
}

/**
 * Format instructions based on stop type
 */
export function formatInstructions(
	instructions: string | undefined,
	stopType?: string,
): ReactNode | null {
	if (!instructions) {
		return null;
	}

	return (
		<div className="mt-4 space-y-2">
			<div className="flex items-center gap-2 text-sm font-medium">
				<Info className="h-4 w-4 text-muted-foreground" />
				<h4>
					{stopType?.toLowerCase() === "loading"
						? "Loading"
						: "Unloading"}{" "}
					Instructions
				</h4>
			</div>
			<p className="text-sm text-muted-foreground">{instructions}</p>
		</div>
	);
}
