"use client";

import {
	useDocumentMutations,
	useDocuments,
} from "@saas/orders/hooks/use-documents";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ScrollArea } from "@ui/components/scroll-area";
import { format } from "date-fns";
import {
	Download,
	ExternalLink,
	FileIcon,
	Loader2,
	MoreHorizontal,
	Trash,
	Upload,
} from "lucide-react";
import { useRef, useState } from "react";

interface DocumentsPanelProps {
	orderId: string;
}

export function DocumentsPanel({ orderId }: DocumentsPanelProps) {
	const [documentToDelete, setDocumentToDelete] = useState<string | null>(
		null,
	);
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	// Fetch documents data for this specific order
	const { data, isLoading, error, refetch } = useDocuments(orderId);
	const { uploadDocument, deleteDocument } = useDocumentMutations(orderId, {
		onSuccess: () => refetch(),
	});

	const documents = data?.items || [];

	const handleUpload = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) {
			return;
		}

		setIsUploading(true);
		try {
			const file = files[0];
			// Pass the file directly to uploadDocument which now handles the FormData conversion
			await uploadDocument(file);
		} catch (error) {
			console.error("Failed to upload document:", error);
		} finally {
			setIsUploading(false);
			// Reset the file input
			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		}
	};

	const handleDelete = async (documentId: string) => {
		setDocumentToDelete(documentId);
	};

	const confirmDelete = async () => {
		if (!documentToDelete) {
			return;
		}

		try {
			await deleteDocument(documentToDelete);
		} catch (error) {
			console.error("Failed to delete document:", error);
		} finally {
			setDocumentToDelete(null);
		}
	};

	// Helper to get file icon based on file type
	const getFileIcon = (fileType: string) => {
		// You could add more specific icons based on file type
		return <FileIcon className="h-4 w-4 text-muted-foreground mr-2" />;
	};

	// Format file size to human-readable format
	const formatFileSize = (bytes: number) => {
		if (bytes === 0) {
			return "0 Bytes";
		}
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
	};

	// Format date for display
	const formatDate = (date: string | Date | undefined) => {
		if (!date) {
			return "N/A";
		}
		return format(new Date(date), "PPP p");
	};

	// Helper to get the entity type badge
	const getEntityTypeBadge = (entityType: "order" | "tour") => {
		if (entityType === "tour") {
			return (
				<Badge status="info" className="ml-2 text-xs">
					Tour
				</Badge>
			);
		}
		return null; // Don't show badge for order documents since they're the default
	};

	return (
		<div className="space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Documents</h3>
				<Button
					variant="outline"
					size="sm"
					onClick={handleUpload}
					disabled={isUploading}
				>
					{isUploading ? (
						<>
							<Loader2 className="h-4 w-4 mr-2 animate-spin" />
							Uploading...
						</>
					) : (
						<>
							<Upload className="h-4 w-4 mr-2" />
							Upload Document
						</>
					)}
				</Button>
				<input
					type="file"
					ref={fileInputRef}
					onChange={handleFileChange}
					className="hidden"
				/>
			</div>

			{isLoading ? (
				<div className="flex justify-center items-center py-6">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
				</div>
			) : error ? (
				<div className="text-center py-4 text-sm text-destructive">
					Error loading documents
				</div>
			) : documents.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No documents attached to this order
				</div>
			) : (
				<ScrollArea className="h-[300px] pr-3 -mr-3">
					<div className="space-y-3">
						{documents.map((document) => (
							<div
								key={document.id}
								className="flex flex-col p-3 border rounded-md"
							>
								<div className="flex items-start justify-between">
									<div>
										<div className="flex items-center">
											{getFileIcon(document.fileType)}
											<p className="font-medium text-sm">
												{document.fileName}
											</p>
											{getEntityTypeBadge(
												document.entityType,
											)}
										</div>

										<div className="text-xs text-muted-foreground mt-1 space-y-1">
											<p>Type: {document.fileType}</p>
											<p>
												Size:{" "}
												{formatFileSize(
													document.fileSize,
												)}
											</p>
											<p>
												Uploaded:{" "}
												{formatDate(
													document.uploadedAt ||
														undefined,
												)}
											</p>
											{document.description && (
												<p>
													Description:{" "}
													{document.description}
												</p>
											)}
										</div>
									</div>

									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0"
											>
												<MoreHorizontal className="h-4 w-4" />
												<span className="sr-only">
													Actions
												</span>
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuItem
												onClick={() =>
													window.open(
														document.signedUrl,
														"_blank",
													)
												}
											>
												<ExternalLink className="h-3.5 w-3.5 mr-2" />
												View Document
											</DropdownMenuItem>
											<DropdownMenuItem
												onClick={() => {
													// Download the document using global document object
													const a =
														window.document.createElement(
															"a",
														);
													a.href = document.signedUrl;
													a.download =
														document.fileName;
													a.click();
												}}
											>
												<Download className="h-3.5 w-3.5 mr-2" />
												Download
											</DropdownMenuItem>
											<DropdownMenuItem
												className="text-destructive"
												onClick={() =>
													document.id &&
													handleDelete(document.id)
												}
											>
												<Trash className="h-3.5 w-3.5 mr-2" />
												Delete
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</div>
							</div>
						))}
					</div>
				</ScrollArea>
			)}

			<AlertDialog
				open={documentToDelete !== null}
				onOpenChange={(open) => !open && setDocumentToDelete(null)}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Delete this document?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. The document will be
							permanently deleted.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDelete}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}
