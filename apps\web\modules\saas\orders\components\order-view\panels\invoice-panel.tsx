"use client";

import type { InvoiceStatus } from "@repo/api/src/routes/invoices/types";
import { InvoiceEmailPreview } from "@saas/invoices/components/invoice-email-preview";
import {
	useInvoiceMutations,
	useInvoices,
} from "@saas/invoices/hooks/use-invoice";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ScrollArea } from "@ui/components/scroll-area";
import { format } from "date-fns";
import {
	BanknoteIcon,
	CheckCircle,
	ExternalLink,
	FileX,
	Loader2,
	Mail,
	MoreHorizontal,
	SendHorizontal,
} from "lucide-react";
import { useState } from "react";

// Define the email log interface
interface EmailLog {
	id: string;
	status: string;
	sentAt: string;
	invoiceId?: string;
}

// Define an extended invoice type that includes email logs
interface InvoiceWithEmailLogs {
	id: string;
	status: string;
	invoice_number?: string | null;
	invoice_date?: string | Date | null;
	due_date?: string | Date | null;
	payment_date?: string | Date | null;
	comment?: string | null;
	internal_comment?: string | null;
	financialData?: any[];
	contact?: {
		email: string | null;
	} | null;
	customer?: {
		email: string | null;
	} | null;
	emailLogs?: EmailLog[];
	// Other invoice properties can be added as needed
}

interface InvoicesPanelProps {
	orderId: string;
	onCreateInvoice?: () => void;
}

export function InvoicesPanel({
	orderId,
	onCreateInvoice,
}: InvoicesPanelProps) {
	const [invoiceToCancel, setInvoiceToCancel] = useState<string | null>(null);
	const [isProcessing, setIsProcessing] = useState(false);
	const [sendingEmailId, setSendingEmailId] = useState<string | null>(null);
	const [createCreditNote, setCreateCreditNote] = useState(false);
	const [previewInvoiceId, setPreviewInvoiceId] = useState<string | null>(
		null,
	);
	// New state for invoice details dialog
	const [viewInvoiceId, setViewInvoiceId] = useState<string | null>(null);
	const [viewInvoicePdfData, setViewInvoicePdfData] = useState<string | null>(
		null,
	);
	const [isLoadingPdf, setIsLoadingPdf] = useState(false);

	// Fetch invoices data for this specific order
	const { data, isLoading, error, refetch } = useInvoices(orderId);
	const { cancelInvoice, markInvoicePaid, sendInvoiceEmail, getInvoicePdf } =
		useInvoiceMutations({
			onSuccess: () => refetch(),
		});

	const invoices = data?.items || [];

	const handleMarkPaid = async (invoiceId: string) => {
		setIsProcessing(true);
		try {
			await markInvoicePaid(invoiceId);
		} catch (error) {
			console.error(error);
		} finally {
			setIsProcessing(false);
		}
	};

	const handleCancel = async (invoiceId: string) => {
		setInvoiceToCancel(invoiceId);
		setCreateCreditNote(true);
	};

	const confirmCancel = async () => {
		if (!invoiceToCancel) {
			return;
		}

		setIsProcessing(true);
		try {
			await cancelInvoice({
				id: invoiceToCancel,
				createCancellationInvoice: createCreditNote,
			});
		} catch (error) {
			console.error(error);
		} finally {
			setIsProcessing(false);
			setInvoiceToCancel(null);
			setCreateCreditNote(false);
		}
	};

	const cancelAction = () => {
		setInvoiceToCancel(null);
		setCreateCreditNote(false);
	};

	const handleSend = async (invoiceId: string) => {
		setPreviewInvoiceId(invoiceId);
	};

	const handleConfirmSend = async (
		invoiceId: string,
		documentIds?: string[],
		customSubject?: string,
	) => {
		setSendingEmailId(invoiceId);
		try {
			await sendInvoiceEmail({
				id: invoiceId,
				documentIds,
				customSubject,
			});
		} catch (error) {
			console.error("Failed to send email:", error);
		} finally {
			setSendingEmailId(null);
			setPreviewInvoiceId(null);
		}
	};

	// New function to view invoice details
	const handleViewDetails = async (invoiceId: string) => {
		setIsLoadingPdf(true);
		setViewInvoiceId(invoiceId);
		try {
			const pdfResult = await getInvoicePdf(invoiceId);
			if (pdfResult?.pdfBase64) {
				setViewInvoicePdfData(pdfResult.pdfBase64);
			}
		} catch (error) {
			console.error("Failed to load invoice PDF:", error);
		} finally {
			setIsLoadingPdf(false);
		}
	};

	const getStatusBadge = (status: InvoiceStatus) => {
		const statusConfig: Record<string, { label: string; status: string }> =
			{
				OPEN: { label: "Open", status: "info" },
				SENT: { label: "Sent", status: "info" },
				PAID: { label: "Paid", status: "success" },
				CANCELLED: { label: "Cancelled", status: "error" },
				OVERDUE: { label: "Overdue", status: "warning" },
			};

		const config = statusConfig[status] || {
			label: status,
			status: "default",
		};
		return (
			<Badge status={config.status as any} className="ml-2">
				{config.label}
			</Badge>
		);
	};

	const formatCurrency = (invoice: any) => {
		if (!invoice.financialData || invoice.financialData.length === 0) {
			return "-";
		}
		// Get the first financial data entry
		const financialData = invoice.financialData[0];
		return new Intl.NumberFormat("de-DE", {
			style: "currency",
			currency: financialData.currency || "EUR",
		}).format(financialData.totalAmount);
	};

	const formatDate = (date?: string | Date | null) => {
		if (!date) {
			return "-";
		}
		return format(new Date(date), "PP");
	};

	const formatDateTime = (date?: string | Date | null) => {
		if (!date) {
			return "-";
		}
		return format(new Date(date), "PP p"); // Format with date and time
	};

	return (
		<div className="space-y-2">
			{/* <div className="flex items-center justify-between">
				<h3 className="font-medium">Invoices</h3>
			</div> */}

			{isLoading ? (
				<div className="flex justify-center items-center py-6">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
				</div>
			) : error ? (
				<div className="text-center py-4 text-sm text-destructive">
					Error loading invoices
				</div>
			) : invoices.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No invoices created for this order
				</div>
			) : (
				<ScrollArea className="h-[300px] pr-3 -mr-3">
					<div className="space-y-3">
						{invoices.map((originalInvoice) => {
							// Cast to our extended type
							const invoice =
								originalInvoice as unknown as InvoiceWithEmailLogs;
							return (
								<div
									key={invoice.id}
									className="border rounded-lg p-4 bg-card"
								>
									{/* Header with Invoice Number and Status */}
									<div className="flex items-center justify-between mb-3">
										<div className="flex items-center gap-2">
											<BanknoteIcon className="h-4 w-4 text-muted-foreground" />
											<h4 className="font-semibold text-sm">
												Invoice #
												{invoice.invoice_number}
											</h4>
										</div>
										<div className="flex items-center gap-2">
											{getStatusBadge(
												invoice.status as InvoiceStatus,
											)}
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 p-0"
													>
														<MoreHorizontal className="h-4 w-4" />
														<span className="sr-only">
															Actions
														</span>
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													{invoice.status !==
														"paid" &&
														invoice.status !==
															"cancelled" && (
															<DropdownMenuItem
																onClick={() =>
																	handleMarkPaid(
																		invoice.id,
																	)
																}
																disabled={
																	isProcessing
																}
															>
																<CheckCircle className="h-3.5 w-3.5 mr-2" />
																Mark as Paid
															</DropdownMenuItem>
														)}
													{invoice.status ===
														"open" && (
														<DropdownMenuItem
															onClick={() =>
																handleSend(
																	invoice.id,
																)
															}
															disabled={
																isProcessing ||
																sendingEmailId ===
																	invoice.id
															}
														>
															{sendingEmailId ===
															invoice.id ? (
																<>
																	<Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
																	Sending...
																</>
															) : (
																<>
																	<SendHorizontal className="h-3.5 w-3.5 mr-2" />
																	Send
																</>
															)}
														</DropdownMenuItem>
													)}
													<DropdownMenuItem
														onClick={() =>
															handleViewDetails(
																invoice.id,
															)
														}
													>
														<ExternalLink className="h-3.5 w-3.5 mr-2" />
														View Details
													</DropdownMenuItem>
													{(invoice.status ===
														"open" ||
														invoice.status ===
															"sent") && (
														<>
															<DropdownMenuSeparator />
															<DropdownMenuItem
																className="text-destructive"
																onClick={() =>
																	handleCancel(
																		invoice.id,
																	)
																}
																disabled={
																	isProcessing
																}
															>
																<FileX className="h-3.5 w-3.5 mr-2" />
																Cancel with
																Credit Note
															</DropdownMenuItem>
														</>
													)}
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</div>

									{/* Invoice Details Grid */}
									<div className="grid grid-cols-2 gap-4 mb-3 text-sm">
										<div>
											<p className="text-muted-foreground text-xs">
												Invoice date:
											</p>
											<p className="font-medium">
												{formatDate(
													invoice.invoice_date,
												)}
											</p>
										</div>
										<div>
											<p className="text-muted-foreground text-xs">
												Due date:
											</p>
											<p className="font-medium">
												{formatDate(invoice.due_date)}
											</p>
										</div>
										{invoice.payment_date && (
											<div className="col-span-2">
												<p className="text-muted-foreground text-xs">
													Paid at:
												</p>
												<p className="font-medium">
													{formatDate(
														invoice.payment_date,
													)}
												</p>
											</div>
										)}
									</div>

									{/* Amount - Prominent Display */}
									<div className="mb-3 p-3 bg-muted/50 rounded-md">
										<p className="text-muted-foreground text-xs">
											Amount:
										</p>
										<p className="font-bold text-lg">
											{formatCurrency(invoice)}
										</p>
									</div>

									{invoice.comment && (
										<div className="mt-2 text-xs italic text-muted-foreground">
											{invoice.comment}
										</div>
									)}
									{invoice.internal_comment && (
										<div className="mt-2 text-xs italic text-muted-foreground">
											{invoice.internal_comment}
										</div>
									)}

									{/* Email History - Enhanced */}
									{invoice.emailLogs &&
										invoice.emailLogs.length > 0 && (
											<div className="mb-3 p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-md border border-blue-200/50 dark:border-blue-800/50">
												<div className="flex items-center gap-2 mb-2">
													<Mail className="h-4 w-4 text-blue-600 dark:text-blue-400" />
													<span className="font-medium text-sm text-blue-900 dark:text-blue-100">
														Email History:
													</span>
												</div>
												<div className="space-y-2">
													{invoice.emailLogs
														.slice(0, 3)
														.map((log) => (
															<div
																key={log.id}
																className="flex items-center justify-between text-xs"
															>
																<div className="flex items-center gap-2">
																	<Badge
																		status={
																			log.status ===
																			"SENT"
																				? "success"
																				: "warning"
																		}
																		className="h-5 px-2 text-[10px] font-medium"
																	>
																		{
																			log.status
																		}
																	</Badge>
																	<span className="text-muted-foreground">
																		{formatDateTime(
																			log.sentAt,
																		)}
																	</span>
																</div>
															</div>
														))}
													{invoice.emailLogs.length >
														3 && (
														<div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
															+
															{invoice.emailLogs
																.length -
																3}{" "}
															more
														</div>
													)}
												</div>
											</div>
										)}

									{/* Action buttons for common actions */}
									{(invoice.status === "OPEN" ||
										invoice.status === "SENT") && (
										<div className="flex gap-2 mt-3">
											<Button
												variant="outline"
												size="sm"
												className="h-7 text-xs"
												onClick={() =>
													handleMarkPaid(invoice.id)
												}
												disabled={isProcessing}
											>
												<CheckCircle className="h-3 w-3 mr-1" />
												Mark Paid
											</Button>
											{invoice.status === "OPEN" && (
												<Button
													variant="outline"
													size="sm"
													className="h-7 text-xs"
													onClick={() =>
														handleSend(invoice.id)
													}
													disabled={
														isProcessing ||
														sendingEmailId ===
															invoice.id
													}
												>
													{sendingEmailId ===
													invoice.id ? (
														<>
															<Loader2 className="h-3.5 w-3.5 mr-2 animate-spin" />
															Sending...
														</>
													) : (
														<>
															<SendHorizontal className="h-3.5 w-3.5 mr-2" />
															Send
														</>
													)}
												</Button>
											)}

											{/* Remove the first cancel button and keep only the credit note button */}
											<Button
												variant="outline"
												size="sm"
												className="h-7 text-xs text-destructive border-destructive"
												onClick={() =>
													handleCancel(invoice.id)
												}
												disabled={isProcessing}
											>
												<FileX className="h-3 w-3 mr-1" />
												With Credit
											</Button>
										</div>
									)}
								</div>
							);
						})}
					</div>
				</ScrollArea>
			)}

			<AlertDialog
				open={invoiceToCancel !== null}
				onOpenChange={cancelAction}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Cancel with Credit Note?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This will cancel the original invoice and create a
							credit note with negative total amount. The credit
							note will appear as a separate invoice. This action
							cannot be undone.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>No, keep it</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmCancel}
							disabled={isProcessing}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							{isProcessing
								? "Cancelling..."
								: createCreditNote
									? "Yes, create credit note"
									: "Yes, cancel invoice"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			<InvoiceEmailPreview
				invoiceId={previewInvoiceId}
				isOpen={previewInvoiceId !== null}
				onClose={() => {
					setPreviewInvoiceId(null);
				}}
				onSend={handleConfirmSend}
				isSending={!!sendingEmailId}
			/>

			{/* Invoice Details Dialog */}
			<Dialog
				open={viewInvoiceId !== null}
				onOpenChange={(open) => {
					if (!open) {
						setViewInvoiceId(null);
						setViewInvoicePdfData(null);
					}
				}}
			>
				<DialogContent className="max-w-4xl h-[80vh] flex flex-col">
					<DialogHeader className="flex-shrink-0">
						<DialogTitle>Invoice Details</DialogTitle>
					</DialogHeader>
					<div className="flex-1 min-h-0">
						{isLoadingPdf ? (
							<div className="flex items-center justify-center h-full">
								<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
							</div>
						) : viewInvoicePdfData ? (
							<iframe
								src={`data:application/pdf;base64,${viewInvoicePdfData}`}
								width="100%"
								height="100%"
								title="Invoice Details"
							/>
						) : (
							<div className="flex items-center justify-center h-full text-muted-foreground">
								Failed to load invoice PDF
							</div>
						)}
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
