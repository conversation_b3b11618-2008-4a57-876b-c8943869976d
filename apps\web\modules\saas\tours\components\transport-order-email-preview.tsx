"use client";

import { UniversalEmailPreview } from "@saas/shared/components/universal-email-preview";
import { useUniversalSendEmail } from "@saas/shared/hooks/use-universal-send-email";
import type { SendEmailParams } from "@saas/shared/types/email-preview";

interface TransportOrderEmailPreviewProps {
	tourId: string | null;
	isOpen: boolean;
	onClose: () => void;
	onSend: (tourId: string, documentIds?: string[]) => Promise<void>;
	isSending: boolean;
}

export function TransportOrderEmailPreview({
	tourId,
	isOpen,
	onClose,
	onSend,
	isSending,
}: TransportOrderEmailPreviewProps) {
	const { sendEmail } = useUniversalSendEmail();

	// Adapter function to convert universal params to the original onSend interface
	const handleUniversalSend = async (
		params: SendEmailParams,
	): Promise<void> => {
		// Always use the universal send email hook for consistency and full feature support
		await sendEmail(params);
	};

	return (
		<UniversalEmailPreview
			entityId={tourId}
			entityType="transport-order"
			isOpen={isOpen}
			onClose={onClose}
			onSend={handleUniversalSend}
			isSending={isSending}
		/>
	);
}
