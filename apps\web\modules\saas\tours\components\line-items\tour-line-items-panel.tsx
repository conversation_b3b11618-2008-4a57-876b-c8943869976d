"use client";

import type { LineItemFormValues } from "@repo/api/src/routes/line-items/types";
import {
	useTourLineItemMutations,
	useTourLineItems,
} from "@saas/orders/hooks/use-line-items";
import type { LineItem } from "@saas/shared/components/line-items/line-items-table";
import { Button } from "@ui/components/button";
import { Loader2, Plus } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { TourLineItemDialog } from "./tour-line-item-dialog";
import { TourLineItemsTable } from "./tour-line-items-table";

interface TourLineItemsPanelProps {
	tourId: string;
}

export function TourLineItemsPanel({ tourId }: TourLineItemsPanelProps) {
	const [dialogOpen, setDialogOpen] = useState(false);
	const [editingItem, setEditingItem] = useState<LineItem | null>(null);

	const { data, isLoading, refetch } = useTourLineItems(tourId);
	const { createLineItem, updateLineItem, deleteLineItem } =
		useTourLineItemMutations(tourId, {
			onSuccess: () => {
				refetch();
			},
		});

	const handleAddItem = () => {
		setEditingItem(null);
		setDialogOpen(true);
	};

	const handleEditItem = (item: LineItem) => {
		setEditingItem(item);
		setDialogOpen(true);
	};

	const handleSaveItem = async (
		item: LineItemFormValues & { allocations?: any[] },
	) => {
		try {
			if (editingItem?.id) {
				await updateLineItem({ ...item, id: editingItem.id });
				toast.success("Line item updated successfully");
			} else {
				await createLineItem(item);
				toast.success("Line item added successfully");
			}
			setDialogOpen(false);
		} catch (error) {
			console.error("Error saving line item:", error);
			toast.error("Failed to save line item");
		}
	};

	const handleDeleteItem = async (itemId: string) => {
		try {
			await deleteLineItem(itemId);
			toast.success("Line item deleted successfully");
		} catch (error) {
			console.error("Error deleting line item:", error);
			toast.error("Failed to delete line item");
		}
	};

	return (
		<div className="space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Tour Costs</h3>
				<Button variant="outline" size="sm" onClick={handleAddItem}>
					<Plus className="h-4 w-4 mr-2" />
					Add Cost
				</Button>
			</div>

			{isLoading ? (
				<div className="flex justify-center items-center py-6">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
				</div>
			) : data?.items && data.items.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No costs added to this tour
				</div>
			) : (
				<TourLineItemsTable
					items={data?.items || []}
					onEditItem={handleEditItem}
					onDeleteItem={handleDeleteItem}
				/>
			)}

			<TourLineItemDialog
				open={dialogOpen}
				onOpenChange={setDialogOpen}
				initialData={editingItem}
				tourId={tourId}
				onSave={handleSaveItem}
			/>
		</div>
	);
}
