"use client";

import { useInvoiceMutations } from "@saas/invoices/hooks/use-invoice";
import { invoiceKeys } from "@saas/invoices/lib/api";
import type { fetchInvoiceById } from "@saas/invoices/lib/api";

import type { Invoice } from "@repo/database";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { DELETE_DIALOG_SHORTCUTS } from "@saas/shared/components/shortcuts/registry/shortcut-registry";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import {
	type ReactNode,
	createContext,
	useCallback,
	useContext,
	useRef,
	useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import { InvoiceEmailPreview } from "../components/invoice-email-preview";
import { useInvoices } from "../hooks/use-invoice";

// Infer the response type from the fetch function
type InvoiceResponse = Awaited<ReturnType<typeof fetchInvoiceById>>;

interface InvoicesUIContextValue {
	// State
	deleteInvoice: Invoice | null;
	selectedInvoice: InvoiceResponse | null;
	isDeleteDialogOpen: boolean;
	isViewSheetOpen: boolean;
	focusedDeleteButton: "cancel" | "confirm";
	cancelRef: React.RefObject<HTMLButtonElement | null>;
	confirmRef: React.RefObject<HTMLButtonElement | null>;
	isEmailPreviewOpen: boolean;
	previewInvoiceId: string | null;
	customRecipient: string | undefined;
	isMarkPaidDialogOpen: boolean;
	paymentDate: Date | null;
	isCreateDialogOpen: boolean;
	isViewPdfDialogOpen: boolean;
	viewInvoiceId: string | null;
	viewInvoicePdfData: string | null;
	isLoadingPdf: boolean;
	isEditDialogOpen: boolean;

	// Actions
	handleDeleteInvoice: (invoice: Invoice) => void;
	handleViewInvoice: (invoice: Invoice) => void;
	handleMarkAsPaid: (invoice: Invoice) => void;
	handleSendInvoiceEmail: (
		invoice: { id: string },
		recipientEmail?: string,
	) => void;
	handleCancelDelete: () => void;
	handleConfirmDelete: () => Promise<void>;
	handleConfirmMarkPaid: () => Promise<void>;
	handleSetDeleteButtonFocus: (button: "cancel" | "confirm") => void;
	setSelectedInvoice: (invoice: InvoiceResponse | null) => void;
	closeAllDialogs: () => void;
	setCustomRecipient: (recipientEmail?: string) => void;
	setPaymentDate: (date: Date | null) => void;
	handleViewPdf: (invoice: Invoice) => Promise<void>;
	handleEditInvoice: (invoice: Invoice) => void;

	// Dialog management
	setDeleteDialogOpen: (open: boolean) => void;
	setViewSheetOpen: (open: boolean) => void;
	setMarkPaidDialogOpen: (open: boolean) => void;
	setCreateDialogOpen: (open: boolean) => void;
	setViewPdfDialogOpen: (open: boolean) => void;
	setEditDialogOpen: (open: boolean) => void;
}

const InvoicesUIContext = createContext<InvoicesUIContextValue | null>(null);

export function InvoicesUIProvider({
	children,
	onInvoiceDeleted,
}: {
	children: ReactNode;
	onInvoiceDeleted?: () => void;
}) {
	const t = useTranslations();
	const {
		cancelInvoice: cancelInvoiceMutation,
		markInvoicePaid: markInvoicePaidMutation,
		sendInvoiceEmail: sendInvoiceEmailMutation,
		getInvoicePdf,
	} = useInvoiceMutations({
		onSuccess: onInvoiceDeleted,
	});
	const { data } = useInvoices();
	const queryClient = useQueryClient();
	const {
		enableScope,
		disableScope,
		addShortcuts,
		removeShortcuts,
		activeScopes,
	} = useShortcuts();

	// State
	const [deleteInvoice, setDeleteInvoice] = useState<Invoice | null>(null);
	const [selectedInvoice, setSelectedInvoice] =
		useState<InvoiceResponse | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isMarkPaidDialogOpen, setIsMarkPaidDialogOpen] = useState(false);
	const [isViewSheetOpen, setIsViewSheetOpen] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [focusedDeleteButton, setFocusedDeleteButton] = useState<
		"cancel" | "confirm"
	>("cancel");
	const [isEmailPreviewOpen, setIsEmailPreviewOpen] = useState(false);
	const [previewInvoiceId, setPreviewInvoiceId] = useState<string | null>(
		null,
	);
	const [isSendingEmail, setIsSendingEmail] = useState(false);
	const [customRecipient, setCustomRecipient] = useState<string | undefined>(
		undefined,
	);
	const [paymentDate, setPaymentDate] = useState<Date | null>(new Date());
	// New PDF viewer states
	const [isViewPdfDialogOpen, setIsViewPdfDialogOpen] = useState(false);
	const [viewInvoiceId, setViewInvoiceId] = useState<string | null>(null);
	const [viewInvoicePdfData, setViewInvoicePdfData] = useState<string | null>(
		null,
	);
	const [isLoadingPdf, setIsLoadingPdf] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

	// Refs for focus management
	const cancelRef = useRef<HTMLButtonElement>(null);
	const confirmRef = useRef<HTMLButtonElement>(null);

	// Actions
	const handleViewInvoice = useCallback((invoice: Invoice) => {
		setSelectedInvoice(invoice as unknown as InvoiceResponse);
		setIsViewSheetOpen(true);
	}, []);

	const handleMarkAsPaid = useCallback((invoice: Invoice) => {
		setSelectedInvoice(invoice as unknown as InvoiceResponse);
		setPaymentDate(new Date()); // Default to current date
		setIsMarkPaidDialogOpen(true);
	}, []);

	const handleSendInvoiceEmail = useCallback(
		async (invoice: { id: string }, recipientEmail?: string) => {
			setPreviewInvoiceId(invoice.id);
			setCustomRecipient(recipientEmail);
			setIsEmailPreviewOpen(true);
		},
		[],
	);

	const handleConfirmSendEmail = useCallback(
		async (
			invoiceId: string,
			documentIds?: string[],
			customSubject?: string,
		) => {
			try {
				setIsSendingEmail(true);
				await sendInvoiceEmailMutation({
					id: invoiceId,
					recipientEmail: customRecipient,
					documentIds,
					customSubject,
				});

				// Close the preview dialog on success
				setIsEmailPreviewOpen(false);
				setPreviewInvoiceId(null);
			} catch (error) {
				console.error("Error sending invoice email:", error);
			} finally {
				setIsSendingEmail(false);
			}
		},
		[sendInvoiceEmailMutation, customRecipient],
	);

	const handleDeleteInvoice = useCallback(
		(invoice: Invoice) => {
			setDeleteInvoice(invoice);
			setFocusedDeleteButton("cancel");
			setIsDeleteDialogOpen(true);

			// When delete dialog opens, disable table scope and enable dialog scope
			disableScope("invoices-shortcuts");
			enableScope("invoices-delete-dialog");

			// Register shortcuts for the delete dialog
			addShortcuts(DELETE_DIALOG_SHORTCUTS);

			return () => {
				removeShortcuts(DELETE_DIALOG_SHORTCUTS.map((s) => s.id));
				disableScope("invoices-delete-dialog");
				enableScope("invoices-shortcuts");
			};
		},
		[addShortcuts, disableScope, enableScope, removeShortcuts],
	);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setDeleteInvoice(null);
		setFocusedDeleteButton("cancel");

		// Re-enable table scope when dialog closes
		disableScope("invoices-delete-dialog");
		enableScope("invoices-shortcuts");
	}, [disableScope, enableScope]);

	const handleConfirmDelete = useCallback(async () => {
		if (!deleteInvoice) {
			return;
		}

		try {
			// Cancel the invoice with createCancellationInvoice: true
			await cancelInvoiceMutation({
				id: deleteInvoice.id,
				createCancellationInvoice: true,
			});
			toast.success("Invoice cancelled successfully");

			// Invalidate queries to refresh the data
			await queryClient.invalidateQueries({
				queryKey: invoiceKeys.all,
			});

			// Close dialog and reset state
			setIsDeleteDialogOpen(false);
			setDeleteInvoice(null);
			setFocusedDeleteButton("cancel");

			// Re-enable table scope when dialog closes
			disableScope("invoices-delete-dialog");
			enableScope("invoices-shortcuts");

			// Notify parent component
			onInvoiceDeleted?.();
		} catch (error) {
			toast.error("Failed to cancel invoice");
		}
	}, [
		deleteInvoice,
		cancelInvoiceMutation,
		disableScope,
		enableScope,
		onInvoiceDeleted,
		queryClient,
	]);

	const handleConfirmMarkPaid = useCallback(async () => {
		if (!selectedInvoice) {
			return;
		}

		try {
			// Mark the invoice as paid
			await markInvoicePaidMutation({
				id: selectedInvoice.id,
				paymentDate: paymentDate || undefined,
			});
			toast.success("Invoice marked as paid successfully");

			// Invalidate queries to refresh the data
			await queryClient.invalidateQueries({
				queryKey: invoiceKeys.all,
			});

			// Close dialog and reset state
			setIsMarkPaidDialogOpen(false);
			setSelectedInvoice(null);

			// Notify parent component
			onInvoiceDeleted?.();
		} catch (error) {
			toast.error("Failed to mark invoice as paid");
		}
	}, [
		selectedInvoice,
		paymentDate,
		markInvoicePaidMutation,
		onInvoiceDeleted,
		queryClient,
	]);

	const handleSetDeleteButtonFocus = useCallback(
		(button: "cancel" | "confirm") => {
			setFocusedDeleteButton(button);
		},
		[],
	);

	const handleViewPdf = useCallback(
		async (invoice: Invoice) => {
			setIsLoadingPdf(true);
			setViewInvoiceId(invoice.id);
			setIsViewPdfDialogOpen(true);

			try {
				const pdfResult = await getInvoicePdf(invoice.id);
				if (pdfResult?.pdfBase64) {
					setViewInvoicePdfData(pdfResult.pdfBase64);
				}
			} catch (error) {
				console.error("Failed to load invoice PDF:", error);
				toast.error("Failed to load invoice PDF");
			} finally {
				setIsLoadingPdf(false);
			}
		},
		[getInvoicePdf],
	);

	const handleEditInvoice = useCallback((invoice: Invoice) => {
		setSelectedInvoice(invoice as unknown as InvoiceResponse);
		setIsEditDialogOpen(true);
	}, []);

	const closeAllDialogs = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setIsViewSheetOpen(false);
		setIsEmailPreviewOpen(false);
		setIsMarkPaidDialogOpen(false);
		setIsCreateDialogOpen(false);
		setIsViewPdfDialogOpen(false);
		setIsEditDialogOpen(false);
		setDeleteInvoice(null);
		setSelectedInvoice(null);
		setPreviewInvoiceId(null);
		setViewInvoiceId(null);
		setViewInvoicePdfData(null);
		setFocusedDeleteButton("cancel");
	}, []);

	// Add keyboard handlers for arrow keys in delete dialog
	useHotkeys(
		"left",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("cancel");
			cancelRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("invoices-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"right",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("confirm");
			confirmRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("invoices-delete-dialog"),
			preventDefault: true,
		},
	);

	// Add keyboard handlers for enter/esc in delete dialog
	useHotkeys(
		"enter",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				if (focusedDeleteButton === "cancel") {
					handleCancelDelete();
				} else {
					void handleConfirmDelete();
				}
			}
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("invoices-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"esc",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				handleCancelDelete();
			}
			if (isViewSheetOpen) {
				setIsViewSheetOpen(false);
				setSelectedInvoice(null);
			}
			if (isMarkPaidDialogOpen) {
				setIsMarkPaidDialogOpen(false);
				setSelectedInvoice(null);
			}
		},
		{
			enabled:
				(isDeleteDialogOpen &&
					activeScopes.includes("invoices-delete-dialog")) ||
				isViewSheetOpen ||
				isMarkPaidDialogOpen,
			preventDefault: true,
		},
	);

	// Add keyboard handler for Ctrl+D to trigger delete
	useHotkeys(
		"ctrl+d",
		(e) => {
			e.preventDefault();
			const highlightedRow = document.querySelector(
				"[data-row-id].bg-muted",
			);
			if (!isDeleteDialogOpen && highlightedRow) {
				const rowId = highlightedRow.getAttribute("data-row-id");
				const invoice = data?.items?.find((p) => p.id === rowId);
				if (invoice) {
					// Convert to proper Invoice type
					const typedInvoice = {
						...invoice,
						createdAt: new Date(invoice.createdAt),
					} as Invoice;
					handleDeleteInvoice(typedInvoice);
				}
			}
		},
		{
			enabled: activeScopes.includes("invoices-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	// Add keyboard handler for Ctrl+P to mark as paid
	useHotkeys(
		"ctrl+p",
		(e) => {
			e.preventDefault();
			const highlightedRow = document.querySelector(
				"[data-row-id].bg-muted",
			);
			if (!isMarkPaidDialogOpen && highlightedRow) {
				const rowId = highlightedRow.getAttribute("data-row-id");
				const invoice = data?.items?.find((p) => p.id === rowId);
				if (invoice) {
					// Convert to proper Invoice type
					const typedInvoice = {
						...invoice,
						createdAt: new Date(invoice.createdAt),
					} as Invoice;
					handleMarkAsPaid(typedInvoice);
				}
			}
		},
		{
			enabled: activeScopes.includes("invoices-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	// Add keyboard handler for Ctrl+N to create new invoice
	useHotkeys(
		"ctrl+n",
		(e) => {
			e.preventDefault();
			if (activeScopes.includes("invoices-shortcuts")) {
				setIsCreateDialogOpen(true);
			}
		},
		{
			enabled: activeScopes.includes("invoices-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	return (
		<InvoicesUIContext.Provider
			value={{
				// State
				deleteInvoice,
				selectedInvoice,
				isDeleteDialogOpen,
				isViewSheetOpen,
				focusedDeleteButton,
				cancelRef,
				confirmRef,
				isEmailPreviewOpen,
				previewInvoiceId,
				customRecipient,
				isMarkPaidDialogOpen,
				paymentDate,
				isCreateDialogOpen,
				isViewPdfDialogOpen,
				viewInvoiceId,
				viewInvoicePdfData,
				isLoadingPdf,
				isEditDialogOpen,

				// Actions
				handleDeleteInvoice,
				handleViewInvoice,
				handleMarkAsPaid,
				handleSendInvoiceEmail,
				handleCancelDelete,
				handleConfirmDelete,
				handleConfirmMarkPaid,
				handleSetDeleteButtonFocus,
				setSelectedInvoice,
				closeAllDialogs,
				setCustomRecipient,
				setPaymentDate,
				handleViewPdf,
				handleEditInvoice,

				// Dialog management
				setDeleteDialogOpen: setIsDeleteDialogOpen,
				setViewSheetOpen: setIsViewSheetOpen,
				setMarkPaidDialogOpen: setIsMarkPaidDialogOpen,
				setCreateDialogOpen: setIsCreateDialogOpen,
				setViewPdfDialogOpen: setIsViewPdfDialogOpen,
				setEditDialogOpen: setIsEditDialogOpen,
			}}
		>
			{children}

			{/* Email preview dialog */}
			<InvoiceEmailPreview
				invoiceId={previewInvoiceId}
				isOpen={isEmailPreviewOpen}
				onClose={() => {
					setIsEmailPreviewOpen(false);
					setPreviewInvoiceId(null);
					setCustomRecipient(undefined);
				}}
				onSend={handleConfirmSendEmail}
				isSending={isSendingEmail}
			/>
		</InvoicesUIContext.Provider>
	);
}

export function useInvoicesUI() {
	const context = useContext(InvoicesUIContext);
	if (!context) {
		throw new Error(
			"useInvoicesUI must be used within an InvoicesUIProvider",
		);
	}
	return context;
}
