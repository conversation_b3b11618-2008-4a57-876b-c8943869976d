"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { contactConfigurationBaseSchema } from "@repo/api/src/routes/settings/contact-settings/types";
import { useCustomerSettings } from "@saas/organizations/hooks/use-customer-settings";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InfoIcon, Loader2 } from "lucide-react";
import { useEffect, useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import type { z } from "zod";

// Form values derived from the base schema, omitting fields managed by the backend
type CustomerSettingsFormValues = Omit<
	z.infer<typeof contactConfigurationBaseSchema>,
	"organizationId"
>;

// Function to generate a sample customer number based on the current settings
function generateExampleCustomerNumber(
	prefix: string | undefined,
	leadingZeros: number,
): string {
	// Example sequential number with leading zeros
	const seq = "1".padStart(leadingZeros || 4, "0");

	// Prepend prefix if set
	return (prefix || "") + seq;
}

export function CustomerSettingsForm() {
	const { data, isLoading, updateCustomerSettings, isUpdating } =
		useCustomerSettings();

	const form = useForm<CustomerSettingsFormValues>({
		resolver: zodResolver(contactConfigurationBaseSchema),
		defaultValues: {
			prefix: "",
			leadingZeros: 4,
			lastNumber: 0,
		},
		mode: "onBlur",
		reValidateMode: "onChange",
	});

	// Watch form values for preview
	const watchedValues = useWatch({
		control: form.control,
		defaultValue: form.getValues(),
	});

	// Generate example customer number based on current form values
	const exampleCustomerNumber = useMemo(() => {
		return generateExampleCustomerNumber(
			watchedValues.prefix,
			watchedValues.leadingZeros || 4,
		);
	}, [watchedValues.prefix, watchedValues.leadingZeros]);

	// Update form values when data is loaded
	useEffect(() => {
		if (data) {
			form.reset({
				prefix: data.prefix ?? "",
				leadingZeros: data.leadingZeros,
				lastNumber: data.lastNumber ?? 0,
			});
		}
	}, [data, form]);

	const onSubmit = async (values: CustomerSettingsFormValues) => {
		try {
			await updateCustomerSettings(values);
		} catch (error) {
			console.error("Failed to update customer settings", error);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full">
				<CardContent className="pt-6">
					<div className="flex items-center justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>Customer Number Configuration</CardTitle>
				<CardDescription>
					Configure how customer numbers are generated for your
					organization.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Alert className="mb-6">
					<InfoIcon className="h-4 w-4" />
					<AlertDescription>
						<span className="font-medium">
							Example customer number:
						</span>{" "}
						{exampleCustomerNumber}
					</AlertDescription>
				</Alert>

				<Form {...form}>
					<form
						id="customer-settings-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="prefix"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Customer Prefix</FormLabel>
									<FormControl>
										<Input
											placeholder="2"
											{...field}
											value={field.value || ""}
											disabled={isUpdating}
										/>
									</FormControl>
									<FormDescription>
										Optional prefix for customer numbers
										(e.g., 2, 3).
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="leadingZeros"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Leading Zeros</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											max={10}
											{...field}
											disabled={isUpdating}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("leadingZeros");
											}}
											className={
												form.formState.errors
													.leadingZeros
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Number of leading zeros in the
										sequential part (e.g., 4 = "0001")
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="lastNumber"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Current Sequence Number
									</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											value={field.value || 0}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("lastNumber");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors.lastNumber
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										The current sequence number. Set this
										when migrating from another system to
										avoid duplicate customer numbers.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
			</CardContent>
			<CardFooter>
				<Button
					type="submit"
					form="customer-settings-form"
					disabled={isUpdating}
				>
					{isUpdating && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					Save Configuration
				</Button>
			</CardFooter>
		</Card>
	);
}
