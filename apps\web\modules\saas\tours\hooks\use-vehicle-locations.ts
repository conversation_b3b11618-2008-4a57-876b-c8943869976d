import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useMemo, useState } from "react";
import { useVehicleLocationsQuery } from "../lib/api-vehicle-location";

/**
 * Hook for retrieving vehicle locations for display on a map
 * or in a real-time tracking interface.
 */
export function useVehicleLocations(includeRouteCalculations = false) {
	const { activeOrganization } = useActiveOrganization();
	const [isAutoRefreshing, setIsAutoRefreshing] = useState(true);

	// Fetch vehicle locations using the API
	const vehicleLocationsQuery = useVehicleLocationsQuery({
		organizationId: activeOrganization?.id ?? "",
		includeRouteCalculations,
	});

	// Return the processed data and control functions
	return {
		vehicleLocations: vehicleLocationsQuery.data || [],
		isLoading: vehicleLocationsQuery.isLoading,
		error: vehicleLocationsQuery.error,
		refetch: vehicleLocationsQuery.refetch,
		// Control auto-refresh functionality
		isAutoRefreshing,
		setIsAutoRefreshing,
	};
}

/**
 * Hook for getting active vehicles with their current locations
 * Filters out vehicles without a current location
 */
export function useActiveVehicleLocations(includeRouteCalculations = false) {
	const {
		vehicleLocations,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	} = useVehicleLocations(includeRouteCalculations);

	// Filter to only vehicles with a current location
	const activeVehicles = useMemo(() => {
		return vehicleLocations.filter(
			(vehicle) => vehicle.currentLocation !== null,
		);
	}, [vehicleLocations]);

	return {
		activeVehicles,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	};
}

/**
 * Hook for getting vehicles currently on tour with their routes
 */
export function useVehiclesOnTour(includeRouteCalculations = true) {
	const {
		vehicleLocations,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	} = useVehicleLocations(includeRouteCalculations);

	// Filter to only vehicles with a current tour
	const vehiclesOnTour = useMemo(() => {
		return vehicleLocations.filter(
			(vehicle) => vehicle.currentTour !== null,
		);
	}, [vehicleLocations]);

	return {
		vehiclesOnTour,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	};
}

/**
 * Hook specifically for tracking a single vehicle's location
 */
export function useVehicleLocation(
	vehicleId: string,
	includeRouteCalculations = true,
) {
	const {
		vehicleLocations,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	} = useVehicleLocations(includeRouteCalculations);

	// Find the specific vehicle
	const vehicleLocation = useMemo(() => {
		return (
			vehicleLocations.find((vehicle) => vehicle.id === vehicleId) || null
		);
	}, [vehicleLocations, vehicleId]);

	return {
		vehicleLocation,
		isLoading,
		error,
		refetch,
		isAutoRefreshing,
		setIsAutoRefreshing,
	};
}
