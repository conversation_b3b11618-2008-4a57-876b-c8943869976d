"use client";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { MultiEntitySelector } from "@ui/components/multi-entity-selector";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Calendar, Plus, Trash2 } from "lucide-react";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";

// Use the API allocation type directly
type AllocationData = ExpenseLineItemAllocationInput;

interface ExpenseAllocationPanelProps {
	lineItem: {
		id: string;
		description: string;
		totalPrice: number;
		allocations?: AllocationData[];
	} | null;
	currency: string;
	onSave: (allocations: AllocationData[]) => void;
	onCancel: () => void;
}

export function ExpenseAllocationPanel({
	lineItem,
	currency,
	onSave,
	onCancel,
}: ExpenseAllocationPanelProps) {
	const [allocations, setAllocations] = useState<AllocationData[]>([]);

	// Initialize allocations when lineItem changes
	useEffect(() => {
		if (lineItem?.allocations) {
			const normalizedAllocations = lineItem.allocations.map((alloc) => ({
				...alloc,
				entityIds: alloc.entityIds || [],
			}));
			setAllocations(normalizedAllocations);
		} else {
			setAllocations([]);
		}
	}, [lineItem]);

	const formatCurrency = (value: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(value);
	};

	const addAllocation = () => {
		const newAllocation: AllocationData = {
			type: "general",
			method: "fixed",
			value: 0,
			entityIds: [],
			selectAll: false,
		};
		setAllocations([...allocations, newAllocation]);
	};

	const removeAllocation = (index: number) => {
		setAllocations(allocations.filter((_, i) => i !== index));
	};

	const updateAllocation = (
		index: number,
		field: keyof AllocationData,
		value: any,
	) => {
		const updated = [...allocations];
		updated[index] = { ...updated[index], [field]: value };
		setAllocations(updated);
	};

	const getTotalAllocated = () => {
		if (!lineItem) return 0;
		return allocations.reduce((sum, allocation) => {
			if (allocation.method === "fixed") {
				return sum + allocation.value;
			}
			return sum + ((lineItem.totalPrice || 0) * allocation.value) / 100;
		}, 0);
	};

	const getRemainingAmount = () => {
		if (!lineItem) return 0;
		return (lineItem.totalPrice || 0) - getTotalAllocated();
	};

	const handleSave = () => {
		// Validate allocations
		const invalidAllocations = allocations.filter(
			(allocation) =>
				allocation.type !== "general" &&
				!allocation.selectAll &&
				allocation.entityIds.length === 0,
		);

		if (invalidAllocations.length > 0) {
			toast.error(
				`Please select entities for all ${invalidAllocations[0].type} allocations`,
			);
			return;
		}

		const invalidAmounts = allocations.filter(
			(allocation) => allocation.value <= 0,
		);

		if (invalidAmounts.length > 0) {
			toast.error("All allocation amounts must be greater than 0");
			return;
		}

		onSave(allocations);
	};

	if (!lineItem) {
		return null;
	}

	return (
		<div className="flex flex-col h-full">
			{/* Allocation Summary */}
			<div className="border rounded-lg p-4 bg-muted/50 mb-4 flex-shrink-0">
				<h4 className="font-medium mb-2">Allocation Summary</h4>
				<div className="grid grid-cols-3 gap-4 text-sm">
					<div>
						<span className="text-muted-foreground">
							Total Amount:
						</span>
						<div className="font-medium">
							{formatCurrency(lineItem.totalPrice || 0)}
						</div>
					</div>
					<div>
						<span className="text-muted-foreground">
							Allocated:
						</span>
						<div className="font-medium">
							{formatCurrency(getTotalAllocated())}
						</div>
					</div>
					<div>
						<span className="text-muted-foreground">
							Remaining:
						</span>
						<div
							className={`font-medium ${
								getRemainingAmount() < 0
									? "text-red-600"
									: "text-green-600"
							}`}
						>
							{formatCurrency(getRemainingAmount())}
						</div>
					</div>
				</div>
			</div>

			{/* Allocations List */}
			<div className="flex-1 overflow-y-auto space-y-3 mb-4">
				<div className="flex items-center justify-between">
					<h4 className="font-medium">
						Allocations ({allocations.length})
					</h4>
					<Button onClick={addAllocation} size="sm" variant="outline">
						<Plus className="h-4 w-4 mr-2" />
						Add Allocation
					</Button>
				</div>

				{allocations.length === 0 && (
					<div className="border rounded-lg p-8 text-center text-muted-foreground">
						<p>No allocations added yet</p>
						<p className="text-sm mt-1">
							Click "Add Allocation" to start
						</p>
					</div>
				)}

				{allocations.map((allocation, index) => (
					<div
						key={index}
						className="border rounded-lg p-4 space-y-3"
					>
						<div className="flex items-center justify-between">
							<span className="font-medium">
								Allocation {index + 1}
							</span>
							<Button
								onClick={() => removeAllocation(index)}
								size="sm"
								variant="ghost"
								className="text-red-600"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>

						<div className="grid grid-cols-2 gap-3">
							{/* Allocation Type */}
							<div className="space-y-2">
								<Label>Type</Label>
								<Select
									value={allocation.type}
									onValueChange={(
										value:
											| "general"
											| "order"
											| "vehicle"
											| "personnel",
									) => updateAllocation(index, "type", value)}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="general">
											General
										</SelectItem>
										<SelectItem value="order">
											Order
										</SelectItem>
										<SelectItem value="vehicle">
											Vehicle
										</SelectItem>
										<SelectItem value="personnel">
											Personnel
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Allocation Method */}
							<div className="space-y-2">
								<Label>Method</Label>
								<Select
									value={allocation.method}
									onValueChange={(
										value: "fixed" | "percentage",
									) =>
										updateAllocation(index, "method", value)
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="fixed">
											Fixed Amount
										</SelectItem>
										<SelectItem value="percentage">
											Percentage
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Amount/Percentage Input */}
						<div className="space-y-2">
							<Label>
								{allocation.method === "fixed"
									? "Amount"
									: "Percentage"}
							</Label>
							<Input
								type="number"
								step="0.01"
								min="0"
								max={
									allocation.method === "percentage"
										? "100"
										: undefined
								}
								value={allocation.value || ""}
								onChange={(e) =>
									updateAllocation(
										index,
										"value",
										Number.parseFloat(e.target.value) || 0,
									)
								}
								onFocus={(e) => e.target.select()}
								placeholder={
									allocation.method === "fixed" ? "0.00" : "0"
								}
							/>
							{allocation.method === "percentage" && (
								<div className="text-sm text-muted-foreground">
									={" "}
									{formatCurrency(
										((lineItem.totalPrice || 0) *
											allocation.value) /
											100,
									)}
								</div>
							)}
						</div>

						{/* Entity Selection */}
						{allocation.type !== "general" && (
							<div className="space-y-2">
								<Label>Select {allocation.type}s</Label>
								<MultiEntitySelector
									entityType={
										allocation.type as
											| "order"
											| "vehicle"
											| "personnel"
									}
									selectedIds={allocation.entityIds}
									onChange={(ids) =>
										updateAllocation(
											index,
											"entityIds",
											ids,
										)
									}
									selectAll={allocation.selectAll}
									onSelectAllChange={(selectAll) => {
										const updated = [...allocations];
										updated[index] = {
											...updated[index],
											selectAll,
											entityIds: selectAll
												? []
												: updated[index].entityIds,
										};
										setAllocations(updated);
									}}
									placeholder={`Search and select ${allocation.type}s...`}
									maxHeight="150px"
								/>
							</div>
						)}

						{/* Notes */}
						<div className="space-y-2">
							<Label>Notes (Optional)</Label>
							<Input
								value={allocation.notes || ""}
								onChange={(e) =>
									updateAllocation(
										index,
										"notes",
										e.target.value,
									)
								}
								placeholder="Additional notes for this allocation"
							/>
						</div>

						{/* Timeframe Fields */}
						<div className="grid grid-cols-2 gap-3">
							<div className="space-y-2">
								<Label className="flex items-center gap-2">
									<Calendar className="h-4 w-4" />
									Start Date (Optional)
								</Label>
								<Input
									type="date"
									value={
										allocation.allocation_start
											? allocation.allocation_start
													.toISOString()
													.split("T")[0]
											: ""
									}
									onChange={(e) =>
										updateAllocation(
											index,
											"allocation_start",
											e.target.value
												? new Date(e.target.value)
												: undefined,
										)
									}
								/>
							</div>
							<div className="space-y-2">
								<Label className="flex items-center gap-2">
									<Calendar className="h-4 w-4" />
									End Date (Optional)
								</Label>
								<Input
									type="date"
									value={
										allocation.allocation_end
											? allocation.allocation_end
													.toISOString()
													.split("T")[0]
											: ""
									}
									onChange={(e) =>
										updateAllocation(
											index,
											"allocation_end",
											e.target.value
												? new Date(e.target.value)
												: undefined,
										)
									}
								/>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end space-x-2 pt-4 border-t">
				<Button onClick={onCancel} variant="outline">
					Cancel
				</Button>
				<Button onClick={handleSave}>Save Allocations</Button>
			</div>
		</div>
	);
}
