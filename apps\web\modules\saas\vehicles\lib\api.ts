import type {
	UpdateVehicleInput,
	VehicleFormValues,
} from "@repo/api/src/routes/vehicles/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

type FetchVehiclesParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	isActiveInDispatch?: boolean;
};

export const vehicleKeys = {
	all: ["vehicles"] as const,
	list: (params: FetchVehiclesParams) =>
		[...vehicleKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...vehicleKeys.all, "detail", organizationId, id] as const,
};

export const fetchVehicles = async (params: FetchVehiclesParams) => {
	const response = await apiClient.vehicles.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			isActiveInDispatch:
				params.isActiveInDispatch !== undefined
					? params.isActiveInDispatch.toString()
					: undefined,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch vehicles list");
	}

	return response.json();
};

export const useVehiclesQuery = (params: FetchVehiclesParams) => {
	return useQuery({
		queryKey: vehicleKeys.list(params),
		queryFn: () => fetchVehicles(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchVehicleById = async (organizationId: string, id: string) => {
	const response = await apiClient.vehicles[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch vehicle details");
	}

	return response.json();
};

export const useVehicleByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: vehicleKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchVehicleById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create vehicle mutation
export const useCreateVehicleMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: VehicleFormValues) => {
			const response = await apiClient.vehicles.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create vehicle");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Vehicle created successfully");
			queryClient.invalidateQueries({
				queryKey: vehicleKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create vehicle");
			console.error("Create vehicle error:", error);
		},
	});
};

// Update vehicle mutation
export const useUpdateVehicleMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateVehicleInput) => {
			const response = await apiClient.vehicles[":id"].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update vehicle");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Vehicle updated successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: vehicleKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: vehicleKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update vehicle");
			console.error("Update vehicle error:", error);
		},
	});
};

// Delete vehicle mutation
export const useDeleteVehicleMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.vehicles[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete vehicle");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Vehicle deleted successfully");
			queryClient.invalidateQueries({
				queryKey: vehicleKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete vehicle");
			console.error("Delete vehicle error:", error);
		},
	});
};
