"use client";

import { UniversalEmailPreview } from "@saas/shared/components/universal-email-preview";
import { useUniversalSendEmail } from "@saas/shared/hooks/use-universal-send-email";
import type { SendEmailParams } from "@saas/shared/types/email-preview";

interface OfferEmailPreviewProps {
	offerId: string | null;
	isOpen: boolean;
	onClose: () => void;
	onSend?: (offerId: string) => Promise<void>; // Made optional for backward compatibility
	isSending?: boolean;
	recipientEmail?: string;
}

export function OfferEmailPreview({
	offerId,
	isOpen,
	onClose,
	onSend,
	isSending = false,
	recipientEmail,
}: OfferEmailPreviewProps) {
	const { sendEmail, isLoading } = useUniversalSendEmail();

	// Use the universal send email hook for proper CC/BCC support
	const handleUniversalSend = async (
		params: SendEmailParams,
	): Promise<void> => {
		await sendEmail(params);
	};

	return (
		<UniversalEmailPreview
			entityId={offerId}
			entityType="offer"
			isOpen={isOpen}
			onClose={onClose}
			onSend={handleUniversalSend}
			isSending={isSending || isLoading}
			recipientEmail={recipientEmail}
		/>
	);
}
