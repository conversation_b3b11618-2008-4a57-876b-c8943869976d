"use client";

import * as React from "react";
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts";

import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	type ChartConfig,
	ChartContainer,
	ChartLegend,
	ChartLegendContent,
	ChartTooltip,
	ChartTooltipContent,
} from "@ui/components/chart";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import {
	BadgePercent,
	Banknote,
	LineChart,
	Package,
	Receipt,
	TrendingDown,
	TrendingUp,
} from "lucide-react";
import { useFinance } from "../hooks/use-finance";

// Chart config for shadcn chart
const chartConfig = {
	revenue: {
		label: "Revenue",
	},
	projected: {
		label: "Projected",
		color: "hsl(var(--chart-1))",
	},
	confirmed: {
		label: "Confirmed",
		color: "hsl(var(--chart-2))",
	},
	average: {
		label: "Team Average (confirmed only)",
		color: "hsl(var(--chart-3))",
	},
} satisfies ChartConfig;

export function FinanceRevenue() {
	const {
		revenueData,
		totalProjected,
		totalConfirmed,
		orderMetrics,
		averageUserRevenue,
		userPerformance,
		activeUserCount,
		isLoading,
		timeRange,
		setTimeRange,
		utils,
	} = useFinance();

	// Map our timeRange to select value
	const getSelectValue = () => {
		switch (timeRange) {
			case "week":
				return "7d";
			case "month":
				return "30d";
			case "year":
				return "365d";
			default:
				return "30d";
		}
	};

	// Map select value to our timeRange
	const handleTimeRangeChange = (value: string) => {
		switch (value) {
			case "7d":
				setTimeRange("week");
				break;
			case "30d":
				setTimeRange("month");
				break;
			case "365d":
				setTimeRange("year");
				break;
		}
	};

	// Determine if user is performing above or below average
	const isAboveAverage = userPerformance?.percentOfAverage >= 100;
	const percentDiff = userPerformance?.percentOfAverage
		? Math.abs(Math.round(userPerformance.percentOfAverage - 100))
		: 0;

	return (
		<Card>
			<CardHeader className="flex flex-col space-y-2 p-4 sm:flex-row sm:items-center sm:space-y-0 sm:p-6">
				<div className="grid flex-1 gap-1 text-center sm:text-left">
					<CardTitle className="text-lg sm:text-xl">
						Revenue Overview
					</CardTitle>
					<CardDescription>
						Compare projected and confirmed revenue over time
					</CardDescription>
				</div>
				{!isLoading && (
					<Select
						value={getSelectValue()}
						onValueChange={handleTimeRangeChange}
					>
						<SelectTrigger
							className="w-full mt-2 sm:mt-0 sm:w-[160px] rounded-lg sm:ml-auto"
							aria-label="Select a time range"
						>
							<SelectValue placeholder="Last 30 days" />
						</SelectTrigger>
						<SelectContent className="rounded-xl">
							<SelectItem value="7d" className="rounded-lg">
								Last 7 days
							</SelectItem>
							<SelectItem value="30d" className="rounded-lg">
								Last 30 days
							</SelectItem>
							<SelectItem value="365d" className="rounded-lg">
								Last year
							</SelectItem>
						</SelectContent>
					</Select>
				)}
			</CardHeader>
			<CardContent className="p-3 sm:px-6 sm:pt-6">
				{isLoading ? (
					<div className="space-y-3">
						<Skeleton className="h-[250px] sm:h-[300px] w-full" />
					</div>
				) : revenueData.length === 0 ? (
					<div className="flex flex-col items-center justify-center h-[250px] sm:h-[300px] border rounded-md">
						<p className="text-muted-foreground">
							No revenue data available
						</p>
					</div>
				) : (
					<>
						<div className="w-full overflow-hidden">
							<ChartContainer
								config={chartConfig}
								className="aspect-auto h-[250px] sm:h-[300px] w-full"
							>
								<AreaChart
									data={revenueData}
									margin={{
										top: 10,
										right: 0,
										left: 0,
										bottom: 0,
									}}
								>
									<defs>
										<linearGradient
											id="fillProjected"
											x1="0"
											y1="0"
											x2="0"
											y2="1"
										>
											<stop
												offset="5%"
												stopColor="hsl(var(--chart-1))"
												stopOpacity={0.8}
											/>
											<stop
												offset="95%"
												stopColor="hsl(var(--chart-1))"
												stopOpacity={0.1}
											/>
										</linearGradient>
										<linearGradient
											id="fillConfirmed"
											x1="0"
											y1="0"
											x2="0"
											y2="1"
										>
											<stop
												offset="5%"
												stopColor="hsl(var(--chart-2))"
												stopOpacity={0.8}
											/>
											<stop
												offset="95%"
												stopColor="hsl(var(--chart-2))"
												stopOpacity={0.1}
											/>
										</linearGradient>
										<linearGradient
											id="fillAverage"
											x1="0"
											y1="0"
											x2="0"
											y2="1"
										>
											<stop
												offset="5%"
												stopColor="hsl(var(--chart-3))"
												stopOpacity={0.8}
											/>
											<stop
												offset="95%"
												stopColor="hsl(var(--chart-3))"
												stopOpacity={0.1}
											/>
										</linearGradient>
									</defs>
									<CartesianGrid
										vertical={false}
										strokeDasharray="3 3"
									/>
									<XAxis
										dataKey="date"
										tickLine={false}
										axisLine={false}
										tickMargin={8}
										minTickGap={10}
										tick={{ fontSize: 12 }}
										tickFormatter={(value) => value}
									/>
									<ChartTooltip
										cursor={false}
										content={
											<ChartTooltipContent
												labelKey="revenue"
												indicator="dot"
											/>
										}
									/>
									<Area
										dataKey="confirmed"
										type="monotone"
										fill="url(#fillConfirmed)"
										stroke="hsl(var(--chart-2))"
										fillOpacity={1}
									/>
									<Area
										dataKey="projected"
										type="monotone"
										fill="url(#fillProjected)"
										stroke="hsl(var(--chart-1))"
										fillOpacity={0.8}
									/>
									<Area
										dataKey="average"
										type="monotone"
										fill="none"
										stroke="hsl(var(--chart-3))"
										strokeWidth={2}
										strokeDasharray="5 5"
										dot={false}
									/>
									<ChartLegend
										content={<ChartLegendContent />}
									/>
								</AreaChart>
							</ChartContainer>
						</div>

						<div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mt-4 sm:mt-6">
							<div className="p-3 sm:p-4 border rounded-lg">
								<div className="text-sm font-medium text-muted-foreground mb-1 sm:mb-2 flex items-center">
									<LineChart className="h-4 w-4 mr-1" />
									Projected Revenue
								</div>
								<div className="text-xl sm:text-2xl font-bold">
									{utils.formatCurrency(totalProjected)}
								</div>
								<div className="text-xs text-muted-foreground mt-1">
									Based on all confirmed orders without
									invoices
								</div>
							</div>

							<div className="p-3 sm:p-4 border rounded-lg">
								<div className="text-sm font-medium text-muted-foreground mb-1 sm:mb-2 flex items-center">
									<Receipt className="h-4 w-4 mr-1" />
									Confirmed Revenue
								</div>
								<div className="text-xl sm:text-2xl font-bold">
									{utils.formatCurrency(totalConfirmed)}
								</div>
								<div className="text-xs text-muted-foreground mt-1">
									Invoiced revenue from confirmed orders
								</div>
							</div>

							<div className="p-3 sm:p-4 border rounded-lg bg-muted/10">
								<div className="text-sm font-medium text-muted-foreground mb-1 sm:mb-2 flex items-center">
									<BadgePercent className="h-4 w-4 mr-1" />
									Comparison to Team Average
								</div>
								<div className="text-xl sm:text-2xl font-bold flex items-center">
									{isAboveAverage ? (
										<TrendingUp className="h-5 w-5 mr-1 text-success" />
									) : (
										<TrendingDown className="h-5 w-5 mr-1 text-destructive" />
									)}
									<span
										className={
											isAboveAverage
												? "text-success"
												: "text-destructive"
										}
									>
										{isAboveAverage ? "+" : "-"}
										{percentDiff}%
									</span>
								</div>
								<div className="text-xs text-muted-foreground mt-1">
									{isAboveAverage
										? `Above average of ${activeUserCount} active team members`
										: `Below average of ${activeUserCount} active team members`}
									<span className="block mt-1">
										Based on confirmed revenue only
									</span>
								</div>
							</div>
						</div>

						{/* Order Metrics Row */}
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-3 sm:mt-4">
							{/* Total Orders Card */}
							<div className="p-3 sm:p-4 border rounded-lg">
								<div className="text-sm font-medium text-muted-foreground mb-1 sm:mb-2 flex items-center">
									<Package className="h-4 w-4 mr-1" />
									Total Orders
								</div>
								<div className="text-xl sm:text-2xl font-bold">
									{orderMetrics.totalOrders}
								</div>
								<div className="text-xs text-muted-foreground mt-1">
									All orders within the selected time period
								</div>
							</div>

							{/* Revenue Per Order Card */}
							<div className="p-3 sm:p-4 border rounded-lg">
								<div className="text-sm font-medium text-muted-foreground mb-1 sm:mb-2 flex items-center">
									<Banknote className="h-4 w-4 mr-1" />
									Revenue Per Order
								</div>
								<div className="text-xl sm:text-2xl font-bold">
									{utils.formatCurrency(
										orderMetrics.revenuePerOrder,
									)}
								</div>
								<div className="text-xs text-muted-foreground mt-1">
									Average revenue generated per order
								</div>
							</div>
						</div>
					</>
				)}
			</CardContent>
		</Card>
	);
}
