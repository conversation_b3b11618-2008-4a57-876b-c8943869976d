import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";
import { orderKeys } from "./api";

type FetchOrderDocumentsParams = {
	organizationId: string;
	orderId: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
};

// Additional keys for document-related queries
export const documentKeys = {
	all: (orderId: string) => [...orderKeys.all, "documents", orderId] as const,
	list: (params: FetchOrderDocumentsParams) =>
		[...documentKeys.all(params.orderId), "list", params] as const,
};

// Fetch documents for an order
export const fetchOrderDocuments = async (
	params: FetchOrderDocumentsParams,
) => {
	const response = await apiClient.orders[":id"].documents.$get({
		param: { id: params.orderId },
		query: {
			organizationId: params.organizationId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch order documents");
	}

	return response.json();
};

// Query hook for documents list
export const useOrderDocumentsQuery = (params: FetchOrderDocumentsParams) => {
	return useQuery({
		queryKey: documentKeys.list(params),
		queryFn: () => fetchOrderDocuments(params),
		placeholderData: keepPreviousData,
		enabled: Boolean(params.organizationId && params.orderId),
	});
};

// Type for document upload
export type UploadDocumentInput = {
	fileBuffer: Buffer;
	fileName: string;
	fileType: string;
	fileSize: number;
	description?: string;
	tags?: string[];
};

// Upload document mutation
export const useUploadDocumentMutation = (
	organizationId: string,
	orderId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: UploadDocumentInput) => {
			// Use a direct fetch approach instead of relying on the client's type definitions
			const url = new URL(
				`/api/orders/${orderId}/documents`,
				window.location.origin,
			);
			url.searchParams.append("organizationId", organizationId);

			const response = await fetch(url.toString(), {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(data),
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to upload document");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Document uploaded successfully");
			queryClient.invalidateQueries({
				queryKey: documentKeys.list({ organizationId, orderId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to upload document");
			console.error("Upload document error:", error);
		},
	});
};

// Delete document mutation
export const useDeleteDocumentMutation = (
	organizationId: string,
	orderId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (documentId: string) => {
			const response = await apiClient.orders[":id"].documents[
				":documentId"
			].$delete({
				param: { id: orderId, documentId },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete document");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Document deleted successfully");
			queryClient.invalidateQueries({
				queryKey: documentKeys.list({ organizationId, orderId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete document");
			console.error("Delete document error:", error);
		},
	});
};

// Helper function to prepare file for upload (converts File to required format)
export const prepareFileForUpload = async (
	file: File,
): Promise<UploadDocumentInput> => {
	// Convert file to ArrayBuffer first
	const arrayBuffer = await file.arrayBuffer();
	// Then convert to Buffer (which is what the API expects)
	// In browser environments, we need to create a proper Buffer
	const fileBuffer = Buffer.from(arrayBuffer);

	return {
		fileBuffer,
		fileName: file.name,
		fileType: file.type,
		fileSize: file.size,
	};
};
