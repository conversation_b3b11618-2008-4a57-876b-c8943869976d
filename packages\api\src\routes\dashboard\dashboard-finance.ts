import { db } from "@repo/database";
import {
	addDays,
	endOfDay,
	endOfMonth,
	endOfWeek,
	endOfYear,
	format,
	startOfDay,
	startOfMonth,
	startOfWeek,
	startOfYear,
	subDays,
} from "date-fns";
import { HTTPException } from "hono/http-exception";
import type {
	FinanceDataParams,
	RevenueDataPoint,
	RevenueResponse,
} from "./types";

/**
 * Get date range based on the selected time range
 */
function getDateRange(
	timeRange: "week" | "month" | "year",
	customStart?: Date,
	customEnd?: Date,
): { start: Date; end: Date } {
	const now = new Date();

	// If custom dates are provided, use them
	if (customStart && customEnd) {
		return {
			start: startOfDay(customStart),
			end: endOfDay(customEnd),
		};
	}

	// Otherwise calculate based on timeRange
	switch (timeRange) {
		case "week":
			return {
				start: startOfWeek(now, { weekStartsOn: 1 }), // Start from Monday
				end: endOfWeek(now, { weekStartsOn: 1 }),
			};
		case "month":
			return {
				start: startOfMonth(now),
				end: endOfMonth(now),
			};
		case "year":
			return {
				start: startOfYear(now),
				end: endOfYear(now),
			};
		default:
			return {
				start: startOfDay(subDays(now, 7)),
				end: endOfDay(now),
			};
	}
}

/**
 * Generate formatted dates for the chart based on time range
 */
function generateDateLabels(
	timeRange: "week" | "month" | "year",
	start: Date,
	end: Date,
): string[] {
	const labels: string[] = [];
	let current = new Date(start);

	// Format pattern based on time range
	let formatPattern: string;
	let increment: (date: Date) => Date;

	switch (timeRange) {
		case "week":
			formatPattern = "E"; // Mon, Tue, etc.
			increment = (date) => addDays(date, 1);
			break;
		case "month":
			formatPattern = "dd.MM";
			increment = (date) => addDays(date, 1);
			break;
		case "year":
			formatPattern = "MMM"; // Jan, Feb, etc.
			increment = (date) => {
				const newDate = new Date(date);
				newDate.setMonth(date.getMonth() + 1);
				return newDate;
			};
			break;
		default:
			formatPattern = "dd.MM";
			increment = (date) => addDays(date, 1);
	}

	// Generate all labels within the range
	while (current <= end) {
		labels.push(format(current, formatPattern));
		current = increment(current);
	}

	return labels;
}

/**
 * Get finance data for dashboard
 */
export async function getFinanceData({
	userId,
	organizationId,
	timeRange = "month",
	startDate,
	endDate,
}: FinanceDataParams): Promise<RevenueResponse> {
	try {
		// Get date range
		const { start, end } = getDateRange(timeRange, startDate, endDate);

		// Get projected revenue from confirmed orders with customer line items
		// that don't have invoices yet
		const orders = await db.order.findMany({
			where: {
				organizationId,
				creatorId: userId,
				order_status: "confirmed",
				createdAt: {
					gte: start,
					lte: end,
				},
				// Only include orders that don't have invoices
				NOT: {
					orderInvoices: {
						some: {},
					},
				},
			},
			include: {
				lineItems: {
					// Removed type filter - all line items are now revenue-only
					select: {
						totalPrice: true,
						createdAt: true,
					},
				},
			},
		});

		// Get confirmed revenue from invoices
		const invoices = await db.invoice.findMany({
			where: {
				organizationId,
				creatorId: userId,
				createdAt: {
					gte: start,
					lte: end,
				},
			},
			include: {
				lineItems: {
					select: {
						totalPrice: true,
						createdAt: true,
					},
				},
			},
		});

		// Get all invoices for the org to calculate team averages
		const allOrgInvoices = await db.invoice.findMany({
			where: {
				organizationId,
				createdAt: {
					gte: start,
					lte: end,
				},
			},
			include: {
				lineItems: {
					select: {
						totalPrice: true,
						createdAt: true,
					},
				},
				creator: {
					select: {
						id: true,
					},
				},
			},
		});

		// Generate all date labels
		const dateLabels = generateDateLabels(timeRange, start, end);

		// Initialize data structure with all dates
		const revenueByDate: Record<string, RevenueDataPoint> = {};

		// Initialize with zero values
		dateLabels.forEach((dateLabel) => {
			revenueByDate[dateLabel] = {
				date: dateLabel,
				projected: 0,
				confirmed: 0,
				average: 0, // New field for average
			};
		});

		// Calculate confirmed revenue
		let totalConfirmed = 0;
		invoices.forEach((invoice) => {
			invoice.lineItems.forEach((lineItem) => {
				if (lineItem.totalPrice) {
					// Get formatted date for grouping
					const dateKey = format(
						new Date(lineItem.createdAt),
						timeRange === "week"
							? "E"
							: timeRange === "month"
								? "dd.MM"
								: "MMM",
					);

					if (revenueByDate[dateKey]) {
						revenueByDate[dateKey].confirmed += lineItem.totalPrice;
						totalConfirmed += lineItem.totalPrice;
					}
				}
			});
		});

		// Calculate projected revenue
		let totalProjected = 0;
		orders.forEach((order) => {
			order.lineItems.forEach((lineItem) => {
				if (lineItem.totalPrice) {
					// Get formatted date for grouping
					const dateKey = format(
						new Date(lineItem.createdAt),
						timeRange === "week"
							? "E"
							: timeRange === "month"
								? "dd.MM"
								: "MMM",
					);

					if (revenueByDate[dateKey]) {
						revenueByDate[dateKey].projected += lineItem.totalPrice;
						totalProjected += lineItem.totalPrice;
					}
				}
			});
		});

		// Track which users have contributed with invoices and how much
		const userContributions: Record<string, number> = {};
		const orgConfirmedByDate: Record<string, number> = {};

		// Initialize date records
		dateLabels.forEach((dateLabel) => {
			orgConfirmedByDate[dateLabel] = 0;
		});

		// Sum org-wide confirmed revenue by date and by user
		allOrgInvoices.forEach((invoice) => {
			// Initialize user if not already in the record
			if (!userContributions[invoice.creatorId]) {
				userContributions[invoice.creatorId] = 0;
			}

			invoice.lineItems.forEach((lineItem) => {
				if (lineItem.totalPrice) {
					const dateKey = format(
						new Date(lineItem.createdAt),
						timeRange === "week"
							? "E"
							: timeRange === "month"
								? "dd.MM"
								: "MMM",
					);

					if (orgConfirmedByDate[dateKey] !== undefined) {
						orgConfirmedByDate[dateKey] += lineItem.totalPrice;
						userContributions[invoice.creatorId] +=
							lineItem.totalPrice;
					}
				}
			});
		});

		// Count only users who actually created invoices with revenue
		const activeContributors =
			Object.keys(userContributions).filter(
				(userId) => userContributions[userId] > 0,
			).length || 1; // Avoid division by zero

		// Calculate average revenue per active user by date
		dateLabels.forEach((dateLabel) => {
			// Only use confirmed revenue for the average
			revenueByDate[dateLabel].average =
				orgConfirmedByDate[dateLabel] / activeContributors;
		});

		// Calculate total org-wide confirmed revenue
		const totalOrgConfirmed = Object.values(orgConfirmedByDate).reduce(
			(sum, value) => sum + value,
			0,
		);

		// Average revenue per user who has contributed
		const averageUserRevenue = totalOrgConfirmed / activeContributors;

		// Convert to array and sort by date
		const data = Object.values(revenueByDate);

		// Get all orders that have invoices for counting
		const invoicedOrders = await db.order.findMany({
			where: {
				organizationId,
				creatorId: userId,
				order_status: "confirmed",
				createdAt: {
					gte: start,
					lte: end,
				},
				orderInvoices: {
					some: {},
				},
			},
			select: {
				id: true,
			},
		});

		// Calculate order metrics
		const totalOrders = orders.length + invoicedOrders.length;
		const revenuePerOrder =
			totalOrders > 0 ? totalConfirmed / totalOrders : 0;

		// Return data with team comparison and order metrics
		return {
			data,
			totalProjected,
			totalConfirmed,
			averageUserRevenue,
			activeUserCount: activeContributors,
			userPerformance: {
				totalRevenue: totalConfirmed, // The user's confirmed revenue only
				percentOfAverage:
					averageUserRevenue > 0
						? (totalConfirmed / averageUserRevenue) * 100
						: 0,
			},
			orderMetrics: {
				totalOrders,
				revenuePerOrder,
			},
		};
	} catch (error) {
		console.error("Failed to fetch finance data:", error);
		throw new HTTPException(500, {
			message: "Failed to fetch finance data",
		});
	}
}
