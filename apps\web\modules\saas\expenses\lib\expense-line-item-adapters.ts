/**
 * Adapter functions to convert between expense data types and shared LineItem format
 * This enables the shared LineItemsTable to work with expense-specific data
 */

import type { ExpenseLineItemInput } from "@repo/api/src/routes/costs/types";
import type { ExpenseLineItemWithAllocations } from "@saas/expenses/types/expense-line-items";
import type { LineItem } from "@saas/shared/components/line-items/line-items-table";
import {} from "lucide-react";
import React from "react";

/**
 * Convert ExpenseLineItemWithAllocations to shared LineItem format
 */
export function expenseToLineItem(
	expense: ExpenseLineItemWithAllocations,
	categoryName?: string,
): LineItem {
	return {
		// Core fields from API
		id: expense.id,
		description: expense.description,
		quantity: expense.quantity ?? 1,
		unit: expense.unit || "",
		unitPrice: expense.unitPrice ?? 0,
		totalPrice: expense.totalPrice || 0,
		currency: "EUR", // Expenses use fixed currency
		notes: expense.notes || "",
		vatRate: expense.vatRate ?? 0,

		// Expense-specific fields
		vatAmount: expense.vatAmount ?? 0,
		categoryId: expense.categoryId || "",
		categoryName: categoryName || "",

		// Allocation fields
		allocations: expense.allocations || [],
		savedAllocations: expense.savedAllocations || [],
		isFullyAllocated: expense.isFullyAllocated ?? false,
		allocatedAmount: expense.allocatedAmount ?? 0,
		remainingAmount: expense.remainingAmount ?? 0,
		allocationCount: expense.allocationCount ?? 0,

		// OCR fields
		isOcrGenerated: expense.isOcrGenerated ?? false,
		isOcrCategorized: expense.isOcrCategorized ?? false,
		isOcrAllocated: expense.isOcrAllocated ?? false,
	};
}

/**
 * Convert shared LineItem back to ExpenseLineItemInput format
 */
export function lineItemToExpenseInput(
	lineItem: LineItem,
): ExpenseLineItemInput {
	return {
		description: lineItem.description || "",
		quantity: lineItem.quantity ?? 1,
		unit: lineItem.unit || "",
		unitPrice: lineItem.unitPrice ?? 0,
		totalPrice: lineItem.totalPrice || 0,
		vatRate: lineItem.vatRate ?? 0,
		vatAmount: lineItem.vatAmount ?? 0,
		categoryId: lineItem.categoryId || "",
		notes: lineItem.notes || "",
		allocations: [], // Will be handled separately by allocation system
	};
}

/**
 * Calculate allocation status for display in the shared table
 */
export function calculateAllocationStatus(item: LineItem): {
	icon: React.ReactNode;
	text: string;
	color: string;
} {
	// Calculate allocation count from both backend allocations and savedAllocations
	const backendAllocationCount = item.allocations?.length || 0;
	const savedAllocationCount = item.savedAllocations?.length || 0;
	const totalAllocationCount = Math.max(
		backendAllocationCount,
		savedAllocationCount,
	);

	// Get effective allocated amount
	const allocatedAmount = getEffectiveAllocatedAmount(item);
	const totalAmount = item.totalPrice || 0;
	const isFullyAllocated = allocatedAmount >= totalAmount && totalAmount > 0;

	if (totalAllocationCount === 0) {
		return {
			icon: React.createElement("div", {
				className: "h-4 w-4 rounded-full bg-muted-foreground/20",
			}),
			text: "No allocations",
			color: "text-muted-foreground",
		};
	}

	if (isFullyAllocated) {
		return {
			icon: React.createElement("div", {
				className: "h-4 w-4 rounded-full bg-green-600",
			}),
			text: "Fully allocated",
			color: "text-green-600",
		};
	}

	return {
		icon: React.createElement("div", {
			className: "h-4 w-4 rounded-full bg-yellow-600",
		}),
		text: "Partially allocated",
		color: "text-yellow-600",
	};
}

/**
 * Calculate allocated amount from savedAllocations (for OCR line items)
 */
function calculateAllocatedAmountFromSaved(item: LineItem): number {
	if (!item.savedAllocations || item.savedAllocations.length === 0) {
		return 0;
	}

	return item.savedAllocations.reduce((total, allocation) => {
		if (allocation.method === "fixed") {
			return total + allocation.value;
		}
		// Percentage allocation
		return total + ((item.totalPrice || 0) * allocation.value) / 100;
	}, 0);
}

/**
 * Get the effective allocated amount (backend or calculated from savedAllocations)
 */
function getEffectiveAllocatedAmount(item: LineItem): number {
	// For new OCR items, calculate from savedAllocations
	if (item.savedAllocations && item.savedAllocations.length > 0) {
		return calculateAllocatedAmountFromSaved(item);
	}
	// For existing items, use backend allocatedAmount
	return item.allocatedAmount || 0;
}

/**
 * Convert array of ExpenseLineItemWithAllocations to LineItem array
 */
export function expenseArrayToLineItems(
	expenses: ExpenseLineItemWithAllocations[],
	getCategoryName?: (categoryId: string) => string,
): LineItem[] {
	return expenses.map((expense) =>
		expenseToLineItem(
			expense,
			getCategoryName
				? getCategoryName(expense.categoryId || "")
				: undefined,
		),
	);
}

/**
 * Create custom actions for expense line items
 */
export function createExpenseCustomActions(): Array<{
	label: string;
	icon: React.ReactNode;
	action: string;
	condition?: (item: LineItem) => boolean;
}> {
	return [
		{
			label: "Allocate",
			icon: React.createElement(
				"div",
				{
					className:
						"h-4 w-4 flex items-center justify-center text-xs font-bold border rounded",
				},
				"A",
			),
			action: "allocate",
			condition: (item) => !item.isFullyAllocated,
		},
	];
}

/**
 * Calculate VAT amount for display
 */
export function calculateVatAmount(
	totalPrice: number,
	vatRate: number,
): number {
	return (totalPrice * vatRate) / 100;
}
