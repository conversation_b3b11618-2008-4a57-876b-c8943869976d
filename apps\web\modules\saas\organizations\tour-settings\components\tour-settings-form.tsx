"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { invoiceConfigurationBaseSchema } from "@repo/api/src/routes/settings/invoice-settings/types";
import type { tourConfigurationBaseSchema } from "@repo/api/src/routes/settings/tour-settings/types";
import { useTourSettings } from "@saas/organizations/hooks/use-tour-settings";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { AlertTriangle, InfoIcon, Loader2 } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import type { z } from "zod";

// Form values derived from the base schema, omitting fields managed by the backend
type TourSettingsFormValues = Omit<
	z.infer<typeof tourConfigurationBaseSchema>,
	"lastDate"
>;

// Function to check if a string consists only of valid placeholders
function containsOnlyValidPlaceholders(format: string): boolean {
	// Remove all valid placeholders from the string
	const cleanedFormat = format
		.replace(/\{YYYY\}/g, "")
		.replace(/\{MM\}/g, "")
		.replace(/\{SEQ\}/g, "");

	// If anything remains, the string contains invalid characters
	return cleanedFormat.length === 0;
}

// Function to generate a sample invoice number based on the current settings
function generateExampleTransportOrderNumber(
	prefix: string | undefined,
	leadingZeros: number,
	format: string,
): string {
	// Current date components
	const now = new Date();
	const year = now.getFullYear().toString();
	const month = (now.getMonth() + 1).toString().padStart(2, "0");
	const shortYear = year.substring(2);

	// Example sequential number with leading zeros
	const seq = "1".padStart(leadingZeros || 4, "0");

	// Replace placeholders in the format string
	let result = format
		.replace(/\{YYYY\}/g, year)
		.replace(/\{YY\}/g, shortYear)
		.replace(/\{MM\}/g, month)
		.replace(/\{SEQ\}/g, seq);

	// Simple prefix logic - prepend prefix if it exists
	if (prefix) {
		result = prefix + result;
	}

	return result;
}

export function TourSettingsForm() {
	const { data, isLoading, updateTourSettings, isUpdating } =
		useTourSettings();
	const [showValidationAlert, setShowValidationAlert] = useState(false);

	// Create extended form schema with additional validation
	const formSchema = invoiceConfigurationBaseSchema.extend({
		// Add stricter validation for the number format
		numberFormat: invoiceConfigurationBaseSchema.shape.numberFormat.refine(
			containsOnlyValidPlaceholders,
			"Format must consist only of valid placeholders: {YYYY}, {MM}, and {SEQ}",
		),
	});

	const form = useForm<TourSettingsFormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			prefix: "",
			leadingZeros: 4,
			numberFormat: "{YYYY}{MM}{SEQ}",
			counterReset: "YEARLY",
			lastNumber: 0,
		},
		mode: "onBlur",
		reValidateMode: "onChange",
	});

	// Watch form values for preview
	const watchedValues = useWatch({
		control: form.control,
		defaultValue: form.getValues(),
	});

	// Generate example invoice number based on current form values
	const exampleTransportOrderNumber = useMemo(() => {
		return generateExampleTransportOrderNumber(
			watchedValues.prefix,
			watchedValues.leadingZeros || 4,
			watchedValues.numberFormat || "{YYYY}{MM}{SEQ}",
		);
	}, [
		watchedValues.prefix,
		watchedValues.leadingZeros,
		watchedValues.numberFormat,
	]);

	// Update form values when data is loaded
	useEffect(() => {
		if (data) {
			form.reset({
				prefix: data.prefix ?? "",
				leadingZeros: data.leadingZeros,
				numberFormat: data.numberFormat,
				counterReset: data.counterReset as
					| "YEARLY"
					| "MONTHLY"
					| "NEVER",
				lastNumber: data.lastNumber ?? 0,
			});

			// Trigger validation after loading data to ensure prefilled values are validated
			setTimeout(() => {
				form.trigger();
			}, 0);
		}
	}, [data, form]);

	const onSubmit = async (values: TourSettingsFormValues) => {
		// Check if form is valid before submitting
		if (!form.formState.isValid) {
			// Form contains validation errors, show them to the user
			// by triggering validation display
			form.trigger();
			setShowValidationAlert(true);
			return; // Prevent submission
		}

		// Hide validation alert when submitting valid form
		setShowValidationAlert(false);

		// Only proceed if validation passes
		try {
			// Pass form values to the update function
			// Backend will handle lastNumber and lastDate fields
			await updateTourSettings(values);
		} catch (error) {
			// API error handling is already done in the mutation hook
			console.error("Failed to update tour settings", error);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full">
				<CardContent className="pt-6">
					<div className="flex items-center justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>Transport Order Number Configuration</CardTitle>
				<CardDescription>
					Configure how transport order numbers are generated for your
					organization.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Alert className="mb-6">
					<InfoIcon className="h-4 w-4" />
					<AlertDescription>
						<span className="font-medium">
							Example transport order number:
						</span>{" "}
						{exampleTransportOrderNumber}
					</AlertDescription>
				</Alert>

				{showValidationAlert && (
					<Alert className="mb-6 border-destructive" variant="error">
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							Please correct the validation errors before saving.
						</AlertDescription>
					</Alert>
				)}

				<Form {...form}>
					<form
						id="tour-settings-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="prefix"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Invoice Prefix</FormLabel>
									<FormControl>
										<Input
											placeholder="TO"
											{...field}
											value={field.value || ""}
											disabled={isUpdating}
										/>
									</FormControl>
									<FormDescription>
										Optional prefix for transport order
										numbers (e.g., TO, FACT). Will be
										automatically added before the number
										format.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="leadingZeros"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Leading Zeros</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											max={10}
											{...field}
											disabled={isUpdating}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("leadingZeros");
											}}
											className={
												form.formState.errors
													.leadingZeros
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Number of leading zeros in the
										sequential part (e.g., 4 = "0001")
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="numberFormat"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Number Format</FormLabel>
									<FormControl>
										<Input
											placeholder="{YYYY}{MM}{SEQ}"
											{...field}
											onChange={(e) => {
												field.onChange(e.target.value);
												// Validate immediately after changing
												form.trigger("numberFormat");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors
													.numberFormat
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Format template using placeholders:
										{"{YYYY}"} - Year (4 digits),
										{"{YY}"} - Year (2 digits),
										{"{MM}"} - Month,
										{"{SEQ}"} - Sequential number
										(required). Only these placeholders can
										be used, no other characters are
										allowed.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="counterReset"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Counter Reset</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
										disabled={isUpdating}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select when to reset the counter" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="YEARLY">
												Yearly
											</SelectItem>
											<SelectItem value="MONTHLY">
												Monthly
											</SelectItem>
											<SelectItem value="NEVER">
												Never
											</SelectItem>
										</SelectContent>
									</Select>
									<FormDescription>
										When to reset the sequential number
										counter
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="lastNumber"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Current Sequence Number
									</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											value={field.value || 0}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("lastNumber");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors.lastNumber
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										The current sequence number. Set this
										when migrating from another system to
										avoid duplicate invoice numbers.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
			</CardContent>
			<CardFooter>
				<Button
					type="submit"
					form="tour-settings-form"
					disabled={isUpdating}
				>
					{isUpdating && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					Save Configuration
				</Button>
			</CardFooter>
		</Card>
	);
}
