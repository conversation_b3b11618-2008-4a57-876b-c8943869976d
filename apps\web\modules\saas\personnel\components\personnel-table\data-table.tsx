"use client";

import { usePersonnelUI } from "@saas/personnel/context/personnel-ui-context";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { usePersonnel } from "../../hooks/use-personnel";
import type { PersonnelWithRelations } from "../../lib/types";
import { useColumns } from "./columns";

// Delete dialog component
function DeletePersonnelDialog() {
	const t = useTranslations();
	const {
		deletePersonnel,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = usePersonnelUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deletePersonnel) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent data-shortcuts-scope="personnel-delete-dialog">
				<AlertDialogHeader>
					<AlertDialogTitle>
						{t("app.personnel.delete.title")}
					</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							{t("app.personnel.delete.description")}
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deletePersonnel.firstName} {deletePersonnel.lastName}
						{deletePersonnel.email && (
							<span className="block text-sm text-muted-foreground">
								{deletePersonnel.email}
							</span>
						)}
					</div>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							{t("common.confirmation.cancel")}
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							{t("common.confirmation.delete")}
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// Table component with the delete dialog
export function PersonnelDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
	} = usePersonnel();
	const { setSelectedPersonnel } = usePersonnelUI();
	const columns = useColumns();
	const personnel: PersonnelWithRelations[] = data?.items ?? [];
	const t = useTranslations();

	return (
		<>
			<DataTable
				columns={columns}
				data={personnel}
				defaultColumnVisibility={{
					id: false,
					createdAt: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder={t("app.personnel.table.searchPlaceholder")}
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="personnel-shortcuts"
			/>
			<DeletePersonnelDialog />
		</>
	);
}
