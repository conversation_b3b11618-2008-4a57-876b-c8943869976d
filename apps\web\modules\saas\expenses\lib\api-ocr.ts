import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

// Types for the API response
interface ExtractedInvoiceData {
	supplierName: string;
	supplierAddress?: string;
	invoiceNumber?: string;
	invoiceDate?: string;
	dueDate?: string;
	totalAmount?: number;
	currency?: string;
	vatAmount?: number;
	lineItems?: Array<{
		description: string;
		quantity?: number;
		unitPrice?: number;
		totalPrice: number;
		vatRate?: number;
	}>;
}

interface ValidationWarning {
	field: string;
	message: string;
	suggestedValue?: string;
}

interface ExpensePrefillData {
	supplierCounterpartyId?: string;
	supplier_invoice_number?: string;
	expense_date?: string;
	due_date?: string;
	totalAmount?: number;
	totalVat?: number;
	totalGross?: number;
	currency?: string;
	lineItems?: Array<{
		description: string;
		quantity?: number;
		unitPrice?: number;
		totalPrice: number;
		vatRate?: number;
		vatAmount?: number;
	}>;
}

interface InvoiceOCRResponse {
	success: boolean;
	extractedData: ExtractedInvoiceData;
	prefillData: ExpensePrefillData;
	warnings: ValidationWarning[];
}

// Query keys
export const invoiceOCRKeys = {
	all: ["invoice-ocr"] as const,
	extract: (organizationId: string) =>
		[...invoiceOCRKeys.all, "extract", organizationId] as const,
};

// API function
async function extractInvoiceData(
	file: File,
	organizationId: string,
): Promise<InvoiceOCRResponse> {
	const formData = new FormData();
	formData.append("file", file);
	formData.append("organizationId", organizationId);

	const response = await fetch("/api/validators/invoice", {
		method: "POST",
		body: formData,
	});

	if (!response.ok) {
		const errorData = await response
			.json()
			.catch(() => ({ message: "Unknown error" }));
		throw new Error(
			errorData.message ||
				`HTTP ${response.status}: ${response.statusText}`,
		);
	}

	return response.json();
}

// Hook
export function useInvoiceOCRMutation(
	organizationId: string,
	onDataExtracted?: (data: InvoiceOCRResponse) => void,
) {
	return useMutation({
		mutationFn: (file: File) => extractInvoiceData(file, organizationId),
		onSuccess: (data) => {
			toast.success("Invoice data extracted successfully!");

			// Show warnings if any
			if (data.warnings?.length > 0) {
				for (const warning of data.warnings) {
					toast.warning(warning.message);
				}
			}

			// Call custom callback if provided
			onDataExtracted?.(data);
		},
		onError: (error: Error) => {
			console.error("OCR extraction failed:", error);
			toast.error(`Failed to extract invoice data: ${error.message}`);
		},
	});
}
