"use client";
import { useInvoicesUI } from "@saas/invoices/context/invoices-ui-context";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { InputWithDate } from "@ui/components/input-with-date";
import { Label } from "@ui/components/label";
import { format } from "date-fns";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { useInvoices } from "../../hooks/use-invoice";
import { useColumns } from "./columns";

// Delete dialog component
function DeleteInvoiceDialog() {
	const t = useTranslations();
	const {
		deleteInvoice,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = useInvoicesUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteInvoice) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent data-shortcuts-scope="invoices-delete-dialog">
				<AlertDialogHeader>
					<AlertDialogTitle>Cancel Invoice</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							Are you sure you want to cancel this invoice? This
							action cannot be undone.
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deleteInvoice.invoice_number || deleteInvoice.id} -{" "}
						{deleteInvoice.status || "Unknown"}
						<span className="block text-sm text-muted-foreground">
							{deleteInvoice.customer_name || "Unknown customer"}
						</span>
					</div>
				</AlertDialogHeader>

				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							Confirm
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// Mark as Paid dialog component
function MarkAsPaidDialog() {
	const t = useTranslations();
	const {
		selectedInvoice,
		isMarkPaidDialogOpen,
		handleConfirmMarkPaid,
		paymentDate,
		setPaymentDate,
		setMarkPaidDialogOpen,
	} = useInvoicesUI();

	if (!selectedInvoice) {
		return null;
	}

	return (
		<AlertDialog
			open={isMarkPaidDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					setMarkPaidDialogOpen(false);
				}
			}}
		>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Mark Invoice as Paid</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							Record payment for this invoice. You can select the
							payment date.
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{selectedInvoice.invoice_number || selectedInvoice.id}{" "}
						<span className="block text-sm text-muted-foreground">
							{selectedInvoice.customer_name ||
								"Unknown customer"}
						</span>
					</div>
				</AlertDialogHeader>

				<div className="my-4">
					<Label htmlFor="payment-date" className="mb-2 block">
						Payment Date
					</Label>
					<InputWithDate
						inputProps={{
							id: "payment-date",
							value: paymentDate
								? format(paymentDate, "dd.MM.yyyy")
								: "",
							className: "w-full",
						}}
						onDateChange={(date) =>
							setPaymentDate(date instanceof Date ? date : null)
						}
					/>
				</div>

				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							onClick={() => setMarkPaidDialogOpen(false)}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction onClick={handleConfirmMarkPaid}>
							Mark as Paid
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// New PDF viewer dialog component
function InvoicePdfDialog() {
	const {
		isViewPdfDialogOpen,
		setViewPdfDialogOpen,
		viewInvoiceId,
		viewInvoicePdfData,
		isLoadingPdf,
	} = useInvoicesUI();

	return (
		<Dialog
			open={isViewPdfDialogOpen}
			onOpenChange={(open) => {
				if (!open) {
					setViewPdfDialogOpen(false);
				}
			}}
		>
			<DialogContent className="max-w-5xl max-h-[90vh]">
				<DialogHeader>
					<DialogTitle>Invoice Details</DialogTitle>
				</DialogHeader>
				<div className="h-[80vh]">
					{isLoadingPdf ? (
						<div className="flex items-center justify-center h-full">
							<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
						</div>
					) : viewInvoicePdfData ? (
						<iframe
							src={`data:application/pdf;base64,${viewInvoicePdfData}`}
							width="100%"
							height="100%"
							title="Invoice Details"
						/>
					) : (
						<div className="flex items-center justify-center h-full text-muted-foreground">
							Failed to load invoice PDF
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

// Table component with the delete dialog
export function InvoicesDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange,
	} = useInvoices();
	const { setSelectedInvoice } = useInvoicesUI();
	const columns = useColumns();

	// Convert date strings to Date objects with explicit type conversion
	const rawItems = data?.items ?? [];
	const invoices = rawItems.map((item: any) => ({
		...item,
		createdAt: new Date(item.createdAt),
		invoice_date: item.invoice_date ? new Date(item.invoice_date) : null,
		due_date: item.due_date ? new Date(item.due_date) : null,
		payment_date: item.payment_date ? new Date(item.payment_date) : null,
		financialData: item.financialData || [],
	}));

	const t = useTranslations();

	return (
		<>
			<DataTable
				columns={columns}
				data={invoices}
				defaultColumnVisibility={{
					id: false,
					createdAt: false,
					payment_date: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder="Search invoices..."
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="invoices-shortcuts"
			/>
			<DeleteInvoiceDialog />
			<MarkAsPaidDialog />
			<InvoicePdfDialog />
		</>
	);
}
