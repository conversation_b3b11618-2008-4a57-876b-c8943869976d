import { db } from "@repo/database";

// Generate invoice number based on organization's invoice configuration
export async function generateInvoiceNumber(organizationId: string) {
	try {
		// Get the organization's invoice configuration
		const organization = await db.organization.findUnique({
			where: { id: organizationId },
			include: { invoiceConfiguration: true },
		});

		if (!organization) {
			throw new Error("Organization not found");
		}

		// Use default values if no configuration exists
		const config = organization.invoiceConfiguration || {
			prefix: "",
			leadingZeros: 4,
			numberFormat: "{YYYY}{MM}{SEQ}",
			counterReset: "YEARLY",
			lastNumber: 0,
			lastDate: null,
		};

		// Current date for generating the invoice number
		const now = new Date();

		// Determine if we need to reset the counter based on lastDate and counterReset setting
		let nextNumber = config.lastNumber + 1;

		if (config.lastDate && config.counterReset !== "NEVER") {
			const lastDate = new Date(config.lastDate);

			if (
				config.counterReset === "YEARLY" &&
				lastDate.getFullYear() < now.getFullYear()
			) {
				// Reset counter for new year
				nextNumber = 1;
			} else if (
				config.counterReset === "MONTHLY" &&
				(lastDate.getFullYear() < now.getFullYear() ||
					lastDate.getMonth() < now.getMonth())
			) {
				// Reset counter for new month
				nextNumber = 1;
			}
		}

		// Format the sequential number with leading zeros
		const sequentialNumber = nextNumber
			.toString()
			.padStart(config.leadingZeros, "0");

		// Get date components for the format
		const year = now.getFullYear().toString();
		const shortYear = year.substring(2);
		const month = (now.getMonth() + 1).toString().padStart(2, "0");

		// Replace placeholders in the format string
		let invoiceNumber = config.numberFormat
			.replace(/\{YYYY\}/g, year)
			.replace(/\{YY\}/g, shortYear)
			.replace(/\{MM\}/g, month)
			.replace(/\{SEQ\}/g, sequentialNumber);

		// Simple prefix logic - prepend prefix if it exists
		if (config.prefix) {
			invoiceNumber = config.prefix + invoiceNumber;
		}

		// Update the configuration with the new lastNumber and lastDate
		await db.invoiceConfiguration.upsert({
			where: {
				organizationId,
			},
			create: {
				organizationId,
				prefix: config.prefix,
				leadingZeros: config.leadingZeros,
				numberFormat: config.numberFormat,
				counterReset: config.counterReset,
				lastNumber: nextNumber,
				lastDate: now,
			},
			update: {
				lastNumber: nextNumber,
				lastDate: now,
			},
		});

		return invoiceNumber;
	} catch (error) {
		console.error("Failed to generate invoice number:", error);
		throw new Error("Failed to generate invoice number");
	}
}
