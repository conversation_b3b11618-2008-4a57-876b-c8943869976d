import { ACTIONS, ENTITY_TYPES, db, logAuditEvent } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	createCreditNote,
	deleteCreditNote,
	getCreditNoteById,
	getCreditNoteWithDocument,
	listCreditNotes,
	updateCreditNote,
} from "./lib/credit-note";
import { createCreditNoteSchema, updateCreditNoteSchema } from "./types";

export const customerCreditNotesRouter = new Hono()
	.basePath("/customer-credit-notes")
	// Get all credit notes for organization with optional filters
	.get(
		"/",
		authMiddleware,
		validator(
			"query",
			z.object({
				organizationId: z.string(),
				customerId: z.string().optional(),
				orderId: z.string().optional(),
				search: z.string().optional(),
				page: z.string().optional().transform(Number),
				limit: z.string().optional().transform(Number),
				startDate: z
					.string()
					.optional()
					.transform((date) => (date ? new Date(date) : undefined)),
				endDate: z
					.string()
					.optional()
					.transform((date) => (date ? new Date(date) : undefined)),
			}),
		),
		describeRoute({
			tags: ["Customer Credit Notes"],
			summary: "List credit notes",
			description:
				"List all credit notes for an organization with optional filtering",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "customerId",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "orderId",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "search",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "number" },
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: { type: "number" },
				},
				{
					name: "startDate",
					in: "query",
					required: false,
					schema: { type: "string", format: "date" },
				},
				{
					name: "endDate",
					in: "query",
					required: false,
					schema: { type: "string", format: "date" },
				},
			],
			responses: {
				200: {
					description: "List of credit notes",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
			},
		}),
		async (c) => {
			const {
				organizationId,
				customerId,
				orderId,
				search,
				page,
				limit,
				startDate,
				endDate,
			} = c.req.valid("query");
			const user = c.get("user");

			try {
				// Check if user has access to the organization
				await verifyOrganizationMembership(organizationId, user.id);

				// Get credit notes with filters
				const creditNotes = await listCreditNotes(organizationId, {
					customerId,
					orderId,
					search,
					page,
					limit,
					startDate,
					endDate,
				});

				return c.json(creditNotes);
			} catch (error) {
				console.error("Error listing credit notes:", error);
				if (error instanceof HTTPException) {
					return c.json(
						{
							success: false,
							error: { message: error.message },
						},
						error.status,
					);
				}
				return c.json(
					{
						success: false,
						error: { message: "Server error processing request" },
					},
					500,
				);
			}
		},
	)
	// Get a specific credit note by ID
	.get(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
				includeDocument: z
					.enum(["true", "false"])
					.optional()
					.transform((val) => val === "true"),
			}),
		),
		describeRoute({
			tags: ["Customer Credit Notes"],
			summary: "Get credit note",
			description: "Get details of a specific credit note by ID",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "includeDocument",
					in: "query",
					required: false,
					schema: { type: "boolean" },
					description: "Include document with signed URL in response",
				},
			],
			responses: {
				200: {
					description: "Credit note details",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Credit note not found",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { organizationId, includeDocument } = c.req.valid("query");
			const user = c.get("user");

			// Check if user has access to the organization
			await verifyOrganizationMembership(organizationId, user.id);

			// Get credit note by ID (with or without document)
			const creditNote = includeDocument
				? await getCreditNoteWithDocument(organizationId, id)
				: await getCreditNoteById(organizationId, id);

			return c.json(creditNote);
		},
	)
	// Create a new credit note
	.post(
		"/",
		authMiddleware,
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Customer Credit Notes"],
			summary: "Create credit note",
			description:
				"Create a new credit note with optional document upload",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			requestBody: {
				required: true,
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								customerId: { type: "string" },
								credit_note_number: { type: "string" },
								credit_note_date: {
									type: "string",
									format: "date-time",
								},
								description: { type: "string" },
								gross_amount: { type: "number" },
								net_amount: { type: "number" },
								// VAT tracked per line item, not document level
								orderIds: {
									type: "array",
									items: { type: "string" },
								},
								order_allocations: {
									type: "array",
									items: {
										type: "object",
										properties: {
											orderId: { type: "string" },
											gross_amount: { type: "number" },
											net_amount: { type: "number" },
											// VAT tracked per line item, not allocation level
											lineItems: {
												type: "array",
												items: {
													type: "object",
													properties: {
														orderLineItemId: {
															type: "string",
														},
														quantity: {
															type: "number",
														},
														unitPrice: {
															type: "number",
														},
														totalPrice: {
															type: "number",
														},
														description: {
															type: "string",
														},
													},
													required: [
														"orderLineItemId",
														"totalPrice",
													],
												},
											},
										},
									},
								},
							},
							required: [
								"customerId",
								"credit_note_date",
								"gross_amount",
								"net_amount",
								// VAT not required at document level
							],
						},
					},
					"multipart/form-data": {
						schema: {
							type: "object",
							properties: {
								data: {
									type: "string",
									description:
										"JSON string of credit note data",
								},
								document: {
									type: "string",
									format: "binary",
									description: "Credit note document file",
								},
							},
							required: ["data"],
						},
					},
				},
			},
			responses: {
				201: {
					description: "Credit note created successfully",
				},
				400: {
					description: "Invalid credit note data",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
			},
		}),
		async (c) => {
			try {
				const { organizationId } = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(organizationId, user.id);

				let creditNoteData = {};
				let documentFile: File | undefined;

				// Handle multipart form data (for file upload)
				if (
					c.req.raw.headers
						.get("content-type")
						?.includes("multipart/form-data")
				) {
					const formData = await c.req.formData();
					const jsonData = formData.get("data");
					if (jsonData && typeof jsonData === "string") {
						creditNoteData = JSON.parse(jsonData);
					}
					documentFile =
						(formData.get("document") as File) || undefined;
				}
				// Handle regular JSON
				else {
					creditNoteData = await c.req.json();
				}

				// Validate credit note data
				const validatedData =
					createCreditNoteSchema.parse(creditNoteData);

				// Create the credit note with document if provided
				const data = {
					...validatedData,
					documentFile,
				};

				const createdCreditNote = await createCreditNote(
					organizationId,
					user.id,
					data,
				);

				// Log the audit event
				await logAuditEvent({
					organizationId,
					userId: user.id,
					entityType: ENTITY_TYPES.CREDIT_NOTE,
					entityId: createdCreditNote?.id || "",
					action: ACTIONS.CREATE,
					payload: {
						creditNoteData: validatedData,
						documentUploaded: !!documentFile,
					},
				});

				return c.json(createdCreditNote, 201);
			} catch (error) {
				console.error("Error creating credit note:", error);
				if (error instanceof HTTPException) {
					return c.json(
						{
							success: false,
							error: { message: error.message },
						},
						error.status,
					);
				}
				return c.json(
					{
						success: false,
						error: { message: "Server error processing request" },
					},
					500,
				);
			}
		},
	)
	// Update a credit note
	.put(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Customer Credit Notes"],
			summary: "Update credit note",
			description:
				"Update an existing credit note with optional document changes",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			requestBody: {
				required: true,
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								credit_note_number: { type: "string" },
								credit_note_date: {
									type: "string",
									format: "date-time",
								},
								description: { type: "string" },
								gross_amount: { type: "number" },
								net_amount: { type: "number" },
								// VAT tracked per line item, not document level
								removeDocument: { type: "boolean" },
								order_allocations: {
									type: "array",
									items: {
										type: "object",
										properties: {
											id: { type: "string" },
											orderId: { type: "string" },
											gross_amount_applied_to_order: {
												type: "number",
											},
											net_amount_applied_to_order: {
												type: "number",
											},
											// VAT tracked per line item, not allocation level
											amount_delta: { type: "number" },
											amount_delta_reason: {
												type: "string",
											},
											lineItems: {
												type: "array",
												items: {
													type: "object",
													properties: {
														orderLineItemId: {
															type: "string",
														},
														quantity: {
															type: "number",
														},
														unitPrice: {
															type: "number",
														},
														totalPrice: {
															type: "number",
														},
														description: {
															type: "string",
														},
													},
													required: [
														"orderLineItemId",
														"totalPrice",
													],
												},
											},
										},
									},
								},
							},
						},
					},
					"multipart/form-data": {
						schema: {
							type: "object",
							properties: {
								data: {
									type: "string",
									description:
										"JSON string of credit note update data",
								},
								document: {
									type: "string",
									format: "binary",
									description:
										"New credit note document file",
								},
							},
							required: ["data"],
						},
					},
				},
			},
			responses: {
				200: {
					description: "Credit note updated successfully",
				},
				400: {
					description: "Invalid update data",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Credit note not found",
				},
			},
		}),
		async (c) => {
			try {
				const { id } = c.req.valid("param");
				const { organizationId } = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(organizationId, user.id);

				// Fetch existing credit note for audit log
				const existingCreditNote = await getCreditNoteById(
					organizationId,
					id,
				);

				let updateData = {};
				let documentFile: File | undefined;

				// Handle multipart form data
				if (
					c.req.raw.headers
						.get("content-type")
						?.includes("multipart/form-data")
				) {
					const formData = await c.req.formData();
					const jsonData = formData.get("data");
					if (jsonData && typeof jsonData === "string") {
						updateData = JSON.parse(jsonData);
					}
					documentFile =
						(formData.get("document") as File) || undefined;
				}
				// Handle regular JSON
				else {
					updateData = await c.req.json();
				}

				// Validate update data
				const validatedData = updateCreditNoteSchema.parse(updateData);

				// Prepare update data with document if provided
				const data = {
					...validatedData,
					documentFile,
				};

				// Update the credit note
				const updatedCreditNote = await updateCreditNote(
					organizationId,
					id,
					data,
				);

				// Log the audit event
				await logAuditEvent({
					organizationId,
					userId: user.id,
					entityType: ENTITY_TYPES.CREDIT_NOTE,
					entityId: id,
					action: ACTIONS.UPDATE,
					payload: {
						before: existingCreditNote,
						after: updatedCreditNote,
						changes: validatedData,
						documentUploaded: !!documentFile,
						documentRemoved: !!validatedData.removeDocument,
					},
				});

				return c.json(updatedCreditNote);
			} catch (error) {
				console.error("Error updating credit note:", error);
				if (error instanceof HTTPException) {
					return c.json(
						{
							success: false,
							error: { message: error.message },
						},
						error.status,
					);
				}
				return c.json(
					{
						success: false,
						error: { message: "Server error processing request" },
					},
					500,
				);
			}
		},
	)
	// Delete a credit note
	.delete(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Customer Credit Notes"],
			summary: "Delete credit note",
			description: "Soft-delete a credit note and associated document",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			responses: {
				200: {
					description: "Credit note deleted successfully",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Credit note not found",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const { organizationId } = c.req.valid("query");
			const user = c.get("user");

			try {
				// Check if user has access to the organization
				await verifyOrganizationMembership(organizationId, user.id);

				// Get credit note before deletion for audit log
				const creditNoteToDelete = await getCreditNoteById(
					organizationId,
					id,
				);

				// Check if credit note has an associated document
				const document = await db.document.findFirst({
					where: {
						entityType: "CreditNote",
						entityId: id,
					},
				});

				// Delete the credit note
				const result = await deleteCreditNote(organizationId, id);

				// Log the audit event
				await logAuditEvent({
					organizationId,
					userId: user.id,
					entityType: ENTITY_TYPES.CREDIT_NOTE,
					entityId: id,
					action: ACTIONS.DELETE,
					payload: {
						deletedCreditNote: creditNoteToDelete,
						documentDeleted: document ? document.id : null,
					},
				});

				return c.json({
					success: true,
					id: result.id,
					documentDeleted: !!document,
				});
			} catch (error) {
				console.error("Error deleting credit note:", error);
				if (error instanceof HTTPException) {
					return c.json(
						{
							success: false,
							error: { message: error.message },
						},
						error.status,
					);
				}
				return c.json(
					{
						success: false,
						error: { message: "Server error processing request" },
					},
					500,
				);
			}
		},
	);
