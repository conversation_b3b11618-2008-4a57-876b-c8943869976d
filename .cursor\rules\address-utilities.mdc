---
description: Helpful for address utlitites and badge system
globs: 
alwaysApply: false
---
# Address Utilities & Badge System

## Badge Component Usage

**System Badge (UI Component):**
```typescript
// Located: apps/web/modules/ui/components/badge.tsx
// Uses `status` prop: success|info|warning|error
<Badge status="success" className="...">Content</Badge>
```

**Custom Badge Styling (Address Types):**
```typescript
// Use span elements for custom badge types
<span className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300">
  {React.createElement(IconComponent, { className: "h-3 w-3" })}
  TYPE_NAME
</span>
```

## Address Utilities

**Import Pattern:**
```typescript
import {
  getAddressTypeIcon,
  getAddressTypeBadgeClass,
  formatAddressForDisplay,
  selectBestPrimaryAddress,
  type Address,
} from "@saas/shared/lib/address-utils";
```

**Address Badge Rendering:**
```typescript
<span className={cn("px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1", getAddressTypeBadgeClass(address.type))}>
  {React.createElement(getAddressTypeIcon(address.type), { className: "h-3 w-3" })}
  {address.type}
</span>
```

**Priority Selection:**
```typescript
const bestAddress = selectBestPrimaryAddress(primaryAddresses);
// Priority: PRIMARY+default → PRIMARY
```

## Address Types & Colors

- **PRIMARY**: Blue with Building icon
- **LOADING**: Green with Truck icon
- **UNLOADING**: Purple with PackageCheck icon

## Rules

1. **Never duplicate badge styling** - always use shared utilities
2. **Always include dark mode support** - utilities handle this automatically
3. **Use React.createElement for dynamic icons** - ensures proper TypeScript support
4. **Consistent icon sizing**: `h-3 w-3` for badges, `h-4 w-4` for larger displays
5. **Always use address utilities instead of local implementations**

