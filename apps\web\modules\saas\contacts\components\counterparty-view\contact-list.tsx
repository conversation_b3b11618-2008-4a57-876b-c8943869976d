"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useContactMutations } from "@saas/contacts/hooks/use-counterparty";
import { useContactsQuery } from "@saas/contacts/lib/api-contacts";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	AlertTriangle,
	Edit2,
	Globe,
	Loader2,
	Mail,
	MoreHorizontal,
	Phone,
	Plus,
	Trash2,
	UserPlus,
	X,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

interface Contact {
	id: string;
	firstName: string;
	lastName: string;
	email?: string | null;
	telephone?: string | null;
	position?: string | null;
	department?: string | null;
	isActive: boolean;
	salutation?: string | null;
	languages?: Array<{
		code: string;
		level: string;
	}> | null;
}

interface Counterparty {
	id: string;
	email?: string | null;
	[key: string]: any;
}

interface ContactListProps {
	counterpartyId: string;
	counterparty?: Counterparty;
	onEditContact?: (contactId: string) => void;
	onCreateContact?: () => void;
}

const contactLanguageSchema = z.object({
	code: z.string().min(2, "Language code is required"),
	level: z.enum(["BASIC", "INTERMEDIATE", "FLUENT", "NATIVE"]),
});

const contactFormSchema = z.object({
	firstName: z.string().min(1, "First name is required"),
	lastName: z.string().min(1, "Last name is required"),
	position: z.string().optional(),
	department: z.string().optional(),
	email: z
		.string()
		.email("Invalid email address")
		.optional()
		.or(z.literal("")),
	telephone: z.string().optional().or(z.literal("")),
	salutation: z.enum(["mr", "mrs", "other"]).optional(),
	isActive: z.boolean().default(true),
	languages: z.array(contactLanguageSchema).optional(),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

// Available language options
const languageOptions = [
	{ value: "en", label: "English" },
	{ value: "de", label: "German" },
	{ value: "fr", label: "French" },
	{ value: "es", label: "Spanish" },
	{ value: "it", label: "Italian" },
	{ value: "nl", label: "Dutch" },
];

// Language proficiency levels
const proficiencyLevels = [
	{ value: "BASIC", label: "Basic" },
	{ value: "INTERMEDIATE", label: "Intermediate" },
	{ value: "FLUENT", label: "Fluent" },
	{ value: "NATIVE", label: "Native" },
];

export function ContactList({
	counterpartyId,
	counterparty,
	onEditContact,
	onCreateContact,
}: ContactListProps) {
	const t = useTranslations();
	const { activeOrganization } = useActiveOrganization();
	const { deleteContact, createContact, updateContact } =
		useContactMutations(counterpartyId);
	const [contactToDelete, setContactToDelete] = useState<string | null>(null);
	const [isDeleting, setIsDeleting] = useState(false);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [editingContactId, setEditingContactId] = useState<string | null>(
		null,
	);

	// For the universal form
	const form = useForm<ContactFormValues>({
		resolver: zodResolver(contactFormSchema),
		defaultValues: {
			firstName: "",
			lastName: "",
			position: "",
			department: "",
			email: "",
			telephone: "",
			isActive: true,
			languages: [],
		},
	});

	// For languages field array
	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: "languages",
	});

	// Fetch contacts data
	const {
		data: contacts = [],
		isLoading: isLoadingContacts,
		error,
		refetch,
	} = useContactsQuery(activeOrganization?.id || "", counterpartyId);

	// Helper function to extract domain from email
	const getEmailDomain = (email: string): string => {
		if (!email || !email.includes("@")) return "";
		return email.split("@")[1].toLowerCase();
	};

	// Helper function to check if email domains match
	const hasEmailDomainMismatch = (contactEmail: string): boolean => {
		if (!contactEmail || !counterparty?.email) return false;

		const contactDomain = getEmailDomain(contactEmail);
		const counterpartyDomain = getEmailDomain(counterparty.email);

		return (
			contactDomain !== "" &&
			counterpartyDomain !== "" &&
			contactDomain !== counterpartyDomain
		);
	};

	// Watch the email field for real-time validation
	const watchedEmail = form.watch("email");
	const showDomainWarning =
		watchedEmail && hasEmailDomainMismatch(watchedEmail);

	const handleOpenCreateDialog = () => {
		setEditingContactId(null);
		form.reset({
			firstName: "",
			lastName: "",
			position: "",
			department: "",
			email: "",
			telephone: "",
			salutation: undefined,
			isActive: true,
			languages: [],
		});
		setIsDialogOpen(true);
	};

	const handleOpenEditDialog = (contactId: string) => {
		const contactToEdit = contacts.find(
			(contact) => contact.id === contactId,
		);
		if (!contactToEdit) {
			return;
		}

		setEditingContactId(contactId);
		form.reset({
			firstName: contactToEdit.firstName,
			lastName: contactToEdit.lastName,
			email: contactToEdit.email || "",
			telephone: contactToEdit.telephone || "",
			position: contactToEdit.position || "",
			department: contactToEdit.department || "",
			salutation: (contactToEdit.salutation as any) || undefined,
			isActive: contactToEdit.isActive,
			// Map languages to match the form's expected format
			languages: contactToEdit.languages
				? contactToEdit.languages.map((lang) => ({
						code: lang.code,
						level: lang.level as
							| "BASIC"
							| "INTERMEDIATE"
							| "FLUENT"
							| "NATIVE",
					}))
				: [],
		});

		setIsDialogOpen(true);
	};

	const handleAddLanguage = () => {
		append({ code: "", level: "BASIC" });
	};

	const handleSubmitContact = async (values: ContactFormValues) => {
		setIsSubmitting(true);
		try {
			// Make sure languages don't have duplicates by code
			const uniqueLanguages =
				values.languages && values.languages.length > 0
					? Array.from(
							new Map(
								values.languages.map((lang) => [
									lang.code,
									lang,
								]),
							).values(),
						)
					: undefined;

			// Clean up data, only including fields that should be sent
			const baseData = {
				firstName: values.firstName,
				lastName: values.lastName,
				email: values.email || undefined,
				telephone: values.telephone || undefined,
				position: values.position || undefined,
				department: values.department || undefined,
				salutation: values.salutation,
				isActive: values.isActive,
				languages: uniqueLanguages,
				counterpartyId,
			};

			// For debugging
			console.log("Submitting contact data:", {
				isEdit: !!editingContactId,
				data: baseData,
			});

			if (editingContactId) {
				// For update, only send the data and ID
				await updateContact({
					...baseData,
					id: editingContactId,
				});
			} else {
				// For create, include the counterpartyId (already in baseData)
				await createContact(baseData);
			}

			setIsDialogOpen(false);
			refetch();
		} catch (error) {
			console.error("Contact submission error:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleDelete = async (contactId: string) => {
		setContactToDelete(contactId);
	};

	const confirmDelete = async () => {
		if (!contactToDelete) {
			return;
		}

		setIsDeleting(true);
		try {
			await deleteContact(contactToDelete);
		} catch (error) {
			console.error(error);
		} finally {
			setIsDeleting(false);
			setContactToDelete(null);
		}
	};

	const cancelDelete = () => {
		setContactToDelete(null);
	};

	// Generate initials from first and last name
	const getInitials = (firstName: string, lastName: string) => {
		return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
	};

	// Get language name from code
	const getLanguageName = (code: string) => {
		return (
			languageOptions.find((option) => option.value === code)?.label ||
			code
		);
	};

	// Get proficiency level label
	const getProficiencyLabel = (level: string) => {
		return (
			proficiencyLevels.find((option) => option.value === level)?.label ||
			level
		);
	};

	// Filter out inactive contacts or show a badge for them
	const displayContacts = contacts || [];

	// Determine if we're in create or edit mode
	const isCreateMode = editingContactId === null;

	return (
		<div className="space-y-2">
			<div className="flex items-center justify-between">
				<h3 className="font-medium">Contact Persons</h3>
				<Button
					variant="ghost"
					size="sm"
					className="h-8 w-8 p-0"
					onClick={handleOpenCreateDialog}
				>
					<UserPlus className="h-4 w-4" />
					<span className="sr-only">Add Contact</span>
				</Button>
			</div>

			{isLoadingContacts ? (
				<div className="flex justify-center items-center py-6">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin" />
				</div>
			) : error ? (
				<div className="text-center py-4 text-sm text-destructive">
					Error loading contacts
				</div>
			) : displayContacts.length === 0 ? (
				<div className="text-center py-4 text-sm text-muted-foreground">
					No contacts found
				</div>
			) : (
				<ScrollArea className="h-[300px] pr-3 -mr-3">
					<div className="space-y-3">
						{displayContacts.map((contact) => (
							<div
								key={contact.id}
								className="flex items-start justify-between py-2"
							>
								<div className="flex items-start space-x-3">
									<Avatar className="h-8 w-8">
										<AvatarFallback className="text-xs">
											{getInitials(
												contact.firstName,
												contact.lastName,
											)}
										</AvatarFallback>
									</Avatar>
									<div>
										<p className="font-medium text-sm">
											{contact.firstName}{" "}
											{contact.lastName}
											{!contact.isActive && (
												<span className="ml-2 text-xs text-muted-foreground">
													(inactive)
												</span>
											)}
										</p>

										{(contact.position ||
											contact.department) && (
											<p className="text-xs text-muted-foreground">
												{contact.position}
												{contact.position &&
													contact.department &&
													", "}
												{contact.department}
											</p>
										)}

										<div className="flex flex-col mt-1">
											{contact.email && (
												<div className="flex items-center text-xs text-muted-foreground">
													<Mail className="h-3 w-3 mr-1 flex-shrink-0" />
													<span className="truncate">
														{contact.email}
													</span>
												</div>
											)}

											{contact.telephone && (
												<div className="flex items-center text-xs text-muted-foreground">
													<Phone className="h-3 w-3 mr-1 flex-shrink-0" />
													<span>
														{contact.telephone}
													</span>
												</div>
											)}

											{contact.languages &&
												contact.languages.length >
													0 && (
													<div className="flex items-center mt-1 gap-1 flex-wrap">
														<Globe className="h-3 w-3 mr-1 text-muted-foreground flex-shrink-0" />
														{contact.languages.map(
															(lang, index) => (
																<Badge
																	key={index}
																	status="info"
																	className="text-xs px-1 py-0"
																>
																	{getLanguageName(
																		lang.code,
																	)}{" "}
																	(
																	{getProficiencyLabel(
																		lang.level,
																	)}
																	)
																</Badge>
															),
														)}
													</div>
												)}
										</div>
									</div>
								</div>

								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											className="h-8 w-8 p-0"
										>
											<MoreHorizontal className="h-4 w-4" />
											<span className="sr-only">
												Actions
											</span>
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent align="end">
										<DropdownMenuItem
											onClick={() =>
												handleOpenEditDialog(contact.id)
											}
										>
											<Edit2 className="h-3.5 w-3.5 mr-2" />
											Edit
										</DropdownMenuItem>
										<DropdownMenuItem
											className="text-destructive"
											onClick={() =>
												handleDelete(contact.id)
											}
										>
											<Trash2 className="h-3.5 w-3.5 mr-2" />
											Delete
										</DropdownMenuItem>
									</DropdownMenuContent>
								</DropdownMenu>
							</div>
						))}
					</div>
				</ScrollArea>
			)}

			{/* Delete Confirmation Dialog */}
			<AlertDialog
				open={contactToDelete !== null}
				onOpenChange={cancelDelete}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Are you absolutely sure?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently
							delete the contact from our servers.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDelete}
							disabled={isDeleting}
						>
							{isDeleting ? "Deleting..." : "Delete"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{/* Universal Contact Dialog */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{isCreateMode ? "Add Contact" : "Edit Contact"}
						</DialogTitle>
					</DialogHeader>
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleSubmitContact)}
							className="space-y-4"
						>
							<div className="grid grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="salutation"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Salutation</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="mr">
														Mr.
													</SelectItem>
													<SelectItem value="mrs">
														Mrs.
													</SelectItem>
													<SelectItem value="other">
														Other
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="firstName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>First Name*</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="lastName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Last Name*</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email</FormLabel>
										<FormControl>
											<Input {...field} type="email" />
										</FormControl>
										{showDomainWarning && (
											<div className="flex items-center gap-2 mt-1 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
												<AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
												<p className="text-xs text-yellow-700 dark:text-yellow-300">
													Email domain (
													{getEmailDomain(
														watchedEmail,
													)}
													) differs from contact
													domain (
													{getEmailDomain(
														counterparty?.email ||
															"",
													)}
													)
												</p>
											</div>
										)}
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="department"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Department</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="position"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Position</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="telephone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Telephone</FormLabel>
										<FormControl>
											<Input {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Languages Section */}
							<div className="space-y-2">
								<div className="flex items-center justify-between">
									<FormLabel>Languages</FormLabel>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={handleAddLanguage}
										className="h-8"
									>
										<Plus className="h-3.5 w-3.5 mr-1" />
										Add Language
									</Button>
								</div>

								{fields.length === 0 ? (
									<p className="text-sm text-muted-foreground">
										No languages added
									</p>
								) : (
									<div className="space-y-2">
										{fields.map((field, index) => (
											<div
												key={field.id}
												className="flex items-center space-x-2"
											>
												<FormField
													control={form.control}
													name={`languages.${index}.code`}
													render={({ field }) => (
														<FormItem className="flex-1">
															<Select
																onValueChange={
																	field.onChange
																}
																defaultValue={
																	field.value
																}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Select language" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	{languageOptions.map(
																		(
																			option,
																		) => (
																			<SelectItem
																				key={
																					option.value
																				}
																				value={
																					option.value
																				}
																			>
																				{
																					option.label
																				}
																			</SelectItem>
																		),
																	)}
																</SelectContent>
															</Select>
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name={`languages.${index}.level`}
													render={({ field }) => (
														<FormItem className="flex-1">
															<Select
																onValueChange={
																	field.onChange
																}
																defaultValue={
																	field.value
																}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Proficiency" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	{proficiencyLevels.map(
																		(
																			option,
																		) => (
																			<SelectItem
																				key={
																					option.value
																				}
																				value={
																					option.value
																				}
																			>
																				{
																					option.label
																				}
																			</SelectItem>
																		),
																	)}
																</SelectContent>
															</Select>
														</FormItem>
													)}
												/>

												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={() =>
														remove(index)
													}
													className="px-2"
												>
													<X className="h-4 w-4" />
												</Button>
											</div>
										))}
									</div>
								)}
							</div>

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={() => setIsDialogOpen(false)}
								>
									Cancel
								</Button>
								<Button type="submit" disabled={isSubmitting}>
									{isSubmitting
										? isCreateMode
											? "Creating..."
											: "Saving..."
										: isCreateMode
											? "Create Contact"
											: "Save Changes"}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</DialogContent>
			</Dialog>
		</div>
	);
}
