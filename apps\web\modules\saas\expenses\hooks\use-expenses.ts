import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useQueryClient } from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import {
	expenseKeys,
	useCreateExpenseMutation,
	useDeleteExpenseMutation,
	useExpenseByIdQuery,
	useExpensesQuery,
	useUpdateExpenseMutation,
} from "../lib/api";

export function useExpenses(initialFilters?: {
	categoryId?: string;
	supplierId?: string;
	orderId?: string;
	vehicleId?: string;
	personnelId?: string;
}) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});
	const [categoryId, setCategoryId] = useState<string | undefined>(
		initialFilters?.categoryId,
	);
	const [supplierId, setSupplierId] = useState<string | undefined>(
		initialFilters?.supplierId,
	);
	const [orderId, setOrderId] = useState<string | undefined>(
		initialFilters?.orderId,
	);
	const [vehicleId, setVehicleId] = useState<string | undefined>(
		initialFilters?.vehicleId,
	);
	const [personnelId, setPersonnelId] = useState<string | undefined>(
		initialFilters?.personnelId,
	);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useExpensesQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		categoryId,
		supplierId,
		orderId,
		vehicleId,
		personnelId,
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
	});

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		categoryId,
		setCategoryId,
		supplierId,
		setSupplierId,
		orderId,
		setOrderId,
		vehicleId,
		setVehicleId,
		personnelId,
		setPersonnelId,
		dateRange,
		setDateRange,
		refetch: query.refetch,
	};
}

export function useExpenseById(expenseId: string) {
	const { activeOrganization } = useActiveOrganization();
	return useExpenseByIdQuery(activeOrganization?.id, expenseId);
}

export function useExpenseMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const queryClient = useQueryClient();

	// Helper function to handle refetching expenses after mutations
	const refetchExpenses = () => {
		// Invalidate all expense queries
		queryClient.invalidateQueries({
			queryKey: expenseKeys.all,
		});

		// Call the external onSuccess callback if provided
		if (options?.onSuccess) {
			options.onSuccess();
		}
	};

	const createMutation = useCreateExpenseMutation(orgId);
	const updateMutation = useUpdateExpenseMutation(orgId);
	const deleteMutation = useDeleteExpenseMutation(orgId);

	const createWithCallback = async (data: any) => {
		try {
			const result = await createMutation.mutateAsync(data);
			refetchExpenses();
			return result;
		} catch (error) {
			console.error("Create expense error:", error);
			throw error;
		}
	};

	const updateWithCallback = async (id: string, data: any) => {
		try {
			const result = await updateMutation.mutateAsync({ id, data });
			refetchExpenses();
			return result;
		} catch (error) {
			console.error("Update expense error:", error);
			throw error;
		}
	};

	const deleteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);
			refetchExpenses();
			return result;
		} catch (error) {
			console.error("Delete expense error:", error);
			throw error;
		}
	};

	return {
		createExpense: createWithCallback,
		updateExpense: updateWithCallback,
		deleteExpense: deleteWithCallback,
		isCreating: createMutation.isPending,
		isUpdating: updateMutation.isPending,
		isDeleting: deleteMutation.isPending,
	};
}
