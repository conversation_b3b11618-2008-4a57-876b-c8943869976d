"use client";

import {
	useAddressMutations,
	useCounterpartyAddresses,
} from "@saas/contacts/hooks/use-counterparty";
import type { useCounterpartyById } from "@saas/contacts/hooks/use-counterparty";
import {
	getAddressTypeBadgeClass,
	getAddressTypeIcon,
} from "@saas/shared/lib/address-utils";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { cn } from "@ui/lib";
import {
	Clock,
	Edit,
	Globe,
	Loader2,
	MapPin,
	PlusCircle,
	Trash,
} from "lucide-react";
import { useTranslations } from "next-intl";
import React, { useCallback, useState } from "react";
import { AddressFormDialog } from "../address-form-dialog";

type Counterparty = NonNullable<
	ReturnType<typeof useCounterpartyById>["counterparty"]
>;

interface AddressesPanelProps {
	counterparty: Counterparty;
}

export function AddressesPanel({ counterparty }: AddressesPanelProps) {
	const t = useTranslations();
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [editingAddressId, setEditingAddressId] = useState<string | null>(
		null,
	);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [addressToDeleteId, setAddressToDeleteId] = useState<string | null>(
		null,
	);

	// Get addresses for the counterparty
	const { addresses, isLoading, refetch } = useCounterpartyAddresses(
		counterparty.id,
		{ includeBlocked: true },
	);

	// Get mutation functions
	const { deleteAddress, isLoading: isMutating } = useAddressMutations(
		counterparty.id,
	);

	// Always define all handlers, even if they're not used in certain render paths
	const handleDeleteAddress = useCallback(async (id: string) => {
		setAddressToDeleteId(id);
		setDeleteDialogOpen(true);
	}, []);

	const confirmDeleteAddress = useCallback(async () => {
		if (addressToDeleteId) {
			try {
				await deleteAddress(addressToDeleteId);
				// No need for toast here since it's handled in the mutation
			} catch (error) {
				console.error("Error deleting address:", error);
			}
			setAddressToDeleteId(null);
		}
	}, [addressToDeleteId, deleteAddress]);

	// Format JSON operating hours object into readable text - use useCallback for consistency
	const formatOperatingHours = useCallback((operatingHours: any) => {
		if (!operatingHours) {
			return null;
		}

		const days = Object.keys(operatingHours);
		if (days.length === 0) {
			return null;
		}

		return (
			<div className="mt-2 space-y-1">
				<h4 className="text-sm font-medium flex items-center">
					<Clock className="h-3 w-3 mr-1" /> Operating Hours
				</h4>
				<div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
					{days.map((day) => (
						<div key={day} className="flex justify-between">
							<span className="capitalize">{day}:</span>
							<span>{operatingHours[day].join(", ")}</span>
						</div>
					))}
				</div>
			</div>
		);
	}, []);

	return (
		<Card>
			<CardHeader className="flex flex-row items-center justify-between">
				<CardTitle>Addresses</CardTitle>
				<Button
					size="sm"
					variant="outline"
					onClick={() => setIsAddDialogOpen(true)}
					disabled={isMutating}
				>
					{isMutating ? (
						<Loader2 className="h-4 w-4 mr-2 animate-spin" />
					) : (
						<PlusCircle className="h-4 w-4 mr-2" />
					)}
					Add Address
				</Button>
			</CardHeader>
			<CardContent>
				{isLoading ? (
					<div className="flex justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				) : addresses && addresses.length > 0 ? (
					<div className="space-y-6">
						{addresses.map((addressUsage) => (
							<div
								key={addressUsage.id}
								className="border rounded-md p-4 relative"
							>
								<div className="flex justify-between items-start">
									<div className="space-y-2">
										<div className="flex items-center gap-2 flex-wrap">
											<div
												className={cn(
													"px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1",
													getAddressTypeBadgeClass(
														addressUsage.type,
													),
												)}
											>
												{React.createElement(
													getAddressTypeIcon(
														addressUsage.type,
													),
													{ className: "h-4 w-4" },
												)}
												{addressUsage.type}
											</div>

											{addressUsage.isDefault && (
												<div className="px-2 py-0.5 border rounded-full text-xs font-medium">
													Default
												</div>
											)}

											{/* Status indicator logic - Update to use only addressUsage.isGlobal */}
											{addressUsage.isGlobal && (
												<div className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 bg-amber-100 text-amber-800 border border-amber-200 dark:bg-amber-900 dark:text-amber-300">
													<Globe className="h-3 w-3" />
													Global Address
												</div>
											)}

											{addressUsage.counterpartyId ===
												null && (
												<div className="px-2 py-0.5 rounded-full text-xs font-medium flex items-center gap-1 bg-purple-100 text-purple-800 border border-purple-200 dark:bg-purple-900 dark:text-purple-300">
													<Globe className="h-3 w-3" />
													Shared Usage
												</div>
											)}
										</div>

										<div>
											{addressUsage.address.nameLine && (
												<p className="font-medium">
													{
														addressUsage.address
															.nameLine
													}
												</p>
											)}

											<p>{addressUsage.address.street}</p>
											{addressUsage.address
												.addressSupplement && (
												<p>
													{
														addressUsage.address
															.addressSupplement
													}
												</p>
											)}
											<p>
												{addressUsage.address.zipCode}{" "}
												{addressUsage.address.city}
											</p>
											<p>
												{addressUsage.address.country}
											</p>
										</div>

										{/* Address ownership info */}
										{addressUsage.counterpartyId !==
											counterparty.id &&
											addressUsage.counterpartyId !==
												null && (
												<div className="mt-1 text-xs text-muted-foreground italic">
													Owned by another
													counterparty
												</div>
											)}

										{/* Loading or Unloading specific information */}
										{addressUsage.loadingConfig && (
											<div className="mt-2 border-t pt-2">
												{addressUsage.loadingConfig
													.instructions && (
													<div className="mt-1">
														<h4 className="text-sm font-medium">
															Loading Instructions
														</h4>
														<p className="text-sm">
															{
																addressUsage
																	.loadingConfig
																	.instructions
															}
														</p>
													</div>
												)}
												{formatOperatingHours(
													addressUsage.loadingConfig
														.operatingHours,
												)}
												{addressUsage.loadingConfig
													.slotBooking && (
													<div className="mt-2 text-xs text-muted-foreground italic">
														Slot Booking: Enabled
													</div>
												)}
											</div>
										)}

										{addressUsage.unloadingConfig && (
											<div className="mt-2 border-t pt-2">
												{addressUsage.unloadingConfig
													.instructions && (
													<div className="mt-1">
														<h4 className="text-sm font-medium">
															Unloading
															Instructions
														</h4>
														<p className="text-sm">
															{
																addressUsage
																	.unloadingConfig
																	.instructions
															}
														</p>
													</div>
												)}
												{formatOperatingHours(
													addressUsage.unloadingConfig
														.operatingHours,
												)}
												{addressUsage.unloadingConfig
													.slotBooking && (
													<div className="mt-2 text-xs text-muted-foreground italic">
														Slot Booking: Enabled
													</div>
												)}
											</div>
										)}
									</div>

									<div className="flex items-center gap-2">
										<Button
											size="icon"
											variant="ghost"
											onClick={() =>
												setEditingAddressId(
													addressUsage.id,
												)
											}
											disabled={isMutating}
										>
											<Edit className="h-4 w-4" />
										</Button>
										<Button
											size="icon"
											variant="ghost"
											className="text-destructive"
											onClick={() =>
												handleDeleteAddress(
													addressUsage.id,
												)
											}
											disabled={isMutating}
										>
											<Trash className="h-4 w-4" />
										</Button>
									</div>
								</div>
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-8 text-muted-foreground">
						<MapPin className="h-12 w-12 mx-auto mb-3 opacity-20" />
						<p>No addresses added yet</p>
						<p className="text-sm mt-1">
							Add an address to get started
						</p>
					</div>
				)}
			</CardContent>

			{/* Add address dialog */}
			<AddressFormDialog
				open={isAddDialogOpen}
				onOpenChange={setIsAddDialogOpen}
				counterpartyId={counterparty.id}
				onSuccess={refetch}
			/>

			{/* Edit address dialog */}
			{editingAddressId && (
				<AddressFormDialog
					open={!!editingAddressId}
					onOpenChange={() => setEditingAddressId(null)}
					counterpartyId={counterparty.id}
					addressId={editingAddressId}
					onSuccess={refetch}
				/>
			)}

			{/* Delete confirmation dialog */}
			<AlertDialog
				open={deleteDialogOpen}
				onOpenChange={setDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							Are you absolutely sure?
						</AlertDialogTitle>
						<AlertDialogDescription>
							This action cannot be undone. This will permanently
							delete the address.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDeleteAddress}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
						>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Card>
	);
}
