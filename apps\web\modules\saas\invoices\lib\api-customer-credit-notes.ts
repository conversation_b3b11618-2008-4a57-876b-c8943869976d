import type {
	CreateCreditNoteInput as ApiCreateCreditNoteInput,
	UpdateCreditNoteInput as ApiUpdateCreditNoteInput,
} from "@repo/api/src/routes/customer-credit-notes/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define extended types with line items
export interface CreditNoteLineItem {
	orderLineItemId: string;
	quantity?: number;
	unitPrice?: number;
	totalPrice: number;
	description?: string;
}

export interface OrderAllocation {
	orderId: string;
	gross_amount?: number;
	net_amount?: number;
	vat_amount?: number;
	vat_rate?: number;
	lineItems?: CreditNoteLineItem[];
}

export interface CreateCreditNoteInput
	extends Omit<ApiCreateCreditNoteInput, "order_allocations"> {
	order_allocations?: OrderAllocation[];
}

export interface UpdateCreditNoteInput
	extends Omit<ApiUpdateCreditNoteInput, "order_allocations"> {
	order_allocations?: Array<
		OrderAllocation & {
			id?: string;
			gross_amount_applied_to_order?: number;
			net_amount_applied_to_order?: number;
			vat_amount_applied_to_order?: number;
			vat_rate_applied_to_order?: number;
			amount_delta?: number;
			amount_delta_reason?: string;
		}
	>;
}

type FetchCreditNotesParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	customerId?: string;
	orderId?: string;
	startDate?: Date;
	endDate?: Date;
};

export const creditNoteKeys = {
	all: ["creditNotes"] as const,
	list: (params: FetchCreditNotesParams) =>
		[...creditNoteKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...creditNoteKeys.all, "detail", organizationId, id] as const,
};

export const fetchCreditNotes = async (params: FetchCreditNotesParams) => {
	const response = await apiClient["customer-credit-notes"].$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page:
				params.page !== undefined ? params.page.toString() : undefined,
			limit:
				params.limit !== undefined
					? params.limit.toString()
					: undefined,
			customerId: params.customerId,
			orderId: params.orderId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch credit notes list");
	}

	return response.json();
};

export const useCreditNotesQuery = (params: FetchCreditNotesParams) => {
	return useQuery({
		queryKey: creditNoteKeys.list(params),
		queryFn: () => fetchCreditNotes(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchCreditNoteById = async (
	organizationId: string,
	id: string,
	includeDocument = false,
) => {
	const response = await apiClient["customer-credit-notes"][":id"].$get({
		param: { id },
		query: {
			organizationId,
			includeDocument: includeDocument ? "true" : "false",
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch credit note details");
	}

	return response.json();
};

export const useCreditNoteByIdQuery = (
	organizationId?: string,
	id?: string,
	includeDocument = false,
) => {
	return useQuery({
		queryKey: [
			...creditNoteKeys.detail(organizationId, id),
			includeDocument,
		],
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchCreditNoteById(organizationId, id, includeDocument);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create credit note with document upload support
export const useCreateCreditNoteMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: CreateCreditNoteInput & { documentFile?: File },
		) => {
			// Transform data if needed for API compatibility
			const apiData: ApiCreateCreditNoteInput & { documentFile?: File } =
				{
					...data,
					organizationId,
				};

			// Handle document upload with multipart/form-data if a file is provided
			if (data.documentFile instanceof File) {
				const formData = new FormData();

				// Remove the file from the data and stringify the rest
				const { documentFile, ...restData } = apiData;
				formData.append("data", JSON.stringify(restData));
				formData.append("document", documentFile);

				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				const uploadUrl = `${baseUrl}/api/customer-credit-notes?organizationId=${organizationId}`;

				const response = await fetch(uploadUrl, {
					method: "POST",
					body: formData,
					credentials: "include",
				});

				if (!response.ok) {
					throw new Error(
						"Failed to create credit note with document",
					);
				}

				return response.json();
			}
			// Regular JSON request without document

			const response = await apiClient["customer-credit-notes"].$post({
				query: { organizationId },
				json: apiData,
			} as any);

			if (!response.ok) {
				throw new Error("Failed to create credit note");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Credit note created successfully");
			queryClient.invalidateQueries({
				queryKey: creditNoteKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create credit note");
			console.error("Create credit note error:", error);
		},
	});
};

// Update credit note mutation
export const useUpdateCreditNoteMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			id,
			...data
		}: UpdateCreditNoteInput & {
			id: string;
			documentFile?: File;
			removeDocument?: boolean;
		}) => {
			// Transform data if needed for API compatibility
			const apiData: ApiUpdateCreditNoteInput & {
				documentFile?: File;
				removeDocument?: boolean;
			} = {
				...data,
			};

			// Handle document upload with multipart/form-data if a file is provided
			if (data.documentFile instanceof File) {
				const formData = new FormData();

				// Remove the file from the data and stringify the rest
				const { documentFile, ...restData } = apiData;
				formData.append("data", JSON.stringify(restData));
				formData.append("document", documentFile);

				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				const uploadUrl = `${baseUrl}/api/customer-credit-notes/${id}?organizationId=${organizationId}`;

				const response = await fetch(uploadUrl, {
					method: "PUT",
					body: formData,
					credentials: "include",
				});

				if (!response.ok) {
					throw new Error(
						"Failed to update credit note with document",
					);
				}

				return response.json();
			}
			// Regular JSON request without document

			const response = await apiClient["customer-credit-notes"][
				":id"
			].$put({
				param: { id },
				query: { organizationId },
				json: apiData,
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update credit note");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Credit note updated successfully");
			queryClient.invalidateQueries({
				queryKey: creditNoteKeys.list({ organizationId }),
			});
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: creditNoteKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update credit note");
			console.error("Update credit note error:", error);
		},
	});
};

// Delete credit note mutation
export const useDeleteCreditNoteMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient["customer-credit-notes"][
				":id"
			].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete credit note");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Credit note deleted successfully");
			queryClient.invalidateQueries({
				queryKey: creditNoteKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete credit note");
			console.error("Delete credit note error:", error);
		},
	});
};
