import { CreditNoteFormDialog } from "@saas/invoices/components/credit-note-form-dialog";
import { InvoiceFormDialog } from "@saas/invoices/components/invoice-form-dialog";
import { useOfferMutations } from "@saas/offers/hooks/use-offer";
import { NumberInput } from "@saas/orders/components/forms/order-form/sections/stops-section/components/ui-components/number-input";
import { OrderConfirmationEmailPreview } from "@saas/orders/components/order-confirmation-email-preview";
import type { useOrderById } from "@saas/orders/hooks/use-orders";
import {
	useOrderConfirmationPDFPreview,
	useSendOrderConfirmationEmail,
} from "@saas/orders/hooks/use-orders";
import { ThreadManagementSheet } from "@shared/components/thread/components/ThreadManagementSheet";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Label } from "@ui/components/label";
import { format } from "date-fns";
import { FileText, FileX, MessageSquare, PlusCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState } from "react";

interface QuickInfoPanelProps {
	order: ReturnType<typeof useOrderById>["data"];
	onCreateOffer?: () => void;
	onCreateInvoice?: () => void;
	onCreateCreditNote?: () => void;
}

export function QuickInfoPanel({
	order,
	onCreateOffer,
	onCreateInvoice,
	onCreateCreditNote,
}: QuickInfoPanelProps) {
	const t = useTranslations();
	const params = useParams<{ organizationSlug: string }>();
	const [isCreatingOffer, setIsCreatingOffer] = useState(false);
	const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false);
	const [isCreditNoteDialogOpen, setIsCreditNoteDialogOpen] = useState(false);
	const [isOfferDialogOpen, setIsOfferDialogOpen] = useState(false);
	const [validDays, setValidDays] = useState<number | undefined>(30);

	// Order confirmation states
	const [isPDFPreviewOpen, setIsPDFPreviewOpen] = useState(false);
	const [isEmailPreviewOpen, setIsEmailPreviewOpen] = useState(false);
	const [recipientEmail, setRecipientEmail] = useState<string>("");

	// Thread management state
	const [isThreadsOpen, setIsThreadsOpen] = useState(false);

	const { createOffer } = useOfferMutations();

	// Order confirmation hooks
	const { sendConfirmationEmail, isLoading: isSendingConfirmation } =
		useSendOrderConfirmationEmail();
	const { data: pdfPreviewData, isLoading: isLoadingPDF } =
		useOrderConfirmationPDFPreview(order?.id, isPDFPreviewOpen);

	// Format date for display or show placeholder
	const formatDate = (date: Date | string | null | undefined) => {
		if (!date) {
			return t("common.notSet");
		}
		return format(new Date(date), "PPP");
	};

	const handleCreateOffer = async () => {
		if (!order?.id) {
			return;
		}

		setIsCreatingOffer(true);
		try {
			await createOffer({
				orderId: order.id,
				status: "open",
				valid_until: validDays
					? new Date(Date.now() + validDays * 24 * 60 * 60 * 1000)
					: undefined,
			});
			// Toast is already in the API hook
			if (onCreateOffer) {
				onCreateOffer();
			}
			setIsOfferDialogOpen(false);
		} catch (error) {
			// Error toast is already in the API hook
			console.error("Create offer error:", error);
		} finally {
			setIsCreatingOffer(false);
		}
	};

	// Order confirmation flow
	const handleStartOrderConfirmation = () => {
		if (!order?.id) {
			return;
		}

		// Default email to customer email if available
		setRecipientEmail(
			order?.contact?.email || order?.customer?.email || "",
		);

		// First show PDF preview
		setIsPDFPreviewOpen(true);
	};

	const handleProceedToEmailPreview = () => {
		// Close PDF preview and open email preview
		setIsPDFPreviewOpen(false);
		setIsEmailPreviewOpen(true);
	};

	const handleSendConfirmationEmail = async (
		orderId: string,
		email?: string,
		documentIds?: string[],
	) => {
		try {
			await sendConfirmationEmail({
				id: orderId,
				recipientEmail: email,
				documentIds,
			});

			// Close the email preview dialog
			setIsEmailPreviewOpen(false);

			// Reset states
			setRecipientEmail("");
		} catch (error) {
			console.error("Send order confirmation email error:", error);
		}
	};

	return (
		<div className="space-y-4">
			{/* Customer */}
			<div className="space-y-1">
				<dt className="text-sm font-medium text-muted-foreground">
					{t("app.orders.quickInfo.customer")}
				</dt>
				{order?.customer?.id ? (
					<Link
						href={`/app/${params.organizationSlug}/contacts/${order.customer.id}`}
						className="hover:underline cursor-pointer"
					>
						<dd className="text-sm font-semibold">
							{order?.customer?.nameLine1}
						</dd>
					</Link>
				) : (
					<dd className="text-sm font-semibold">
						{order?.customer?.nameLine1}
					</dd>
				)}
			</div>

			{/* Contact */}
			{order?.contact && (
				<div className="space-y-1">
					<dt className="text-sm font-medium text-muted-foreground">
						{t("app.orders.quickInfo.contact")}
					</dt>
					<dd className="text-sm">
						{order.contact.firstName} {order.contact.lastName}
					</dd>
				</div>
			)}

			<div>
				<h3 className="text-sm font-medium text-muted-foreground mb-1">
					{t("app.orders.quickInfo.created")}
				</h3>
				<p className="font-medium">{formatDate(order?.createdAt)}</p>
			</div>

			<div>
				<h3 className="text-sm font-medium text-muted-foreground mb-1">
					{t("app.orders.quickInfo.appointment")}
				</h3>
				<p className="font-medium">{formatDate(order?.appointment)}</p>
			</div>

			{/* Tour Assignment */}
			{order?.tourOrders && order.tourOrders.length > 0 && (
				<div className="space-y-2">
					<h3 className="text-sm font-medium text-muted-foreground mb-1">
						Tour Assignment
					</h3>
					{order.tourOrders.map((tourOrder) => (
						<div key={tourOrder.id} className="space-y-1">
							{/* Tour Name/Number */}
							<div className="space-y-1">
								<dt className="text-xs font-medium text-muted-foreground">
									Tour
								</dt>
								<Link
									href={`/app/${params.organizationSlug}/tours/${tourOrder.tour.id}`}
									className="hover:underline cursor-pointer"
								>
									<dd className="text-sm font-semibold">
										{tourOrder.tour.tourNumber ||
											`Tour ${tourOrder.tour.id.slice(-6)}`}
									</dd>
								</Link>
							</div>

							{/* Carrier */}
							{tourOrder.tour.carrier && (
								<div className="space-y-1">
									<dt className="text-xs font-medium text-muted-foreground">
										Carrier
									</dt>
									<Link
										href={`/app/${params.organizationSlug}/contacts/${tourOrder.tour.carrier.id}`}
										className="hover:underline cursor-pointer"
									>
										<dd className="text-sm font-semibold">
											{tourOrder.tour.carrier.nameLine1}
										</dd>
									</Link>
								</div>
							)}

							{/* Vehicles */}
							{(tourOrder.tour.vehicle ||
								tourOrder.tour.trailer) && (
								<div className="space-y-1">
									<dt className="text-xs font-medium text-muted-foreground">
										Vehicles
									</dt>
									<dd className="text-sm">
										{tourOrder.tour.vehicle && (
											<Link
												href={`/app/${params.organizationSlug}/vehicles/${tourOrder.tour.vehicle.id}`}
												className="hover:underline cursor-pointer"
											>
												<div className="font-semibold">
													{
														tourOrder.tour.vehicle
															.licensePlate
													}
												</div>
											</Link>
										)}
										{tourOrder.tour.trailer && (
											<Link
												href={`/app/${params.organizationSlug}/vehicles/${tourOrder.tour.trailer.id}`}
												className="hover:underline cursor-pointer"
											>
												<div className="font-semibold">
													Trailer:{" "}
													{
														tourOrder.tour.trailer
															.licensePlate
													}
												</div>
											</Link>
										)}
									</dd>
								</div>
							)}
						</div>
					))}
				</div>
			)}

			<div className="space-y-2">
				<h3 className="font-medium">Quick Actions</h3>
				<div className="grid grid-cols-2 gap-2">
					<Button
						variant="outline"
						size="sm"
						className="justify-start"
						onClick={() => setIsInvoiceDialogOpen(true)}
						disabled={!order?.id}
					>
						<FileText className="h-4 w-4 mr-2" />
						Create Invoice
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="justify-start"
						onClick={() => setIsCreditNoteDialogOpen(true)}
						disabled={!order?.id}
					>
						<FileX className="h-4 w-4 mr-2" />
						Create Credit Note
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="justify-start"
						onClick={() => setIsOfferDialogOpen(true)}
						disabled={!order?.id}
					>
						<PlusCircle className="h-4 w-4 mr-2" />
						Create Offer
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="justify-start"
						onClick={() => setIsThreadsOpen(true)}
						disabled={!order?.id}
					>
						<MessageSquare className="h-4 w-4 mr-2" />
						View Threads
					</Button>
				</div>
			</div>

			{/* Order Confirmation PDF Preview Dialog */}
			<Dialog
				open={isPDFPreviewOpen}
				onOpenChange={(open) => {
					if (!open) {
						setIsPDFPreviewOpen(false);
					}
				}}
			>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>Order Confirmation Preview</DialogTitle>
					</DialogHeader>
					<div className="h-[500px]">
						{isLoadingPDF ? (
							<div className="flex items-center justify-center h-full">
								<p>Loading PDF preview...</p>
							</div>
						) : pdfPreviewData?.pdfBase64 ? (
							<iframe
								src={`data:application/pdf;base64,${pdfPreviewData.pdfBase64}`}
								width="100%"
								height="100%"
								title="PDF Preview"
							/>
						) : (
							<div className="flex items-center justify-center h-full text-muted-foreground">
								Failed to load PDF preview
							</div>
						)}
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsPDFPreviewOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleProceedToEmailPreview}
							disabled={!pdfPreviewData?.pdfBase64}
						>
							Continue to Email
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Order Confirmation Email Preview Dialog */}
			<OrderConfirmationEmailPreview
				orderId={order?.id || null}
				recipientEmail={recipientEmail}
				isOpen={isEmailPreviewOpen}
				onClose={() => setIsEmailPreviewOpen(false)}
				onBack={() => {
					setIsEmailPreviewOpen(false);
					setIsPDFPreviewOpen(true); // Go back to PDF preview
				}}
				onSend={handleSendConfirmationEmail}
				isSending={isSendingConfirmation}
			/>

			{/* Reusable Invoice Form Dialog */}
			<InvoiceFormDialog
				open={isInvoiceDialogOpen}
				onOpenChange={setIsInvoiceDialogOpen}
				initialCustomerId={order?.customerId}
				defaultMode="order"
				orderIds={order?.id ? [order.id] : []}
				onSuccess={() => {
					if (onCreateInvoice) {
						onCreateInvoice();
					}
				}}
			/>

			{/* Reusable Credit Note Form Dialog */}
			<CreditNoteFormDialog
				open={isCreditNoteDialogOpen}
				onOpenChange={setIsCreditNoteDialogOpen}
				onSuccess={() => {
					if (onCreateCreditNote) {
						onCreateCreditNote();
					}
				}}
			/>

			{/* Create Offer Dialog */}
			<Dialog
				open={isOfferDialogOpen}
				onOpenChange={setIsOfferDialogOpen}
			>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Create Offer</DialogTitle>
					</DialogHeader>
					<div className="space-y-4 py-4">
						<div className="space-y-2">
							<Label htmlFor="validDays">Valid for (days)</Label>
							<NumberInput
								id="validDays"
								value={validDays}
								onChange={setValidDays}
								min={1}
								placeholder="30"
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsOfferDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							onClick={handleCreateOffer}
							disabled={isCreatingOffer}
						>
							{isCreatingOffer ? "Creating..." : "Create Offer"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Thread Management Sheet */}
			<ThreadManagementSheet
				open={isThreadsOpen}
				onOpenChange={setIsThreadsOpen}
				entityType="Order"
				entityId={order?.id || ""}
				entityTitle={order?.order_number || ""}
			/>
		</div>
	);
}
