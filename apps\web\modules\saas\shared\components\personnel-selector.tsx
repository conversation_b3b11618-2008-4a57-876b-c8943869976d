"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { usePersonnelQuery } from "@saas/personnel/lib/api";
import { useDebounce } from "@shared/hooks/use-debounce";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { Loader2, User, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface PersonnelSelectorProps {
	value?: string;
	onChange: (value: string) => void;
	onBlur?: () => void;
	onSelect?: () => void;
	disabled?: boolean;
}

export function PersonnelSelector({
	value,
	onChange,
	onBlur,
	onSelect,
	disabled,
}: PersonnelSelectorProps) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [showResults, setShowResults] = useState(false);
	const [isUserInput, setIsUserInput] = useState(false);
	const [selectedPerson, setSelectedPerson] = useState<any>(null);
	const [highlightedIndex, setHighlightedIndex] = useState(-1);
	const debouncedSearch = useDebounce(search, 500);
	const inputRef = useRef<HTMLInputElement>(null);

	const { data, isFetching } = usePersonnelQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		limit: 10,
	});

	const isSearching =
		isUserInput &&
		search.length > 0 &&
		(isFetching || search !== debouncedSearch);

	// Reset highlight when results change
	useEffect(() => {
		setHighlightedIndex(-1);
	}, [data?.items]);

	useEffect(() => {
		if (value && data?.items) {
			const selected = data.items.find((person) => person.id === value);
			if (selected) {
				setIsUserInput(false);
				setSearch(`${selected.firstName} ${selected.lastName}`);
				setShowResults(false);
				setSelectedPerson(selected);
			}
		}
	}, [value, data?.items]);

	const clearSelection = () => {
		onChange("");
		setSelectedPerson(null);
		setSearch("");
		setIsUserInput(false);
		setShowResults(false);
		setTimeout(() => {
			inputRef.current?.focus();
		}, 0);
	};

	const selectPerson = (person: any) => {
		onChange(person.id);
		setIsUserInput(false);
		setSearch(`${person.firstName} ${person.lastName}`);
		setShowResults(false);
		setSelectedPerson(person);
		if (onBlur) {
			onBlur();
		}

		// Focus next field if onSelect callback is provided
		if (onSelect) {
			setTimeout(() => {
				onSelect();
			}, 0);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		// Only handle if we have results and not searching
		if (!data?.items?.length || isSearching) {
			return;
		}

		switch (e.key) {
			case "ArrowDown":
				e.preventDefault();
				if (highlightedIndex < 0) {
					setHighlightedIndex(0);
				} else if (highlightedIndex < data.items.length - 1) {
					setHighlightedIndex(highlightedIndex + 1);
				}
				break;
			case "ArrowUp":
				e.preventDefault();
				if (highlightedIndex > 0) {
					setHighlightedIndex(highlightedIndex - 1);
				}
				break;
			case "Enter":
				e.preventDefault();
				if (highlightedIndex >= 0) {
					selectPerson(data.items[highlightedIndex]);
				}
				break;
			case "Escape":
				e.preventDefault();
				setShowResults(false);
				break;
		}
	};

	return (
		<div className="relative w-full">
			{selectedPerson ? (
				<div className="p-2 border rounded-md bg-background">
					<div className="flex items-start gap-3">
						<div className="p-1.5 bg-muted rounded-md">
							<User className="h-4 w-4 text-muted-foreground" />
						</div>
						<div className="flex-1 min-w-0">
							<div className="font-medium truncate">
								{`${selectedPerson.firstName} ${selectedPerson.lastName}`}
							</div>
							<div className="text-xs text-muted-foreground space-y-0.5">
								{selectedPerson.email && (
									<div className="truncate">
										{selectedPerson.email}
									</div>
								)}
								{selectedPerson.department && (
									<div>{selectedPerson.department}</div>
								)}
							</div>
						</div>
						<Button
							variant="ghost"
							size="sm"
							className="h-8 w-8 p-0"
							onClick={clearSelection}
							disabled={disabled}
						>
							<X className="h-4 w-4" />
							<span className="sr-only">Clear selection</span>
						</Button>
					</div>
				</div>
			) : (
				<div className="relative">
					<Input
						ref={inputRef}
						placeholder="Search personnel..."
						value={search}
						onChange={(e) => {
							setIsUserInput(true);
							setSearch(e.target.value);
							setShowResults(true);
							setHighlightedIndex(-1); // Reset highlight on input change
						}}
						onFocus={() => {
							if (search.length > 0) {
								setIsUserInput(true);
								setShowResults(true);
							}
						}}
						onKeyDown={handleKeyDown}
						disabled={disabled}
						className="pr-8"
					/>
					{isSearching && (
						<div className="absolute inset-y-0 right-0 flex items-center pr-3">
							<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
						</div>
					)}
				</div>
			)}

			{/* Search Results Dropdown */}
			{showResults && search.length > 0 && !selectedPerson && (
				<div className="absolute z-50 w-full mt-1 bg-popover text-popover-foreground shadow-md rounded-md border max-h-60 overflow-auto">
					<div className="p-2">
						{isSearching ? (
							<div className="text-sm text-muted-foreground p-2">
								Searching...
							</div>
						) : !data?.items?.length ? (
							<div className="text-sm text-muted-foreground p-2">
								No personnel found
							</div>
						) : (
							<div className="space-y-1">
								{data.items.map((person, index) => (
									<button
										type="button"
										key={person.id}
										className={cn(
											"flex w-full items-center p-2 hover:bg-accent rounded-md text-left",
											(value === person.id ||
												highlightedIndex === index) &&
												"bg-accent",
										)}
										onClick={() => selectPerson(person)}
										onMouseEnter={() =>
											setHighlightedIndex(index)
										}
										onMouseDown={(e) => e.preventDefault()} // Prevent input blur
									>
										<div className="flex flex-1 items-center justify-between">
											<div className="flex flex-col">
												<div className="font-medium">{`${person.firstName} ${person.lastName}`}</div>
												{person.email && (
													<span className="text-xs text-muted-foreground">
														{person.email}
													</span>
												)}
											</div>
											{person.department && (
												<div className="text-xs text-muted-foreground ml-2">
													{person.department.name}
												</div>
											)}
										</div>
									</button>
								))}
							</div>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
