"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";

import { Check, ChevronDown, Mail, Phone, X } from "lucide-react";
import { HelpCircle } from "lucide-react";
import { useEffect, useId, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

// Query keys for contacts
const contactKeys = {
	all: ["contacts"] as const,
	list: (params: any) => [...contactKeys.all, "list", params] as const,
};

// Fetch contacts with search and filtering
const fetchContacts = async ({
	organizationId,
	counterpartyId,
	search = "",
}: {
	organizationId: string;
	counterpartyId?: string;
	search?: string;
	limit?: number;
}) => {
	// Don't fetch if no customer/counterparty ID is provided
	if (!counterpartyId) {
		return { items: [] };
	}

	// Get counterparty details which includes the contacts array
	const response = await apiClient.counterparties[":id"].$get({
		param: { id: counterpartyId },
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparty data");
	}

	const counterparty = await response.json();

	// Extract contacts and filter them based on search if needed
	const contacts = counterparty.contacts || [];

	// Simple search filtering on the client side
	const filteredContacts = search
		? contacts.filter((contact: any) => {
				const searchLower = search.toLowerCase();
				const fullName =
					`${contact.firstName} ${contact.lastName}`.toLowerCase();
				return (
					fullName.includes(searchLower) ||
					contact.email?.toLowerCase().includes(searchLower) ||
					contact.position?.toLowerCase().includes(searchLower) ||
					contact.department?.toLowerCase().includes(searchLower)
				);
			})
		: contacts;

	return { items: filteredContacts };
};

export interface ContactSelectorProps {
	value?: string;
	onChange?: (value: string) => void;
	onDataFetched?: (data: any) => void;
	onSelectedContactChange?: (contact: any) => void;
	name: string;
	label?: string;
	tooltip?: string;
	placeholder?: string;
	counterpartyId?: string;
	disabled?: boolean;
	error?: string;
	className?: string;
	isLoading?: boolean;
	isModal?: boolean;
	allowClear?: boolean;
}

/**
 * A reusable Contact selector component that displays contact details for a specific counterparty
 */
export function ContactSelector({
	value,
	onChange,
	onDataFetched,
	onSelectedContactChange,
	placeholder = "Select contact",
	counterpartyId,
	disabled = false,
	error,
	className,
	isLoading: externalIsLoading = false,
	allowClear = false,
}: ContactSelectorProps): JSX.Element {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";
	const [open, setOpen] = useState(false);
	const [search, setSearch] = useState("");
	const debouncedSearch = useDebounce(search, 300);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const searchInputRef = useRef<HTMLInputElement>(null);
	const dropdownContentRef = useRef<HTMLDivElement>(null);
	const shouldFocusRef = useRef(false);
	const uniqueId = useId();
	const searchInputId = `contact-search-${uniqueId}`;

	// Ref to track the previous contact ID to avoid infinite updates
	const prevContactIdRef = useRef<string | undefined>(undefined);

	// Reset search when closing the dropdown
	useEffect(() => {
		if (!open) {
			setSearch("");
		} else {
			// Flag that we should focus when the dropdown opens
			shouldFocusRef.current = true;
		}
	}, [open]);

	const { data, isLoading: isLoadingData } = useQuery({
		queryKey: contactKeys.list({
			organizationId,
			counterpartyId,
			search: debouncedSearch,
		}),
		queryFn: () =>
			fetchContacts({
				organizationId,
				counterpartyId,
				search: debouncedSearch,
				limit: 50,
			}),
		enabled: !!organizationId && !!counterpartyId,
	});

	// Pass the fetched data to parent if needed
	useEffect(() => {
		if (data && onDataFetched) {
			onDataFetched(data);
		}
	}, [data, onDataFetched]);

	// Find the selected contact
	const selectedContact = data?.items?.find((item: any) => item.id === value);

	// Notify parent when selected contact changes - only when ID actually changes
	useEffect(() => {
		if (
			selectedContact &&
			onSelectedContactChange &&
			value !== prevContactIdRef.current
		) {
			prevContactIdRef.current = value;
			onSelectedContactChange(selectedContact);
		}
	}, [selectedContact, onSelectedContactChange, value]);

	// Multi-approach focus strategy
	useEffect(() => {
		if (!open || !shouldFocusRef.current) {
			return;
		}

		// Schedule multiple focus attempts with increasing delays
		const attempts = [10, 50, 100, 250, 500];

		const focusAttempts = attempts.map((delay) =>
			setTimeout(() => {
				if (searchInputRef.current) {
					searchInputRef.current.focus();
					// Also try to select any existing text
					searchInputRef.current.select();
				} else {
					// As a fallback, try to focus by ID
					const inputEl = document.getElementById(searchInputId);
					inputEl?.focus();
				}
			}, delay),
		);

		shouldFocusRef.current = false;

		return () => {
			// Clean up all timeouts
			focusAttempts.forEach(clearTimeout);
		};
	}, [open, searchInputId]);

	// Combined loading state
	const isLoading = externalIsLoading || isLoadingData;

	// Helper to handle clearing selection (without using a button element)
	const handleClearSelection = (e: React.MouseEvent) => {
		e.stopPropagation();
		onChange?.("");
	};

	// Format the contact name
	const formatContactName = (contact: any) => {
		const { salutation, firstName, lastName } = contact;
		const salutationText = salutation ? `${salutation}. ` : "";
		return `${salutationText}${firstName} ${lastName}`;
	};

	// Handle keyboard navigation
	const handleKeyDown = (e: React.KeyboardEvent) => {
		// Close the dropdown menu when pressing Escape
		if (e.key === "Escape") {
			e.preventDefault();
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle search input
	const handleSearchInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
	) => {
		setSearch(e.target.value);
	};

	// Prevent dropdown from stealing focus from search input
	const handleSearchInputKeyDown = (
		e: React.KeyboardEvent<HTMLInputElement>,
	) => {
		// Prevent propagation to stop the dropdown from handling these events
		e.stopPropagation();

		// Still close on Escape
		if (e.key === "Escape") {
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle dropdown trigger click, to set focus flag
	const handleTriggerClick = () => {
		shouldFocusRef.current = true;
	};

	// Handle wheel events in the dropdown content to ensure scrolling works
	const handleWheel = (e: React.WheelEvent) => {
		// Don't block the wheel event, let it propagate naturally
		e.stopPropagation();
	};

	// Render the selected contact card when we have a value and the data is loaded
	if (value && selectedContact && !isLoading) {
		return (
			<div className={cn("space-y-2", className)}>
				<div className="relative">
					<Card className="w-full">
						<CardContent className="p-4">
							<div className="flex flex-col items-start">
								<div className="flex justify-between w-full items-center">
									<div className="font-medium text-base">
										{formatContactName(selectedContact)}
									</div>
									{allowClear && (
										<button
											type="button"
											className="h-6 w-6 rounded-md inline-flex items-center justify-center text-muted-foreground hover:bg-accent hover:text-accent-foreground"
											onClick={handleClearSelection}
											aria-label="Clear selection"
										>
											<X className="h-4 w-4" />
										</button>
									)}
								</div>
								<div className="mt-1 text-sm text-muted-foreground flex flex-col gap-1">
									{selectedContact.position && (
										<span>{selectedContact.position}</span>
									)}
									{selectedContact.email && (
										<div className="flex items-center gap-1">
											<Mail className="h-3.5 w-3.5 text-muted-foreground" />
											<span>{selectedContact.email}</span>
										</div>
									)}
									{selectedContact.telephone && (
										<div className="flex items-center gap-1">
											<Phone className="h-3.5 w-3.5 text-muted-foreground" />
											<span>
												{selectedContact.telephone}
											</span>
										</div>
									)}
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{error && (
					<p className="text-sm font-medium text-destructive">
						{error}
					</p>
				)}
			</div>
		);
	}

	// Otherwise, render the dropdown selector
	return (
		<div className={cn("space-y-2", className)}>
			<DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
				<DropdownMenuTrigger
					asChild
					disabled={disabled || isLoading || !counterpartyId}
				>
					<Button
						ref={buttonRef}
						variant="outline"
						className={cn(
							"w-full justify-between p-4 h-auto text-left",
							!selectedContact && "text-muted-foreground",
							!counterpartyId && "opacity-70",
						)}
						onClick={handleTriggerClick}
					>
						{isLoading ? (
							<div>Loading...</div>
						) : !counterpartyId ? (
							<div className="flex justify-between w-full items-center">
								<span>Select a customer first</span>
								<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
							</div>
						) : (
							<div className="flex justify-between w-full items-center">
								<span>{placeholder}</span>
								<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
							</div>
						)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					ref={dropdownContentRef}
					className="p-0 max-h-[300px] overflow-auto"
					align="start"
					sideOffset={4}
					style={{
						width: "var(--radix-dropdown-menu-trigger-width)",
					}}
					role="menu"
					aria-orientation="vertical"
					aria-label="Contacts list"
					onKeyDown={handleKeyDown}
					onWheel={handleWheel}
				>
					<div className="p-2 border-b">
						<Input
							id={searchInputId}
							ref={searchInputRef}
							placeholder="Search contacts..."
							value={search}
							onChange={handleSearchInputChange}
							onKeyDown={handleSearchInputKeyDown}
							onClick={(e) => e.stopPropagation()}
							className="h-8"
							autoFocus
							// Force autofocus
							onFocus={(e) => e.currentTarget.select()}
						/>
					</div>
					<div className="overflow-y-auto max-h-[250px]">
						{!Array.isArray(data?.items) ||
						data?.items.length === 0 ? (
							<div className="py-6 text-center">
								<p className="text-sm text-muted-foreground">
									No contacts found.
								</p>
							</div>
						) : (
							<>
								{data.items.map((contact: any) => (
									<DropdownMenuItem
										key={contact.id}
										className={cn(
											"flex flex-col w-full items-start text-left px-3 py-2 cursor-pointer",
											value === contact.id
												? "bg-accent/5 text-accent-foreground"
												: "",
											"focus:bg-accent/10 focus:text-accent-foreground/90",
										)}
										onSelect={() => {
											onChange?.(contact.id);
											setOpen(false); // Close dropdown after selection
										}}
										role="menuitemradio"
										aria-checked={value === contact.id}
									>
										<div className="flex w-full items-center justify-between">
											<div className="font-medium">
												{formatContactName(contact)}
											</div>
											<div className="flex items-center gap-2">
												{contact.department && (
													<Badge className="text-xs">
														{contact.department}
													</Badge>
												)}
												{value === contact.id && (
													<Check className="h-4 w-4 text-primary" />
												)}
											</div>
										</div>
										<div className="mt-1 text-xs text-muted-foreground flex flex-wrap gap-2">
											{contact.position && (
												<div className="flex items-center gap-1">
													<span>
														{contact.position}
													</span>
												</div>
											)}
											{contact.email && (
												<div className="flex items-center gap-1">
													<Mail className="h-3 w-3" />
													<span>{contact.email}</span>
												</div>
											)}
											{contact.telephone && (
												<div className="flex items-center gap-1">
													<Phone className="h-3 w-3" />
													<span>
														{contact.telephone}
													</span>
												</div>
											)}
										</div>
									</DropdownMenuItem>
								))}
							</>
						)}
					</div>
				</DropdownMenuContent>
			</DropdownMenu>

			{error && (
				<p className="text-sm font-medium text-destructive">{error}</p>
			)}
		</div>
	);
}

/**
 * A form-connected version of the Contact selector for use with react-hook-form
 */
export function FormContactSelector({
	name,
	label,
	tooltip,
	placeholder = "Select contact",
	counterpartyId,
	disabled = false,
	className,
	isLoading = false,
	isModal = false,
	allowClear = false,
	onSelectedContactChange,
}: Omit<ContactSelectorProps, "onChange" | "value" | "error">): JSX.Element {
	const form = useFormContext();

	if (!form) {
		throw new Error(
			"FormContactSelector must be used within a FormProvider",
		);
	}

	return (
		<FormField
			control={form.control}
			name={name}
			render={({ field }) => (
				<FormItem className={className}>
					{label && (
						<FormLabel
							htmlFor={name}
							className="flex items-center gap-1"
						>
							{label}
							{tooltip && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
										</TooltipTrigger>
										<TooltipContent>
											<p>{tooltip}</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}
						</FormLabel>
					)}
					<FormControl>
						<ContactSelector
							onChange={field.onChange}
							value={field.value}
							name={name}
							placeholder={placeholder}
							counterpartyId={counterpartyId}
							disabled={disabled}
							isLoading={isLoading}
							isModal={isModal}
							allowClear={allowClear}
							onSelectedContactChange={onSelectedContactChange}
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
