"use client";

import { useEffect } from "react";
import { isHotkeyPressed, useHotkeys } from "react-hotkeys-hook";
import { useCommands } from "../../command/context/command-context";
import { useShortcuts } from "../context/shortcuts";
// import { CORE_SHORTCUTS } from "../registry/shortcut-registry";
// import { KeyboardIndicator } from "./KeyboardIndicator";

export function GlobalShortcuts() {
	const { shortcuts, activeScopes, registerScopeWithShortcuts } =
		useShortcuts();
	const { openCommandDialog, setShowShortcutsDialog } = useCommands();

	// Register the global scope
	useEffect(() => {
		// The * scope is already registered by default, but we make sure
		registerScopeWithShortcuts("*");
	}, [registerScopeWithShortcuts]);

	// Command palette shortcut (ctrl+k or ⌘+k)
	useHotkeys(
		"ctrl+k, cmd+k",
		(e) => {
			e.preventDefault();
			openCommandDialog();
		},
		{
			enableOnFormTags: true,
			preventDefault: true,
			enabled: activeScopes.includes("*"),
		},
	);

	// Help dialog shortcut - check if ? is pressed
	useEffect(() => {
		const checkHelpKey = () => {
			// Only show when active scopes includes global scope
			if (activeScopes.includes("*")) {
				// Set the dialog state to whether the ? key is currently pressed
				// This will automatically close the dialog when the key is released
				setShowShortcutsDialog(isHotkeyPressed("?"));
			}
		};

		window.addEventListener("keydown", checkHelpKey);
		window.addEventListener("keyup", checkHelpKey);

		return () => {
			window.removeEventListener("keydown", checkHelpKey);
			window.removeEventListener("keyup", checkHelpKey);
		};
	}, [activeScopes, setShowShortcutsDialog]);

	return (
		<div className="fixed bottom-4 right-4 flex gap-2">
			{/* {CORE_SHORTCUTS.map((shortcut) => (
				<KeyboardIndicator
					key={shortcut.id}
					combo={shortcut.combo}
					description={shortcut.description}
					translationKey={shortcut.translationKey}
				/>
			))} */}
		</div>
	);
}
