"use client";
import type { ExtendedUpdateTourInput } from "@repo/api/src/routes/tours/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	type Stop,
	TourBuilderProvider,
	useTourBuilder,
} from "@saas/tours/context/tours-planner-context";
import {
	useCreateTourMutation,
	useTourByIdQuery,
	useUpdateTourMutation,
} from "@saas/tours/lib/api";
import { Button } from "@ui/components/button";
import { Loader2 } from "lucide-react";
import {
	forwardRef,
	useCallback,
	useEffect,
	useImperativeHandle,
	useMemo,
	useState,
} from "react";
import { toast } from "sonner";
import { StopSearchPanel } from "./stop-search-panel";
import { TourDetailsPanel } from "./tour-details-panel";
import { TourMap } from "./tour-map";
import { TourStopsPanel } from "./tour-stops-panel";
import { VehicleCalendar } from "./vehicle-calendar";
import { ViewSwitcher } from "./view-switcher";

type View = "map" | "calendar";

export interface TourBuilderProps {
	mode: "create" | "edit";
	tourId?: string;
	onSuccess?: (tourId: string) => void;
	showSaveButton?: boolean;
}

// Main tour builder component that wraps the context provider
const TourBuilder = forwardRef<
	{ handleSave: () => Promise<void> },
	TourBuilderProps
>(function TourBuilder(
	{ mode, tourId, onSuccess, showSaveButton = true },
	ref,
) {
	// Always call hooks in the same order, regardless of conditions
	const { activeOrganization } = useActiveOrganization();
	const orgId = useMemo(
		() => activeOrganization?.id || "",
		[activeOrganization],
	);

	// Initialize state
	const [initialStops, setInitialStops] = useState<Stop[]>([]);
	const [initialTourData, setInitialTourData] = useState({});
	const [isLoading, setIsLoading] = useState(mode === "edit");

	// Memoize these values to keep them stable
	const memoizedMode = useMemo(() => mode, [mode]);
	const memoizedTourId = useMemo(() => tourId, [tourId]);

	// Always call the query hook, but conditionally enable fetching
	const fetchTourEnabled =
		memoizedMode === "edit" && !!memoizedTourId && !!orgId;
	const { data: tourData, isLoading: isFetchingTour } = useTourByIdQuery(
		fetchTourEnabled ? orgId : undefined,
		fetchTourEnabled ? memoizedTourId : undefined,
	);

	// Process data when it's available
	const processData = useCallback(() => {
		if (memoizedMode === "edit" && tourData) {
			// Extract tour data for the form
			setInitialTourData({
				organizationId: tourData.organizationId,
				carrierId: tourData.carrierId,
				vehicleId: tourData.vehicleId,
				trailerVehicleId: tourData.trailerVehicleId,
				startDate: tourData.startDate
					? new Date(tourData.startDate)
					: undefined,
				endDate: tourData.endDate
					? new Date(tourData.endDate)
					: undefined,
			});

			// Extract stops for the context
			if (tourData.stops) {
				const stops: Stop[] = tourData.stops.map((stop, index) => {
					// Convert the API response to our Stop type with properly typed fields
					return {
						...stop,
						// Handle only the fields that need special conversion
						orderId: stop.orderId || undefined,
						offerId: stop.offerId || undefined,
						street: stop.street || undefined,
						city: stop.city || undefined,
						datetime_start: stop.datetime_start
							? new Date(stop.datetime_start)
							: null,
						datetime_end: stop.datetime_end
							? new Date(stop.datetime_end)
							: null,
						position:
							stop.positionTour !== null
								? stop.positionTour
								: index,
						// Required by Stop type - use the tour's organizationId
						organizationId: tourData.organizationId || "",
					} as unknown as Stop; // Cast after handling required fields
				});

				setInitialStops(stops);
			}

			setIsLoading(false);
		}
	}, [memoizedMode, tourData]);

	// Call the effect with stable dependencies
	useEffect(() => {
		processData();
	}, [processData]);

	if (isLoading || isFetchingTour) {
		return (
			<div className="h-full flex items-center justify-center">
				<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
			</div>
		);
	}

	return (
		<TourBuilderProvider
			initialTourData={initialTourData}
			initialStops={initialStops}
			mode={mode}
			tourId={tourId}
		>
			<TourBuilderContent
				onSuccess={onSuccess}
				showSaveButton={showSaveButton}
				ref={ref}
			/>
		</TourBuilderProvider>
	);
});

// Content separated to use the context
const TourBuilderContent = forwardRef<
	{ handleSave: () => Promise<void> },
	{
		onSuccess?: (tourId: string) => void;
		showSaveButton?: boolean;
	}
>(function TourBuilderContent({ onSuccess, showSaveButton = true }, ref) {
	const {
		tourData,
		stops,
		newTourStops,
		mode,
		tourId,
		getStopOrders,
		getTourStops,
		isSaving,
		setIsSaving,
		removeStop: contextRemoveStop, // Rename to avoid conflict
	} = useTourBuilder();
	const { activeOrganization } = useActiveOrganization();
	const orgId = useMemo(
		() => activeOrganization?.id || "",
		[activeOrganization],
	);

	// View state (map or calendar)
	const [activeView, setActiveView] = useState<View>("map");

	// Track all stop IDs that have been part of the tour
	const [allTourStopIds, setAllTourStopIds] = useState<Set<string>>(() => {
		// Initialize with the existing stops if in edit mode
		if (mode === "edit" && stops.length > 0) {
			return new Set(stops.map((stop) => stop.id));
		}
		return new Set();
	});

	// Track explicitly removed stop IDs
	const [removedStopIds, setRemovedStopIds] = useState<Set<string>>(
		new Set(),
	);

	// Override the removeStop function to track removed stops
	const removeStop = useCallback(
		(stopId: string) => {
			if (mode === "edit") {
				setRemovedStopIds((prev) => {
					const newSet = new Set(prev);
					newSet.add(stopId);
					return newSet;
				});
			}
			contextRemoveStop(stopId);
		},
		[contextRemoveStop, mode],
	);

	// Update tracked stop IDs whenever stops change
	useEffect(() => {
		if (mode === "edit") {
			setAllTourStopIds((prevIds) => {
				const newIds = new Set(prevIds);
				stops.forEach((stop) => newIds.add(stop.id));
				return newIds;
			});
		}
	}, [mode, stops]);

	// Add a debugging log to track the state
	useEffect(() => {
		if (mode === "edit") {
			console.log("All tour stop IDs:", Array.from(allTourStopIds));
			console.log("Removed stop IDs:", Array.from(removedStopIds));
			console.log(
				"Current stops:",
				stops.map((stop) => stop.id),
			);
		}
	}, [mode, allTourStopIds, removedStopIds, stops]);

	// Expose the removeStop function to the TourStopsPanel
	useEffect(() => {
		if (mode === "edit" && typeof window !== "undefined") {
			// Expose our removeStop function through a global object for debugging
			(window as any).__tourBuilder = {
				...(window as any).__tourBuilder,
				removeStop,
				getAllStopIds: () => Array.from(allTourStopIds),
				getRemovedStopIds: () => Array.from(removedStopIds),
			};
		}
	}, [mode, removeStop, allTourStopIds, removedStopIds]);

	// Create/Update mutations
	const createMutation = useCreateTourMutation(orgId);
	const updateMutation = useUpdateTourMutation();

	// Memoize the handleSave function to keep it stable
	const handleSave = useCallback(async () => {
		if (!tourData.startDate || !tourData.endDate) {
			toast.error("Please provide start and end dates");
			return;
		}

		setIsSaving(true);

		try {
			if (mode === "create") {
				// For creation, include stops in the initial call
				const stopIds =
					stops.length > 0 ? stops.map((stop) => stop.id) : undefined;
				const stopOrdersData =
					stops.length > 0 ? getStopOrders() : undefined;
				const tourStopsData =
					newTourStops.length > 0 ? getTourStops() : undefined;

				// Create new tour with stops included
				const cleanedTourData = Object.fromEntries(
					Object.entries(tourData).map(([key, value]) =>
						// Convert null to undefined and keep other values as is
						[key, value === null ? undefined : value],
					),
				);

				const result = await createMutation.mutateAsync({
					...cleanedTourData,
					organizationId: orgId,
					startDate: tourData.startDate
						? new Date(tourData.startDate as string)
						: new Date(),
					endDate: tourData.endDate
						? new Date(tourData.endDate as string)
						: new Date(),
					// Include stop data in the initial creation
					addStopIds: stopIds,
					stopOrders: stopOrdersData,
					tourStops: tourStopsData,
				});

				onSuccess?.(result?.id || "");
			} else if (mode === "edit" && tourId) {
				// For editing, use the existing update flow
				const tourStopsData =
					newTourStops.length > 0 ? getTourStops() : undefined;

				// Clean the tour data to replace null values with undefined
				const cleanedTourData = Object.fromEntries(
					Object.entries(tourData).map(([key, value]) =>
						// Convert null to undefined and keep other values as is
						[key, value === null ? undefined : value],
					),
				);

				const updateData: ExtendedUpdateTourInput = {
					id: tourId,
					organizationId: orgId,
					...cleanedTourData,
				};

				// Convert string dates to Date objects if needed
				if (typeof updateData.startDate === "string") {
					updateData.startDate = new Date(updateData.startDate);
				}
				if (typeof updateData.endDate === "string") {
					updateData.endDate = new Date(updateData.endDate);
				}

				// Determine if we need to remove stops or reorder existing ones
				if (stops.length === 0 && allTourStopIds.size > 0) {
					// Combine all tracked stop IDs and removed stop IDs
					const allStopsToRemove = new Set([
						...Array.from(allTourStopIds),
						...Array.from(removedStopIds),
					]);
					updateData.removeStopIds = Array.from(allStopsToRemove);
					console.log(
						"Removing all stops:",
						updateData.removeStopIds,
					);
				} else if (removedStopIds.size > 0) {
					// If we have specific stops to remove but still have some stops left
					updateData.removeStopIds = Array.from(removedStopIds);
					updateData.stopOrders = getStopOrders();
					console.log(
						"Removing specific stops:",
						updateData.removeStopIds,
					);
				} else {
					// Otherwise, just use normal stop ordering
					updateData.stopOrders = getStopOrders();
				}

				// Add new tour stops if any
				if (tourStopsData && tourStopsData.length > 0) {
					updateData.tourStops = tourStopsData;
				}

				await updateMutation.mutateAsync({
					tourId,
					tourData: updateData,
					// Only include stopOrders if we're not removing all stops and have stopOrders
					stopOrders: updateData.stopOrders || undefined,
					tourStops: tourStopsData,
				});

				onSuccess?.(tourId);
			}
		} catch (error) {
			console.error("Error saving tour:", error);
		} finally {
			setIsSaving(false);
		}
	}, [
		tourData,
		mode,
		tourId,
		stops,
		newTourStops,
		orgId,
		getStopOrders,
		getTourStops,
		createMutation,
		updateMutation,
		onSuccess,
		setIsSaving,
		allTourStopIds,
		removedStopIds,
	]);

	// Expose handleSave method through ref with stable reference
	useImperativeHandle(
		ref,
		() => ({
			handleSave,
		}),
		[handleSave],
	);

	return (
		<div className="flex flex-col h-full overflow-hidden">
			{/* Top section with header and view switcher for the map area */}
			<div className="mb-4">
				<div className="flex justify-end items-center mb-2">
					<ViewSwitcher
						activeView={activeView}
						onChange={setActiveView}
					/>
				</div>

				{/* Map and Calendar views - both always rendered but conditionally visible */}
				<div className="h-max-[500px] relative">
					<div className={activeView === "map" ? "block" : "hidden"}>
						<TourMap />
					</div>
					<div
						className={
							activeView === "calendar" ? "block" : "hidden"
						}
					>
						<VehicleCalendar />
					</div>
				</div>
			</div>

			<div className="flex-1 grid grid-cols-1 lg:grid-cols-[1fr_1.5fr_1fr] gap-4 overflow-hidden max-w-full min-h-0">
				<TourDetailsPanel />
				<TourStopsPanel onRemoveStop={removeStop} />
				<StopSearchPanel />
			</div>

			{showSaveButton && (
				<div className="mt-6 flex justify-end">
					<Button size="lg" onClick={handleSave} disabled={isSaving}>
						{isSaving && (
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
						)}
						{mode === "create" ? "Create Tour" : "Update Tour"}
					</Button>
				</div>
			)}
		</div>
	);
});

export { TourBuilder };
