import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";
import type { CalendarEvent } from "@repo/api/src/routes/personnel-calendar/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useQueryClient } from "@tanstack/react-query";
import { addMonths, endOfMonth, startOfMonth, subMonths } from "date-fns";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
	fetchCalendarEvents,
	isEventOnDate,
	personnelCalendarKeys,
	useCalendarMonthEvents,
} from "../lib/api-calendar";

export function usePersonnelCalendar(options?: {
	initialDate?: Date;
	departmentId?: string;
	personnelId?: string;
}) {
	const { activeOrganization } = useActiveOrganization();
	const [currentDate, setCurrentDate] = useState<Date>(
		options?.initialDate || new Date(),
	);
	const [departmentId, setDepartmentId] = useState<string | undefined>(
		options?.departmentId,
	);
	const [personnelId, setPersonnelId] = useState<string | undefined>(
		options?.personnelId,
	);
	const [activeFilters, setActiveFilters] = useState<AbsenceType[]>([]);
	const queryClient = useQueryClient();

	// Fetch calendar events for the current month
	const calendarQuery = useCalendarMonthEvents(
		activeOrganization?.id ?? "",
		currentDate,
		departmentId,
		personnelId,
		{ enabled: !!activeOrganization?.id },
	);

	// Pre-fetch adjacent months
	const prefetchAdjacentMonths = useCallback(() => {
		if (!activeOrganization?.id) {
			return;
		}

		// Create date objects for next and previous months
		const nextMonth = new Date(currentDate);
		nextMonth.setMonth(nextMonth.getMonth() + 1);

		const prevMonth = new Date(currentDate);
		prevMonth.setMonth(prevMonth.getMonth() - 1);

		// Calculate query params for next month
		const nextMonthQueryStartDate = subMonths(startOfMonth(nextMonth), 1);
		const nextMonthQueryEndDate = addMonths(endOfMonth(nextMonth), 1);

		// Calculate query params for previous month
		const prevMonthQueryStartDate = subMonths(startOfMonth(prevMonth), 1);
		const prevMonthQueryEndDate = addMonths(endOfMonth(prevMonth), 1);

		// Prefetch next month
		queryClient.prefetchQuery({
			queryKey: personnelCalendarKeys.monthEvents(
				activeOrganization.id,
				nextMonth,
				departmentId,
				personnelId,
			),
			queryFn: () =>
				fetchCalendarEvents({
					organizationId: activeOrganization.id,
					startDate: nextMonthQueryStartDate,
					endDate: nextMonthQueryEndDate,
					departmentId,
					personnelId,
				}),
			staleTime: 1000 * 60 * 5, // 5 minutes
		});

		// Prefetch previous month
		queryClient.prefetchQuery({
			queryKey: personnelCalendarKeys.monthEvents(
				activeOrganization.id,
				prevMonth,
				departmentId,
				personnelId,
			),
			queryFn: () =>
				fetchCalendarEvents({
					organizationId: activeOrganization.id,
					startDate: prevMonthQueryStartDate,
					endDate: prevMonthQueryEndDate,
					departmentId,
					personnelId,
				}),
			staleTime: 1000 * 60 * 5, // 5 minutes
		});
	}, [
		activeOrganization?.id,
		currentDate,
		departmentId,
		personnelId,
		queryClient,
	]);

	// Trigger prefetch when current month data loads successfully
	useEffect(() => {
		if (calendarQuery.isSuccess) {
			prefetchAdjacentMonths();
		}
	}, [calendarQuery.isSuccess, prefetchAdjacentMonths]);

	// Apply client-side filters
	const filteredEvents = useMemo(() => {
		if (!calendarQuery.data) {
			return [];
		}

		if (activeFilters.length === 0) {
			return calendarQuery.data;
		}

		return calendarQuery.data.filter((event) =>
			activeFilters.includes(event.type as AbsenceType),
		);
	}, [calendarQuery.data, activeFilters]);

	// Helper to get events for a specific date
	const getEventsForDate = useCallback(
		(date: Date): CalendarEvent[] => {
			return filteredEvents.filter((event) => isEventOnDate(event, date));
		},
		[filteredEvents],
	);

	// Get all unique absence types
	const availableAbsenceTypes = useMemo(() => {
		if (!calendarQuery.data?.length) {
			return [];
		}

		const types = new Set<AbsenceType>();
		calendarQuery.data.forEach((event) => {
			types.add(event.type as AbsenceType);
		});
		return Array.from(types);
	}, [calendarQuery.data]);

	// Toggle a filter
	const toggleFilter = useCallback((type: AbsenceType) => {
		setActiveFilters((prev) => {
			if (prev.includes(type)) {
				return prev.filter((t) => t !== type);
			}
			return [...prev, type];
		});
	}, []);

	// Clear all filters
	const clearFilters = useCallback(() => {
		setActiveFilters([]);
	}, []);

	// Change month
	const goToNextMonth = useCallback(() => {
		setCurrentDate((prev) => {
			const newDate = new Date(prev);
			newDate.setMonth(newDate.getMonth() + 1);
			return newDate;
		});
	}, []);

	const goToPreviousMonth = useCallback(() => {
		setCurrentDate((prev) => {
			const newDate = new Date(prev);
			newDate.setMonth(newDate.getMonth() - 1);
			return newDate;
		});
	}, []);

	return {
		// Data
		events: filteredEvents,
		isLoading: calendarQuery.isLoading,
		isFetching: calendarQuery.isFetching,
		error: calendarQuery.error,

		// State
		currentDate,
		setCurrentDate,
		departmentIdFilter: departmentId,
		setDepartmentIdFilter: setDepartmentId,
		personnelFilter: personnelId,
		setPersonnelFilter: setPersonnelId,
		activeFilters,

		// Derived data
		availableAbsenceTypes,
		getEventsForDate,

		// Actions
		toggleFilter,
		clearFilters,
		goToNextMonth,
		goToPreviousMonth,
		prefetchAdjacentMonths,

		// Refetch
		refetch: calendarQuery.refetch,
	};
}
