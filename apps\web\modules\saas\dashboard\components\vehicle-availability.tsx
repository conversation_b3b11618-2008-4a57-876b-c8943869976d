import { Badge } from "@ui/components/badge";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { endOfDay, format, isSameDay, startOfDay } from "date-fns";
import { CalendarIcon, ClockIcon, TruckIcon } from "lucide-react";
import { useDashboard } from "../hooks/use-dashboard";

// Function to filter tours for a specific date
const filterToursByDate = (tours: any[], date: Date) => {
	if (!tours || !tours.length) {
		return [];
	}

	const dayStart = startOfDay(date);
	const dayEnd = endOfDay(date);

	return tours.filter((tour) => {
		// Get tour's overall timeline
		const tourStartDate = tour.firstStop?.datetime_start
			? new Date(tour.firstStop.datetime_start)
			: tour.firstStop?.datetime_end
				? new Date(tour.firstStop.datetime_end)
				: null;

		const tourEndDate = tour.lastStop?.datetime_start
			? new Date(tour.lastStop.datetime_start)
			: tour.lastStop?.datetime_end
				? new Date(tour.lastStop.datetime_end)
				: null;

		if (!tourStartDate || !tourEndDate) {
			return false;
		}

		// Check if this day is part of the tour's timeline
		return tourStartDate <= dayEnd && tourEndDate >= dayStart;
	});
};

// Function to determine vehicle status for a day based on work hours coverage
const determineVehicleStatus = (
	tours: any[] | undefined,
	date: Date,
	workHours: { start: string; end: string },
) => {
	if (!tours || tours.length === 0) {
		return {
			status: "available",
			label: "Available All Day",
			color: "bg-green-50 text-green-700",
		};
	}

	// Parse work hours
	const startHourParts = workHours.start.split(":");
	const endHourParts = workHours.end.split(":");
	const workStartHour = Number.parseInt(startHourParts[0], 10);
	const workEndHour = Number.parseInt(endHourParts[0], 10);

	// Current day's boundaries
	const currentDayStart = startOfDay(date);
	const currentDayEnd = endOfDay(date);

	// Track the total coverage of the day by tours
	let totalCoveredMinutes = 0;
	const workDayMinutes = (workEndHour - workStartHour) * 60;

	// Convert work hours to Date objects for today
	const workDayStart = new Date(date);
	workDayStart.setHours(workStartHour, 0, 0, 0);

	const workDayEnd = new Date(date);
	workDayEnd.setHours(workEndHour, 0, 0, 0);

	// Calculate covered time for each tour
	tours.forEach((tour) => {
		// Get tour times
		const tourStartDate = tour.firstStop?.datetime_start
			? new Date(tour.firstStop.datetime_start)
			: tour.firstStop?.datetime_end
				? new Date(tour.firstStop.datetime_end)
				: null;

		const tourEndDate = tour.lastStop?.datetime_start
			? new Date(tour.lastStop.datetime_start)
			: tour.lastStop?.datetime_end
				? new Date(tour.lastStop.datetime_end)
				: null;

		if (!tourStartDate || !tourEndDate) {
			return;
		}

		// Calculate this tour's coverage within working hours
		const effectiveStartTime = new Date(
			Math.max(
				Math.max(currentDayStart.getTime(), tourStartDate.getTime()),
				workDayStart.getTime(),
			),
		);

		const effectiveEndTime = new Date(
			Math.min(
				Math.min(currentDayEnd.getTime(), tourEndDate.getTime()),
				workDayEnd.getTime(),
			),
		);

		if (effectiveEndTime > effectiveStartTime) {
			// Calculate minutes covered by this tour
			const coveredMinutes =
				(effectiveEndTime.getTime() - effectiveStartTime.getTime()) /
				(1000 * 60);
			totalCoveredMinutes += coveredMinutes;
		}
	});

	// Calculate coverage percentage
	const coveragePercentage = Math.min(
		100,
		(totalCoveredMinutes / workDayMinutes) * 100,
	);

	// Determine status based on coverage
	if (coveragePercentage >= 90) {
		return {
			status: "booked",
			label: "Fully Booked",
			color: "bg-red-50 text-red-700",
		};
	}

	if (coveragePercentage > 0) {
		return {
			status: "partial",
			label: "Partial Availability",
			color: "bg-amber-50 text-amber-700",
		};
	}

	return {
		status: "available",
		label: "Available All Day",
		color: "bg-green-50 text-green-700",
	};
};

// Render timeline for a vehicle on a specific day
const DayTimeline = ({
	date,
	tours,
	workHours,
}: {
	date: Date;
	tours: any[] | undefined;
	utils: any;
	workHours: { start: string; end: string };
}) => {
	// Format the date as day name
	const dayName = new Intl.DateTimeFormat("en-GB", {
		weekday: "long",
	}).format(date);
	const dateString = new Intl.DateTimeFormat("en-GB", {
		month: "long",
		day: "numeric",
	}).format(date);

	// Determine status based on tours
	const status = determineVehicleStatus(tours, date, workHours);

	// Parse work hours for timeline calculations
	const startHourParts = workHours.start.split(":");
	const endHourParts = workHours.end.split(":");
	const startHour = Number.parseInt(startHourParts[0], 10);
	const endHour = Number.parseInt(endHourParts[0], 10);
	const totalHours = endHour - startHour;

	// Current day's boundaries
	const currentDayStart = startOfDay(date);
	const currentDayEnd = endOfDay(date);

	// Sort tours by start time if available
	const sortedTours = tours?.length
		? [...tours].sort((a, b) => {
				if (
					!a.firstStop?.datetime_start ||
					!b.firstStop?.datetime_start
				) {
					return 0;
				}
				return (
					new Date(a.firstStop.datetime_start).getTime() -
					new Date(b.firstStop.datetime_start).getTime()
				);
			})
		: [];

	// Calculate time spans covered by tours
	const coveredTimeSpans: { start: number; end: number }[] = [];

	sortedTours.forEach((tour) => {
		const tourStartDate = tour.firstStop?.datetime_start
			? new Date(tour.firstStop.datetime_start)
			: tour.firstStop?.datetime_end
				? new Date(tour.firstStop.datetime_end)
				: null;

		const tourEndDate = tour.lastStop?.datetime_start
			? new Date(tour.lastStop.datetime_start)
			: tour.lastStop?.datetime_end
				? new Date(tour.lastStop.datetime_end)
				: null;

		if (!tourStartDate || !tourEndDate) {
			return;
		}

		// Calculate effective time within this day
		const displayStartTime = new Date(
			Math.max(currentDayStart.getTime(), tourStartDate.getTime()),
		);

		const displayEndTime = new Date(
			Math.min(currentDayEnd.getTime(), tourEndDate.getTime()),
		);

		// Convert to hours for timeline positioning
		const displayStartHour =
			displayStartTime.getHours() + displayStartTime.getMinutes() / 60;

		const displayEndHour =
			displayEndTime.getHours() + displayEndTime.getMinutes() / 60;

		// Clamp to work hours
		const effectiveStartHour = Math.max(displayStartHour, startHour);
		const effectiveEndHour = Math.min(displayEndHour, endHour);

		if (effectiveEndHour > effectiveStartHour) {
			coveredTimeSpans.push({
				start: effectiveStartHour,
				end: effectiveEndHour,
			});
		}
	});

	// Sort and merge overlapping spans
	coveredTimeSpans.sort((a, b) => a.start - b.start);
	const mergedSpans: { start: number; end: number }[] = [];

	for (const span of coveredTimeSpans) {
		if (mergedSpans.length === 0) {
			mergedSpans.push({ ...span });
			continue;
		}

		const lastSpan = mergedSpans[mergedSpans.length - 1];

		if (span.start <= lastSpan.end) {
			// Merge overlapping spans
			lastSpan.end = Math.max(lastSpan.end, span.end);
		} else {
			// Add non-overlapping span
			mergedSpans.push({ ...span });
		}
	}

	// Calculate gaps between tours
	const timeGaps: {
		startHour: number;
		endHour: number;
		hours: number;
	}[] = [];

	// Find gaps between work start and first tour, between tours, and between last tour and work end
	if (mergedSpans.length === 0) {
		// If no tours, the whole day is available
		timeGaps.push({
			startHour: startHour,
			endHour: endHour,
			hours: endHour - startHour,
		});
	} else {
		// Check gap at the start of the day
		if (mergedSpans[0].start > startHour) {
			timeGaps.push({
				startHour: startHour,
				endHour: mergedSpans[0].start,
				hours: mergedSpans[0].start - startHour,
			});
		}

		// Check gaps between tours
		for (let i = 0; i < mergedSpans.length - 1; i++) {
			const currentEnd = mergedSpans[i].end;
			const nextStart = mergedSpans[i + 1].start;

			if (nextStart > currentEnd) {
				timeGaps.push({
					startHour: currentEnd,
					endHour: nextStart,
					hours: nextStart - currentEnd,
				});
			}
		}

		// Check gap at the end of the day
		const lastSpan = mergedSpans[mergedSpans.length - 1];
		if (lastSpan.end < endHour) {
			timeGaps.push({
				startHour: lastSpan.end,
				endHour: endHour,
				hours: endHour - lastSpan.end,
			});
		}
	}

	// Create hour markers for the timeline (but without numbers)
	const hours = Array.from({ length: totalHours }, (_, i) => i + startHour);

	return (
		<div className="mb-4">
			<div className="flex items-center mb-2">
				<CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
				<span className="text-sm font-medium mr-2">
					{dayName}, {dateString}
				</span>
				<Badge
					status="info"
					className={cn(
						"ml-auto inline-flex items-center",
						status.color,
					)}
				>
					<ClockIcon className="mr-1 h-3 w-3" />
					<span className="whitespace-nowrap">{status.label}</span>
				</Badge>
			</div>

			<div className="relative h-8 bg-gray-100 rounded-md overflow-hidden">
				{/* Hour markers (grid only, no numbers) */}
				<div className="flex h-full">
					{hours.map((hour) => (
						<div
							key={hour}
							className="flex-1 border-r border-gray-200"
						/>
					))}
				</div>

				{/* Tour blocks */}
				{sortedTours.map((tour, index) => {
					// Determine the tour's overall timeline
					const tourStartDate = tour.firstStop?.datetime_start
						? new Date(tour.firstStop.datetime_start)
						: tour.firstStop?.datetime_end
							? new Date(tour.firstStop.datetime_end)
							: null;

					const tourEndDate = tour.lastStop?.datetime_start
						? new Date(tour.lastStop.datetime_start)
						: tour.lastStop?.datetime_end
							? new Date(tour.lastStop.datetime_end)
							: null;

					if (!tourStartDate || !tourEndDate) {
						return null;
					}

					// Calculate appropriate start and end for this day's display
					let displayStartTime: Date;
					let displayEndTime: Date;

					// If tour starts before or on this day and ends after or on this day
					if (
						tourStartDate <= currentDayEnd &&
						tourEndDate >= currentDayStart
					) {
						// For display start time: use later of day start or tour start
						displayStartTime = new Date(
							Math.max(
								currentDayStart.getTime(),
								tourStartDate.getTime(),
							),
						);

						// For display end time: use earlier of day end or tour end
						displayEndTime = new Date(
							Math.min(
								currentDayEnd.getTime(),
								tourEndDate.getTime(),
							),
						);

						// Convert to hours for timeline positioning
						const displayStartHour =
							displayStartTime.getHours() +
							displayStartTime.getMinutes() / 60;

						const displayEndHour =
							displayEndTime.getHours() +
							displayEndTime.getMinutes() / 60;

						// Calculate position and width, clamping to work hours
						const effectiveStartHour = Math.max(
							displayStartHour,
							startHour,
						);
						const effectiveEndHour = Math.min(
							displayEndHour,
							endHour,
						);

						const startPosition =
							((effectiveStartHour - startHour) / totalHours) *
							100;
						const width =
							((effectiveEndHour - effectiveStartHour) /
								totalHours) *
							100;

						// Format destination from tour data
						const destination = tour.firstStop?.city
							? `${tour.firstStop.city}${tour.firstStop.street ? `, ${tour.firstStop.street}` : ""}`
							: "Unknown destination";

						// Format times for display
						const startTimeDisplay = format(tourStartDate, "HH:mm");
						const endTimeDisplay = format(tourEndDate, "HH:mm");

						return (
							<TooltipProvider key={`tour-${tour.id}-${index}`}>
								<Tooltip>
									<TooltipTrigger asChild>
										<div
											className="absolute top-0 h-full bg-blue-500 rounded-sm"
											style={{
												left: `${startPosition}%`,
												width: `${width}%`,
											}}
										/>
									</TooltipTrigger>
									<TooltipContent>
										<p className="font-medium">
											{tour.tourNumber || destination}
										</p>
										<p className="text-xs">
											{startTimeDisplay} -{" "}
											{endTimeDisplay}
										</p>
										{!isSameDay(
											tourStartDate,
											tourEndDate,
										) && (
											<p className="text-xs font-medium text-blue-600">
												Multi-day tour
											</p>
										)}
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						);
					}

					return null; // Tour doesn't appear on this day
				})}

				{/* Gap indicators */}
				{timeGaps.map((gap, index) => {
					// Calculate position and width for this gap
					const startPosition =
						((gap.startHour - startHour) / totalHours) * 100;
					const width =
						((gap.endHour - gap.startHour) / totalHours) * 100;

					// Only show gaps >= 1 hour
					if (gap.hours < 1) {
						return null;
					}

					// Round to 1 decimal place for display
					const displayHours = Math.round(gap.hours * 10) / 10;

					return (
						<TooltipProvider key={`gap-${index}`}>
							<Tooltip>
								<TooltipTrigger asChild>
									<div
										className="absolute top-0 flex items-center justify-center text-center"
										style={{
											left: `${startPosition}%`,
											width: `${width}%`,
											height: "100%",
											zIndex: 10,
										}}
									>
										<div className="relative w-full h-full">
											{/* Gap indicator with diagonal stripes background */}
											<div
												className="absolute inset-0"
												style={{
													background: `repeating-linear-gradient(
														45deg,
														rgba(74, 222, 128, 0.1),
														rgba(74, 222, 128, 0.1) 5px,
														rgba(134, 239, 172, 0.05) 5px,
														rgba(134, 239, 172, 0.05) 10px
													)`,
												}}
											/>

											{/* Gap duration label - show only for gaps >= 3 hours */}
											{gap.hours >= 3 && (
												<div className="absolute inset-0 flex items-center justify-center">
													<span className="text-xs font-medium text-green-700 bg-green-50 px-1.5 py-0.5 rounded-sm whitespace-nowrap">
														{displayHours}h gap
													</span>
												</div>
											)}
										</div>
									</div>
								</TooltipTrigger>
								<TooltipContent>
									<p className="font-medium">
										Available Time Slot
									</p>
									<p className="text-xs">
										{format(
											new Date().setHours(
												gap.startHour,
												(gap.startHour % 1) * 60,
											),
											"HH:mm",
										)}{" "}
										-{" "}
										{format(
											new Date().setHours(
												gap.endHour,
												(gap.endHour % 1) * 60,
											),
											"HH:mm",
										)}
									</p>
									<p className="text-xs font-medium text-green-600">
										{displayHours} hour gap
									</p>
								</TooltipContent>
							</Tooltip>
						</TooltipProvider>
					);
				})}
			</div>

			<div className="mt-1 text-xs text-muted-foreground flex justify-between">
				<span>{workHours.start}</span>
				<span>{workHours.end}</span>
			</div>
		</div>
	);
};

// Component for a single vehicle card
const VehicleCard = ({ vehicle }: { vehicle: any }) => {
	// Get today and tomorrow dates
	const today = new Date();
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);

	// Default work hours if not provided
	const workHours = vehicle.workHours || { start: "06:00", end: "22:00" };

	// Use custom hook to access dashboard data and utilities
	const { utils } = useDashboard();

	// Filter tours for today and tomorrow
	const todayTours = filterToursByDate(vehicle.tours || [], today);
	const tomorrowTours = filterToursByDate(vehicle.tours || [], tomorrow);

	return (
		<div className="p-4 border rounded-lg mb-6">
			<div className="flex items-center mb-4">
				<TruckIcon className="h-5 w-5 mr-2 text-primary" />
				<div>
					<h4 className="font-medium">
						{vehicle.licensePlate || "Unknown Vehicle"}
					</h4>
					{vehicle.owner && (
						<p className="text-sm text-muted-foreground">
							{vehicle.owner.nameLine1}
						</p>
					)}
				</div>
			</div>

			{/* Today's timeline */}
			<DayTimeline
				date={today}
				tours={todayTours}
				utils={utils}
				workHours={workHours}
			/>

			{/* Tomorrow's timeline */}
			<DayTimeline
				date={tomorrow}
				tours={tomorrowTours}
				utils={utils}
				workHours={workHours}
			/>
		</div>
	);
};

// Loading state for vehicle cards
const VehicleCardSkeleton = () => (
	<div className="p-4 border rounded-lg mb-6">
		<div className="flex items-center mb-4">
			<Skeleton className="h-5 w-5 mr-2" />
			<div className="w-full">
				<Skeleton className="h-6 w-40 mb-1" />
				<Skeleton className="h-4 w-20" />
			</div>
		</div>
		<div className="mb-4">
			<Skeleton className="h-6 w-48 mb-2" />
			<Skeleton className="h-10 w-full" />
			<Skeleton className="h-4 w-full mt-1" />
		</div>
		<div>
			<Skeleton className="h-6 w-48 mb-2" />
			<Skeleton className="h-10 w-full" />
			<Skeleton className="h-4 w-full mt-1" />
		</div>
	</div>
);

// Main component
export const VehicleAvailability = () => {
	const {
		vehicles,
		isLoading,
		error,
		page,
		setPage,
		totalPages = 1,
		total = 0,
	} = useDashboard();

	if (error) {
		return (
			<div className="p-4">
				<div className="text-red-500">Failed to load vehicle data</div>
			</div>
		);
	}

	return (
		<Card className="w-1/2">
			<CardHeader className="pb-3">
				<CardTitle>Vehicle Availability</CardTitle>
				<CardDescription>
					Manage vehicle assignments and find available time slots
				</CardDescription>
			</CardHeader>
			<CardContent>
				<ScrollArea className="h-[600px] pr-4">
					<div className="space-y-6">
						{isLoading ? (
							// Show skeletons while loading
							<>
								<VehicleCardSkeleton />
								<VehicleCardSkeleton />
							</>
						) : vehicles.length > 0 ? (
							// Show vehicle list
							vehicles.map((vehicle) => (
								<VehicleCard
									key={vehicle.id}
									vehicle={vehicle}
								/>
							))
						) : (
							// No vehicles message
							<div className="text-center py-8">
								<p className="text-gray-500">
									No vehicles found
								</p>
							</div>
						)}
					</div>
				</ScrollArea>

				{!isLoading && totalPages > 1 && (
					<div className="flex justify-between items-center mt-4 text-sm">
						<div>
							Showing {vehicles.length} of {total} vehicles
						</div>
						<div className="flex gap-2">
							<button
								type="button"
								onClick={() => setPage(Math.max(1, page - 1))}
								disabled={page === 1}
								className={`px-3 py-1 rounded ${
									page === 1
										? "bg-gray-100 text-gray-400"
										: "bg-gray-200 hover:bg-gray-300"
								}`}
							>
								Previous
							</button>
							<span className="px-3 py-1">
								Page {page} of {totalPages}
							</span>
							<button
								type="button"
								onClick={() =>
									setPage(Math.min(totalPages ?? 1, page + 1))
								}
								disabled={page === totalPages}
								className={`px-3 py-1 rounded ${
									page === totalPages
										? "bg-gray-100 text-gray-400"
										: "bg-gray-200 hover:bg-gray-300"
								}`}
							>
								Next
							</button>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
};

export default VehicleAvailability;
