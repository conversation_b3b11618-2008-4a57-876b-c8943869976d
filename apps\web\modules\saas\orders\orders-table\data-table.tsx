"use client";

import { useOrdersUI } from "@saas/orders/context/orders-ui-context";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import type { Order } from "../context/orders-ui-context";
import { useOrders } from "../hooks/use-orders";
import { useColumns } from "./columns";

// Delete dialog component
function DeleteOrderDialog() {
	const t = useTranslations();
	const {
		deleteOrder,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
	} = useOrdersUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteOrder) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent data-shortcuts-scope="orders-delete-dialog">
				<AlertDialogHeader>
					<AlertDialogTitle>
						{t("app.orders.delete.title")}
					</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							{t("app.orders.delete.description")}
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deleteOrder.order_number ? (
							<span>Order #{deleteOrder.order_number}</span>
						) : (
							<span>Order ID: {deleteOrder.id}</span>
						)}
					</div>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							{t("common.confirmation.cancel")}
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							{t("common.confirmation.delete")}
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// Table component with the delete dialog
export function OrdersDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange,
		customerId,
		setCustomerId,
	} = useOrders();
	const { setSelectedOrder } = useOrdersUI();
	const columns = useColumns();
	const orders: Order[] = data?.items ?? [];
	const t = useTranslations();

	// Convert from dateRange format in useOrders to the format expected by DataTable
	const formattedDateRange = {
		from: dateRange.startDate,
		to: dateRange.endDate,
	};

	return (
		<>
			<DataTable
				columns={columns}
				data={orders}
				defaultColumnVisibility={{
					id: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder={t("app.orders.table.searchPlaceholder")}
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="orders-shortcuts"
				dateRange={formattedDateRange}
				onDateRangeChange={setDateRange}
				dateRangeLabel={t("app.orders.table.dateFilter")}
			/>
			<DeleteOrderDialog />
		</>
	);
}
