import type {
	OrderStatus,
	SendOrderConfirmationEmailInput,
} from "@repo/api/src/routes/orders/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { vehicleKeys } from "@saas/vehicles/lib/api";
import { useDebounce } from "@shared/hooks/use-debounce";
import { apiClient } from "@shared/lib/api-client";
import { useQueries, useQuery } from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import {
	fetchOrderEmailLogs,
	orderKeys,
	useCreateOrderMutation,
	useDeleteOrderMutation,
	useOrderByIdQuery,
	useOrderEmailLogsQuery,
	useOrdersQuery,
	useOrdersWithUninvoicedItemsQuery,
	usePreviewOrderConfirmationEmail,
	usePreviewOrderConfirmationPDF,
	useSendOrderConfirmationEmailMutation,
	useUpdateOrderMutation,
} from "../lib/api";

// Fetch vehicles
export const fetchVehicles = async (organizationId: string, limit = 100) => {
	const response = await apiClient.vehicles.$get({
		query: {
			organizationId,
			limit: limit.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch vehicles");
	}

	return response.json();
};

// Hook for fetching vehicles
export function useVehicles(limit = 100) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	return useQuery({
		queryKey: [...vehicleKeys.all, "list", { organizationId, limit }],
		queryFn: () => fetchVehicles(organizationId, limit),
		enabled: !!organizationId,
	});
}

export function useOrders() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState<string>("");
	const [page, setPage] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(25);
	const [sorting, setSorting] = useState<SortingState>([
		{ id: "createdAt", desc: true },
	]);
	const [status, setStatus] = useState<OrderStatus | undefined>(undefined);
	const [customerId, setCustomerId] = useState<string | undefined>(undefined);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	// Helper function to handle date range updates from the DataTable component
	const handleDateRangeChange = (
		range: { from?: Date; to?: Date } | undefined,
	) => {
		setDateRange({
			startDate: range?.from,
			endDate: range?.to,
		});
	};

	const query = useOrdersQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		status,
		customerId,
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
	});

	const deleteMutation = useDeleteOrderMutation(activeOrganization?.id ?? "");

	// Wrap the delete function to refetch after deletion
	const deleteOrder = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete order error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange: handleDateRangeChange, // Use the helper function directly
		customerId,
		setCustomerId,
		refetch: query.refetch,
		deleteOrder,
	};
}

export function useOrderById(orderId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useOrderByIdQuery(activeOrganization?.id, orderId);
}

export function useOrderMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateOrderMutation(orgId);
	const updateMutation = useUpdateOrderMutation(orgId);
	const deleteMutation = useDeleteOrderMutation(orgId);
	const sendConfirmationEmailMutation =
		useSendOrderConfirmationEmailMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		try {
			const result = await createMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Create order error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const updateWithCallback = async (data: any) => {
		try {
			const result = await updateMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Update order error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const deleteWithCallback = async (id: string) => {
		try {
			const result = await deleteMutation.mutateAsync(id);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Delete order error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const sendConfirmationEmailWithCallback = async (
		data: { id: string } & SendOrderConfirmationEmailInput,
	) => {
		try {
			const result =
				await sendConfirmationEmailMutation.mutateAsync(data);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		} catch (error) {
			console.error("Send order confirmation email error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	return {
		createOrder: createWithCallback,
		updateOrder: updateWithCallback,
		deleteOrder: deleteWithCallback,
		sendOrderConfirmationEmail: sendConfirmationEmailWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending ||
			sendConfirmationEmailMutation.isPending,
	};
}

// Dedicated hook for sending order confirmation email
export function useSendOrderConfirmationEmail() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const mutation = useSendOrderConfirmationEmailMutation(orgId);

	const sendConfirmationEmail = async (
		data: { id: string } & SendOrderConfirmationEmailInput,
	) => {
		if (!orgId) {
			throw new Error("No active organization");
		}

		return mutation.mutateAsync(data);
	};

	return {
		sendConfirmationEmail,
		isLoading: mutation.isPending,
		isError: mutation.isError,
		error: mutation.error,
		data: mutation.data,
	};
}

// Hook for previewing order confirmation PDF
export function useOrderConfirmationPDFPreview(
	orderId?: string,
	enabled = true,
) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	return usePreviewOrderConfirmationPDF(orgId, orderId, { enabled });
}

// Hook for previewing order confirmation email
export function useOrderConfirmationEmailPreview(
	orderId?: string,
	recipientEmail?: string,
	enabled = true,
) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	return usePreviewOrderConfirmationEmail(orgId, orderId, recipientEmail, {
		enabled,
	});
}

// Hook for fetching addresses that can be used in order stops
export function useAddressesForOrderStop(
	counterpartyId?: string,
	stopType?: string,
) {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Only enable the query if we have both organization ID and counterparty ID
	const enabled = !!organizationId && !!counterpartyId;

	// Map stop type to address type
	let addressType: string | undefined;
	if (stopType) {
		// The API expects LOADING, UNLOADING, or PRIMARY
		addressType = stopType.toUpperCase();
	}

	const addressesQuery = useQuery({
		// Include the stopType in the query key to refetch when it changes
		queryKey: ["addresses", organizationId, counterpartyId, addressType],
		queryFn: async () => {
			// Make sure we have both IDs
			if (!organizationId || !counterpartyId) {
				return { items: [] };
			}

			try {
				// Use the API client directly with a boolean value
				// Even though TypeScript might show an error, the client should handle the conversion correctly
				const response = await apiClient.counterparties[
					":counterpartyId"
				].addresses.$get({
					param: { counterpartyId },
					query: {
						organizationId,
						counterpartyId,
						type: addressType, // Filter by address type if provided
						includeGlobal: "true",
					},
				});

				if (!response.ok) {
					throw new Error("Failed to fetch addresses");
				}

				const addresses = await response.json();
				return { items: addresses };
			} catch (error) {
				console.error("Error fetching addresses:", error);
				return { items: [] };
			}
		},
		enabled,
		placeholderData: { items: [] },
	});

	// Format addresses for the combobox
	const addressOptions =
		addressesQuery.data?.items.map((address: any) => {
			const addressData = address.address;

			// Create a formatted label for display
			const label = [
				addressData.street,
				addressData.city,
				addressData.zipCode,
				addressData.country,
			]
				.filter(Boolean)
				.join(", ");

			return {
				value: addressData.id,
				label,
				addressData: {
					...addressData,
					entrance_number: address.entrance_number || "",
					addressSupplement: addressData.addressSupplement || "",
				},
				// Store the complete original address with loading/unloading configs
				originalAddress: address,
			};
		}) || [];

	return {
		addressOptions,
		isLoading: addressesQuery.isLoading,
		isError: addressesQuery.isError,
		error: addressesQuery.error,
	};
}

// Hook for fetching order email logs
export function useOrderEmailLogs(orderId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const query = useOrderEmailLogsQuery(orgId, orderId);

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
	};
}

// Hook for fetching email logs for multiple orders
export function useOrderEmailLogsForMultipleOrders(orderIds: string[] = []) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	// Use a single query to fetch all email logs data
	const query = useQueries({
		queries: orderIds.map((orderId) => ({
			queryKey: [...orderKeys.detail(orgId, orderId), "email-logs"],
			queryFn: () => {
				if (!orgId || !orderId) {
					return null;
				}
				return fetchOrderEmailLogs(orgId, orderId);
			},
			enabled: !!orgId && !!orderId,
		})),
	});

	// Process the results into a map
	const results: Record<string, any> = {};

	// Only process if we have results and orderIds match
	if (query.length === orderIds.length) {
		orderIds.forEach((orderId, index) => {
			if (query[index].data) {
				results[orderId] = query[index].data;
			}
		});
	}

	return results;
}

// Hook for fetching orders with uninvoiced items
export function useOrdersWithUninvoicedItems(options?: {
	excludeCreditNoteId?: string;
}) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [status, setStatus] = useState<OrderStatus | undefined>(undefined);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});
	const [customerId, setCustomerId] = useState<string | undefined>(undefined);
	const [onlyWithUninvoicedItems, setOnlyWithUninvoicedItems] =
		useState<boolean>(true);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useOrdersWithUninvoicedItemsQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		status,
		customerId,
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
		onlyWithUninvoicedItems,
		excludeCreditNoteId: options?.excludeCreditNoteId,
	});

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		status,
		setStatus,
		dateRange,
		setDateRange,
		customerId,
		setCustomerId,
		onlyWithUninvoicedItems,
		setOnlyWithUninvoicedItems,
		refetch: query.refetch,
	};
}
