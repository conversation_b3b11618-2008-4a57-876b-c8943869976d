import type {
	AdminEditInvoiceLineItemsInput,
	InvoiceStatus,
} from "@repo/api/src/routes/invoices/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import { toast } from "sonner";
import type {
	CancelInvoiceParams,
	MarkPaidParams,
	SendInvoiceEmailParams,
} from "../lib/api";
import {
	generateInvoicePDFPreview,
	getInvoicePdf,
	invoiceKeys,
	useAdminEditInvoiceLineItemsMutation,
	useCancelInvoiceMutation,
	useCreateInvoiceMutation,
	useInvoiceByIdQuery,
	useInvoicesQuery,
	useMarkInvoicePaidMutation,
	useSendInvoiceEmailMutation,
} from "../lib/api";

export function useInvoices(initialOrderId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [status, setStatus] = useState<InvoiceStatus | undefined>(undefined);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});
	const [orderId, setOrderId] = useState<string | undefined>(initialOrderId);
	const [customerId, setCustomerId] = useState<string | undefined>(undefined);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useInvoicesQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		status,
		orderId,
		customerId,
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
	});

	const cancelMutation = useCancelInvoiceMutation(
		activeOrganization?.id ?? "",
	);
	const markPaidMutation = useMarkInvoicePaidMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the cancel function to refetch after cancellation
	const cancelInvoice = async (id: string | CancelInvoiceParams) => {
		if (activeOrganization?.id) {
			try {
				await cancelMutation.mutateAsync(id);
			} catch (error) {
				console.error("Cancel invoice error:", error);
			}
		}
	};

	// Wrap the mark as paid function to refetch after payment
	const markInvoicePaid = async (idOrParams: string | MarkPaidParams) => {
		if (activeOrganization?.id) {
			try {
				await markPaidMutation.mutateAsync(idOrParams);
			} catch (error) {
				console.error("Mark invoice paid error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange,
		orderId,
		setOrderId,
		customerId,
		setCustomerId,
		refetch: query.refetch,
		cancelInvoice,
		markInvoicePaid,
	};
}

export function useInvoiceById(invoiceId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useInvoiceByIdQuery(activeOrganization?.id, invoiceId);
}

export function useInvoiceMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";
	const queryClient = useQueryClient();

	// Helper function to handle refetching invoices after mutations
	const refetchInvoices = () => {
		// Simply invalidate all invoice queries - no need for specific order ID
		queryClient.invalidateQueries({
			queryKey: invoiceKeys.all,
		});

		// Call the external onSuccess callback if provided
		if (options?.onSuccess) {
			options.onSuccess();
		}
	};

	const createMutation = useCreateInvoiceMutation(orgId);
	const markPaidMutation = useMarkInvoicePaidMutation(orgId);
	const cancelMutation = useCancelInvoiceMutation(orgId);
	const sendEmailMutation = useSendInvoiceEmailMutation(orgId);
	const adminEditMutation = useAdminEditInvoiceLineItemsMutation(orgId);

	// New mutation for generating PDF preview
	const generatePreviewMutation = useMutation({
		mutationFn: (data: any) => generateInvoicePDFPreview(data),
		// No automatic onSuccess toast for previews usually, caller handles the PDF data
		onError: (error: Error) => {
			toast.error(
				error.message || "Failed to generate invoice PDF preview",
			);
			console.error("Generate invoice preview error:", error);
		},
	});

	// New mutation for fetching PDF of existing invoice
	const getPdfMutation = useMutation({
		mutationFn: (invoiceId: string) => getInvoicePdf(invoiceId, orgId),
		onError: (error: Error) => {
			toast.error(error.message || "Failed to get invoice PDF");
			console.error("Get invoice PDF error:", error);
		},
	});

	// Add custom success callback if provided
	const createWithCallback = async (data: any) => {
		try {
			const result = await createMutation.mutateAsync(data);
			// Handle refetching here
			refetchInvoices();
			return result;
		} catch (error) {
			console.error("Create invoice error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const markPaidWithCallback = async (
		idOrParams: string | MarkPaidParams,
	) => {
		try {
			const result = await markPaidMutation.mutateAsync(idOrParams);
			// Handle refetching here - no need to extract order IDs
			refetchInvoices();
			return result;
		} catch (error) {
			console.error("Mark invoice paid error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const cancelWithCallback = async (
		idOrParams: string | CancelInvoiceParams,
	) => {
		try {
			const result = await cancelMutation.mutateAsync(idOrParams);
			// Handle refetching here - no need to extract order IDs
			refetchInvoices();
			return result;
		} catch (error) {
			console.error("Cancel invoice error:", error);
			// Re-throw the error so it can be caught by the caller
			throw error;
		}
	};

	const sendEmailWithCallback = async (params: SendInvoiceEmailParams) => {
		try {
			const result = await sendEmailMutation.mutateAsync(params);
			return result;
		} catch (error) {
			console.error("Send invoice email error:", error);
			throw error;
		}
	};

	const getInvoicePdfCallback = async (invoiceId: string) => {
		try {
			return await getPdfMutation.mutateAsync(invoiceId);
		} catch (error) {
			console.error("Get invoice PDF error:", error);
			throw error;
		}
	};

	// Admin edit invoice line items with callback
	const adminEditInvoiceLineItemsWithCallback = async ({
		id,
		lineItemsToUpdate,
	}: {
		id: string;
		lineItemsToUpdate: AdminEditInvoiceLineItemsInput["lineItemsToUpdate"];
	}) => {
		try {
			const result = await adminEditMutation.mutateAsync({
				id,
				lineItemsToUpdate,
			});
			// Refetch to update all invoice data
			refetchInvoices();
			// Also invalidate the specific invoice detail to ensure it's updated
			queryClient.invalidateQueries({
				queryKey: invoiceKeys.detail(orgId, id),
			});
			return result;
		} catch (error) {
			console.error("Admin edit invoice line items error:", error);
			throw error;
		}
	};

	return {
		createInvoice: createWithCallback,
		markInvoicePaid: markPaidWithCallback,
		cancelInvoice: cancelWithCallback,
		sendInvoiceEmail: sendEmailWithCallback,
		generateInvoicePreview: generatePreviewMutation.mutateAsync,
		getInvoicePdf: getInvoicePdfCallback,
		adminEditInvoiceLineItems: adminEditInvoiceLineItemsWithCallback,
		isGeneratingPreview: generatePreviewMutation.isPending,
		isLoading:
			createMutation.isPending ||
			markPaidMutation.isPending ||
			cancelMutation.isPending ||
			sendEmailMutation.isPending ||
			generatePreviewMutation.isPending ||
			getPdfMutation.isPending ||
			adminEditMutation.isPending,
	};
}
