import {
	type SendInvoiceEmailParams,
	useSendInvoiceEmailMutation,
} from "@saas/invoices/lib/api";
import {
	type SendOfferEmailParams,
	useSendOfferEmailMutation,
} from "@saas/offers/lib/api";
import { useSendOrderConfirmationEmailMutation } from "@saas/orders/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useSendTransportOrderEmailMutation } from "@saas/tours/lib/api";
import { useMemo } from "react";

import type {
	SendEmailParams,
	UseUniversalSendEmailReturn,
} from "../types/email-preview";

/**
 * Universal hook for sending emails that abstracts different entity-specific mutations
 */
export function useUniversalSendEmail(): UseUniversalSendEmailReturn {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";

	// Entity-specific send mutations
	const sendInvoiceEmail = useSendInvoiceEmailMutation(organizationId);
	const sendOfferEmail = useSendOfferEmailMutation(organizationId);
	const sendOrderConfirmationEmail =
		useSendOrderConfirmationEmailMutation(organizationId);
	const sendTransportOrderEmail =
		useSendTransportOrderEmailMutation(organizationId);

	// Determine if any mutation is loading
	const isLoading =
		sendInvoiceEmail.isPending ||
		sendOfferEmail.isPending ||
		sendOrderConfirmationEmail.isPending ||
		sendTransportOrderEmail.isPending;

	// Get the most recent error
	const error =
		sendInvoiceEmail.error ||
		sendOfferEmail.error ||
		sendOrderConfirmationEmail.error ||
		sendTransportOrderEmail.error ||
		null;

	const sendEmail = useMemo(
		() =>
			async ({
				entityId,
				entityType,
				recipientEmail,
				ccEmails,
				bccEmails,
				documentIds,
				customSubject,
				documentFilenames,
			}: SendEmailParams): Promise<void> => {
				switch (entityType) {
					case "invoice": {
						const params: SendInvoiceEmailParams = {
							id: entityId,
							...(recipientEmail && { recipientEmail }),
							...(ccEmails && { ccEmails }),
							...(bccEmails && { bccEmails }),
							...(documentIds && { documentIds }),
							...(customSubject && { customSubject }),
							...(documentFilenames && { documentFilenames }),
						};
						await sendInvoiceEmail.mutateAsync(params);
						return;
					}

					case "offer": {
						const params: SendOfferEmailParams = {
							id: entityId,
							...(recipientEmail && { recipientEmail }),
							...(ccEmails &&
								ccEmails.length > 0 && { ccEmails }),
							...(bccEmails &&
								bccEmails.length > 0 && { bccEmails }),
						};
						await sendOfferEmail.mutateAsync(params);
						return;
					}

					case "order-confirmation": {
						await sendOrderConfirmationEmail.mutateAsync({
							id: entityId,
							recipientEmail,
							ccEmails,
							bccEmails,
							documentIds,
							...(documentFilenames && { documentFilenames }),
						});
						return;
					}

					case "transport-order": {
						await sendTransportOrderEmail.mutateAsync({
							id: entityId,
							recipientEmail,
							ccEmails,
							bccEmails,
							documentIds,
							...(documentFilenames && { documentFilenames }),
						});
						return;
					}

					default:
						throw new Error(
							`Unsupported entity type: ${entityType}`,
						);
				}
			},
		[
			sendInvoiceEmail,
			sendOfferEmail,
			sendOrderConfirmationEmail,
			sendTransportOrderEmail,
		],
	);

	return {
		sendEmail,
		isLoading,
		error,
	};
}
