import type {
	CreateTourInput,
	TourStopInput,
	UpdateTourInput,
} from "@repo/api/src/routes/tours/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import { useMutation } from "@tanstack/react-query";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";
import {
	type SendTransportOrderEmailParams,
	type TransportOrderPDFPreviewResponse,
	generateTransportOrderPDFPreview,
	useCreateTourMutation,
	useDeleteTourMutation,
	useSendTransportOrderEmailMutation,
	useTourByIdQuery,
	useTourEmailLogsQuery,
	useToursQuery,
	useTransportOrderEmailPreview,
	useUpdateTourMutation,
} from "../lib/api";

export function useTours() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [dateRange, setDateRange] = useState<{
		startDate?: Date;
		endDate?: Date;
	}>({});

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useToursQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
		startDate: dateRange.startDate,
		endDate: dateRange.endDate,
	});

	const deleteMutation = useDeleteTourMutation(activeOrganization?.id ?? "");

	// Wrap the delete function to refetch after deletion
	const deleteTour = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete tour error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		dateRange,
		setDateRange,
		refetch: query.refetch,
		deleteTour,
	};
}

export function useTourById(tourId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useTourByIdQuery(activeOrganization?.id, tourId);
}

export function useTourMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateTourMutation(orgId);
	const updateMutation = useUpdateTourMutation();
	const deleteMutation = useDeleteTourMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (data: CreateTourInput) => {
		const result = await createMutation.mutateAsync(data);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async ({
		tourId,
		tourData,
		stopOrders,
		tourStops,
	}: {
		tourId: string;
		tourData: UpdateTourInput;
		stopOrders?: { stopId: string; position: number }[];
		tourStops?: TourStopInput[];
	}) => {
		const result = await updateMutation.mutateAsync({
			tourId,
			tourData,
			stopOrders,
			tourStops,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createTour: createWithCallback,
		updateTour: updateWithCallback,
		deleteTour: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// New hook for transport order operations
export function useTransportOrderOperations(
	tourId?: string,
	enabled?: boolean,
) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id || "";

	// Use the existing hook for email preview
	const {
		data: emailPreview,
		isLoading: isLoadingEmailPreview,
		refetch: refetchEmailPreview,
	} = useTransportOrderEmailPreview(
		orgId,
		tourId || "",
		undefined, // No specific recipient email
		{
			enabled: enabled !== undefined ? enabled : false, // Disable auto-fetching to prevent double API calls unless explicitly enabled
		},
	);

	// Use tour query to be able to refetch after sending
	const { data: tourData, refetch: refetchTour } = useTourByIdQuery(
		orgId,
		tourId || "",
	);

	// Mutation for PDF preview generation
	const generatePreviewMutation = useMutation({
		mutationFn: (tourId: string) =>
			generateTransportOrderPDFPreview(orgId, tourId),
		onError: (error: Error) => {
			console.error("Generate transport order preview error:", error);
		},
	});

	// Mutation for sending email
	const sendEmailMutation = useSendTransportOrderEmailMutation(orgId);

	// Function to generate a transport order preview
	const generateTransportOrderPreview = async (
		tourId: string,
	): Promise<TransportOrderPDFPreviewResponse> => {
		try {
			return await generatePreviewMutation.mutateAsync(tourId);
		} catch (error) {
			console.error("Failed to generate transport order preview:", error);
			throw error;
		}
	};

	// Function to send a transport order email
	const sendTransportOrderEmail = async (
		params: SendTransportOrderEmailParams,
	) => {
		try {
			const result = await sendEmailMutation.mutateAsync(params);
			// Refetch tour data to get updated status and tour number
			if (result.success && tourId) {
				await refetchTour();
			}
			return result;
		} catch (error: any) {
			console.error("Failed to send transport order email:", error);
			throw error;
		}
	};

	return {
		// Email preview data and state
		emailPreview,
		isLoadingEmailPreview,
		refetchEmailPreview,

		// Tour data
		tourData,
		refetchTour,

		// PDF preview function and state
		generateTransportOrderPreview,
		isGeneratingPreview: generatePreviewMutation.isPending,

		// Email sending function and state
		sendTransportOrderEmail,
		isSendingEmail: sendEmailMutation.isPending,
	};
}

// Hook for email logs for a specific tour
export function useTourEmailLogs(tourId?: string) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id || "";

	const {
		data: emailLogs,
		isLoading,
		error,
		refetch,
	} = useTourEmailLogsQuery(orgId, tourId);

	return {
		emailLogs,
		isLoading,
		error,
		refetch,
	};
}
