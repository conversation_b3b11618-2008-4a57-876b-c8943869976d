"use client";
import { useSession } from "@saas/auth/hooks/use-session";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { format } from "date-fns";
import { Clock, MoreVertical, Pin, Reply, Trash2 } from "lucide-react";
import { useState } from "react";
import {
	useCommentMutations,
	useCommentReactionMutations,
} from "../hooks/use-threads";
import { CommentReactions } from "./CommentReactions";
import { EmojiPicker } from "./EmojiPicker";
import { RichCommentRenderer } from "./RichCommentRenderer";

interface CommentItemProps {
	comment: any; // TODO: Type this properly
	isReply?: boolean;
	isPinned?: boolean;
	onReply?: (commentId: string, authorName: string) => void;
	onEdit?: (
		commentId: string,
		authorName: string,
		content: string,
		richContent?: string,
	) => void;
}

function formatTime(dateString: string) {
	return format(new Date(dateString), "HH:mm");
}

export function CommentItem({
	comment,
	isReply = false,
	isPinned = false,
	onReply,
	onEdit,
}: CommentItemProps) {
	const { user } = useSession();
	const [isHovered, setIsHovered] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
	const { updateComment, deleteComment } = useCommentMutations({
		onSuccess: () => {
			// Success handled by parent component
		},
	});

	const { toggleReaction, isLoading: isReactionLoading } =
		useCommentReactionMutations();

	// Check if current user can edit this comment
	const canEdit = user?.id === comment.authorId;

	// Don't render deleted comments
	if (comment.status === "DELETED") {
		return (
			<div
				className={`${isReply ? "ml-12 border-l-2 border-border pl-4" : ""}`}
			>
				<div className="flex gap-3 py-3">
					<div className="h-9 w-9 rounded-full bg-muted flex items-center justify-center">
						<Trash2 className="w-4 h-4 text-muted-foreground" />
					</div>
					<div className="flex-1 min-w-0">
						<div className="text-sm text-muted-foreground italic">
							This comment has been deleted
						</div>
					</div>
				</div>
			</div>
		);
	}

	const handleReact = async (emoji: string, hasReacted: boolean) => {
		try {
			await toggleReaction(comment.id, emoji, hasReacted);
		} catch (error) {
			console.error("Failed to toggle reaction:", error);
		}
	};

	const handleEmojiSelect = async (emoji: string) => {
		try {
			// Check if user already reacted with this emoji
			const existingReaction = comment.reactions?.find(
				(r: any) => r.emoji === emoji && r.hasReacted,
			);
			// If existingReaction exists, the user has already reacted, so we want to remove it
			// toggleReaction expects hasReacted to be the current state
			await toggleReaction(comment.id, emoji, !!existingReaction);
		} catch (error) {
			console.error("Failed to toggle reaction:", error);
		}
	};

	const handlePin = async () => {
		try {
			await updateComment({
				id: comment.id,
				isPinned: !comment.isPinned,
			});
		} catch (error) {
			console.error("Failed to toggle comment pin:", error);
		}
	};

	const handleDelete = async () => {
		try {
			await deleteComment(comment.id);
		} catch (error) {
			console.error("Failed to delete comment:", error);
		}
	};

	const handleReplyClick = () => {
		if (onReply && comment.author?.name) {
			// Always create top-level replies to avoid deep nesting
			// But still mention the person we're replying to
			onReply(isReply ? "thread" : comment.id, comment.author.name);
		}
	};

	const handleEditClick = () => {
		if (onEdit && comment.author?.name) {
			onEdit(
				comment.id,
				comment.author.name,
				comment.content,
				comment.richContent,
			);
		}
	};

	const handleMouseEnter = (e: React.MouseEvent) => {
		// Stop propagation to prevent parent comments from being hovered
		e.stopPropagation();
		setIsHovered(true);
	};

	const handleMouseLeave = (e: React.MouseEvent) => {
		// Stop propagation to prevent affecting parent comments
		e.stopPropagation();
		// Don't hide on mouse leave if dropdown or emoji picker is open
		if (!isDropdownOpen && !isEmojiPickerOpen) {
			setIsHovered(false);
		}
	};

	return (
		<div
			className={`${isReply ? "ml-12 border-l-2 border-border pl-4" : ""}`}
		>
			<div
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}
			>
				<div
					className={`flex gap-3 py-3 relative group transition-colors duration-150 ${
						isHovered ? "bg-muted/30 rounded-lg px-3 -mx-3" : ""
					}`}
				>
					<Avatar className={isReply ? "h-7 w-7" : "h-9 w-9"}>
						<AvatarImage
							src={
								comment.author?.profileImage ||
								comment.author?.avatar
							}
							alt={comment.author?.name}
						/>
						<AvatarFallback className="text-xs">
							{comment.author?.name
								?.split(" ")
								.map((n: string) => n[0])
								.join("")
								.toUpperCase() || "U"}
						</AvatarFallback>
					</Avatar>

					<div className="flex-1 min-w-0">
						<div className="flex items-center gap-2 mb-1">
							<span className="font-medium text-sm text-foreground">
								{comment.author?.name || "Unknown User"}
							</span>
							<div className="flex items-center gap-1 text-xs text-muted-foreground">
								<Clock className="w-3 h-3" />
								{formatTime(comment.createdAt)}
							</div>
							{comment.isEdited && (
								<span className="text-xs text-muted-foreground">
									(edited)
								</span>
							)}
							{(comment.isPinned || isPinned) && (
								<div className="flex items-center gap-1">
									<Pin className="w-3 h-3 text-blue-600" />
									<span className="text-xs text-blue-700 font-medium">
										Pinned
									</span>
								</div>
							)}
						</div>

						<div
							className={`text-sm text-foreground mb-3 leading-relaxed ${
								comment.isPinned || isPinned
									? "bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/50 rounded-lg p-3"
									: ""
							}`}
						>
							<RichCommentRenderer
								content={comment.content}
								richContent={comment.richContent}
								className=""
							/>
						</div>

						{/* Reactions display */}
						<CommentReactions
							reactions={comment.reactions || []}
							onReactionClick={handleReact}
							disabled={isReactionLoading}
						/>
					</div>

					{/* Floating Action Menu - appears on hover */}
					{(isHovered || isDropdownOpen || isEmojiPickerOpen) && (
						<div className="absolute right-5 top-2 flex items-center gap-1 bg-background border border-border rounded-lg shadow-lg px-2 py-1 z-20">
							<TooltipProvider>
								{/* Emoji Picker */}
								<EmojiPicker
									onEmojiSelect={handleEmojiSelect}
									disabled={isReactionLoading}
									size="sm"
									open={isEmojiPickerOpen}
									onOpenChange={setIsEmojiPickerOpen}
								/>

								{!isReply && (
									<Tooltip>
										<TooltipTrigger asChild>
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
												onClick={handleReplyClick}
											>
												<Reply className="w-3 h-3" />
											</Button>
										</TooltipTrigger>
										<TooltipContent>
											<p>Reply</p>
										</TooltipContent>
									</Tooltip>
								)}

								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="ghost"
											size="sm"
											className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
											onClick={handlePin}
										>
											<Pin className="w-3 h-3" />
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>
											{comment.isPinned ? "Unpin" : "Pin"}
										</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>

							{/* Dropdown Menu outside of Tooltip */}
							<DropdownMenu
								onOpenChange={(open) => {
									setIsDropdownOpen(open);
									// Keep the hover state when dropdown opens
									if (open) {
										setIsHovered(true);
									}
								}}
							>
								<DropdownMenuTrigger asChild>
									<Button
										variant="ghost"
										size="sm"
										className="h-7 w-7 p-0 text-muted-foreground hover:text-foreground hover:bg-muted"
									>
										<MoreVertical className="w-3 h-3" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end">
									{canEdit && (
										<DropdownMenuItem
											onClick={handleEditClick}
										>
											Edit
										</DropdownMenuItem>
									)}
									{canEdit && (
										<>
											<DropdownMenuSeparator />
											<DropdownMenuItem
												className="text-red-600"
												onClick={handleDelete}
											>
												Delete
											</DropdownMenuItem>
										</>
									)}
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					)}
				</div>
			</div>

			{/* Nested replies - moved outside the hover area */}
			{comment.replies && comment.replies.length > 0 && !isReply && (
				<div className="mt-4 space-y-2">
					{comment.replies.map((reply: any) => (
						<CommentItem
							key={reply.id}
							comment={reply}
							isReply={true}
							onReply={onReply}
							onEdit={onEdit}
						/>
					))}
				</div>
			)}
		</div>
	);
}
