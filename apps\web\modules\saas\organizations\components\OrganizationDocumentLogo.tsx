"use client";

import { config } from "@repo/config";
import { cn } from "@ui/lib";
import Image from "next/image";
import { forwardRef, useMemo, useState } from "react";
import { useIsClient } from "usehooks-ts";

export const OrganizationDocumentLogo = forwardRef<
	HTMLDivElement,
	{
		name: string;
		logoUrl?: string | null;
		className?: string;
	}
>(({ name, logoUrl, className }, ref) => {
	const isClient = useIsClient();
	const [imageError, setImageError] = useState(false);

	const logoSrc = useMemo(
		() =>
			logoUrl && !imageError
				? logoUrl.startsWith("http")
					? logoUrl
					: `/image-proxy/${config.storage.bucketNames.avatars}/${logoUrl}`
				: undefined,
		[logoUrl, imageError],
	);

	if (!isClient) {
		return null;
	}

	// If no logo is available, return null or a placeholder
	if (!logoSrc) {
		return (
			<div
				ref={ref}
				className={cn(
					"bg-muted flex items-center justify-center text-muted-foreground h-24 max-w-xs",
					className,
				)}
			>
				{name.charAt(0)}
			</div>
		);
	}

	// Use regular img tag for local image-proxy URLs, Next.js Image for external URLs
	const isExternalUrl =
		logoSrc.startsWith("http") && !logoSrc.includes("/image-proxy/");

	return (
		<div ref={ref} className={cn("relative max-w-xs", className)}>
			{isExternalUrl ? (
				<Image
					src={logoSrc}
					alt={`${name} logo`}
					width={240}
					height={120}
					className="object-contain h-auto w-full"
					style={{ maxHeight: "120px" }}
					onError={() => setImageError(true)}
				/>
			) : (
				<img
					src={logoSrc}
					alt={`${name} logo`}
					className="object-contain h-auto w-full max-h-[120px]"
					onError={() => setImageError(true)}
				/>
			)}
		</div>
	);
});

OrganizationDocumentLogo.displayName = "OrganizationDocumentLogo";
