import type { UpdateCompanyDetailsInput } from "@repo/api/src/routes/settings/company-details/types";
import type {
	CreateDepartmentInput,
	UpdateDepartmentInput,
} from "@repo/api/src/routes/settings/department/types";
import type {
	CreateExpenseCategoryInput,
	UpdateExpenseCategoryInput,
} from "@repo/api/src/routes/settings/expense-categories/types";
import type { UpdateOrganizationAddressInput } from "@repo/api/src/routes/settings/organization-address/types";
import type {
	CreatePaymentTermInput,
	UpdatePaymentTermInput,
} from "@repo/api/src/routes/settings/payment-term/types";
import type {
	CreateVatTermInput,
	UpdateVatTermInput,
} from "@repo/api/src/routes/settings/vatterm/types";
import type {
	CreateVehicleOrderTypeInput,
	UpdateVehicleOrderTypeInput,
} from "@repo/api/src/routes/settings/vehicle-order-type/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import type { SortingState } from "@tanstack/react-table";
import { useState } from "react";

import {
	useCompanyDetailsQuery,
	useUpdateCompanyDetailsMutation,
} from "../lib/api-company-details";
import {
	useCreateDepartmentMutation,
	useDeleteDepartmentMutation,
	useDepartmentsQuery,
	useUpdateDepartmentMutation,
} from "../lib/api-departments";
import {
	useCreateExpenseCategoryMutation,
	useDeleteExpenseCategoryMutation,
	useExpenseCategoriesQuery,
	useUpdateExpenseCategoryMutation,
} from "../lib/api-expense-categories";
import {
	useOrganizationAddressQuery,
	useUpdateOrganizationAddressMutation,
} from "../lib/api-organization-address";
import {
	useCreatePaymentTermMutation,
	useDeletePaymentTermMutation,
	usePaymentTermsQuery,
	useUpdatePaymentTermMutation,
} from "../lib/api-payment-terms";
import {
	useCreateVatTermMutation,
	useDeleteVatTermMutation,
	useUpdateVatTermMutation,
	useVatTermsQuery,
} from "../lib/api-vat-terms";
import {
	useCreateVehicleOrderTypeMutation,
	useDeleteVehicleOrderTypeMutation,
	useUpdateVehicleOrderTypeMutation,
	useVehicleOrderTypesQuery,
} from "../lib/api-vehicle-order-types";

// Main hook for company details
export function useCompanyDetails() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const detailsQuery = useCompanyDetailsQuery(orgId, {
		enabled: !!orgId,
	});

	const updateMutation = useUpdateCompanyDetailsMutation(orgId);

	const updateCompanyDetails = async (
		data: Omit<UpdateCompanyDetailsInput, "organizationId">,
	) => {
		if (!orgId) {
			return null;
		}

		try {
			return await updateMutation.mutateAsync(data);
		} catch (error) {
			console.error("Update company details error:", error);
			return null;
		}
	};

	return {
		details: detailsQuery.data,
		isLoading: detailsQuery.isLoading || updateMutation.isPending,
		isError: detailsQuery.isError,
		refetch: detailsQuery.refetch,
		updateCompanyDetails,
	};
}

// Main hook for organization address
export function useOrganizationAddress() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const addressQuery = useOrganizationAddressQuery(orgId, {
		enabled: !!orgId,
	});

	const updateMutation = useUpdateOrganizationAddressMutation(orgId);

	const updateOrganizationAddress = async (
		data: Omit<UpdateOrganizationAddressInput, "organizationId">,
	) => {
		if (!orgId) {
			return null;
		}

		try {
			return await updateMutation.mutateAsync(data);
		} catch (error) {
			console.error("Update organization address error:", error);
			return null;
		}
	};

	return {
		address: addressQuery.data,
		isLoading: addressQuery.isLoading || updateMutation.isPending,
		isError: addressQuery.isError,
		refetch: addressQuery.refetch,
		updateOrganizationAddress,
	};
}

// Main hook for listing VAT terms with filtering and pagination
export function useVatTerms() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useVatTermsQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
	});

	const deleteMutation = useDeleteVatTermMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deleteVatTerm = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete VAT term error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deleteVatTerm,
	};
}

// Main hook for listing expense categories with filtering and pagination
export function useExpenseCategories() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useExpenseCategoriesQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
	});

	const deleteMutation = useDeleteExpenseCategoryMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deleteExpenseCategory = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete expense category error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deleteExpenseCategory,
	};
}

// Main hook for listing departments with filtering and pagination
export function useDepartments() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useDepartmentsQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
	});

	const deleteMutation = useDeleteDepartmentMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deleteDepartment = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete department error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deleteDepartment,
	};
}

// Main hook for listing payment terms with filtering and pagination
export function usePaymentTerms() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = usePaymentTermsQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
	});

	const deleteMutation = useDeletePaymentTermMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deletePaymentTerm = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete payment term error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deletePaymentTerm,
	};
}

// Main hook for listing vehicle order types with filtering and pagination
export function useVehicleOrderTypes() {
	const { activeOrganization, loaded } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useVehicleOrderTypesQuery({
		organizationId: activeOrganization?.id ?? "",
		search: debouncedSearch,
		page,
		limit: pageSize,
		sortBy: sorting[0]?.id,
		sortDirection: sorting[0]?.desc ? "desc" : "asc",
	});

	const deleteMutation = useDeleteVehicleOrderTypeMutation(
		activeOrganization?.id ?? "",
	);

	// Wrap the delete function to refetch after deletion
	const deleteVehicleOrderType = async (id: string) => {
		if (activeOrganization?.id) {
			try {
				await deleteMutation.mutateAsync(id);
			} catch (error) {
				console.error("Delete vehicle order type error:", error);
			}
		}
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
		deleteVehicleOrderType,
	};
}

// Hook for VAT term mutations with callbacks
export function useVatTermMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateVatTermMutation(orgId);
	const updateMutation = useUpdateVatTermMutation(orgId);
	const deleteMutation = useDeleteVatTermMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (
		data: Omit<CreateVatTermInput, "organizationId">,
	) => {
		const result = await createMutation.mutateAsync({
			...data,
			organizationId: orgId,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (
		id: string,
		data: Omit<UpdateVatTermInput, "organizationId" | "id">,
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			data: {
				...data,
				organizationId: orgId,
				id,
			},
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createVatTerm: createWithCallback,
		updateVatTerm: updateWithCallback,
		deleteVatTerm: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Hook for expense category mutations with callbacks
export function useExpenseCategoryMutations(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateExpenseCategoryMutation(orgId);
	const updateMutation = useUpdateExpenseCategoryMutation(orgId);
	const deleteMutation = useDeleteExpenseCategoryMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (
		data: Omit<CreateExpenseCategoryInput, "organizationId">,
	) => {
		const result = await createMutation.mutateAsync({
			...data,
			organizationId: orgId,
		} as CreateExpenseCategoryInput);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (
		id: string,
		data: Omit<UpdateExpenseCategoryInput, "organizationId" | "id">,
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			data: {
				...data,
				organizationId: orgId,
			} as UpdateExpenseCategoryInput,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createExpenseCategory: createWithCallback,
		updateExpenseCategory: updateWithCallback,
		deleteExpenseCategory: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Hook for department mutations with callbacks
export function useDepartmentMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateDepartmentMutation(orgId);
	const updateMutation = useUpdateDepartmentMutation(orgId);
	const deleteMutation = useDeleteDepartmentMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (
		data: Omit<CreateDepartmentInput, "organizationId">,
	) => {
		const result = await createMutation.mutateAsync({
			...data,
			organizationId: orgId,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (
		id: string,
		data: Omit<UpdateDepartmentInput, "organizationId" | "id">,
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			data: {
				...data,
				organizationId: orgId,
				id,
			},
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createDepartment: createWithCallback,
		updateDepartment: updateWithCallback,
		deleteDepartment: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Hook for payment term mutations with callbacks
export function usePaymentTermMutations(options?: { onSuccess?: () => void }) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreatePaymentTermMutation(orgId);
	const updateMutation = useUpdatePaymentTermMutation(orgId);
	const deleteMutation = useDeletePaymentTermMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (
		data: Omit<CreatePaymentTermInput, "organizationId">,
	) => {
		const result = await createMutation.mutateAsync({
			...data,
			organizationId: orgId,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (
		id: string,
		data: Omit<UpdatePaymentTermInput, "organizationId" | "id">,
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			data: {
				...data,
				organizationId: orgId,
				id,
			},
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createPaymentTerm: createWithCallback,
		updatePaymentTerm: updateWithCallback,
		deletePaymentTerm: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

// Hook for vehicle order type mutations with callbacks
export function useVehicleOrderTypeMutations(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateVehicleOrderTypeMutation(orgId);
	const updateMutation = useUpdateVehicleOrderTypeMutation(orgId);
	const deleteMutation = useDeleteVehicleOrderTypeMutation(orgId);

	// Add custom success callback if provided
	const createWithCallback = async (
		data: Omit<CreateVehicleOrderTypeInput, "organizationId">,
	) => {
		const result = await createMutation.mutateAsync({
			...data,
			organizationId: orgId,
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const updateWithCallback = async (
		id: string,
		data: Omit<UpdateVehicleOrderTypeInput, "organizationId" | "id">,
	) => {
		const result = await updateMutation.mutateAsync({
			id,
			data: {
				...data,
				organizationId: orgId,
				id,
			},
		});
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createVehicleOrderType: createWithCallback,
		updateVehicleOrderType: updateWithCallback,
		deleteVehicleOrderType: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}
