import type {
	CreateLineItemInput,
	UpdateLineItemInput,
} from "@repo/api/src/routes/line-items/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define parent type
export type LineItemParentType = "order" | "offer" | "invoice" | "tour";

// Define query params type with updated parentType
type FetchLineItemsParams = {
	organizationId: string;
	parentId: string;
	parentType: LineItemParentType;
	page?: number;
	limit?: number;
	search?: string;
	includeDeleted?: boolean;
	excludeInvoiced?: boolean;
};

export const lineItemKeys = {
	all: ["line-items"] as const,
	list: (params: FetchLineItemsParams) =>
		[...lineItemKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...lineItemKeys.all, "detail", organizationId, id] as const,
};

export const fetchLineItems = async (params: FetchLineItemsParams) => {
	const response = await apiClient["line-items"].$get({
		query: {
			organizationId: params.organizationId,
			parentId: params.parentId,
			parentType: params.parentType,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			excludeInvoiced: params.excludeInvoiced?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch line items");
	}

	return response.json();
};

export const useLineItemsQuery = (params: FetchLineItemsParams) => {
	return useQuery({
		queryKey: lineItemKeys.list(params),
		queryFn: () => fetchLineItems(params),
		placeholderData: keepPreviousData,
		enabled: !!params.parentId && !!params.organizationId,
	});
};

export const fetchLineItemById = async (organizationId: string, id: string) => {
	const response = await apiClient["line-items"][":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch line item details");
	}

	return response.json();
};

export const useLineItemByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: lineItemKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchLineItemById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

export const useCreateLineItemMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<CreateLineItemInput, "organizationId">,
		) => {
			const response = await apiClient["line-items"].$post({
				json: {
					...data,
					organizationId,
				} as any,
			});

			if (!response.ok) {
				throw new Error("Failed to create line item");
			}

			return response.json();
		},
		onSuccess: (data: any) => {
			toast.success("Line item created successfully");

			// Determine parent ID and type from the created line item
			let parentId = "";
			let parentType: LineItemParentType = "order";

			// Use type checking to determine the parent type
			if ("orderId" in data) {
				parentId = data.orderId;
				parentType = "order";
			} else if ("offerId" in data) {
				parentId = data.offerId;
				parentType = "offer";
			} else if ("tourId" in data) {
				parentId = data.tourId;
				parentType = "tour";
			} else if ("invoiceId" in data) {
				parentId = data.invoiceId;
				parentType = "invoice";
			}

			if (parentId) {
				queryClient.invalidateQueries({
					queryKey: lineItemKeys.list({
						organizationId,
						parentId,
						parentType,
					}),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to create line item");
			console.error("Create line item error:", error);
		},
	});
};

export const useUpdateLineItemMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdateLineItemInput) => {
			// Filter out frontend-only fields before sending to backend
			const { _references, ...backendData } = data as any;

			const response = await apiClient["line-items"][":id"].$put({
				param: { id },
				json: {
					...backendData,
					id,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to update line item");
			}

			return response.json();
		},
		onSuccess: (data: any) => {
			toast.success("Line item updated successfully");

			// Determine parent ID and type from the updated line item
			let parentId = "";
			let parentType: LineItemParentType = "order";

			// Use type checking to determine the parent type
			if ("orderId" in data) {
				parentId = data.orderId;
				parentType = "order";
			} else if ("offerId" in data) {
				parentId = data.offerId;
				parentType = "offer";
			} else if ("tourId" in data) {
				parentId = data.tourId;
				parentType = "tour";
			} else if ("invoiceId" in data) {
				parentId = data.invoiceId;
				parentType = "invoice";
			}

			if (parentId) {
				queryClient.invalidateQueries({
					queryKey: lineItemKeys.list({
						organizationId,
						parentId,
						parentType,
					}),
				});
			}

			// Also invalidate the detail query
			if (data?.id) {
				queryClient.invalidateQueries({
					queryKey: lineItemKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update line item");
			console.error("Update line item error:", error);
		},
	});
};

export const useDeleteLineItemMutation = (
	organizationId: string,
	parentId: string,
	parentType: LineItemParentType,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient["line-items"][":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete line item");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Line item deleted successfully");

			queryClient.invalidateQueries({
				queryKey: lineItemKeys.list({
					organizationId,
					parentId,
					parentType,
				}),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete line item");
			console.error("Delete line item error:", error);
		},
	});
};
