"use client";
import { useCounterpartyView } from "@saas/contacts/context/counterparty-view-context";
import type { useCounterpartyById } from "@saas/contacts/hooks/use-counterparty";
import { useCounterpartyMutations } from "@saas/contacts/hooks/use-counterparty";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import {
	FileText,
	MoreHorizontal,
	Pencil,
	Save,
	Shield,
	ShieldOff,
	Trash,
	X,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";

// Extract the return type from useCounterpartyById hook
type CounterpartyResponse = ReturnType<
	typeof useCounterpartyById
>["counterparty"];

interface CounterpartyViewHeaderProps {
	counterparty: CounterpartyResponse;
}

export function CounterpartyViewHeader({
	counterparty,
}: CounterpartyViewHeaderProps) {
	const t = useTranslations();
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isSaving, setIsSaving] = useState(false);
	const [isBlockDialogOpen, setIsBlockDialogOpen] = useState(false);
	const [blockReason, setBlockReason] = useState("");
	const { startEditing, stopEditing, isEditing, setActiveTab, submitForm } =
		useCounterpartyView();
	const {
		blockCounterparty,
		unblockCounterparty,
		isLoading: isMutationLoading,
	} = useCounterpartyMutations();

	// Check if counterparty is blocked
	const isBlocked = !!counterparty?.blockedAt;
	const counterpartyBlockReason = counterparty?.blockReason;

	// Get initials for avatar
	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map((word) => word[0])
			.join("")
			.toUpperCase()
			.substring(0, 2);
	};

	const handleEdit = () => {
		// Switch to the details tab and enter edit mode
		setActiveTab("details");
		startEditing();
	};

	const handleSave = async () => {
		if (submitForm) {
			setIsSaving(true);
			try {
				const success = await submitForm();
				if (!success) {
					toast.error("Please check form for errors");
				}
			} catch (error) {
				console.error("Save error:", error);
				toast.error("Failed to save changes");
			} finally {
				setIsSaving(false);
			}
		} else {
			toast.error("Unable to save changes - form not ready");
		}
	};

	const handleCancel = () => {
		stopEditing();
	};

	const handleBlock = async () => {
		if (!counterparty?.id) {
			return;
		}

		setIsBlockDialogOpen(true);
	};

	const handleConfirmBlock = async () => {
		if (!counterparty?.id) {
			return;
		}

		try {
			await blockCounterparty(counterparty.id, blockReason || undefined);
			setIsBlockDialogOpen(false);
			setBlockReason("");
		} catch (error) {
			console.error("Block error:", error);
		}
	};

	const handleCancelBlock = () => {
		setIsBlockDialogOpen(false);
		setBlockReason("");
	};

	const handleUnblock = async () => {
		if (!counterparty?.id) {
			return;
		}

		try {
			await unblockCounterparty(counterparty.id);
		} catch (error) {
			console.error("Unblock error:", error);
		}
	};

	const initials = getInitials(counterparty?.nameLine1 || "Contact");

	return (
		<>
			<div className="flex items-center justify-between py-4">
				<div className="flex items-center gap-4">
					<Avatar className="h-12 w-12">
						<AvatarFallback>{initials}</AvatarFallback>
					</Avatar>
					<div>
						<div className="flex items-center gap-2">
							<h1
								className={cn(
									"text-2xl font-bold",
									isBlocked &&
										"text-red-600 dark:text-red-400",
								)}
							>
								{counterparty?.nameLine1}
							</h1>
							{isBlocked && counterpartyBlockReason && (
								<Badge status="error" className="text-xs">
									Blocked: {counterpartyBlockReason}
								</Badge>
							)}
						</div>
						<div className="text-sm text-muted-foreground flex items-center gap-2">
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<Badge
											status="info"
											className="bg-muted/50"
										>
											{counterparty?.customerNumber ||
												"No Customer Number"}
										</Badge>
									</TooltipTrigger>
									<TooltipContent>
										<p>Customer Number</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>

							{counterparty?.freightExchangeProfile
								?.carrierNumber && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
												{
													counterparty
														.freightExchangeProfile
														.carrierNumber
												}
											</Badge>
										</TooltipTrigger>
										<TooltipContent>
											<p>Carrier Number</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}

							{counterparty?.addressUsage?.address?.city &&
								counterparty?.addressUsage?.address
									?.country && (
									<span>{`${counterparty?.addressUsage?.address?.city}, ${counterparty?.addressUsage?.address?.country}`}</span>
								)}
						</div>
					</div>
				</div>

				<div className="flex items-center gap-2">
					{isEditing ? (
						<>
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="outline"
											size="sm"
											onClick={handleCancel}
											disabled={isSaving}
										>
											<X className="h-4 w-4 mr-2" />
											Cancel
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>Press ESC to cancel</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>

							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											variant="primary"
											size="sm"
											onClick={handleSave}
											disabled={isSaving}
										>
											<Save className="h-4 w-4 mr-2" />
											{isSaving
												? "Saving..."
												: "Save Changes"}
										</Button>
									</TooltipTrigger>
									<TooltipContent>
										<p>Press Ctrl+Enter to save</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						</>
					) : null}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" size="icon">
								<MoreHorizontal className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end">
							<DropdownMenuItem onSelect={handleEdit}>
								<Pencil className="h-4 w-4 mr-2" />
								Edit
							</DropdownMenuItem>
							<DropdownMenuItem>
								<FileText className="h-4 w-4 mr-2" />
								Create Invoice
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							{isBlocked ? (
								<DropdownMenuItem
									onSelect={handleUnblock}
									disabled={isMutationLoading}
								>
									<ShieldOff className="h-4 w-4 mr-2" />
									Unblock Counterparty
								</DropdownMenuItem>
							) : (
								<DropdownMenuItem
									onSelect={handleBlock}
									disabled={isMutationLoading}
								>
									<Shield className="h-4 w-4 mr-2" />
									Block Counterparty
								</DropdownMenuItem>
							)}
							<DropdownMenuSeparator />
							<DropdownMenuItem className="text-destructive">
								<Trash className="h-4 w-4 mr-2" />
								Delete
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</div>

			{/* Block Counterparty Dialog */}
			<Dialog
				open={isBlockDialogOpen}
				onOpenChange={setIsBlockDialogOpen}
			>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Block Counterparty</DialogTitle>
						<DialogDescription>
							Are you sure you want to block "
							{counterparty?.nameLine1}"? You can optionally
							provide a reason for blocking.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="blockReason">
								Reason for blocking (optional)
							</Label>
							<Textarea
								id="blockReason"
								placeholder="Enter reason for blocking..."
								value={blockReason}
								onChange={(e) => setBlockReason(e.target.value)}
								className="resize-none"
								rows={3}
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={handleCancelBlock}
							disabled={isMutationLoading}
						>
							Cancel
						</Button>
						<Button
							type="button"
							variant="error"
							onClick={handleConfirmBlock}
							disabled={isMutationLoading}
						>
							{isMutationLoading
								? "Blocking..."
								: "Block Counterparty"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
