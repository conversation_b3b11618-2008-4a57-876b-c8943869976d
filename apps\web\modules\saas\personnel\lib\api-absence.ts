import type {
	AbsenceType,
	CreatePersonnelAbsenceInput,
	UpdatePersonnelAbsenceInput,
} from "@repo/api/src/routes/personnel-absence/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface ListAbsencesParams {
	organizationId: string;
	search?: string;
	personnelId?: string;
	type?: AbsenceType;
	startDate?: Date;
	endDate?: Date;
	departmentId?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
}

// Query keys for React Query
export const personnelAbsenceKeys = {
	all: ["personnel-absence"] as const,
	lists: () => [...personnelAbsenceKeys.all, "list"] as const,
	list: (params: ListAbsencesParams) =>
		[...personnelAbsenceKeys.lists(), params] as const,
	tables: () => [...personnelAbsenceKeys.all, "table"] as const,
	table: (
		organizationId: string,
		year: number,
		departmentId?: string,
		search?: string,
		sortBy?: string,
		sortDirection?: string,
		page?: number,
		limit?: number,
	) =>
		[
			...personnelAbsenceKeys.tables(),
			{
				organizationId,
				year,
				departmentId,
				search,
				sortBy,
				sortDirection,
				page,
				limit,
			},
		] as const,
	details: () => [...personnelAbsenceKeys.all, "detail"] as const,
	detail: (organizationId: string, id: string) =>
		[...personnelAbsenceKeys.details(), organizationId, id] as const,
	statistics: () => [...personnelAbsenceKeys.all, "statistics"] as const,
	statistic: (organizationId: string) =>
		[...personnelAbsenceKeys.statistics(), organizationId] as const,
};

// API functions
export const fetchAbsenceTable = async (
	organizationId: string,
	year: number,
	departmentId?: string,
	page = 1,
	limit = 10,
	search?: string,
	sortBy?: string,
	sortDirection?: "asc" | "desc",
) => {
	const response = await apiClient.absence.data.table.$get({
		query: {
			organizationId,
			year: year.toString(),
			departmentId,
			page: page.toString(),
			limit: limit.toString(),
			search,
			sortBy,
			sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch absence table data");
	}

	return response.json();
};

export const fetchAbsences = async (params: ListAbsencesParams) => {
	const response = await apiClient.absence.absences.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			personnelId: params.personnelId,
			type: params.type,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
			departmentId: params.departmentId,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch absences");
	}

	return response.json();
};

export const fetchAbsenceById = async (organizationId: string, id: string) => {
	const response = await apiClient.absence.absences[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch absence details");
	}

	return response.json();
};

export const fetchAbsenceStatistics = async (organizationId: string) => {
	const response = await apiClient.absence.data.statistics.$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch absence statistics");
	}

	return response.json();
};

// React Query Hooks
export const useAbsenceTableQuery = (
	organizationId: string,
	year: number,
	departmentId?: string,
	page = 1,
	limit = 10,
	search?: string,
	sortBy?: string,
	sortDirection?: "asc" | "desc",
) => {
	return useQuery({
		queryKey: personnelAbsenceKeys.table(
			organizationId,
			year,
			departmentId,
			search,
			sortBy,
			sortDirection,
			page,
			limit,
		),
		queryFn: () =>
			fetchAbsenceTable(
				organizationId,
				year,
				departmentId,
				page,
				limit,
				search,
				sortBy,
				sortDirection,
			),
		placeholderData: keepPreviousData,
		enabled: !!organizationId && !!year,
	});
};

export const useAbsencesQuery = (
	params: ListAbsencesParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: personnelAbsenceKeys.list(params),
		queryFn: () => fetchAbsences(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

export const useAbsenceByIdQuery = (organizationId: string, id: string) => {
	return useQuery({
		queryKey: personnelAbsenceKeys.detail(organizationId, id),
		queryFn: () => fetchAbsenceById(organizationId, id),
		enabled: !!organizationId && !!id,
	});
};

export const useAbsenceStatisticsQuery = (organizationId: string) => {
	return useQuery({
		queryKey: personnelAbsenceKeys.statistic(organizationId),
		queryFn: () => fetchAbsenceStatistics(organizationId),
		enabled: !!organizationId,
	});
};

// Mutation Hooks
export const useCreateAbsenceMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: CreatePersonnelAbsenceInput & { documentFile?: File },
		) => {
			const { documentFile, ...absenceData } = data;

			if (documentFile) {
				const formData = new FormData();
				formData.append(
					"data",
					JSON.stringify({ ...absenceData, organizationId }),
				);
				formData.append("documentFile", documentFile);

				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				try {
					const response = await fetch(
						`${baseUrl}/api/absence/absences`,
						{
							method: "POST",
							body: formData,
							credentials: "include",
						},
					);

					if (!response.ok) {
						// Try to get detailed error message from response
						let errorMessage = "Failed to create absence";
						try {
							const errorData = await response.json();
							errorMessage =
								errorData.error?.message || errorMessage;
						} catch (parseError) {
							console.error(
								"Could not parse error response:",
								parseError,
							);
						}
						throw new Error(errorMessage);
					}

					return response.json();
				} catch (error) {
					console.error("Document upload failed:", error);
					throw error;
				}
			}

			const response = await apiClient.absence.absences.$post({
				json: { ...absenceData, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to create absence");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Absence created successfully");
			// Invalidate relevant queries
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.tables(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create absence",
			);
		},
	});
};

export const useUpdateAbsenceMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			id,
			data,
		}: {
			id: string;
			data: UpdatePersonnelAbsenceInput & { documentFile?: File };
		}) => {
			const { documentFile, ...absenceData } = data;

			if (documentFile) {
				// If there's a file, use multipart/form-data with direct fetch
				const formData = new FormData();
				formData.append(
					"data",
					JSON.stringify({ ...absenceData, organizationId }),
				);
				formData.append("documentFile", documentFile);

				// Create consistent URL using window.location.origin
				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				try {
					const response = await fetch(
						`${baseUrl}/api/absence/absences/${id}`,
						{
							method: "PUT",
							body: formData,
							credentials: "include",
						},
					);

					if (!response.ok) {
						// Try to get detailed error message from response
						let errorMessage = "Failed to update absence";
						try {
							const errorData = await response.json();
							errorMessage =
								errorData.error?.message || errorMessage;
						} catch (parseError) {
							console.error(
								"Could not parse error response:",
								parseError,
							);
						}
						throw new Error(errorMessage);
					}

					return response.json();
				} catch (error) {
					console.error("Document upload failed:", error);
					throw error;
				}
			}

			// Use direct fetch for now as Hono client types are not working correctly
			const baseUrl =
				typeof window !== "undefined" ? window.location.origin : "";
			const response = await fetch(
				`${baseUrl}/api/absence/absences/${id}`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ ...absenceData, organizationId }),
					credentials: "include",
				},
			);

			if (!response.ok) {
				// Try to get detailed error message from response
				let errorMessage = "Failed to update absence";
				try {
					const errorData = await response.json();
					errorMessage = errorData.error?.message || errorMessage;
				} catch (parseError) {
					console.error(
						"Could not parse error response:",
						parseError,
					);
				}
				throw new Error(errorMessage);
			}

			return response.json();
		},
		onSuccess: (_, { id }) => {
			toast.success("Absence updated successfully");
			// Invalidate relevant queries
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.tables(),
			});
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.detail(organizationId, id),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update absence",
			);
		},
	});
};

export const useDeleteAbsenceMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const baseUrl =
				typeof window !== "undefined" ? window.location.origin : "";
			const response = await fetch(
				`${baseUrl}/api/absence/absences/${id}?organizationId=${organizationId}`,
				{
					method: "DELETE",
					credentials: "include",
				},
			);

			if (!response.ok) {
				throw new Error("Failed to delete absence");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Absence deleted successfully");
			// Invalidate relevant queries
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.tables(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete absence",
			);
		},
	});
};

// Document management functions
export const uploadAbsenceDocument = async (
	absenceId: string,
	file: File,
	organizationId: string,
) => {
	const formData = new FormData();
	formData.append("documentFile", file);

	const baseUrl = typeof window !== "undefined" ? window.location.origin : "";
	const response = await fetch(
		`${baseUrl}/api/absence/documents/${absenceId}?organizationId=${organizationId}`,
		{
			method: "POST",
			body: formData,
			credentials: "include",
		},
	);

	if (!response.ok) {
		// Try to get detailed error message from response
		let errorMessage = "Failed to upload document";
		try {
			const errorData = await response.json();
			errorMessage = errorData.error?.message || errorMessage;
		} catch (parseError) {
			console.error("Could not parse error response:", parseError);
		}
		throw new Error(errorMessage);
	}

	return response.json();
};

export const removeAbsenceDocument = async (
	absenceId: string,
	organizationId: string,
) => {
	const baseUrl = typeof window !== "undefined" ? window.location.origin : "";
	const response = await fetch(
		`${baseUrl}/api/absence/documents/${absenceId}?organizationId=${organizationId}`,
		{
			method: "DELETE",
			credentials: "include",
		},
	);

	if (!response.ok) {
		// Try to get detailed error message from response
		let errorMessage = "Failed to remove document";
		try {
			const errorData = await response.json();
			errorMessage = errorData.error?.message || errorMessage;
		} catch (parseError) {
			console.error("Could not parse error response:", parseError);
		}
		throw new Error(errorMessage);
	}

	return response.json();
};

// Mutation hooks for document management
export const useUploadAbsenceDocumentMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			absenceId,
			file,
		}: {
			absenceId: string;
			file: File;
		}) => {
			return uploadAbsenceDocument(absenceId, file, organizationId);
		},
		onSuccess: (data, { absenceId }) => {
			// Remove toast from here - will be handled in the component
			// Invalidate relevant queries
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.detail(
					organizationId,
					absenceId,
				),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to upload document",
			);
		},
	});
};

export const useRemoveAbsenceDocumentMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (absenceId: string) => {
			return removeAbsenceDocument(absenceId, organizationId);
		},
		onSuccess: (data, absenceId) => {
			// Remove toast from here - will be handled in the component
			// Invalidate relevant queries
			queryClient.invalidateQueries({
				queryKey: personnelAbsenceKeys.detail(
					organizationId,
					absenceId,
				),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to remove document",
			);
		},
	});
};
