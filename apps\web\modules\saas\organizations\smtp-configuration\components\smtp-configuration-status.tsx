"use client";

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { format } from "date-fns";
import { Edit, Loader2, TestTube, Trash } from "lucide-react";
import { useState } from "react";
import { useSmtpConfiguration } from "../../hooks/use-smtp-configuration";

interface SmtpConfigurationStatusProps {
	onEdit?: () => void;
}

export function SmtpConfigurationStatus({
	onEdit,
}: SmtpConfigurationStatusProps) {
	const {
		data: smtpConfig,
		isLoading,
		testSmtpConfiguration,
		activateSmtpConfiguration,
		deleteSmtpConfiguration,
		isTestingConnection,
		isActivating,
		isDeleting,
	} = useSmtpConfiguration();

	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [showTestDialog, setShowTestDialog] = useState(false);
	const [testEmail, setTestEmail] = useState("");

	if (isLoading) {
		return (
			<Card>
				<CardContent className="flex justify-center items-center p-8">
					<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
				</CardContent>
			</Card>
		);
	}

	if (!smtpConfig) {
		return (
			<Card>
				<CardContent className="text-center p-8 text-muted-foreground">
					No SMTP configuration found. Add your SMTP settings above to
					get started.
				</CardContent>
			</Card>
		);
	}

	const handleTestConnection = () => {
		// Set default test email and show dialog
		setTestEmail(smtpConfig?.fromEmail || "");
		setShowTestDialog(true);
	};

	const handleSendTestEmail = () => {
		if (!testEmail) {
			return;
		}

		setShowTestDialog(false);
		testSmtpConfiguration({
			sendTestEmail: true,
			testEmail,
		});
	};

	const handleToggleActive = () => {
		activateSmtpConfiguration(!smtpConfig.isActive);
	};

	const handleDelete = () => {
		deleteSmtpConfiguration();
		setShowDeleteDialog(false);
	};

	const getStatusBadge = () => {
		if (!smtpConfig.isVerified) {
			return <Badge status="error">Not Verified</Badge>;
		}

		if (smtpConfig.isActive) {
			return <Badge status="success">Active</Badge>;
		}

		return <Badge status="warning">Inactive</Badge>;
	};

	const getAuthBadge = () => {
		if (smtpConfig.useOAuth2) {
			return <Badge status="info">OAuth2</Badge>;
		}
		return null;
	};

	const getLastTestStatus = () => {
		if (!smtpConfig.lastTestAt) {
			return "Never tested";
		}

		const testDate = format(
			new Date(smtpConfig.lastTestAt),
			"MMM d, yyyy 'at' HH:mm",
		);
		const status =
			smtpConfig.lastTestStatus === "SUCCESS" ? "Passed" : "Failed";

		return `${status} on ${testDate}`;
	};

	return (
		<>
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								SMTP Configuration
								{getStatusBadge()}
								{getAuthBadge()}
							</CardTitle>
							<CardDescription>
								Current SMTP settings for your organization
							</CardDescription>
						</div>
						<div className="flex items-center space-x-2">
							<span className="text-sm text-muted-foreground">
								{smtpConfig.isActive ? "Active" : "Inactive"}
							</span>
							<Switch
								checked={smtpConfig.isActive}
								onCheckedChange={handleToggleActive}
								disabled={
									!smtpConfig.isVerified || isActivating
								}
							/>
						</div>
					</div>
				</CardHeader>
				<CardContent className="space-y-6">
					{/* Connection Details */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								SMTP Host
							</h4>
							<p className="text-sm">{smtpConfig.host}</p>
						</div>
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								Port
							</h4>
							<p className="text-sm">{smtpConfig.port}</p>
						</div>
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								Security
							</h4>
							<p className="text-sm">
								{smtpConfig.secure
									? "SSL/TLS"
									: smtpConfig.requireTLS
										? "STARTTLS"
										: "None"}
							</p>
						</div>
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								Authentication
							</h4>
							<div className="flex items-center gap-2">
								<p className="text-sm">
									{smtpConfig.useOAuth2
										? "OAuth2"
										: "Username & Password"}
								</p>
								{smtpConfig.useOAuth2 && (
									<Badge status="info">
										Microsoft Exchange
									</Badge>
								)}
							</div>
							{!smtpConfig.useOAuth2 && smtpConfig.username && (
								<p className="text-xs text-muted-foreground mt-1">
									Username: {smtpConfig.username}
								</p>
							)}
						</div>
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								From Email
							</h4>
							<p className="text-sm">{smtpConfig.fromEmail}</p>
						</div>
						<div>
							<h4 className="font-medium text-sm text-muted-foreground mb-1">
								From Name
							</h4>
							<p className="text-sm">
								{smtpConfig.fromName || "Not set"}
							</p>
						</div>
					</div>

					{/* Status Information */}
					<div className="border-t pt-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h4 className="font-medium text-sm text-muted-foreground mb-1">
									Last Test
								</h4>
								<p className="text-sm">{getLastTestStatus()}</p>
								{smtpConfig.lastTestError && (
									<p className="text-sm text-red-600 mt-1">
										Error: {smtpConfig.lastTestError}
									</p>
								)}
							</div>
							<div>
								<h4 className="font-medium text-sm text-muted-foreground mb-1">
									Created
								</h4>
								<p className="text-sm">
									{format(
										new Date(smtpConfig.createdAt),
										"MMM d, yyyy",
									)}
								</p>
							</div>
						</div>
					</div>

					{/* Actions */}
					<div className="border-t pt-4 flex gap-2">
						{onEdit && (
							<Button
								variant="outline"
								size="sm"
								onClick={onEdit}
							>
								<Edit className="h-4 w-4 mr-1" />
								Edit Configuration
							</Button>
						)}

						<Button
							variant="outline"
							size="sm"
							onClick={handleTestConnection}
							disabled={isTestingConnection}
						>
							{isTestingConnection ? (
								<Loader2 className="h-4 w-4 mr-1 animate-spin" />
							) : (
								<TestTube className="h-4 w-4 mr-1" />
							)}
							Send Test Email
						</Button>

						<AlertDialog
							open={showDeleteDialog}
							onOpenChange={setShowDeleteDialog}
						>
							<AlertDialogTrigger asChild>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setShowDeleteDialog(true)}
									disabled={isDeleting}
								>
									{isDeleting ? (
										<Loader2 className="h-4 w-4 mr-1 animate-spin" />
									) : (
										<Trash className="h-4 w-4 mr-1" />
									)}
									Delete Configuration
								</Button>
							</AlertDialogTrigger>
							<AlertDialogContent>
								<AlertDialogHeader>
									<AlertDialogTitle>
										Delete SMTP Configuration
									</AlertDialogTitle>
									<AlertDialogDescription>
										Are you sure you want to delete the SMTP
										configuration for{" "}
										<strong>{smtpConfig.host}</strong>? This
										action cannot be undone and you'll need
										to reconfigure your SMTP settings.
									</AlertDialogDescription>
								</AlertDialogHeader>
								<AlertDialogFooter>
									<AlertDialogCancel>
										Cancel
									</AlertDialogCancel>
									<AlertDialogAction
										onClick={(e) => {
											e.preventDefault();
											handleDelete();
										}}
										className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
									>
										Delete Configuration
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					</div>
				</CardContent>
			</Card>
			<Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Send Test Email</DialogTitle>
						<DialogDescription>
							Enter an email address to send a test email and
							verify your SMTP configuration is working correctly.
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="test-email">
								Test Email Address
							</Label>
							<Input
								id="test-email"
								type="email"
								value={testEmail}
								onChange={(e) => setTestEmail(e.target.value)}
								placeholder="<EMAIL>"
								autoFocus
							/>
							<p className="text-sm text-muted-foreground">
								We'll send a test email to this address using
								your configured SMTP settings
							</p>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => setShowTestDialog(false)}
						>
							Cancel
						</Button>
						<Button
							type="button"
							onClick={handleSendTestEmail}
							disabled={!testEmail || isTestingConnection}
						>
							{isTestingConnection && (
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
							)}
							<TestTube className="mr-2 h-4 w-4" />
							Send Test Email
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
