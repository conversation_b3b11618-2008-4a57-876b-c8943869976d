"use client";

import { useOrderView } from "@saas/orders/context/order-view-context";
import type { useOrderById } from "@saas/orders/hooks/use-orders";
import { Button } from "@ui/components/button";
import { Loader2, Pencil, Save, X } from "lucide-react";
import { useTranslations } from "next-intl";

interface OrderViewHeaderProps {
	order: ReturnType<typeof useOrderById>["data"];
}

export function OrderViewHeader({ order }: OrderViewHeaderProps) {
	const t = useTranslations();
	const {
		isEditMode,
		toggleEditMode,
		saveChanges,
		cancelEdit,
		activeTab,
		isSaving,
	} = useOrderView();

	// Only show edit buttons on the details tab
	const showEditButtons = activeTab === "details";

	// Format order status for display
	const getOrderStatusBadgeClass = (status: string | undefined | null) => {
		if (!status) {
			return "bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400";
		}

		switch (status.toLowerCase()) {
			case "open":
				return "bg-blue-100 text-blue-800 dark:bg-blue-800/20 dark:text-blue-400";
			case "cancelled":
				return "bg-red-100 text-red-800 dark:bg-red-800/20 dark:text-red-400";
			case "closed":
				return "bg-green-100 text-green-800 dark:bg-green-800/20 dark:text-green-400";
			case "confirmed":
				return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800/20 dark:text-yellow-400";
			default:
				return "bg-gray-100 text-gray-800 dark:bg-gray-800/20 dark:text-gray-400";
		}
	};

	return (
		<div className="flex justify-between items-center">
			<div>
				<h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
					{t("app.orders.title")} {order?.order_number}
					{order?.order_status && (
						<span
							className={`px-2 py-1 rounded-md text-xs uppercase font-medium ml-2 ${getOrderStatusBadgeClass(
								order.order_status,
							)}`}
						>
							{order.order_status}
						</span>
					)}
				</h1>
				<p className="text-muted-foreground">
					{order?.customer?.nameLine1 || t("app.orders.noCustomer")}
				</p>
			</div>
			<div className="flex gap-2">
				{showEditButtons && isEditMode ? (
					<>
						<Button
							variant="outline"
							size="sm"
							onClick={cancelEdit}
							disabled={isSaving}
						>
							<X className="h-4 w-4 mr-2" />
							{t("app.orders.actions.cancel")}
						</Button>
						<Button
							variant="primary"
							size="sm"
							onClick={saveChanges}
							disabled={isSaving}
						>
							{isSaving ? (
								<Loader2 className="h-4 w-4 mr-2 animate-spin" />
							) : (
								<Save className="h-4 w-4 mr-2" />
							)}
							{t("app.orders.actions.save")}
						</Button>
					</>
				) : showEditButtons ? (
					<Button
						variant="outline"
						size="sm"
						onClick={toggleEditMode}
					>
						<Pencil className="h-4 w-4 mr-2" />
						{t("app.orders.actions.edit")}
					</Button>
				) : null}
			</div>
		</div>
	);
}
