"use client";

import { Badge } from "@ui/components/badge";
import { CommentEditor } from "@ui/components/blocks/editor-comment";
import { Button } from "@ui/components/button";
import type { SerializedEditorState } from "lexical";
import { X } from "lucide-react";
import { useState } from "react";
import { useCommentMutations } from "../hooks/use-threads";

interface RichCommentFormProps {
	threadId?: string;
	parentId?: string;
	editCommentId?: string; // For editing existing comments
	initialContent?: string; // For editing - plain text content
	initialRichContent?: string; // For editing - rich content JSON
	replyTo?: {
		id: string;
		author: string;
	};
	onCancel?: () => void;
	onSuccess?: () => void;
	placeholder?: string;
}

export function RichCommentForm({
	threadId,
	parentId,
	editCommentId,
	initialContent,
	initialRichContent,
	replyTo,
	onCancel,
	onSuccess,
	placeholder = "Add your comment...",
}: RichCommentFormProps) {
	// Initialize editor state with existing content if editing
	const getInitialEditorState = (): SerializedEditorState | null => {
		// Debug logging
		if (editCommentId) {
			console.log("Edit mode - Initial content:", initialContent);
			console.log(
				"Edit mode - Initial rich content:",
				initialRichContent,
			);
		}

		if (initialRichContent) {
			try {
				return JSON.parse(initialRichContent);
			} catch (error) {
				console.warn(
					"Failed to parse rich content, falling back to plain text:",
					error,
				);
			}
		}

		// For plain text content, let the editor handle it via placeholder
		return null;
	};

	const [editorState, setEditorState] =
		useState<SerializedEditorState | null>(getInitialEditorState());
	const [editorKey, setEditorKey] = useState(0); // Force editor reset

	const { createComment, updateComment, isLoading } = useCommentMutations({
		onSuccess: () => {
			setEditorState(null);
			setEditorKey((prev) => prev + 1); // Force editor to reset
			onSuccess?.();
		},
	});

	// Extract mentions from editor state
	const extractMentionsFromEditorState = (
		state: SerializedEditorState,
	): string[] => {
		const mentions: string[] = [];

		// Recursive function to traverse the editor state and find mention nodes
		const traverseNodes = (node: any) => {
			if (node.type === "mention" && node.userId) {
				mentions.push(node.userId);
			}

			node.children?.forEach(traverseNodes);
		};

		state.root?.children?.forEach(traverseNodes);

		return mentions;
	};

	// Extract plain text content from editor state
	const extractTextFromEditorState = (
		state: SerializedEditorState,
	): string => {
		let text = "";

		const traverseNodes = (node: any) => {
			if (node.type === "text" && node.text) {
				text += node.text;
			} else if (node.type === "mention" && node.mentionName) {
				text += `@${node.mentionName}`;
			}

			node.children?.forEach(traverseNodes);
		};

		state.root?.children?.forEach(traverseNodes);

		return text.trim();
	};

	const handleSubmit = async () => {
		if (!editorState) {
			return;
		}

		const content = extractTextFromEditorState(editorState);
		if (!content) {
			return;
		}

		const mentions = extractMentionsFromEditorState(editorState);

		try {
			if (editCommentId) {
				// Update existing comment
				await updateComment({
					id: editCommentId,
					content,
					richContent: JSON.stringify(editorState),
				});
			} else {
				// Create new comment
				if (!threadId) {
					return;
				}
				await createComment({
					threadId,
					content,
					richContent: JSON.stringify(editorState),
					parentId,
					mentions: mentions.length > 0 ? mentions : undefined,
				});
			}
		} catch (error) {
			console.error("Failed to save comment:", error);
		}
	};

	const hasContent =
		!!editorState && extractTextFromEditorState(editorState).length > 0;

	return (
		<div className="w-full">
			{(replyTo || editCommentId) && (
				<div className="px-4 py-3 bg-muted/30 border border-border border-b-0 rounded-t-lg flex items-center justify-between relative z-0 mb-[-8px]">
					<div className="flex items-center gap-2">
						{replyTo && (
							<Badge status="info" className="text-xs">
								Replying to {replyTo.author}
							</Badge>
						)}
						{editCommentId && (
							<Badge status="info" className="text-xs">
								Editing comment
							</Badge>
						)}
					</div>
					{onCancel && (
						<Button
							variant="ghost"
							size="sm"
							onClick={onCancel}
							className="h-6 w-6 p-0"
						>
							<X className="w-3 h-3" />
						</Button>
					)}
				</div>
			)}

			<div className="relative z-10">
				<div className="[&_p]:!mt-0 [&_p]:!mb-1 [&_p]:!leading-tight [&_p:last-child]:!mb-0">
					<CommentEditor
						key={editorKey} // Force reset on key change
						placeholder={
							editCommentId &&
							initialContent &&
							!initialRichContent
								? initialContent
								: placeholder
						}
						editorSerializedState={editorState || undefined}
						onSerializedChange={setEditorState}
						onSubmit={handleSubmit}
						submitButtonText={
							editCommentId
								? "Save"
								: replyTo
									? "Reply"
									: "Comment"
						}
						onCancel={onCancel}
						isLoading={isLoading}
						compact={true}
						hasContent={hasContent}
					/>
				</div>
			</div>
		</div>
	);
}
