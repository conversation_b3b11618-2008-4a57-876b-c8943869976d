"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import {
	type StopFormValues,
	StopType,
	TimeType,
	stopFormSchema,
} from "@saas/shared/lib/stops/stop-types";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { ScrollArea } from "@ui/components/scroll-area";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { StopFormContainer, type StopFormProps } from "./StopFormContainer";

export interface StopDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (values: StopFormValues) => Promise<void>;
	initialValues?: Partial<StopFormValues>;
	title?: string;
	description?: string;
	formProps?: Partial<StopFormProps>;
	counterpartyId?: string;
	availableStopTypes?: StopType[];
}

export function StopDialog({
	isOpen,
	onClose,
	onSubmit,
	initialValues,
	title = "Stop Details",
	description = "Add or edit stop information",
	formProps = {},
	counterpartyId,
	availableStopTypes,
}: StopDialogProps) {
	const t = useTranslations();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Determine fieldPrefix from formProps or use the default
	const fieldPrefix = formProps?.fieldPrefix || "";

	// Custom resolver that handles both nested and flat data structures
	const customResolver = (data: any, context: any, options: any) => {
		// If we have a fieldPrefix, extract the nested values
		const valueFromForm = fieldPrefix ? data[fieldPrefix] : data;

		// If the nested data doesn't exist, use the entire data object as fallback
		const formData = valueFromForm || data;

		// Add the missing required fields for validation
		const validationData = {
			...formData,
			// Add required fields that might be missing
			entityType: "order",
			organizationId: "temp-validation-value", // Just for validation - real value added later
			entityId: "temp-validation-value", // Just for validation - real value added later
		};

		// Use the regular zodResolver on the processed data
		return zodResolver(stopFormSchema)(validationData, context, options);
	};

	// Set up the form with default values
	const form = useForm<any>({
		resolver: customResolver,
		defaultValues: {
			...(fieldPrefix
				? {
						[fieldPrefix]: {
							stopType: StopType.LOADING,
							time_type: TimeType.LATEST,
							latitude: null,
							longitude: null,
							...initialValues,
						},
					}
				: {
						stopType: StopType.LOADING,
						time_type: TimeType.LATEST,
						latitude: null,
						longitude: null,
						...initialValues,
					}),
		},
	});

	// Reset form when initialValues change or dialog opens
	useEffect(() => {
		if (isOpen) {
			form.reset({
				...(fieldPrefix
					? {
							[fieldPrefix]: {
								stopType: StopType.LOADING,
								time_type: TimeType.LATEST,
								latitude: null,
								longitude: null,
								...initialValues,
							},
						}
					: {
							stopType: StopType.LOADING,
							time_type: TimeType.LATEST,
							latitude: null,
							longitude: null,
							...initialValues,
						}),
			});
		}
	}, [isOpen, initialValues, fieldPrefix, form]);

	// Handle form submission
	const handleSubmit = async (values: any) => {
		setIsSubmitting(true);
		try {
			// Extract the actual values based on fieldPrefix
			const submissionValues = fieldPrefix ? values[fieldPrefix] : values;

			// Process form data before submission
			const formData = {
				...submissionValues,
			};

			await onSubmit(formData);
			onClose();
		} catch (error) {
			console.error("Error submitting stop form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="sm:max-w-[900px] max-h-[90vh] flex flex-col">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{description}</DialogDescription>
				</DialogHeader>

				<ScrollArea className="flex-1 h-full max-h-[60vh] overflow-auto pr-4 -mr-4">
					<form
						id="stop-form"
						onSubmit={(e) => {
							e.preventDefault();
							form.handleSubmit(handleSubmit)(e);
						}}
						className="space-y-6"
					>
						<StopFormContainer
							form={form}
							fieldPrefix={fieldPrefix}
							disabled={isSubmitting}
							counterpartyId={counterpartyId}
							availableStopTypes={availableStopTypes}
							{...formProps}
						/>
					</form>
				</ScrollArea>

				<DialogFooter className="mt-6">
					<Button
						variant="outline"
						onClick={onClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						type="button"
						onClick={async () => {
							const values = form.getValues();

							setIsSubmitting(true);
							try {
								const submissionValues = fieldPrefix
									? values[fieldPrefix]
									: values;
								await onSubmit(submissionValues);
								onClose();
							} catch (error) {
								console.error("Save error:", error);
							} finally {
								setIsSubmitting(false);
							}
						}}
						disabled={isSubmitting}
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
