"use client";

import { usePersonnelView } from "@saas/personnel/context/personnel-view-context";
import type { usePersonnelById } from "@saas/personnel/hooks/use-personnel";
import {
	type Country,
	CountrySelect,
} from "@saas/shared/components/CountrySelect";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { DepartmentSelector } from "@ui/components/department-selector";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { PhoneInput } from "@ui/components/phone-input";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { isSupportedCountry } from "react-phone-number-input";
import type { Country as PhoneCountry } from "react-phone-number-input";

// Helper to safely handle date values
const safeValue = (
	value: Date | string | null | undefined,
): string | undefined => {
	return value === null ? undefined : (value as any);
};

interface DetailsPanelProps {
	personnel: ReturnType<typeof usePersonnelById>["data"];
}

export function DetailsPanel({ personnel }: DetailsPanelProps) {
	const t = useTranslations();
	const { isEditMode, fieldChanges, updateField } = usePersonnelView();
	// State to track the current country for phone inputs
	const [selectedCountry, setSelectedCountry] = useState<PhoneCountry>(
		(personnel?.country as PhoneCountry) || "DE",
	);
	// Ref for the first input field to focus when edit mode is activated
	const firstNameInputRef = useRef<HTMLInputElement>(null);

	// Focus the first input field when edit mode is activated
	useEffect(() => {
		if (isEditMode && firstNameInputRef.current) {
			// Short delay to ensure the input is rendered and ready to focus
			setTimeout(() => {
				firstNameInputRef.current?.focus();
			}, 100);
		}
	}, [isEditMode]);

	// Handler for country selection
	const handleCountrySelect = (country: Country) => {
		// Update the phone input country
		if (isSupportedCountry(country.alpha2 as PhoneCountry)) {
			setSelectedCountry(country.alpha2 as PhoneCountry);
		}
	};

	// Get the current value of a field, prioritizing changes over original data
	const getFieldValue = (fieldName: string) => {
		if (fieldChanges && fieldName in fieldChanges) {
			return fieldChanges[fieldName as keyof typeof fieldChanges];
		}
		return personnel?.[fieldName as keyof typeof personnel] || "";
	};

	// Safely get date field values
	const getDateFieldValue = (
		fieldName: string,
	): string | Date | null | undefined => {
		const value = getFieldValue(fieldName);
		return typeof value === "string" ||
			value instanceof Date ||
			value === null ||
			value === undefined
			? (value as string | Date | null | undefined)
			: "";
	};

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.personnel.detailsPanel.personalInfo")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.fullName")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											ref={firstNameInputRef}
											value={
												getFieldValue(
													"firstName",
												) as string
											}
											onChange={(e) =>
												updateField(
													"firstName",
													e.target.value,
												)
											}
											placeholder="First Name"
										/>
										<Input
											value={
												getFieldValue(
													"lastName",
												) as string
											}
											onChange={(e) =>
												updateField(
													"lastName",
													e.target.value,
												)
											}
											placeholder="Last Name"
										/>
									</div>
								) : (
									<p>
										{personnel?.firstName}{" "}
										{personnel?.lastName}
									</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.personnel.detailsPanel.dateOfBirth",
									)}
								</h4>
								{isEditMode ? (
									<InputWithDate
										inputProps={{
											placeholder: "DD.MM.YYYY",
											value: safeValue(
												getDateFieldValue(
													"dateOfBirth",
												),
											),
										}}
										onDateChange={(value) =>
											updateField("dateOfBirth", value)
										}
									/>
								) : (
									<p>
										{personnel?.dateOfBirth
											? format(
													new Date(
														personnel.dateOfBirth,
													),
													"PPP",
												)
											: "-"}
									</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.personnel.detailsPanel.placeOfBirth",
									)}
								</h4>
								{isEditMode ? (
									<Input
										value={
											getFieldValue(
												"placeOfBirth",
											) as string
										}
										onChange={(e) =>
											updateField(
												"placeOfBirth",
												e.target.value,
											)
										}
										placeholder="Place of Birth"
									/>
								) : (
									<p>{personnel?.placeOfBirth || "-"}</p>
								)}
							</div>
						</div>
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.department")}
								</h4>
								{isEditMode ? (
									<DepartmentSelector
										value={
											getFieldValue(
												"departmentId",
											) as string
										}
										onChange={(value) =>
											updateField("departmentId", value)
										}
										name="departmentId"
										placeholder="Select department"
									/>
								) : (
									<p>{personnel?.department?.name || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.email")}
								</h4>
								{isEditMode ? (
									<Input
										type="email"
										value={getFieldValue("email") as string}
										onChange={(e) =>
											updateField("email", e.target.value)
										}
										placeholder="Email Address"
									/>
								) : (
									<p>{personnel?.email || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.phone")}
								</h4>
								{isEditMode ? (
									<div className="space-y-2">
										<div>
											<label
												htmlFor="telephone-input"
												className="text-xs text-muted-foreground mb-1 block"
											>
												Telephone
											</label>
											<PhoneInput
												id="telephone-input"
												value={
													getFieldValue(
														"telephone",
													) as string
												}
												onChange={(value) =>
													updateField(
														"telephone",
														value,
													)
												}
												placeholder="Telephone"
												international
												defaultCountry={
													selectedCountry || "DE"
												}
											/>
										</div>
										<div>
											<label
												htmlFor="mobile-input"
												className="text-xs text-muted-foreground mb-1 block"
											>
												Mobile
											</label>
											<PhoneInput
												id="mobile-input"
												value={
													getFieldValue(
														"mobile",
													) as string
												}
												onChange={(value) =>
													updateField("mobile", value)
												}
												placeholder="Mobile"
												international
												defaultCountry={
													selectedCountry || "DE"
												}
											/>
										</div>
									</div>
								) : (
									<div className="space-y-1">
										{personnel?.telephone && (
											<div>
												<span className="text-xs text-muted-foreground">
													Telephone:{" "}
												</span>
												<span>
													{personnel.telephone}
												</span>
											</div>
										)}
										{personnel?.mobile && (
											<div>
												<span className="text-xs text-muted-foreground">
													Mobile:{" "}
												</span>
												<span>{personnel.mobile}</span>
											</div>
										)}
										{!personnel?.telephone &&
											!personnel?.mobile && <p>-</p>}
									</div>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.personnel.detailsPanel.address")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.street")}
								</h4>
								{isEditMode ? (
									<div className="space-y-2">
										<Input
											value={
												getFieldValue(
													"street",
												) as string
											}
											onChange={(e) =>
												updateField(
													"street",
													e.target.value,
												)
											}
											placeholder="Street"
										/>
										<Input
											value={
												getFieldValue(
													"addressSupplement",
												) as string
											}
											onChange={(e) =>
												updateField(
													"addressSupplement",
													e.target.value,
												)
											}
											placeholder="Address Supplement"
										/>
									</div>
								) : (
									<>
										<p>{personnel?.street || "-"}</p>
										{personnel?.addressSupplement && (
											<p>{personnel.addressSupplement}</p>
										)}
									</>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.cityZip")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											value={
												getFieldValue(
													"zipCode",
												) as string
											}
											onChange={(e) =>
												updateField(
													"zipCode",
													e.target.value,
												)
											}
											placeholder="ZIP Code"
										/>
										<Input
											value={
												getFieldValue("city") as string
											}
											onChange={(e) =>
												updateField(
													"city",
													e.target.value,
												)
											}
											placeholder="City"
										/>
									</div>
								) : (
									<>
										{personnel?.zipCode &&
										personnel?.city ? (
											<p>
												{personnel.zipCode}{" "}
												{personnel.city}
											</p>
										) : (
											<p>-</p>
										)}
									</>
								)}
							</div>
						</div>
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.country")}
								</h4>
								{isEditMode ? (
									<CountrySelect
										name="country"
										value={
											getFieldValue("country") as string
										}
										onValueChange={(value) =>
											updateField("country", value)
										}
										onCountrySelect={handleCountrySelect}
										placeholder="Select country"
									/>
								) : (
									<p>{personnel?.country || "-"}</p>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.personnel.detailsPanel.identification")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 gap-6">
						<div>
							<h4 className="text-sm font-medium text-muted-foreground">
								{t(
									"app.personnel.detailsPanel.socialSecurityNumber",
								)}
							</h4>
							{isEditMode ? (
								<Input
									value={
										getFieldValue(
											"socialSecurityNumber",
										) as string
									}
									onChange={(e) =>
										updateField(
											"socialSecurityNumber",
											e.target.value,
										)
									}
									placeholder="Social Security Number"
								/>
							) : (
								<p>{personnel?.socialSecurityNumber || "-"}</p>
							)}
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.personnel.detailsPanel.workPermit")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.personnel.detailsPanel.workPermitStart",
									)}
								</h4>
								{isEditMode ? (
									<InputWithDate
										inputProps={{
											placeholder: "DD.MM.YYYY",
											value: safeValue(
												getDateFieldValue(
													"workPermitStart",
												),
											),
										}}
										onDateChange={(value) =>
											updateField(
												"workPermitStart",
												value,
											)
										}
									/>
								) : (
									<p>
										{personnel?.workPermitStart
											? format(
													new Date(
														personnel.workPermitStart,
													),
													"PPP",
												)
											: "-"}
									</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.personnel.detailsPanel.workPermitEnd",
									)}
								</h4>
								{isEditMode ? (
									<InputWithDate
										inputProps={{
											placeholder: "DD.MM.YYYY",
											value: safeValue(
												getDateFieldValue(
													"workPermitEnd",
												),
											),
										}}
										onDateChange={(value) =>
											updateField("workPermitEnd", value)
										}
									/>
								) : (
									<p>
										{personnel?.workPermitEnd
											? format(
													new Date(
														personnel.workPermitEnd,
													),
													"PPP",
												)
											: "-"}
									</p>
								)}
							</div>
						</div>
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.visaStart")}
								</h4>
								{isEditMode ? (
									<InputWithDate
										inputProps={{
											placeholder: "DD.MM.YYYY",
											value: safeValue(
												getDateFieldValue("visaStart"),
											),
										}}
										onDateChange={(value) =>
											updateField("visaStart", value)
										}
									/>
								) : (
									<p>
										{personnel?.visaStart
											? format(
													new Date(
														personnel.visaStart,
													),
													"PPP",
												)
											: "-"}
									</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.personnel.detailsPanel.visaEnd")}
								</h4>
								{isEditMode ? (
									<InputWithDate
										inputProps={{
											placeholder: "DD.MM.YYYY",
											value: safeValue(
												getDateFieldValue("visaEnd"),
											),
										}}
										onDateChange={(value) =>
											updateField("visaEnd", value)
										}
									/>
								) : (
									<p>
										{personnel?.visaEnd
											? format(
													new Date(personnel.visaEnd),
													"PPP",
												)
											: "-"}
									</p>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{(personnel?.notes || isEditMode) && (
				<Card>
					<CardHeader>
						<CardTitle>
							{t("app.personnel.detailsPanel.notes")}
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditMode ? (
							<Textarea
								value={getFieldValue("notes") as string}
								onChange={(e) =>
									updateField("notes", e.target.value)
								}
								placeholder="Notes"
								rows={4}
							/>
						) : (
							<p className="whitespace-pre-wrap">
								{personnel?.notes}
							</p>
						)}
					</CardContent>
				</Card>
			)}
		</div>
	);
}
