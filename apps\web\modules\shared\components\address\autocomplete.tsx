"use client";

import { useDebounce } from "@shared/hooks/use-debounce";
import { fetcher } from "@ui/utils/fetcher";
import { Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";

// Define the address type interface
export interface AddressType {
	address1: string;
	address2: string;
	formattedAddress: string;
	city: string;
	region: string;
	postalCode: string;
	country: string;
	lat: number;
	lng: number;
	placeId?: string;
}

interface AddressAutocompleteProps {
	onAddressChange: (address: AddressType) => void;
	placeholder?: string;
}

export const AddressAutocomplete = ({
	onAddressChange,
	placeholder = "Search for an address",
}: AddressAutocompleteProps) => {
	// Use an initialization ref to avoid issues with uncontrolled to controlled warnings
	const initialized = useRef(false);
	// Ref for the autocomplete container to detect clicks outside
	const autocompleteRef = useRef<HTMLDivElement>(null);

	// Core state, all with defined initial values
	const [searchInput, setSearchInput] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [predictions, setPredictions] = useState<any[]>([]);

	// Mark component as initialized after first render
	useEffect(() => {
		initialized.current = true;
	}, []);

	// Add click outside listener to dismiss suggestions
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				autocompleteRef.current &&
				!autocompleteRef.current.contains(event.target as Node) &&
				predictions.length > 0
			) {
				// Close suggestions when clicking outside
				setPredictions([]);
			}
		};

		// Add event listener
		document.addEventListener("mousedown", handleClickOutside);

		// Clean up
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [predictions.length]);

	// Debounce the input to avoid too many API calls
	const debouncedInput = useDebounce(searchInput, 1000);

	// Fetch predictions when debounced input changes
	useEffect(() => {
		// Don't search if input is empty
		if (!debouncedInput.trim()) {
			setPredictions([]);
			return;
		}

		// Token for this search session
		const sessionToken =
			crypto.randomUUID?.() || Math.random().toString(36).slice(2);
		let active = true;

		const fetchPredictions = async () => {
			setIsLoading(true);

			try {
				const response = await fetcher(
					`/api/address/autocomplete?input=${encodeURIComponent(
						debouncedInput,
					)}&sessionToken=${sessionToken}`,
				);

				if (active) {
					setPredictions(response?.data || []);
				}
			} catch (error) {
				console.error("Error fetching predictions:", error);
				if (active) {
					setPredictions([]);
				}
			} finally {
				if (active) {
					setIsLoading(false);
				}
			}
		};

		fetchPredictions();

		return () => {
			active = false;
		};
	}, [debouncedInput]);

	// Handle input change - ensure it's always controlled
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		// Only update if we've initialized (prevents controlled/uncontrolled switches)
		if (initialized.current) {
			setSearchInput(e.target.value || "");
		}
	};

	// Handle address selection
	const handleSelectAddress = async (placeId: string) => {
		if (!placeId) {
			return;
		}

		// Close dropdown immediately
		setPredictions([]);
		setIsLoading(true);

		// Create a new session token for this lookup
		const sessionToken =
			crypto.randomUUID?.() || Math.random().toString(36).slice(2);

		try {
			const response = await fetcher(
				`/api/address/place?placeId=${placeId}&sessionToken=${sessionToken}`,
			);

			if (response?.data?.address) {
				// Create a safe address object
				const address: AddressType = {
					address1: response.data.address.address1 || "",
					address2: response.data.address.address2 || "",
					formattedAddress:
						response.data.address.formattedAddress || "",
					city: response.data.address.city || "",
					region: response.data.address.region || "",
					postalCode: response.data.address.postalCode || "",
					country: response.data.address.country || "",
					lat: response.data.address.lat || 0,
					lng: response.data.address.lng || 0,
					placeId: placeId,
				};

				// Update parent form
				onAddressChange(address);

				// Clear input after selection
				if (initialized.current) {
					setSearchInput("");
				}
			}
		} catch (error) {
			console.error("Error fetching place details:", error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="relative w-full" ref={autocompleteRef}>
			{/* Input field - always controlled after initialization */}
			<div className="flex w-full items-center rounded-lg border bg-background ring-offset-background text-sm focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
				<input
					type="text"
					value={searchInput}
					onChange={handleInputChange}
					placeholder={placeholder}
					className="w-full p-3 rounded-lg outline-none bg-transparent"
					disabled={isLoading}
					// Use key to force a fresh input element to avoid controlled/uncontrolled warnings
					key="address-input"
				/>
				{isLoading && (
					<div className="flex items-center justify-center pr-3">
						<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
					</div>
				)}
			</div>

			{/* Dropdown with results */}
			{predictions.length > 0 && (
				<div className="absolute top-12 z-50 w-full rounded-md border shadow-md bg-background animate-in fade-in-0 zoom-in-95">
					<div className="py-1 max-h-60 overflow-auto">
						{predictions.map((prediction) => (
							<button
								type="button"
								key={prediction.placePrediction.placeId}
								className="w-full text-left px-3 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground border-0 bg-transparent"
								onClick={() =>
									handleSelectAddress(
										prediction.placePrediction.placeId,
									)
								}
							>
								{prediction.placePrediction.text.text}
							</button>
						))}
					</div>
				</div>
			)}
		</div>
	);
};
