import type { CreatePersonnelInput } from "@repo/api/src/routes/personnel/types";
import type { fetchPersonnel } from "./api";

// Convert undefined types to also accept null
type NullableToOptional<T> = {
	[K in keyof T]: T[K] extends undefined | infer U ? U | null : T[K];
};

// Convert Date types to string
type DateToString<T> = {
	[K in keyof T]: T[K] extends Date | undefined | null ? string | null : T[K];
};

// Use the API type for Personnel
export type Personnel = DateToString<
	NullableToOptional<CreatePersonnelInput>
> & {
	id: string;
	organizationId: string;
	createdAt: string;
	updatedAt: string;
};

// Infer personnel type from API response
export type PersonnelResponse = Awaited<ReturnType<typeof fetchPersonnel>>;
export type PersonnelWithRelations = PersonnelResponse["items"][number];

// Personnel form types
export interface DriverLicenseModule {
	id?: string;
	moduleType: string;
	validUntil: Date | null;
	info?: string;
}

export interface DriverLicense {
	id?: string;
	types: string[];
	issuedAt: Date | null;
	expiresAt: Date | null;
	issuer: string;
	documentFile?: File; // Store the actual file object
	documentUrl?: string; // For preview only
	documentName?: string;
	modules: DriverLicenseModule[];
	document?: {
		id: string;
		url: string;
		fileName: string;
		fileType: string;
		fileSize: number;
		uploadedAt: string;
		_deleted?: boolean;
	} | null;
	keepExistingDocument?: boolean;
}

// Match with the schema's ContactSalutation
export type Salutation = "MR" | "MS" | "COMPANY";

export interface PersonnelFormValues {
	// IDs
	id?: string;
	organizationId: string;

	// General Information
	salutation?: Salutation | null;
	firstName: string;
	lastName: string;
	street?: string;
	addressSupplement?: string;
	country?: string;
	zipCode?: string;
	city?: string;
	telephone?: string;
	mobile?: string;
	email?: string;
	dateOfBirth: Date | null;
	placeOfBirth?: string;

	// Work Permits & Registration
	socialSecurityNumber?: string;
	workPermitStart: Date | null;
	workPermitEnd: Date | null;
	visaStart: Date | null;
	visaEnd: Date | null;
	registeredAt: Date | null;
	deregisteredAt: Date | null;

	// Professional Information
	departmentId?: string;
	notes?: string;

	// Relations
	driverLicenses: DriverLicense[];
	languages?: Language[];
}

// Language proficiency level
export type LanguageProficiencyLevel =
	| "BASIC"
	| "INTERMEDIATE"
	| "ADVANCED"
	| "FLUENT"
	| "NATIVE";

// Language interface for consistent usage across components
export interface Language {
	id?: string;
	code: string;
	level: LanguageProficiencyLevel;
	name?: string; // Added by the backend using the code
	personnelId?: string;
}

// Available languages
export const AVAILABLE_LANGUAGES = [
	{ code: "en", label: "English" },
	{ code: "de", label: "German" },
	{ code: "fr", label: "French" },
	{ code: "es", label: "Spanish" },
	{ code: "it", label: "Italian" },
	{ code: "pt", label: "Portuguese" },
	{ code: "nl", label: "Dutch" },
	{ code: "pl", label: "Polish" },
	{ code: "cs", label: "Czech" },
	{ code: "ru", label: "Russian" },
	{ code: "ja", label: "Japanese" },
	{ code: "zh", label: "Chinese" },
	{ code: "ar", label: "Arabic" },
	{ code: "tr", label: "Turkish" },
] as const;

// Proficiency levels with labels
export const PROFICIENCY_LEVELS = [
	{ value: "BASIC" as const, label: "Basic" },
	{ value: "INTERMEDIATE" as const, label: "Intermediate" },
	{ value: "ADVANCED" as const, label: "Advanced" },
	{ value: "FLUENT" as const, label: "Fluent" },
	{ value: "NATIVE" as const, label: "Native Speaker" },
] as const;

// Default values for the personnel form
export const defaultPersonnelFormValues: Partial<PersonnelFormValues> = {
	firstName: "",
	lastName: "",
	street: "",
	addressSupplement: "",
	country: "",
	zipCode: "",
	city: "",
	telephone: "",
	mobile: "",
	email: "",
	dateOfBirth: null,
	placeOfBirth: "",

	// Work Permits & Registration
	socialSecurityNumber: "",
	workPermitStart: null,
	workPermitEnd: null,
	visaStart: null,
	visaEnd: null,
	registeredAt: null,
	deregisteredAt: null,

	// Professional Information
	departmentId: "",
	notes: "",

	// Relations
	driverLicenses: [],
	languages: [],
};
