import type {
	CreatePersonnelInput,
	UpdatePersonnelInput,
} from "@repo/api/src/routes/personnel/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Define types for Driver Licenses
export interface DriverLicenseModule {
	id?: string;
	moduleType: string;
	// Can be Date object in forms, but must be converted to string for API
	validUntil: string | Date | null;
	info?: string;
}

export interface DriverLicense {
	id?: string;
	types: string[];
	// Can be Date object in forms, but must be converted to string for API
	issuedAt: string | Date | null;
	// Can be Date object in forms, but must be converted to string for API
	expiresAt: string | Date | null;
	issuer: string;
	modules?: DriverLicenseModule[];
	documentFile?: File | null;
	documentUrl?: string;
	documentName?: string;
}

export interface CreateDriverLicense {
	types: string[];
	issuedAt: string | Date | null;
	expiresAt: string | Date | null;
	issuer: string;
	modules: {
		moduleType: string;
		validUntil: string | Date | null;
		info?: string;
	}[];
	documentFile?: File;
	documentUrl?: string;
	documentName?: string;
	keepExistingDocument?: boolean;
}

export interface UpdateDriverLicense extends CreateDriverLicense {
	id: string;
}

type FetchPersonnelParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	departmentId?: string;
};

export const personnelKeys = {
	all: ["personnel"] as const,
	list: (params: FetchPersonnelParams) =>
		[...personnelKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...personnelKeys.all, "detail", organizationId, id] as const,
};

export const fetchPersonnel = async (params: FetchPersonnelParams) => {
	const response = await apiClient.personnel.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			departmentId: params.departmentId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch personnel list");
	}

	return response.json();
};

export const usePersonnelQuery = (params: FetchPersonnelParams) => {
	return useQuery({
		queryKey: personnelKeys.list(params),
		queryFn: () => fetchPersonnel(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchPersonnelById = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient.personnel[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch personnel details");
	}

	return response.json();
};

export const usePersonnelByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: personnelKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchPersonnelById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create personnel mutation
export const useCreatePersonnelMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreatePersonnelInput) => {
			const response = await apiClient.personnel.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create personnel");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Personnel created successfully");
			queryClient.invalidateQueries({
				queryKey: personnelKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to create personnel");
			console.error("Create personnel error:", error);
		},
	});
};

// Update personnel mutation
export const useUpdatePersonnelMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({ id, ...data }: UpdatePersonnelInput) => {
			const response = await apiClient.personnel[":id"].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			} as any);

			if (!response.ok) {
				throw new Error("Failed to update personnel");
			}

			return response.json();
		},
		onSuccess: (data) => {
			toast.success("Personnel updated successfully");
			// Invalidate queries to refetch data
			queryClient.invalidateQueries({
				queryKey: personnelKeys.list({ organizationId }),
			});
			// Also invalidate the detail query
			if (data.id) {
				queryClient.invalidateQueries({
					queryKey: personnelKeys.detail(organizationId, data.id),
				});
			}
		},
		onError: (error) => {
			toast.error("Failed to update personnel");
			console.error("Update personnel error:", error);
		},
	});
};

// Delete personnel mutation
export const useDeletePersonnelMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.personnel[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete personnel");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Personnel deleted successfully");
			queryClient.invalidateQueries({
				queryKey: personnelKeys.list({ organizationId }),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete personnel");
			console.error("Delete personnel error:", error);
		},
	});
};

// License-related functions and hooks
export const personnelLicenseKeys = {
	all: ["personnel", "licenses"] as const,
	list: (personnelId: string, organizationId: string) =>
		[...personnelLicenseKeys.all, personnelId, organizationId] as const,
	detail: (personnelId: string, licenseId: string, organizationId: string) =>
		[
			...personnelLicenseKeys.all,
			"detail",
			personnelId,
			licenseId,
			organizationId,
		] as const,
};

// Get licenses for a personnel
export const fetchPersonnelLicenses = async (
	personnelId: string,
	organizationId: string,
) => {
	const response = await apiClient.personnel[":id"].licenses.$get({
		param: { id: personnelId },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch personnel licenses");
	}

	return response.json();
};

export const usePersonnelLicensesQuery = (
	personnelId: string,
	organizationId: string,
) => {
	return useQuery({
		queryKey: personnelLicenseKeys.list(personnelId, organizationId),
		queryFn: () => fetchPersonnelLicenses(personnelId, organizationId),
		enabled: !!personnelId && !!organizationId,
	});
};

// Get a specific license
export const fetchPersonnelLicense = async (
	personnelId: string,
	licenseId: string,
	organizationId: string,
) => {
	const response = await apiClient.personnel[":id"].licenses[
		":licenseId"
	].$get({
		param: { id: personnelId, licenseId },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch personnel license");
	}

	return response.json();
};

export const usePersonnelLicenseQuery = (
	personnelId: string,
	licenseId: string,
	organizationId: string,
) => {
	return useQuery({
		queryKey: personnelLicenseKeys.detail(
			personnelId,
			licenseId,
			organizationId,
		),
		queryFn: () =>
			fetchPersonnelLicense(personnelId, licenseId, organizationId),
		enabled: !!personnelId && !!licenseId && !!organizationId,
	});
};

// License API request types
interface AddPersonnelLicenseRequest {
	param: { id: string };
	query: { organizationId: string };
	json: CreateDriverLicense;
}

interface UpdatePersonnelLicenseRequest {
	param: { id: string; licenseId: string };
	query: { organizationId: string };
	json: UpdateDriverLicense;
}

// Add a license to personnel
export const useAddPersonnelLicenseMutation = (
	personnelId: string,
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (licenseData: CreateDriverLicense) => {
			const request: AddPersonnelLicenseRequest = {
				param: { id: personnelId },
				query: { organizationId },
				json: licenseData,
			};

			const response =
				await apiClient.personnel[":id"].licenses.$post(request);

			if (!response.ok) {
				throw new Error("Failed to add license");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("License added successfully");
			// Invalidate license list query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.list(
					personnelId,
					organizationId,
				),
			});
			// Also invalidate the personnel detail query
			queryClient.invalidateQueries({
				queryKey: personnelKeys.detail(organizationId, personnelId),
			});
		},
		onError: (error) => {
			toast.error("Failed to add license");
			console.error("Add license error:", error);
		},
	});
};

// Update a license
export const useUpdatePersonnelLicenseMutation = (
	personnelId: string,
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			licenseId,
			...licenseData
		}: { licenseId: string } & UpdateDriverLicense) => {
			const request: UpdatePersonnelLicenseRequest = {
				param: { id: personnelId, licenseId },
				query: { organizationId },
				json: licenseData,
			};

			const response =
				await apiClient.personnel[":id"].licenses[":licenseId"].$put(
					request,
				);

			if (!response.ok) {
				throw new Error("Failed to update license");
			}

			return response.json();
		},
		onSuccess: (_, variables) => {
			toast.success("License updated successfully");
			// Invalidate license list query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.list(
					personnelId,
					organizationId,
				),
			});
			// Invalidate the specific license query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.detail(
					personnelId,
					variables.licenseId,
					organizationId,
				),
			});
			// Also invalidate the personnel detail query
			queryClient.invalidateQueries({
				queryKey: personnelKeys.detail(organizationId, personnelId),
			});
		},
		onError: (error) => {
			toast.error("Failed to update license");
			console.error("Update license error:", error);
		},
	});
};

// Delete a license
export const useDeletePersonnelLicenseMutation = (
	personnelId: string,
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (licenseId: string) => {
			const response = await apiClient.personnel[":id"].licenses[
				":licenseId"
			].$delete({
				param: { id: personnelId, licenseId },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete license");
			}

			return response.json();
		},
		onSuccess: (_, licenseId) => {
			toast.success("License deleted successfully");
			// Invalidate license list query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.list(
					personnelId,
					organizationId,
				),
			});
			// Invalidate the specific license query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.detail(
					personnelId,
					licenseId,
					organizationId,
				),
			});
			// Also invalidate the personnel detail query
			queryClient.invalidateQueries({
				queryKey: personnelKeys.detail(organizationId, personnelId),
			});
		},
		onError: (error) => {
			toast.error("Failed to delete license");
			console.error("Delete license error:", error);
		},
	});
};

// Add upload document mutation
export const useUploadLicenseDocumentMutation = (
	personnelId: string,
	licenseId: string,
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (file: File) => {
			// Create a FormData object for multipart/form-data upload
			const formData = new FormData();
			formData.append("file", file);

			// Create consistent URL using window.location.origin
			const baseUrl =
				typeof window !== "undefined" ? window.location.origin : "";
			const uploadUrl = `${baseUrl}/api/personnel/${personnelId}/licenses/${licenseId}/document?organizationId=${organizationId}`;
			console.log(`Upload document URL: ${uploadUrl}`);

			// Make the API request
			const response = await fetch(uploadUrl, {
				method: "POST",
				body: formData,
				credentials: "include",
			});

			if (!response.ok) {
				console.error(
					`Document upload failed with status: ${response.status}`,
				);
				try {
					const errorText = await response.text();
					console.error(`Error response: ${errorText}`);
				} catch (e) {
					console.error("Error reading response:", e);
				}
				throw new Error("Failed to upload license document");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Document uploaded successfully");
			// Invalidate the personnel license query
			queryClient.invalidateQueries({
				queryKey: personnelLicenseKeys.detail(
					personnelId,
					licenseId,
					organizationId,
				),
			});
			// Also invalidate the personnel detail query
			queryClient.invalidateQueries({
				queryKey: personnelKeys.detail(organizationId, personnelId),
			});
		},
		onError: (error) => {
			toast.error("Failed to upload document");
			console.error("Upload document error:", error);
		},
	});
};
