import { ACTIONS, ENTITY_TYPES, logAuditEvent } from "@repo/database";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";
import {
	addCounterpartyToGroup,
	createCounterpartyGroup,
	deleteCounterpartyGroup,
	getCounterpartyGroupById,
	getGroupMembers,
	listCounterpartyGroups,
	removeCounterpartyFromGroup,
	updateCounterpartyGroup,
} from "./lib/counterparty-groups";
import {
	createCounterpartyGroupSchema,
	getGroupMembersSchema,
	listCounterpartyGroupsSchema,
	updateCounterpartyGroupSchema,
} from "./types";

export const counterpartyGroupsRouter = new Hono()
	.basePath("/counterparty-groups")
	.get(
		"/",
		authMiddleware,
		validator("query", listCounterpartyGroupsSchema),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "List counterparty groups",
			description: "Get a paginated list of counterparty groups",
			parameters: [
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "search",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: { type: "integer", format: "int32", minimum: 1 },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "integer", format: "int32", minimum: 1 },
				},
				{
					name: "sortBy",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "sortDirection",
					in: "query",
					required: false,
					schema: { type: "string", enum: ["asc", "desc"] },
				},
				{
					name: "includeMembers",
					in: "query",
					required: false,
					schema: { type: "boolean" },
					description: "Include counterparty members in response",
				},
			],
			responses: {
				200: {
					description:
						"List of counterparty groups with pagination info",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									items: {
										type: "array",
										items: {
											type: "object",
											properties: {
												id: { type: "string" },
												name: { type: "string" },
												description: { type: "string" },
												organizationId: {
													type: "string",
												},
												createdAt: {
													type: "string",
													format: "date-time",
												},
												updatedAt: {
													type: "string",
													format: "date-time",
												},
												members: {
													type: "array",
													items: {
														type: "object",
														properties: {
															id: {
																type: "string",
															},
															counterpartyId: {
																type: "string",
															},
															counterparty: {
																type: "object",
																properties: {
																	id: {
																		type: "string",
																	},
																	nameLine1: {
																		type: "string",
																	},
																	nameLine2: {
																		type: "string",
																	},
																	customerNumber:
																		{
																			type: "string",
																		},
																},
															},
														},
													},
												},
											},
										},
									},
									total: { type: "number" },
									page: { type: "number" },
									totalPages: { type: "number" },
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
			},
		}),
		async (c) => {
			try {
				const options = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					options.organizationId,
					user.id,
				);

				return c.json(await listCounterpartyGroups(options));
			} catch (error) {
				console.error("Error listing counterparty groups:", error);
				return c.json(
					{ message: "Failed to list counterparty groups" },
					500,
				);
			}
		},
	)
	.get(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Get counterparty group by ID",
			description: "Get a single counterparty group by its ID",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			responses: {
				200: {
					description: "Counterparty group details",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									id: { type: "string" },
									name: { type: "string" },
									description: { type: "string" },
									organizationId: { type: "string" },
									createdAt: {
										type: "string",
										format: "date-time",
									},
									updatedAt: {
										type: "string",
										format: "date-time",
									},
									members: {
										type: "array",
										items: {
											type: "object",
											properties: {
												id: { type: "string" },
												counterpartyId: {
													type: "string",
												},
												counterparty: {
													type: "object",
													properties: {
														id: { type: "string" },
														nameLine1: {
															type: "string",
														},
														nameLine2: {
															type: "string",
														},
														customerNumber: {
															type: "string",
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Counterparty group not found",
				},
			},
		}),
		async (c) => {
			try {
				const { id } = c.req.valid("param");
				const { organizationId } = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(organizationId, user.id);

				return c.json(
					await getCounterpartyGroupById(id, organizationId),
				);
			} catch (error) {
				console.error("Error fetching counterparty group:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json(
						{ message: "Counterparty group not found" },
						404,
					);
				}
				return c.json(
					{ message: "Failed to fetch counterparty group" },
					500,
				);
			}
		},
	)
	.post(
		"/",
		authMiddleware,
		validator("json", createCounterpartyGroupSchema),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Create counterparty group",
			description: "Create a new counterparty group",
			requestBody: {
				required: true,
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								name: { type: "string" },
								description: { type: "string" },
								organizationId: { type: "string" },
							},
							required: ["name", "organizationId"],
						},
					},
				},
			},
			responses: {
				201: {
					description: "Counterparty group created successfully",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									id: { type: "string" },
									name: { type: "string" },
									description: { type: "string" },
									organizationId: { type: "string" },
									createdAt: {
										type: "string",
										format: "date-time",
									},
									updatedAt: {
										type: "string",
										format: "date-time",
									},
								},
							},
						},
					},
				},
				400: {
					description: "Bad request - Invalid data",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				409: {
					description: "Conflict - Group name already exists",
				},
			},
		}),
		async (c) => {
			try {
				const data = c.req.valid("json");
				const user = c.get("user");

				// Check if user has access to the organization
				const { organization } = await verifyOrganizationMembership(
					data.organizationId,
					user.id,
				);

				const group = await createCounterpartyGroup(data);

				// Log audit event
				await logAuditEvent({
					organizationId: organization.id,
					userId: user.id,
					entityType: ENTITY_TYPES.COUNTERPARTY,
					entityId: group.id,
					action: ACTIONS.CREATE,
					payload: { name: data.name, description: data.description },
				});

				return c.json(group, 201);
			} catch (error) {
				console.error("Error creating counterparty group:", error);
				if (
					error instanceof Error &&
					error.message.includes("already exists")
				) {
					return c.json({ message: error.message }, 409);
				}
				return c.json(
					{ message: "Failed to create counterparty group" },
					500,
				);
			}
		},
	)
	.put(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Update counterparty group",
			description: "Update an existing counterparty group",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
			],
			requestBody: {
				required: true,
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								name: { type: "string" },
								description: { type: "string" },
								organizationId: { type: "string" },
							},
							required: ["organizationId"],
						},
					},
				},
			},
			responses: {
				200: {
					description: "Counterparty group updated successfully",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									id: { type: "string" },
									name: { type: "string" },
									description: { type: "string" },
									organizationId: { type: "string" },
									updatedAt: {
										type: "string",
										format: "date-time",
									},
								},
							},
						},
					},
				},
				400: {
					description: "Bad request - Invalid data",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Counterparty group not found",
				},
				409: {
					description: "Conflict - Group name already exists",
				},
			},
		}),
		async (c) => {
			try {
				const { id } = c.req.valid("param");
				const jsonData = await c.req.json();

				// Validate with schema
				const validatedData = updateCounterpartyGroupSchema.parse({
					...jsonData,
					id, // Ensure ID is in the data
				});

				const user = c.get("user");

				// Check if user has access to the organization
				const { organization } = await verifyOrganizationMembership(
					validatedData.organizationId!,
					user.id,
				);

				// Get existing group before update for audit log
				const existingGroup = await getCounterpartyGroupById(
					id,
					validatedData.organizationId!,
				);

				const group = await updateCounterpartyGroup(validatedData);

				// Log audit event
				await logAuditEvent({
					organizationId: organization.id,
					userId: user.id,
					entityType: ENTITY_TYPES.COUNTERPARTY,
					entityId: group.id,
					action: ACTIONS.UPDATE,
					payload: {
						before: existingGroup,
						after: group,
						changes: {
							name: validatedData.name,
							description: validatedData.description,
						},
					},
				});

				return c.json(group);
			} catch (error) {
				console.error("Error updating counterparty group:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json(
						{ message: "Counterparty group not found" },
						404,
					);
				}
				if (
					error instanceof Error &&
					error.message.includes("already exists")
				) {
					return c.json({ message: error.message }, 409);
				}
				return c.json(
					{ message: "Failed to update counterparty group" },
					500,
				);
			}
		},
	)
	.delete(
		"/:id",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Delete counterparty group",
			description: "Delete an existing counterparty group",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			responses: {
				200: {
					description: "Counterparty group deleted successfully",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									success: { type: "boolean" },
								},
								required: ["success"],
							},
						},
					},
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Counterparty group not found",
				},
			},
		}),
		async (c) => {
			try {
				const { id } = c.req.valid("param");
				const { organizationId } = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				const { organization } = await verifyOrganizationMembership(
					organizationId,
					user.id,
				);

				// Get group details before deletion for audit log
				const group = await getCounterpartyGroupById(
					id,
					organizationId,
				);
				const result = await deleteCounterpartyGroup(
					id,
					organizationId,
				);

				// Log audit event
				await logAuditEvent({
					organizationId: organization.id,
					userId: user.id,
					entityType: ENTITY_TYPES.COUNTERPARTY,
					entityId: id,
					action: ACTIONS.DELETE,
					payload: {
						deletedGroup: group,
					},
				});

				return c.json(result);
			} catch (error) {
				console.error("Error deleting counterparty group:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json(
						{ message: "Counterparty group not found" },
						404,
					);
				}
				return c.json(
					{ message: "Failed to delete counterparty group" },
					500,
				);
			}
		},
	)
	.post(
		"/:id/members",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator(
			"json",
			z.object({
				counterpartyId: z.string(),
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Add counterparty to group",
			description: "Add a counterparty to a group",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
					description: "Group ID",
				},
			],
			requestBody: {
				required: true,
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								counterpartyId: { type: "string" },
								organizationId: { type: "string" },
							},
							required: ["counterpartyId", "organizationId"],
						},
					},
				},
			},
			responses: {
				201: {
					description: "Counterparty added to group successfully",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Group or counterparty not found",
				},
				409: {
					description: "Counterparty already in group",
				},
			},
		}),
		async (c) => {
			try {
				const { id: groupId } = c.req.valid("param");
				const { counterpartyId, organizationId } = c.req.valid("json");
				const user = c.get("user");

				// Check if user has access to the organization
				const { organization } = await verifyOrganizationMembership(
					organizationId,
					user.id,
				);

				const membership = await addCounterpartyToGroup(
					{ groupId, counterpartyId },
					organizationId,
				);

				// Log audit event
				await logAuditEvent({
					organizationId: organization.id,
					userId: user.id,
					entityType: ENTITY_TYPES.COUNTERPARTY,
					entityId: groupId,
					action: ACTIONS.UPDATE,
					payload: {
						action: "add_member",
						counterpartyId,
					},
				});

				return c.json(membership, 201);
			} catch (error) {
				console.error("Error adding counterparty to group:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json({ message: error.message }, 404);
				}
				if (
					error instanceof Error &&
					error.message.includes("already")
				) {
					return c.json({ message: error.message }, 409);
				}
				return c.json(
					{ message: "Failed to add counterparty to group" },
					500,
				);
			}
		},
	)
	.delete(
		"/:id/members/:counterpartyId",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
				counterpartyId: z.string(),
			}),
		),
		validator(
			"query",
			z.object({
				organizationId: z.string(),
			}),
		),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Remove counterparty from group",
			description: "Remove a counterparty from a group",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
					description: "Group ID",
				},
				{
					name: "counterpartyId",
					in: "path",
					required: true,
					schema: { type: "string" },
					description: "Counterparty ID",
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
			],
			responses: {
				200: {
					description: "Counterparty removed from group successfully",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Group, counterparty, or membership not found",
				},
			},
		}),
		async (c) => {
			try {
				const { id: groupId, counterpartyId } = c.req.valid("param");
				const { organizationId } = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				const { organization } = await verifyOrganizationMembership(
					organizationId,
					user.id,
				);

				const result = await removeCounterpartyFromGroup(
					{ groupId, counterpartyId },
					organizationId,
				);

				// Log audit event
				await logAuditEvent({
					organizationId: organization.id,
					userId: user.id,
					entityType: ENTITY_TYPES.COUNTERPARTY,
					entityId: groupId,
					action: ACTIONS.UPDATE,
					payload: {
						action: "remove_member",
						counterpartyId,
					},
				});

				return c.json(result);
			} catch (error) {
				console.error("Error removing counterparty from group:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json({ message: error.message }, 404);
				}
				return c.json(
					{ message: "Failed to remove counterparty from group" },
					500,
				);
			}
		},
	)
	.get(
		"/:id/members",
		authMiddleware,
		validator(
			"param",
			z.object({
				id: z.string(),
			}),
		),
		validator("query", getGroupMembersSchema.omit({ groupId: true })),
		describeRoute({
			tags: ["Counterparty Groups"],
			summary: "Get group members",
			description: "Get paginated list of counterparties in a group",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: { type: "string" },
					description: "Group ID",
				},
				{
					name: "organizationId",
					in: "query",
					required: true,
					schema: { type: "string" },
				},
				{
					name: "search",
					in: "query",
					required: false,
					schema: { type: "string" },
				},
				{
					name: "limit",
					in: "query",
					required: false,
					schema: { type: "integer", format: "int32", minimum: 1 },
				},
				{
					name: "page",
					in: "query",
					required: false,
					schema: { type: "integer", format: "int32", minimum: 1 },
				},
			],
			responses: {
				200: {
					description: "List of group members with pagination info",
				},
				401: {
					description: "Unauthorized - User not authenticated",
				},
				403: {
					description: "Forbidden - User not member of organization",
				},
				404: {
					description: "Group not found",
				},
			},
		}),
		async (c) => {
			try {
				const { id: groupId } = c.req.valid("param");
				const queryParams = c.req.valid("query");
				const user = c.get("user");

				// Check if user has access to the organization
				await verifyOrganizationMembership(
					queryParams.organizationId,
					user.id,
				);

				const options = {
					...queryParams,
					groupId,
				};

				return c.json(await getGroupMembers(options));
			} catch (error) {
				console.error("Error fetching group members:", error);
				if (
					error instanceof Error &&
					error.message.includes("not found")
				) {
					return c.json({ message: "Group not found" }, 404);
				}
				return c.json(
					{ message: "Failed to fetch group members" },
					500,
				);
			}
		},
	);
