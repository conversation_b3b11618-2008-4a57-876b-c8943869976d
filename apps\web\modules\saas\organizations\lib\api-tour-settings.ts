import type { UpdateTourConfigurationInput } from "@repo/api/src/routes/settings/tour-settings/types";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Query keys for React Query
export const tourConfigurationKeys = {
	all: ["tour-configuration"] as const,
	details: () => [...tourConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...tourConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchTourConfiguration = async (organizationId: string) => {
	const response = await apiClient.settings["tour-settings"].$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch tour configuration");
	}

	return response.json();
};

// React Query Hooks
export const useTourConfigurationQuery = (
	organizationId: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined ? options.enabled : !!organizationId;

	return useQuery({
		queryKey: tourConfigurationKeys.detail(organizationId),
		queryFn: () => fetchTourConfiguration(organizationId),
		enabled: isEnabled && !!organizationId,
	});
};

// Mutation Hooks
export const useUpdateTourConfigurationMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateTourConfigurationInput, "organizationId">,
		) => {
			const response = await apiClient.settings["tour-settings"].$put({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to update tour configuration");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Tour configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: tourConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update tour configuration",
			);
		},
	});
};
