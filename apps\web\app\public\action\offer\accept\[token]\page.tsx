"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Loader2, XCircle } from "lucide-react";
import { useEffect, useState } from "react";

interface AcceptPageProps {
	params: Promise<{
		token: string;
	}>;
	searchParams: Promise<Record<string, string | string[] | undefined>>;
}

export default function AcceptOfferPage({ params }: AcceptPageProps) {
	const [token, setToken] = useState<string>("");
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading",
	);
	const [message, setMessage] = useState<string>("");
	const [offerId, setOfferId] = useState<string | null>(null);
	const [mounted, setMounted] = useState(false);

	// Only set mounted after hydration is complete
	useEffect(() => {
		setMounted(true);
	}, []);

	// Set token after mounting
	useEffect(() => {
		if (!mounted) {
			return;
		}

		const getToken = async () => {
			try {
				const resolvedParams = await params;
				setToken(resolvedParams.token);
			} catch (error) {
				console.error("Error resolving params:", error);
				setStatus("error");
				setMessage("Failed to load the necessary parameters.");
			}
		};

		getToken();
	}, [params, mounted]);

	// Fetch data after mounting and getting token
	useEffect(() => {
		if (!mounted || !token) {
			return;
		}

		const acceptOffer = async () => {
			try {
				const response = await fetch(
					`/api/public/action/offer/accept/${token}`,
					{
						method: "GET",
						headers: {
							"Content-Type": "application/json",
						},
					},
				);

				const data = await response.json();

				if (response.ok && data.success) {
					setStatus("success");
					setMessage(
						data.message || "Offer has been accepted successfully.",
					);
					if (data.offerId) {
						setOfferId(data.offerId);
					}
				} else {
					setStatus("error");
					setMessage(
						data.error?.message ||
							"Failed to accept the offer. Please try again or contact support.",
					);
				}
			} catch (error) {
				console.error("Error accepting offer:", error);
				setStatus("error");
				setMessage(
					"An unexpected error occurred. Please try again later or contact support.",
				);
			}
		};

		acceptOffer();
	}, [token, mounted]);

	if (!mounted) {
		return (
			<>
				<h1 className="text-xl font-semibold mb-2">Offer Response</h1>
				<div className="flex flex-col items-center justify-center py-6">
					<div className="h-12 w-12 bg-gray-200 rounded-full animate-pulse mb-4" />
					<div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
				</div>
			</>
		);
	}

	return (
		<>
			<h1 className="text-xl font-semibold mb-2">Offer Response</h1>

			{status === "loading" ? (
				<div className="flex flex-col items-center justify-center py-6">
					<Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
					<p className="text-gray-600">
						Processing your acceptance...
					</p>
				</div>
			) : status === "success" ? (
				<>
					<div className="flex flex-col items-center justify-center py-6">
						<CheckCircle className="h-16 w-16 text-green-500 mb-4" />
						<p className="text-lg font-medium text-green-700">
							Thank you!
						</p>
						<p className="mt-2 text-gray-600">{message}</p>
						{offerId && (
							<p className="mt-2 text-sm text-gray-500">
								Offer ID: {offerId}
							</p>
						)}
					</div>

					<div className="mt-4">
						<p className="text-gray-600 text-sm">
							Someone from our team will contact you with the next
							steps.
						</p>
					</div>
				</>
			) : (
				<div className="flex flex-col items-center justify-center py-6">
					<XCircle className="h-16 w-16 text-red-500 mb-4" />
					<p className="text-lg font-medium text-red-700">
						Something went wrong
					</p>
					<p className="mt-2 text-gray-600">{message}</p>
				</div>
			)}
		</>
	);
}
