"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import type { CreateCounterpartyInput } from "@repo/api/src/routes/counterparties/types";
import { createCounterpartySchema } from "@repo/api/src/routes/counterparties/types";
import { useCounterpartyMutations } from "@saas/contacts/hooks/use-counterparty";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { Wizard, type WizardStep } from "@saas/shared/components/wizard";
import { Form } from "@ui/components/form";
import { useTranslations } from "next-intl";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { AddressSection } from "./sections/address-section";
import { BusinessSection } from "./sections/business-section";
import { CompanyIdSection } from "./sections/company-id-section";
import { FinancialSection } from "./sections/financial-section";
import { FreightExchangeSection } from "./sections/freight-exchange-section";
import { OrderSection } from "./sections/order-section";
import { PersonsSection } from "./sections/persons-section";

// Define shared types for verified fields
export type VerifiedAddressField =
	| "street"
	| "zipCode"
	| "city"
	| "country"
	| "nameLine1";

interface CounterpartyFormProps {
	counterpartyId?: string;
	organizationId: string;
	onSuccess?: () => void;
	onFormChange?: (hasChanges: boolean) => void;
	defaultValues?: Partial<CreateCounterpartyInput>;
}

interface AddressVerificationState {
	fields: VerifiedAddressField[];
	values: Record<string, string>;
}

// Custom hook for counterparty form
function useCounterpartyForm({
	counterpartyId,
	onSuccess,
	onFormChange,
	defaultValues,
}: CounterpartyFormProps) {
	const { createCounterparty, updateCounterparty, isLoading } =
		useCounterpartyMutations();

	const form = useForm<CreateCounterpartyInput>({
		resolver: zodResolver(createCounterpartySchema),
		defaultValues: {
			nameLine1: "",
			nameLine2: "",
			types: [
				{
					type: "customer",
				},
			],
			addressUsages: [
				{
					type: "PRIMARY",
					isDefault: true,
					isGlobal: false,
					address: {
						nameLine: undefined,
						street: "",
						zipCode: "",
						city: "",
						country: "",
						addressSupplement: "",
						isVerified: false,
						latitude: 0,
						longitude: 0,
					},
				},
			],
			// Ensure all form fields from the schema are initialized
			email: undefined,
			telephone: "",
			uidNumber: "",
			// Initialize financial profile
			financialProfile: {
				invoiceType: "invoice",
				creditNotesEmail: undefined,
				invoiceEmail: undefined,
				invoiceInfo: "",
				taxNotes: "",
				currency: "",
				creditLimit: "",
				balance: "",
			},
			// Initialize order profile
			orderProfile: {
				orderWarningText: "",
				loadingMaterialExchange: false,
				loadingMaterialInfo: "",
				accountingOrderText: "",
				neutralityText: "",
			},
			// Initialize freight exchange profile fields
			freightExchangeProfile: {
				carrierNumber: "",
				timocomNumber: "",
				teleroute: "",
				transEu: "",
				otherExchanges: "",
			},
			...defaultValues,
		},
	});

	// Watch for form changes and notify parent component
	React.useEffect(() => {
		const subscription = form.watch((value, { name, type }) => {
			// Only trigger on user-initiated changes, not on mount
			if (type === "change") {
				onFormChange?.(true);
			}
		});

		return () => subscription.unsubscribe();
	}, [form, onFormChange]);

	// Initialize field array for addresses
	const addressesArray = useFieldArray({
		control: form.control,
		name: "addressUsages",
	});

	async function onSubmit(data: CreateCounterpartyInput) {
		try {
			if (counterpartyId) {
				await updateCounterparty({
					id: counterpartyId,
					...data,
				});
			} else {
				await createCounterparty({
					...data,
				});
			}
			onSuccess?.();
		} catch (error) {}
	}

	return {
		form,
		onSubmit,
		isLoading,
		addressesArray,
	};
}

export function CounterpartyFormWizard(props: CounterpartyFormProps) {
	const t = useTranslations("app.contacts");
	const { form, onSubmit, addressesArray } = useCounterpartyForm({
		counterpartyId: props.counterpartyId,
		organizationId: props.organizationId,
		onSuccess: props.onSuccess,
		onFormChange: props.onFormChange,
		defaultValues: props.defaultValues,
	});

	// Get access to the keyboard shortcut context
	const { activeScopes, registerScopeWithShortcuts } = useShortcuts();

	// Register the shortcut scope when the component mounts
	React.useEffect(() => {
		const cleanup = registerScopeWithShortcuts(
			"create-counterparty-shortcuts",
		);
		return cleanup;
	}, [registerScopeWithShortcuts]);

	// State for address verification
	const [verifiedFields, setVerifiedFields] = React.useState<
		VerifiedAddressField[]
	>([]);
	const [addressVerification, setAddressVerification] =
		React.useState<AddressVerificationState>({
			fields: [],
			values: {},
		});

	// Define steps for the wizard
	const steps: WizardStep[] = [
		{
			id: "companyId",
			label: t("wizard.steps.companyId.label"),
			description: t("wizard.steps.companyId.description"),
			isOptional: false,
		},
		{
			id: "address",
			label: t("wizard.steps.address.label"),
			description: t("wizard.steps.address.description"),
			isOptional: false,
		},
		{
			id: "business",
			label: "Business Details",
			description: "Business and freight exchange information",
			isOptional: false,
		},
		{
			id: "freightExchange",
			label: "Freight Exchange",
			description: "Freight exchange information",
			isOptional: true,
		},
		{
			id: "financial",
			label: "Financial Details",
			description: "Payment and billing information",
			isOptional: true,
		},
		{
			id: "order",
			label: "Order Details",
			description: "Order settings and information",
			isOptional: true,
		},
		{
			id: "persons",
			label: "Contact Persons",
			description: "People associated with this counterparty",
			isOptional: true,
		},
	];

	// Validation function for steps
	const isStepValid = (stepIndex: number): boolean => {
		const formValues = form.getValues();
		const stepId = steps[stepIndex]?.id;

		switch (stepId) {
			case "companyId":
				return !!formValues.nameLine1;
			case "address": {
				// Check if there's at least one address with all required fields
				if (
					!formValues.addressUsages ||
					formValues.addressUsages.length === 0
				) {
					return false;
				}

				// First try to find the default address
				const defaultAddressUsage = formValues.addressUsages.find(
					(addr) => addr.isDefault,
				);
				// If no default, try to find a primary address
				const primaryAddressUsage = formValues.addressUsages.find(
					(addr) => addr.type === "PRIMARY",
				);
				// Use default, primary, or first address in the array
				const addressUsageToCheck =
					defaultAddressUsage ||
					primaryAddressUsage ||
					formValues.addressUsages[0];

				// Check if address exists and has all required fields
				if (!addressUsageToCheck?.address) {
					return false;
				}

				// Check required fields
				return !!(
					addressUsageToCheck.address.street &&
					addressUsageToCheck.address.zipCode &&
					addressUsageToCheck.address.city &&
					addressUsageToCheck.address.country
				);
			}
			case "business":
				return true; // Always valid for now, customize as needed
			case "freightExchange":
				return true; // Optional and always valid
			case "financial":
				return true; // Optional and always valid
			case "order":
				return true; // Optional and always valid
			case "persons":
				return true; // Optional and always valid
			default:
				return false;
		}
	};

	// Function to render the content of each step
	const renderStep = (step: WizardStep) => {
		switch (step.id) {
			case "companyId":
				return (
					<div className="p-4">
						<CompanyIdSection
							onVerifiedAddressFields={setVerifiedFields}
							initialVerificationState={addressVerification}
						/>
					</div>
				);
			case "address":
				return (
					<div className="p-4">
						<AddressSection
							verifiedFields={verifiedFields}
							verifiedValues={addressVerification.values}
							addressesArray={addressesArray}
						/>
					</div>
				);
			case "business":
				return (
					<div className="p-4">
						<BusinessSection />
					</div>
				);
			case "freightExchange":
				return (
					<div className="p-4">
						<FreightExchangeSection />
					</div>
				);
			case "financial":
				return (
					<div className="p-4">
						<FinancialSection />
					</div>
				);
			case "order":
				return (
					<div className="p-4">
						<OrderSection />
					</div>
				);
			case "persons":
				return (
					<div className="p-4">
						<PersonsSection />
					</div>
				);
			default:
				return null;
		}
	};

	// Handle form submission when the wizard is completed
	const handleSubmitWizard = () => {
		form.handleSubmit(onSubmit)();
	};

	return (
		<Form {...form}>
			<form
				className="flex flex-col"
				onSubmit={form.handleSubmit(onSubmit)}
			>
				<Wizard
					steps={steps}
					isStepValid={isStepValid}
					onSubmit={handleSubmitWizard}
					renderStep={renderStep}
					enableKeyboardNavigation={true}
					shortcutsScope="create-counterparty-shortcuts"
				/>
			</form>
		</Form>
	);
}

export default CounterpartyFormWizard;
