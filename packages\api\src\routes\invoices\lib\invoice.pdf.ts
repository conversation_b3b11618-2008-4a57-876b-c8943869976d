import { PDFService } from "@repo/pdf";
import type { InvoiceData, PDFGenerationOptions } from "@repo/pdf";
import { HTTPException } from "hono/http-exception";
import type { CreateInvoiceInput } from "../types";
import { type PreparedInvoiceData, prepareInvoiceData } from "./invoice.data";

// We no longer need the InvoicePdfData interface as we're removing backward compatibility
// export interface InvoicePdfData { ... }

/**
 * Generate invoice PDF using prepared data
 */
export async function createInvoicePDFFromPreparedData(
	preparedData: PreparedInvoiceData,
	preview = false,
): Promise<Buffer | { success: boolean; message: string; pdfBase64: string }> {
	try {
		// Calculate payment conditions based on invoice date and due date
		const paymentConditions = calculatePaymentConditions(
			preparedData.rawData.invoiceDate,
			preparedData.rawData.dueDate,
		);

		// For previews, use the legacy method with formatted data
		if (preview) {
			// Prepare data for the PDFService in the legacy format for previews
			const invoiceData: InvoiceData = {
				// Format address with customer name as first line
				invoice_address: `${preparedData.customerName}\n${preparedData.billingAddress.formatted}`,
				// Pass invoice_number as a simple string
				invoice_number: preparedData.invoiceNumber || "PREVIEW",
				invoice_date: preparedData.rawData.invoiceDate
					? formatDate(preparedData.rawData.invoiceDate)
					: formatDate(new Date()),
				customer_vat: preparedData.customerTaxNumber || "",
				user_name: preparedData.contact?.name || "",
				company_logo: preparedData.company_logo || "",
				// Format invoice details as a multi-line string
				invoice_details: `Invoice Date: ${formatDate(preparedData.rawData.invoiceDate || new Date())}${preparedData.rawData.dueDate ? `\nDue Date: ${formatDate(preparedData.rawData.dueDate)}` : ""}
VAT Number: ${preparedData.customerTaxNumber || ""}
Payment Conditions: ${paymentConditions}
Customer Number: ${preparedData.customer?.customerNumber || preparedData.customer?.id?.substring(0, 8) || "N/A"}${preparedData.customer?.customerRefNumber ? `\nCustomer Ref. Number: ${preparedData.customer.customerRefNumber}` : ""}
Clerk: ${preparedData.creator?.name || preparedData.contact?.name || ""}`,
				// Tables
				invoice_table: preparedData.invoiceTable,
				total_details_table: preparedData.totalDetailsTable,

				// Organization details for the footer
				org_name:
					preparedData.organization?.name || "Premium Logistics GmbH",
				bank_name:
					preparedData.organization?.bankName ||
					"Raiffeisenbank Wien",
				email:
					preparedData.organization?.email ||
					"<EMAIL>",
				address: preparedData.organization?.street || "In der Grub 7",
				reg_number:
					preparedData.organization?.companyRegistrationNumber ||
					"FN 123948",
				iban:
					preparedData.organization?.bankIbanNumber ||
					"DE19412945785",
				phone: preparedData.organization?.telephone || "+***********",
				city: `${preparedData.organization?.zipCode || "4810"} ${preparedData.organization?.city || "Gmunden"}`,
				vat_number:
					preparedData.organization?.vatNumber || "VAT12358518",
				bic: preparedData.organization?.bankBicNumber || "APSKFURR738",
				website:
					preparedData.organization?.website ||
					"www.premiumlogistics.com",
				country: preparedData.organization?.country || "Austria",
			};

			const pdfService = new PDFService();
			const pdfResult = await pdfService.generateInvoice(invoiceData);

			return {
				success: true,
				message: "Invoice PDF generated successfully",
				pdfBase64: pdfResult.buffer.toString("base64"),
			};
		}

		// For real invoices, use the new ZUGFeRD-enabled method
		const pdfService = new PDFService();

		// Use the ZUGFeRD setting from the organization's configuration
		const pdfOptions: PDFGenerationOptions = {
			enableZugferd: preparedData.zugferdEnabled,
		};

		// Use the new PDF service method that accepts PreparedInvoiceData directly
		const pdfResult = await pdfService.generateInvoice(
			preparedData,
			pdfOptions,
		);

		// For regular usage, just return the buffer for email attachments
		return pdfResult.buffer;
	} catch (error: any) {
		console.error("Failed to generate invoice PDF:", error);
		console.error("Invoice data that caused the error:", {
			invoiceNumber: preparedData.invoiceNumber,
			customerName: preparedData.customerName,
		});
		throw error instanceof HTTPException
			? error
			: new HTTPException(500, {
					message: `Failed to generate invoice PDF: ${error.message || "Unknown error"}`,
				});
	}
}

// Helper function to format dates consistently
function formatDate(date: Date | string): string {
	if (!date) {
		return "";
	}

	const d = new Date(date);
	return `${d.getDate().toString().padStart(2, "0")}.${(d.getMonth() + 1).toString().padStart(2, "0")}.${d.getFullYear()}`;
}

// Helper function to calculate payment conditions
function calculatePaymentConditions(
	invoiceDate?: Date,
	dueDate?: Date,
): string {
	if (!invoiceDate || !dueDate) {
		return "20 days";
	}

	const invDate = new Date(invoiceDate);
	const duDate = new Date(dueDate);

	// Calculate days difference
	const diffTime = duDate.getTime() - invDate.getTime();
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

	if (diffDays <= 0) {
		return "Due immediately";
	}

	if (diffDays === 1) {
		return "1 day";
	}

	return `${diffDays} days`;
}

/**
 * Generate a preview PDF from CreateInvoiceInput data
 * This function is the primary entry point for generating invoice PDF previews
 */
export async function previewInvoicePDFFromInput(
	organizationId: string,
	data: CreateInvoiceInput,
	userId?: string,
): Promise<{ success: boolean; message: string; pdfBase64: string }> {
	// We need to cast here to ensure the return type is correct
	const preparedData = await prepareInvoiceData(
		organizationId,
		userId || "system",
		data,
		true,
	);
	const result = await createInvoicePDFFromPreparedData(preparedData, true);

	// Ensure we return the correct type
	if (Buffer.isBuffer(result)) {
		// This shouldn't happen when preview=true, but handle it just in case
		return {
			success: true,
			message: "Invoice PDF generated successfully",
			pdfBase64: result.toString("base64"),
		};
	}

	return result;
}

/**
 * Generate an invoice PDF for email attachment using existing invoice data
 * This avoids creating a preview or generating a new invoice number
 */
export async function generateInvoicePDFForEmail(
	organizationId: string,
	invoiceData: CreateInvoiceInput,
	creatorId?: string,
): Promise<Buffer> {
	// We set isPreview to false but pass the existing invoice number
	const preparedData = await prepareInvoiceData(
		organizationId,
		creatorId || "system",
		invoiceData,
		false,
		invoiceData.invoice_number,
	);

	// Generate the PDF with preview=false to get a Buffer
	const result = await createInvoicePDFFromPreparedData(preparedData, false);

	// Should always be a Buffer since preview=false
	if (!Buffer.isBuffer(result)) {
		throw new Error("Expected buffer result from PDF generation");
	}

	return result;
}
