import type {
	CreateExpenseInput,
	UpdateExpenseInput,
} from "@repo/api/src/routes/costs/types";
import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

export type FetchExpensesParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	categoryId?: string;
	supplierId?: string;
	// Allocation-based filters (removed tourId - not supported by backend)
	orderId?: string;
	vehicleId?: string;
	personnelId?: string;
	startDate?: Date;
	endDate?: Date;
};

export const expenseKeys = {
	all: ["expenses"] as const,
	list: (params: FetchExpensesParams) =>
		[...expenseKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...expenseKeys.all, "detail", organizationId, id] as const,
};

export const fetchExpenses = async (params: FetchExpensesParams) => {
	const response = await apiClient.expenses.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			categoryId: params.categoryId,
			supplierId: params.supplierId,
			vehicleId: params.vehicleId,
			personnelId: params.personnelId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch expenses list");
	}

	return response.json();
};

export const useExpensesQuery = (params: FetchExpensesParams) => {
	return useQuery({
		queryKey: expenseKeys.list(params),
		queryFn: () => fetchExpenses(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchExpenseById = async (organizationId: string, id: string) => {
	const response = await apiClient.expenses[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch expense details");
	}

	return response.json();
};

export const useExpenseByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: expenseKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchExpenseById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create expense mutation (using multipart/form-data like credit notes)
export const useCreateExpenseMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (
			data: CreateExpenseInput & { documentFile?: File },
		) => {
			// Handle document upload with multipart/form-data if a file is provided
			if (data.documentFile instanceof File) {
				const formData = new FormData();

				// Remove the file from the data and stringify the rest
				const { documentFile, ...restData } = data;
				formData.append("data", JSON.stringify(restData));
				formData.append("document", documentFile);
				formData.append("organizationId", organizationId);

				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				const uploadUrl = `${baseUrl}/api/expenses`;

				const response = await fetch(uploadUrl, {
					method: "POST",
					body: formData,
					credentials: "include",
				});

				if (!response.ok) {
					throw new Error("Failed to create expense with document");
				}

				return response.json();
			}

			// Regular form data request without document
			const formData = new FormData();
			formData.append("data", JSON.stringify(data));
			formData.append("organizationId", organizationId);

			const baseUrl =
				typeof window !== "undefined" ? window.location.origin : "";
			const uploadUrl = `${baseUrl}/api/expenses`;

			const response = await fetch(uploadUrl, {
				method: "POST",
				body: formData,
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to create expense");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Expense created successfully");
		},
		onError: (error) => {
			toast.error("Failed to create expense");
			console.error("Create expense error:", error);
		},
	});
};

// Update expense mutation (using multipart/form-data like credit notes)
export const useUpdateExpenseMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async ({
			id,
			data,
		}: {
			id: string;
			data: UpdateExpenseInput & {
				documentFile?: File;
				removeDocument?: boolean;
			};
		}) => {
			// Handle document upload with multipart/form-data if a file is provided
			if (data.documentFile instanceof File) {
				const formData = new FormData();

				// Remove the file from the data and stringify the rest
				const { documentFile, ...restData } = data;
				formData.append("data", JSON.stringify(restData));
				formData.append("document", documentFile);
				formData.append("organizationId", organizationId);

				const baseUrl =
					typeof window !== "undefined" ? window.location.origin : "";
				const uploadUrl = `${baseUrl}/api/expenses/${id}`;

				const response = await fetch(uploadUrl, {
					method: "PUT",
					body: formData,
					credentials: "include",
				});

				if (!response.ok) {
					throw new Error("Failed to update expense with document");
				}

				return response.json();
			}

			// Regular form data request without document
			const formData = new FormData();
			const { removeDocument, ...restData } = data;
			formData.append("data", JSON.stringify(restData));
			formData.append("organizationId", organizationId);

			if (removeDocument) {
				formData.append("removeDocument", "true");
			}

			const baseUrl =
				typeof window !== "undefined" ? window.location.origin : "";
			const uploadUrl = `${baseUrl}/api/expenses/${id}`;

			const response = await fetch(uploadUrl, {
				method: "PUT",
				body: formData,
				credentials: "include",
			});

			if (!response.ok) {
				throw new Error("Failed to update expense");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Expense updated successfully");
		},
		onError: (error) => {
			toast.error("Failed to update expense");
			console.error("Update expense error:", error);
		},
	});
};

// Delete expense mutation
export const useDeleteExpenseMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.expenses[":id"].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete expense");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Expense deleted successfully");
		},
		onError: (error) => {
			toast.error("Failed to delete expense");
			console.error("Delete expense error:", error);
		},
	});
};
