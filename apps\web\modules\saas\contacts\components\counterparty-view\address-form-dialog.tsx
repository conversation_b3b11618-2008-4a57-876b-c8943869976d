"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
	createAddressUsageSchema,
	updateAddressUsageSchema,
} from "@repo/api/src/routes/counterparties/types";
import { OperatingHoursField } from "@saas/contacts/components/counterparty-view/operating-hours-field";
import {
	useAddressById,
	useAddressMutations,
} from "@saas/contacts/hooks/use-counterparty";
import { useCounterpartyAddresses } from "@saas/contacts/hooks/use-counterparty";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { CountrySelect } from "@saas/shared/components/CountrySelect";
import {
	AddressAutocomplete,
	type AddressType,
} from "@shared/components/address/autocomplete";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { Info, Loader2, MapPin } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod";

interface AddressFormDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	counterpartyId: string;
	addressId?: string; // If provided, we're editing an existing address
	onSuccess?: () => void;
}

// Define a type for the address data from the API, extending the AddressType
type AddressData = {
	id: string;
	type: "PRIMARY" | "LOADING" | "UNLOADING";
	isDefault: boolean;
	isGlobal: boolean;
	address: {
		id: string;
		nameLine?: string;
		street: string;
		addressSupplement?: string;
		zipCode: string;
		city: string;
		country: string;
		latitude?: number;
		longitude?: number;
		isVerified?: boolean;
		mapProviderId?: string;
		mapProvider?: string;
	};
	loadingConfig?: {
		operatingHours: Record<string, string[]>;
		instructions?: string;
		slotBooking?: boolean;
	};
	unloadingConfig?: {
		operatingHours: Record<string, string[]>;
		instructions?: string;
		slotBooking?: boolean;
	};
};

export function AddressFormDialog({
	open,
	onOpenChange,
	counterpartyId,
	addressId,
	onSuccess,
}: AddressFormDialogProps) {
	const [activeTab, setActiveTab] = useState<string>("address");
	const { activeOrganization } = useActiveOrganization();

	// Get mutations for creating/updating addresses
	const {
		createAddress,
		updateAddress,
		isLoading: isMutating,
	} = useAddressMutations(counterpartyId);

	// Fetch existing address data when editing
	const { address, isLoading: isLoadingAddress } = addressId
		? useAddressById(addressId)
		: { address: null, isLoading: false };

	// Get addresses for the counterparty to check for existing PRIMARY
	const { addresses } = useCounterpartyAddresses(counterpartyId, {
		includeBlocked: true,
	});
	const hasPrimaryAddress =
		addresses?.some((addr) => addr.type === "PRIMARY") || false;

	// Determine if we're in a loading state
	const isLoading = isMutating || isLoadingAddress;

	// Create form schema based on whether we're creating or editing
	const formSchema = addressId
		? updateAddressUsageSchema
		: createAddressUsageSchema;

	// Set up form with zod resolver
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			type: "PRIMARY",
			isDefault: false,
			isGlobal: false,
			address: {
				nameLine: undefined,
				street: "",
				addressSupplement: "",
				zipCode: "",
				city: "",
				country: "DE",
				latitude: undefined,
				longitude: undefined,
				isVerified: false,
				mapProviderId: "",
				mapProvider: "",
			},
			// Add default values for loadingConfig and unloadingConfig to prevent uncontrolled inputs
			loadingConfig: {
				operatingHours: {},
				instructions: "",
				slotBooking: false,
			},
			unloadingConfig: {
				operatingHours: {},
				instructions: "",
				slotBooking: false,
			},
		},
	});

	// Watch form values to conditionally show fields
	const addressType = form.watch("type") || "PRIMARY"; // Provide default value to avoid undefined

	// Handle address autocomplete selection
	const handleAddressSelected = useCallback(
		(selectedAddress: AddressType) => {
			// Set basic address fields
			form.setValue("address.street", selectedAddress.address1);
			if (selectedAddress.address2) {
				form.setValue(
					"address.addressSupplement",
					selectedAddress.address2,
				);
			}
			form.setValue("address.city", selectedAddress.city);
			form.setValue("address.zipCode", selectedAddress.postalCode);
			form.setValue("address.country", selectedAddress.country);

			// Set geographical coordinates - ensure non-null values and proper decimal format
			if (selectedAddress.lat) {
				// Convert to string using dot as decimal separator
				const latString = selectedAddress.lat
					.toString()
					.replace(",", ".");
				form.setValue("address.latitude", Number.parseFloat(latString));
			} else {
				form.setValue("address.latitude", undefined);
			}

			if (selectedAddress.lng) {
				// Convert to string using dot as decimal separator
				const lngString = selectedAddress.lng
					.toString()
					.replace(",", ".");
				form.setValue(
					"address.longitude",
					Number.parseFloat(lngString),
				);
			} else {
				form.setValue("address.longitude", undefined);
			}

			// Set verification and provider info
			form.setValue("address.isVerified", true);
			form.setValue("address.mapProvider", "google");
			form.setValue(
				"address.mapProviderId",
				selectedAddress.placeId || "",
			);
		},
		[form],
	);

	// Handle form submission
	const onSubmit = useCallback(
		async (data: z.infer<typeof formSchema>) => {
			try {
				if (!activeOrganization?.id) {
					console.error("No active organization");
					return;
				}

				if (!counterpartyId) {
					console.error("No counterparty ID provided");
					return;
				}

				// Ensure numeric type for latitude and longitude to prevent schema validation issues
				if (data.address?.latitude) {
					data.address.latitude = Number(data.address.latitude);
				}
				if (data.address?.longitude) {
					data.address.longitude = Number(data.address.longitude);
				}

				if (addressId) {
					const updateData = {
						...data,
						id: addressId,
						counterpartyId,
						address:
							data.address && address?.address
								? {
										...data.address,
										id: address.address.id,
									}
								: undefined,
					};

					await updateAddress(updateData as any);
				} else {
					const createData = {
						...data,
						type: data.type || "PRIMARY",
						counterpartyId,
						address: data.address
							? {
									...data.address,
								}
							: undefined,
					};

					await createAddress(createData as any);
				}

				form.reset();
				onOpenChange(false);
				onSuccess?.();
			} catch (error) {
				console.error("Error saving address:", error);
			}
		},
		[
			addressId,
			createAddress,
			form,
			onOpenChange,
			onSuccess,
			updateAddress,
			address,
			activeOrganization,
			counterpartyId,
		],
	);

	// Load existing address data when editing
	useEffect(() => {
		if (address) {
			const typedAddress = address as unknown as AddressData;

			const formData: any = {
				id: addressId,
				type: typedAddress.type || "PRIMARY",
				isDefault: typedAddress.isDefault || false,
				isGlobal: typedAddress.isGlobal || false,
				address: {
					id: typedAddress.address.id,
					nameLine: typedAddress.address.nameLine || undefined,
					street: typedAddress.address.street || "",
					addressSupplement:
						typedAddress.address.addressSupplement || "",
					zipCode: typedAddress.address.zipCode || "",
					city: typedAddress.address.city || "",
					country: typedAddress.address.country || "DE",
					latitude: typedAddress.address.latitude
						? Number(typedAddress.address.latitude)
						: undefined,
					longitude: typedAddress.address.longitude
						? Number(typedAddress.address.longitude)
						: undefined,
					isVerified: typedAddress.address.isVerified || false,
					mapProviderId: typedAddress.address.mapProviderId || "",
					mapProvider: typedAddress.address.mapProvider || "",
				},
				loadingConfig: {
					operatingHours:
						typedAddress.loadingConfig?.operatingHours || {},
					instructions:
						typedAddress.loadingConfig?.instructions || "",
					slotBooking:
						typedAddress.loadingConfig?.slotBooking || false,
				},
				unloadingConfig: {
					operatingHours:
						typedAddress.unloadingConfig?.operatingHours || {},
					instructions:
						typedAddress.unloadingConfig?.instructions || "",
					slotBooking:
						typedAddress.unloadingConfig?.slotBooking || false,
				},
			};

			form.reset(formData);

			if (["LOADING", "UNLOADING"].includes(typedAddress.type)) {
				setActiveTab("config");
			}
		}
	}, [address, form, addressId]);

	// Update title based on whether we're creating or editing
	const dialogTitle = addressId ? "Edit Address" : "Add New Address";

	// Show loading state while fetching address data
	if (addressId && isLoadingAddress) {
		return (
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Loading Address</DialogTitle>
						<DialogDescription>
							Please wait while we load the address information.
						</DialogDescription>
					</DialogHeader>
					<div className="flex justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				</DialogContent>
			</Dialog>
		);
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
				<DialogHeader className="flex-shrink-0">
					<DialogTitle>{dialogTitle}</DialogTitle>
					<DialogDescription>
						{addressId
							? "Edit existing address information"
							: "Add a new address to this counterparty"}
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="flex flex-col flex-1 min-h-0"
					>
						<div className="flex-1 overflow-y-auto pr-2 space-y-6">
							<Tabs
								value={activeTab}
								onValueChange={setActiveTab}
							>
								<TabsList className="grid w-full grid-cols-2">
									<TabsTrigger value="address">
										Address
									</TabsTrigger>
									<TabsTrigger
										value="config"
										disabled={
											!["LOADING", "UNLOADING"].includes(
												addressType,
											)
										}
									>
										Configuration
									</TabsTrigger>
								</TabsList>

								<TabsContent
									value="address"
									className="space-y-4 pt-4"
								>
									{/* Address Type */}
									<FormField
										control={form.control}
										name="type"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Address Type
												</FormLabel>
												<Select
													onValueChange={
														field.onChange
													}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select type" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem
															value="PRIMARY"
															disabled={
																hasPrimaryAddress &&
																(!addressId ||
																	address?.type !==
																		"PRIMARY")
															}
														>
															Primary{" "}
															{hasPrimaryAddress &&
																(!addressId ||
																	address?.type !==
																		"PRIMARY") &&
																"(Already exists)"}
														</SelectItem>
														<SelectItem value="LOADING">
															Loading
														</SelectItem>
														<SelectItem value="UNLOADING">
															Unloading
														</SelectItem>
													</SelectContent>
												</Select>
												{hasPrimaryAddress &&
													(!addressId ||
														address?.type !==
															"PRIMARY") && (
														<div className="text-xs text-muted-foreground mt-1 p-2 bg-blue-50 rounded border border-blue-200">
															This counterparty
															already has a
															primary address. You
															can only add loading
															or unloading
															addresses.
														</div>
													)}
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Is Default */}
									<FormField
										control={form.control}
										name="isDefault"
										render={({ field }) => (
											<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
												<FormControl>
													<Checkbox
														checked={!!field.value}
														onCheckedChange={
															field.onChange
														}
													/>
												</FormControl>
												<div className="space-y-1 leading-none">
													<FormLabel>
														Default Address
													</FormLabel>
													<p className="text-sm text-muted-foreground">
														Mark this as the default
														address for this type
													</p>
												</div>
											</FormItem>
										)}
									/>

									{/* Address Autocomplete */}
									<FormItem>
										<FormLabel>Search Address</FormLabel>
										<AddressAutocomplete
											onAddressChange={
												handleAddressSelected
											}
											placeholder="Search for an address..."
										/>
										<p className="text-sm text-muted-foreground mt-1">
											Search for an address or manually
											fill the fields below
										</p>
									</FormItem>

									{/* Name Line */}
									<FormField
										control={form.control}
										name="address.nameLine"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Name Line</FormLabel>
												<FormControl>
													<Input
														placeholder="Business name, building name, etc."
														{...field}
														value={
															field.value || ""
														}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Street */}
									<FormField
										control={form.control}
										name="address.street"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Street</FormLabel>
												<FormControl>
													<Input
														placeholder="Street and number"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Address Supplement */}
									<FormField
										control={form.control}
										name="address.addressSupplement"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Address Supplement
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Floor, building, etc."
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<div className="grid grid-cols-2 gap-4">
										{/* Zip Code */}
										<FormField
											control={form.control}
											name="address.zipCode"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Zip Code
													</FormLabel>
													<FormControl>
														<Input
															placeholder="Zip code"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										{/* City */}
										<FormField
											control={form.control}
											name="address.city"
											render={({ field }) => (
												<FormItem>
													<FormLabel>City</FormLabel>
													<FormControl>
														<Input
															placeholder="City"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									{/* Country - with CountrySelect */}
									<FormField
										control={form.control}
										name="address.country"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Country</FormLabel>
												<FormControl>
													<CountrySelect
														name="address.country"
														value={field.value}
														onValueChange={(
															val,
														) => {
															field.onChange(val);
														}}
														placeholder="Select a country"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Coordinates fields */}
									<div className="grid grid-cols-2 gap-4">
										{/* Latitude */}
										<FormField
											control={form.control}
											name="address.latitude"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Latitude
													</FormLabel>
													<FormControl>
														<Input
															{...field}
															type="text"
															inputMode="decimal"
															pattern="[0-9]*[.,]?[0-9]*"
															placeholder="Enter latitude (e.g., 52.5200)"
															value={
																field.value ===
																	undefined ||
																field.value ===
																	null
																	? ""
																	: String(
																			field.value,
																		).replace(
																			",",
																			".",
																		)
															}
															onChange={(e) => {
																// Replace commas with dots for consistency
																const value =
																	e.target.value.replace(
																		",",
																		".",
																	);
																const numericValue =
																	value === ""
																		? undefined
																		: Number.parseFloat(
																				value,
																			);
																field.onChange(
																	numericValue,
																);
															}}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										{/* Longitude */}
										<FormField
											control={form.control}
											name="address.longitude"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Longitude
													</FormLabel>
													<FormControl>
														<Input
															{...field}
															type="text"
															inputMode="decimal"
															pattern="[0-9]*[.,]?[0-9]*"
															placeholder="Enter longitude (e.g., 13.4050)"
															value={
																field.value ===
																	undefined ||
																field.value ===
																	null
																	? ""
																	: String(
																			field.value,
																		).replace(
																			",",
																			".",
																		)
															}
															onChange={(e) => {
																// Replace commas with dots for consistency
																const value =
																	e.target.value.replace(
																		",",
																		".",
																	);
																const numericValue =
																	value === ""
																		? undefined
																		: Number.parseFloat(
																				value,
																			);
																field.onChange(
																	numericValue,
																);
															}}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									{/* Coordinates info message */}
									{(form.watch("address.latitude") ||
										form.watch("address.longitude")) && (
										<div className="text-xs text-muted-foreground mt-2 p-2 bg-muted/30 rounded-md border border-border/50">
											<div className="flex items-center gap-2">
												<MapPin className="h-4 w-4 text-primary/80" />
												<span className="font-medium">
													GPS Coordinates Available
												</span>
											</div>
											<p className="mt-1 ml-6">
												These coordinates will be used
												for mapping and route
												optimization.
												{form.watch(
													"address.latitude",
												) &&
													form.watch(
														"address.longitude",
													) && (
														<span className="block mt-1">
															Lat:{" "}
															{Number.parseFloat(
																String(
																	form.watch(
																		"address.latitude",
																	),
																),
															).toFixed(6)}
															, Lng:{" "}
															{Number.parseFloat(
																String(
																	form.watch(
																		"address.longitude",
																	),
																),
															).toFixed(6)}
														</span>
													)}
											</p>
										</div>
									)}

									{/* Hidden fields for map provider data */}
									<FormField
										control={form.control}
										name="address.isVerified"
										render={({ field }) => (
											<input
												type="hidden"
												{...field}
												value={
													field.value === true
														? "true"
														: "false"
												}
											/>
										)}
									/>
									<FormField
										control={form.control}
										name="address.mapProvider"
										render={({ field }) => (
											<input
												type="hidden"
												{...field}
												value={field.value || ""}
											/>
										)}
									/>
									<FormField
										control={form.control}
										name="address.mapProviderId"
										render={({ field }) => (
											<input
												type="hidden"
												{...field}
												value={field.value || ""}
											/>
										)}
									/>
								</TabsContent>

								<TabsContent
									value="config"
									className="space-y-4 pt-4"
								>
									{/* Is Global Configuration */}
									<FormField
										control={form.control}
										name="isGlobal"
										render={({ field }) => (
											<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
												<FormControl>
													<Checkbox
														checked={!!field.value}
														onCheckedChange={
															field.onChange
														}
													/>
												</FormControl>
												<div className="space-y-1 leading-none">
													<div className="flex items-center gap-1">
														<FormLabel>
															Global Configuration
														</FormLabel>
														<TooltipProvider>
															<Tooltip>
																<TooltipTrigger
																	asChild
																>
																	<Info className="h-4 w-4 text-muted-foreground" />
																</TooltipTrigger>
																<TooltipContent>
																	<p>
																		Global
																		configuration
																		can be
																		shared
																		across
																		other
																		contacts
																	</p>
																</TooltipContent>
															</Tooltip>
														</TooltipProvider>
													</div>
													<p className="text-sm text-muted-foreground">
														Share this configuration
														with other contacts
													</p>
												</div>
											</FormItem>
										)}
									/>

									{addressType === "LOADING" && (
										<>
											{/* Slot Booking Checkbox */}
											<FormField
												control={form.control}
												name="loadingConfig.slotBooking"
												render={({ field }) => (
													<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
														<FormControl>
															<Checkbox
																checked={
																	!!field.value
																}
																onCheckedChange={
																	field.onChange
																}
															/>
														</FormControl>
														<div className="space-y-1 leading-none">
															<FormLabel>
																Enable Slot
																Booking
															</FormLabel>
															<p className="text-sm text-muted-foreground">
																Slot booking is
																required for
																loading
																operations.
															</p>
														</div>
													</FormItem>
												)}
											/>

											{/* Loading Instructions */}
											<FormField
												control={form.control}
												name="loadingConfig.instructions"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Loading Instructions
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Special instructions for loading"
																className="min-h-24"
																{...field}
																value={
																	field.value ||
																	""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Operating Hours */}
											<FormField
												control={form.control}
												name="loadingConfig.operatingHours"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Operating Hours
														</FormLabel>
														<FormControl>
															<OperatingHoursField
																value={
																	field.value ||
																	{}
																}
																onChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}

									{addressType === "UNLOADING" && (
										<>
											{/* Slot Booking Checkbox */}
											<FormField
												control={form.control}
												name="unloadingConfig.slotBooking"
												render={({ field }) => (
													<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
														<FormControl>
															<Checkbox
																checked={
																	!!field.value
																}
																onCheckedChange={
																	field.onChange
																}
															/>
														</FormControl>
														<div className="space-y-1 leading-none">
															<FormLabel>
																Enable Slot
																Booking
															</FormLabel>
															<p className="text-sm text-muted-foreground">
																Slot booking is
																required for
																unloading
																operations.
															</p>
														</div>
													</FormItem>
												)}
											/>

											{/* Unloading Instructions */}
											<FormField
												control={form.control}
												name="unloadingConfig.instructions"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Unloading
															Instructions
														</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Special instructions for unloading"
																className="min-h-24"
																{...field}
																value={
																	field.value ||
																	""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Operating Hours */}
											<FormField
												control={form.control}
												name="unloadingConfig.operatingHours"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Operating Hours
														</FormLabel>
														<FormControl>
															<OperatingHoursField
																value={
																	field.value ||
																	{}
																}
																onChange={
																	field.onChange
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}
								</TabsContent>
							</Tabs>
						</div>

						<DialogFooter className="flex-shrink-0 mt-4">
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
								disabled={isLoading}
							>
								Cancel
							</Button>
							<Button
								type="submit"
								disabled={isLoading}
								onClick={() => {
									// const isValid = form.formState.isValid; // Debug log removed
									// console.log("Form submission attempt:", {
									// 	isValid,
									// 	errors: form.formState.errors,
									// 	formValues: form.getValues()
									// });
								}}
							>
								{isLoading && (
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								)}
								{addressId ? "Update" : "Create"}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
