import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useQueryClient } from "@tanstack/react-query";
import { endOfMonth, startOfMonth, subMonths } from "date-fns";
import { useMemo, useState } from "react";
import type {
	StatisticsData,
	StatisticsFilterParams,
} from "../components/statistics/utils/statistics-types";
import {
	type StatisticsQueryParams,
	fetchStatistics,
	personnelStatisticsKeys,
	usePersonnelStatistics as useApiPersonnelStatistics,
} from "../lib/api-statistics";

interface UsePersonnelStatisticsOptions {
	initialFilters?: Partial<StatisticsFilterParams>;
}

/**
 * Hook for fetching and managing statistics data
 */
export function usePersonnelStatistics({
	initialFilters,
}: UsePersonnelStatisticsOptions = {}) {
	const { activeOrganization } = useActiveOrganization();
	const queryClient = useQueryClient();

	// Default date range is last 12 months
	const defaultStartDate = startOfMonth(subMonths(new Date(), 11));
	const defaultEndDate = endOfMonth(new Date());

	// Initialize filters state
	const [filters, setFilters] = useState<StatisticsFilterParams>({
		startDate: initialFilters?.startDate ?? defaultStartDate,
		endDate: initialFilters?.endDate ?? defaultEndDate,
		departmentIds: initialFilters?.departmentIds ?? [],
		absenceTypes: initialFilters?.absenceTypes ?? [],
		personnelId: initialFilters?.personnelId,
	});

	// Fetch data using our API client
	const {
		data: apiData,
		isLoading,
		isError,
		error,
		refetch,
	} = useApiPersonnelStatistics(
		{
			organizationId: activeOrganization?.id ?? "",
			startDate: filters.startDate,
			endDate: filters.endDate,
			departmentIds:
				filters.departmentIds && filters.departmentIds.length > 0
					? filters.departmentIds
					: undefined,
			absenceTypes:
				filters.absenceTypes && filters.absenceTypes.length > 0
					? (filters.absenceTypes as AbsenceType[])
					: undefined,
			personnelId: filters.personnelId,
		},
		{
			enabled: !!activeOrganization?.id,
		},
	);

	// Methods for updating filters
	const setDateRange = (startDate: Date, endDate: Date) => {
		setFilters((prev) => ({ ...prev, startDate, endDate }));
	};

	const setDepartmentIds = (departmentIds: string[]) => {
		setFilters((prev) => ({ ...prev, departmentIds }));
	};

	const setAbsenceTypes = (absenceTypes: AbsenceType[]) => {
		setFilters((prev) => ({ ...prev, absenceTypes }));
	};

	const resetFilters = () => {
		setFilters({
			startDate: defaultStartDate,
			endDate: defaultEndDate,
			departmentIds: [],
			absenceTypes: [],
		});
	};

	// Prefetch data with updated filters
	const prefetchStatistics = (
		newFilters: Partial<StatisticsFilterParams>,
	) => {
		if (!activeOrganization?.id) {
			return;
		}

		const updatedFilters: StatisticsQueryParams = {
			organizationId: activeOrganization.id,
			startDate: newFilters.startDate || filters.startDate,
			endDate: newFilters.endDate || filters.endDate,
			departmentIds:
				newFilters.departmentIds && newFilters.departmentIds.length > 0
					? newFilters.departmentIds
					: filters.departmentIds && filters.departmentIds.length > 0
						? filters.departmentIds
						: undefined,
			absenceTypes:
				newFilters.absenceTypes && newFilters.absenceTypes.length > 0
					? (newFilters.absenceTypes as AbsenceType[])
					: filters.absenceTypes && filters.absenceTypes.length > 0
						? (filters.absenceTypes as AbsenceType[])
						: undefined,
			personnelId: newFilters.personnelId || filters.personnelId,
		};

		queryClient.prefetchQuery({
			queryKey:
				personnelStatisticsKeys.filteredStatistics(updatedFilters),
			queryFn: () => fetchStatistics(updatedFilters),
			staleTime: 1000 * 60 * 5, // 5 minutes
		});
	};

	// Process API data for UI
	const statisticsData = useMemo<StatisticsData | undefined>(() => {
		if (!apiData) {
			return undefined;
		}

		// Convert string dates from API to Date objects for frontend compatibility
		const transformedData = {
			...apiData,
			filters: {
				...apiData.filters,
				startDate: new Date(apiData.filters.startDate),
				endDate: new Date(apiData.filters.endDate),
			},
		};

		return transformedData as unknown as StatisticsData;
	}, [apiData]);

	return {
		data: statisticsData,
		filters,
		isLoading,
		isError,
		error,
		refetch,
		// Filter methods
		setDateRange,
		setDepartmentIds,
		setAbsenceTypes,
		resetFilters,
		prefetchStatistics,
	};
}
