"use client";

import { useOrdersWithUninvoicedItems } from "@saas/orders/hooks/use-orders";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";
import {
	ChevronDown,
	ChevronRight,
	Loader2,
	Plus,
	ShoppingBag,
	X,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { OrderSearchDialog } from "./order-search-dialog";

interface OrderSelectorProps {
	customerId: string;
	selectedOrderIds: string[];
	onOrderSelectionChange: (orderIds: string[]) => void;
	selectedLineItems: Record<string, boolean>;
	onLineItemSelectionChange: (
		selectedLineItems: Record<string, boolean>,
	) => void;
	orderLineItems: Record<string, any[]>;
	onOrderLineItemsChange: (orderLineItems: Record<string, any[]>) => void;
	expandedOrders: Record<string, boolean>;
	onExpandedOrdersChange: (expandedOrders: Record<string, boolean>) => void;
	onTotalsChange?: (totals: {
		gross: number;
		net: number;
		vat: number;
		rate: number;
		vatBreakdown: Array<{
			rate: number;
			net: number;
			vat: number;
		}>;
	}) => void;
	lineItemProportions: Record<string, number>;
	onLineItemProportionsChange: (proportions: Record<string, number>) => void;
	defaultVat: number;
	currentVatRate: number; // Add this to trigger VAT updates
	isEditMode?: boolean;
	editCreditNoteId?: string | null;
	editableLineItemIds?: Set<string>; // IDs of line items that should be editable in edit mode
}

export function OrderSelector({
	customerId,
	selectedOrderIds,
	onOrderSelectionChange,
	selectedLineItems,
	onLineItemSelectionChange,
	orderLineItems,
	onOrderLineItemsChange,
	expandedOrders,
	onExpandedOrdersChange,
	onTotalsChange,
	lineItemProportions,
	onLineItemProportionsChange,
	defaultVat,
	currentVatRate,
	isEditMode = false,
	editCreditNoteId,
	editableLineItemIds,
}: OrderSelectorProps) {
	// Get orders data with uninvoiced items
	const {
		data: ordersData,
		isLoading: isOrdersLoading,
		setCustomerId,
		refetch: refetchOrders,
	} = useOrdersWithUninvoicedItems(
		isEditMode && editCreditNoteId
			? { excludeCreditNoteId: editCreditNoteId }
			: undefined,
	);

	// Use a ref to track if totals are being calculated to prevent infinite loops
	const isCalculatingTotals = useRef(false);

	// State for order search dialog
	const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false);

	// Store full order data for selected orders (including manually selected ones)
	const [selectedOrdersData, setSelectedOrdersData] = useState<
		Record<string, any>
	>({});

	// Set customer ID for orders
	useEffect(() => {
		if (customerId) {
			setCustomerId(customerId);
		}
	}, [customerId, setCustomerId]);

	// Track previous VAT rate to avoid unnecessary updates
	const prevVatRateRef = useRef<number>(currentVatRate);

	// Handle VAT rate changes
	useEffect(() => {
		// Skip if no VAT rate change or no data
		if (
			prevVatRateRef.current === currentVatRate ||
			!ordersData?.items?.length ||
			Object.keys(orderLineItems).length === 0
		) {
			prevVatRateRef.current = currentVatRate;
			return;
		}

		prevVatRateRef.current = currentVatRate;

		const updatedOrderLineItems = { ...orderLineItems };
		let hasChanges = false;

		Object.keys(updatedOrderLineItems).forEach((orderId) => {
			const orderData = ordersData.items.find(
				(item: any) => item.id === orderId,
			);
			const lineItemsStatus = orderData?.lineItemsStatus || [];

			updatedOrderLineItems[orderId] = updatedOrderLineItems[orderId].map(
				(item: any) => {
					// Find the original line item data to check for any invoicing activity
					const originalLineItem = orderData?.lineItems?.find(
						(original: any) => original.id === item.id,
					);

					// Skip if there's been ANY invoicing activity at all
					if (
						(originalLineItem?.invoiceConnections?.length ?? 0) >
							0 ||
						(originalLineItem?.creditNoteConnections?.length ?? 0) >
							0
					) {
						return item; // Don't touch items that have been invoiced/credited at all
					}

					// Also check the calculated status as backup
					const itemStatus = lineItemsStatus.find(
						(status: any) => status.id === item.id,
					);

					// Skip if fully invoiced (obvious case)
					if (itemStatus?.isFullyInvoiced) {
						return item;
					}

					// Skip if there's been any invoicing activity based on calculated amounts
					if (
						(itemStatus?.invoicedAmount ?? 0) > 0 ||
						(itemStatus?.creditedAmount ?? 0) > 0
					) {
						return item;
					}

					// Only update VAT rate for completely untouched items AND if rate actually changed
					if (item.vatRate !== currentVatRate) {
						hasChanges = true;
						return { ...item, vatRate: currentVatRate };
					}

					return item; // Keep original VAT rate if no change needed
				},
			);
		});

		if (hasChanges) {
			onOrderLineItemsChange(updatedOrderLineItems);
		}
	}, [currentVatRate, ordersData, onOrderLineItemsChange]);

	// Update totals calculation{  }
	const calculateTotals = useCallback(() => {
		if (!onTotalsChange || isCalculatingTotals.current) {
			return;
		}

		isCalculatingTotals.current = true;

		try {
			if (selectedOrderIds.length === 0) {
				onTotalsChange({
					gross: 0,
					net: 0,
					vat: 0,
					rate: 0,
					vatBreakdown: [],
				});
				return;
			}

			let newTotalGross = 0;
			let newTotalNet = 0;
			let newTotalVat = 0;
			let itemsSelectedCount = 0;

			// Group by VAT rate for breakdown
			const vatGroups: Record<
				string,
				{ net: number; vat: number; rate: number }
			> = {};

			for (const orderId of selectedOrderIds) {
				const items = orderLineItems[orderId] || [];
				// First check selectedOrdersData for manually added orders, then customer orders
				const orderData =
					selectedOrdersData[orderId] ||
					ordersData?.items?.find((item) => item.id === orderId);

				for (let index = 0; index < items.length; index++) {
					const item = items[index];
					// All line items are now revenue-only (removed customer type filter)
					{
						const itemKey = `${orderId}:${item.id || index}`;
						if (selectedLineItems[itemKey]) {
							itemsSelectedCount++;
							const itemVatRate = item.vatRate || 0;

							// Find the status for this line item
							const itemStatus = orderData?.lineItemsStatus?.find(
								(status: any) => status.id === item.id,
							);

							// Use remaining amount if available, otherwise use total price
							const itemNet =
								itemStatus?.remainingAmount !== undefined &&
								itemStatus.remainingAmount < item.totalPrice
									? itemStatus.remainingAmount
									: item.totalPrice || 0;

							const itemVat = itemNet * (itemVatRate / 100);
							const itemGrossPrice = itemNet + itemVat;

							newTotalNet += itemNet;
							newTotalVat += itemVat;
							newTotalGross += itemGrossPrice;

							// Group by VAT rate
							const rateKey = itemVatRate.toString();
							if (!vatGroups[rateKey]) {
								vatGroups[rateKey] = {
									net: 0,
									vat: 0,
									rate: itemVatRate,
								};
							}
							vatGroups[rateKey].net += itemNet;
							vatGroups[rateKey].vat += itemVat;
						}
					}
				}
			}

			if (itemsSelectedCount > 0) {
				// Create VAT breakdown array
				const vatBreakdown = Object.values(vatGroups).sort(
					(a, b) => a.rate - b.rate,
				);

				// Calculate overall average rate (for backward compatibility)
				const newAvgVatRate =
					newTotalNet > 0 ? (newTotalVat / newTotalNet) * 100 : 0;

				onTotalsChange({
					gross: newTotalGross,
					net: newTotalNet,
					vat: newTotalVat,
					rate: newAvgVatRate,
					vatBreakdown: vatBreakdown,
				});
			} else {
				onTotalsChange({
					gross: 0,
					net: 0,
					vat: 0,
					rate: 0,
					vatBreakdown: [],
				});
			}
		} finally {
			isCalculatingTotals.current = false;
		}
	}, [
		onTotalsChange,
		selectedOrderIds,
		orderLineItems,
		selectedLineItems,
		ordersData,
	]);

	// Use a ref to track previous values to avoid unnecessary updates
	const prevDepsRef = useRef({
		selectedLineItems: selectedLineItems,
		selectedOrderIds: selectedOrderIds,
		orderLineItems: orderLineItems,
	});

	// useEffect for calculating totals on relevant changes
	useEffect(() => {
		// Check if the dependencies actually changed in a meaningful way
		const lineItemsChanged =
			JSON.stringify(Object.keys(selectedLineItems).sort()) !==
				JSON.stringify(
					Object.keys(prevDepsRef.current.selectedLineItems).sort(),
				) ||
			Object.keys(selectedLineItems).some(
				(key) =>
					selectedLineItems[key] !==
					prevDepsRef.current.selectedLineItems[key],
			);

		const orderIdsChanged =
			JSON.stringify(selectedOrderIds.sort()) !==
			JSON.stringify(prevDepsRef.current.selectedOrderIds.sort());

		const orderLineItemsChanged =
			JSON.stringify(orderLineItems) !==
			JSON.stringify(prevDepsRef.current.orderLineItems);

		// Only calculate totals if something actually changed
		if (lineItemsChanged || orderIdsChanged || orderLineItemsChanged) {
			calculateTotals();

			// Update the ref with current values
			prevDepsRef.current = {
				selectedLineItems: { ...selectedLineItems },
				selectedOrderIds: [...selectedOrderIds],
				orderLineItems: { ...orderLineItems },
			};
		}
	}, [selectedLineItems, selectedOrderIds, orderLineItems, calculateTotals]);

	// Handle pre-selected orders when ordersData is loaded
	useEffect(() => {
		// Only process if we have ordersData and selectedOrderIds but no orderLineItems yet
		if (
			ordersData?.items?.length &&
			selectedOrderIds.length > 0 &&
			Object.keys(orderLineItems).length === 0
		) {
			// Process each pre-selected order
			selectedOrderIds.forEach((orderId) => {
				const orderData = ordersData.items.find(
					(item: any) => item.id === orderId,
				);

				if (orderData?.lineItems && orderData.lineItems.length > 0) {
					// Simulate the selection process by calling the internal logic
					// This ensures all the state is properly set up

					// Add to line items with smart VAT rate handling
					const newOrderLineItems = { ...orderLineItems };
					newOrderLineItems[orderId] = orderData.lineItems.map(
						(item: any) => {
							// Check if this item has been invoiced/credited
							const hasInvoicingActivity =
								item.invoiceConnections?.length > 0 ||
								item.creditNoteConnections?.length > 0;

							return {
								...item,
								// Keep existing VAT rate for invoiced items, apply default for untouched items
								vatRate: hasInvoicingActivity
									? item.vatRate
									: defaultVat,
							};
						},
					);
					onOrderLineItemsChange(newOrderLineItems);

					// Expand this order by default
					const newExpandedOrders = { ...expandedOrders };
					newExpandedOrders[orderId] = true;
					onExpandedOrdersChange(newExpandedOrders);

					// Get lineItemsStatus to check which items are fully invoiced
					const lineItemsStatus = orderData.lineItemsStatus || [];

					// Pre-select all customer line items that are not fully invoiced
					const newSelectedLineItems = { ...selectedLineItems };
					const newLineItemProportions = { ...lineItemProportions };

					for (let i = 0; i < orderData.lineItems.length; i++) {
						const item = orderData.lineItems[i];
						// All line items are now revenue-only (removed customer type filter)
						{
							const itemStatus = lineItemsStatus.find(
								(status: any) => status.id === item.id,
							);
							const isFullyInvoiced =
								itemStatus?.isFullyInvoiced || false;

							if (!isFullyInvoiced) {
								const itemKey = `${orderId}:${item.id || i}`;
								newSelectedLineItems[itemKey] = true;
								newLineItemProportions[itemKey] = 100;
							}
						}
					}

					onLineItemSelectionChange(newSelectedLineItems);
					onLineItemProportionsChange(newLineItemProportions);
				}
			});
		}
	}, [ordersData, selectedOrderIds.length]); // Only depend on ordersData and length of selectedOrderIds

	// Helper function to add order data to selected orders
	const addOrderToSelected = (orderData: any) => {
		const orderId = orderData.id;

		// Store the full order data
		setSelectedOrdersData((prev) => ({
			...prev,
			[orderId]: orderData,
		}));

		// Add to selected order IDs
		const newOrderIds = [...selectedOrderIds, orderId];
		onOrderSelectionChange(newOrderIds);

		// Process line items
		if (orderData?.lineItems && orderData.lineItems.length > 0) {
			// Add to line items with smart VAT rate handling
			const newOrderLineItems = { ...orderLineItems };
			newOrderLineItems[orderId] = orderData.lineItems.map(
				(item: any) => {
					// Check if this item has been invoiced/credited
					const hasInvoicingActivity =
						item.invoiceConnections?.length > 0 ||
						item.creditNoteConnections?.length > 0;

					return {
						...item,
						// Keep existing VAT rate for invoiced items, apply default for untouched items
						vatRate: hasInvoicingActivity
							? item.vatRate
							: defaultVat,
					};
				},
			);
			onOrderLineItemsChange(newOrderLineItems);

			// Expand this order by default
			const newExpandedOrders = { ...expandedOrders };
			newExpandedOrders[orderId] = true;
			onExpandedOrdersChange(newExpandedOrders);

			// Get lineItemsStatus to check which items are fully invoiced
			const lineItemsStatus = orderData.lineItemsStatus || [];

			// Pre-select all customer line items that are not fully invoiced by default
			const newSelectedLineItems = { ...selectedLineItems };
			const newLineItemProportions = { ...lineItemProportions };

			// Use regular for loop instead of forEach to avoid any iteration issues
			for (let i = 0; i < orderData.lineItems.length; i++) {
				const item = orderData.lineItems[i];
				// All line items are now revenue-only (removed customer type filter)
				{
					// Get item status to check if it's fully invoiced
					const itemStatus = lineItemsStatus.find(
						(status: any) => status.id === item.id,
					);
					const isFullyInvoiced =
						itemStatus?.isFullyInvoiced || false;

					// Only allow selection if not fully invoiced
					if (!isFullyInvoiced) {
						const itemKey = `${orderId}:${item.id || i}`;
						newSelectedLineItems[itemKey] = true;
						// Set default proportion to 100%
						newLineItemProportions[itemKey] = 100;
					}
				}
			}
			onLineItemSelectionChange(newSelectedLineItems);
			onLineItemProportionsChange(newLineItemProportions);
		}
	};

	// Toggle an order selection (supports both ID and full order object)
	const toggleOrderSelection = async (orderIdOrData: string | any) => {
		// Handle both string ID and full order object
		const orderId =
			typeof orderIdOrData === "string"
				? orderIdOrData
				: orderIdOrData.id;
		const orderData =
			typeof orderIdOrData === "string"
				? selectedOrdersData[orderId] ||
					ordersData?.items?.find((item) => item.id === orderId)
				: orderIdOrData;
		const orderIsSelected = selectedOrderIds.includes(orderId);

		// If order is already selected, remove it
		if (orderIsSelected) {
			const newOrderIds = selectedOrderIds.filter((id) => id !== orderId);
			onOrderSelectionChange(newOrderIds);

			// Remove from selected orders data
			setSelectedOrdersData((prev) => {
				const updated = { ...prev };
				delete updated[orderId];
				return updated;
			});

			// Remove from line items
			const newOrderLineItems = { ...orderLineItems };
			delete newOrderLineItems[orderId];
			onOrderLineItemsChange(newOrderLineItems);

			// Remove from expanded state
			const newExpandedOrders = { ...expandedOrders };
			delete newExpandedOrders[orderId];
			onExpandedOrdersChange(newExpandedOrders);

			// Remove line items of this order from the selected line items
			const newSelectedLineItems = { ...selectedLineItems };
			Object.keys(newSelectedLineItems).forEach((key) => {
				if (key.startsWith(`${orderId}:`)) {
					delete newSelectedLineItems[key];
				}
			});
			onLineItemSelectionChange(newSelectedLineItems);
		}
		// If order is not selected, add it
		else {
			try {
				if (orderData) {
					addOrderToSelected(orderData);
				} else {
					console.error(`Order data not found for order ${orderId}`);
				}
			} catch (error) {
				console.error(
					`Failed to use line items for order ${orderId}:`,
					error,
				);
			}
		}
	};

	// Toggle order expansion
	const toggleOrderExpansion = (
		orderId: string,
		event?: React.MouseEvent,
	) => {
		// Prevent event propagation if coming from a click event
		if (event) {
			event.stopPropagation();
		}

		// Create a new object to ensure state update triggers
		const newExpandedOrders = { ...expandedOrders };
		newExpandedOrders[orderId] = !expandedOrders[orderId];
		onExpandedOrdersChange(newExpandedOrders);
	};

	// Toggle line item selection
	const toggleLineItemSelection = (
		orderId: string,
		lineItemId: string | number,
		index: number,
	) => {
		// Create a unique key for this line item
		const itemKey = `${orderId}:${lineItemId || index}`;

		// Get the current selected state
		const isSelected = !!selectedLineItems[itemKey];

		// Update the selected state
		const newSelectedLineItems = { ...selectedLineItems };
		newSelectedLineItems[itemKey] = !isSelected;
		onLineItemSelectionChange(newSelectedLineItems);
	};

	// Add a handler for proportion changes
	const handleProportionChange = (
		orderId: string,
		lineItemId: string | number,
		index: number,
		value: string,
	) => {
		// Parse the value as a number
		let numValue = Number.parseInt(value, 10);

		// Validate the input - keep between 0 and 100
		if (Number.isNaN(numValue) || numValue < 0) {
			numValue = 0;
		} else if (numValue > 100) {
			numValue = 100;
		}

		// Create a unique key for this line item
		const itemKey = `${orderId}:${lineItemId || index}`;

		// Update the proportions state with the new value
		const newProportions = { ...lineItemProportions };

		// Calculate current total amount with all proportions
		let currentTotalAmount = 0;
		let totalLineItems = 0;
		const affectedItems: Array<{ key: string; amount: number }> = [];

		// Create a map of selected items with their original full amounts
		for (const selectedOrderId of selectedOrderIds) {
			const items = orderLineItems[selectedOrderId] || [];
			for (let i = 0; i < items.length; i++) {
				const item = items[i];
				// All line items are now revenue-only (removed customer type filter)
				{
					const selectedItemKey = `${selectedOrderId}:${item.id || i}`;
					if (selectedLineItems[selectedItemKey]) {
						totalLineItems++;
						const itemAmount = item.totalPrice || 0;

						// Special case for the item being modified
						if (selectedItemKey === itemKey) {
							// Calculate the amount based on the new proportion
							const amount = itemAmount * (numValue / 100);
							currentTotalAmount += amount;
							affectedItems.push({
								key: selectedItemKey,
								amount: itemAmount,
							});
						} else {
							// Use existing proportion for other items
							const proportion =
								lineItemProportions[selectedItemKey] || 100;
							const amount = itemAmount * (proportion / 100);
							currentTotalAmount += amount;
							affectedItems.push({
								key: selectedItemKey,
								amount: itemAmount,
							});
						}
					}
				}
			}
		}

		// First, set the specified proportion for the changed item
		newProportions[itemKey] = numValue;

		// Calculate the proportion to distribute to other items
		const requiredAdjustment = onTotalsChange ? 1 : 0; // Only adjust if we're tracking totals

		if (requiredAdjustment && affectedItems.length > 1) {
			// Calculate remaining amount after this item's change
			const changedItemOriginalAmount =
				affectedItems.find((item) => item.key === itemKey)?.amount || 0;
			const changedItemNewAmount =
				changedItemOriginalAmount * (numValue / 100);

			// Identify other selected items (that aren't the one being changed)
			const otherItems = affectedItems.filter(
				(item) => item.key !== itemKey,
			);

			// Calculate original total of all items
			const originalTotal = affectedItems.reduce(
				(sum, item) => sum + item.amount,
				0,
			);

			// If there's a difference to distribute and other items to distribute to
			if (otherItems.length > 0) {
				// Distribute remaining proportion across other selected items
				const remainingProportion =
					100 -
					(numValue * changedItemOriginalAmount) / originalTotal;
				const proportionPerItem =
					remainingProportion / otherItems.length;

				// Update each other item's proportion
				for (const item of otherItems) {
					// Adjust proportion based on relative weight of item
					const weight = item.amount / originalTotal;
					const adjustedProportion = Math.max(
						0,
						Math.min(100, Math.round(proportionPerItem / weight)),
					);
					newProportions[item.key] = adjustedProportion;
				}
			}
		}

		onLineItemProportionsChange(newProportions);

		// Trigger a recalculation of totals
		if (onTotalsChange) {
			calculateTotals();
		}
	};

	// Render available orders section
	const renderAvailableOrders = () => {
		if (isOrdersLoading) {
			return (
				<div className="py-4 text-center">
					<Loader2 className="h-6 w-6 text-muted-foreground animate-spin mx-auto" />
					<p className="text-sm text-muted-foreground mt-2">
						Loading orders...
					</p>
				</div>
			);
		}

		if (ordersData?.items?.length) {
			return (
				<div className="border rounded-md p-3 space-y-2">
					<div className="flex items-center justify-between mb-2">
						<h4 className="text-sm font-medium">
							Available Orders
						</h4>
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() => setIsSearchDialogOpen(true)}
						>
							<Plus className="h-4 w-4 mr-1" />
							Add Order
						</Button>
					</div>
					<div className="flex flex-wrap gap-2">
						{ordersData.items
							.filter(
								(order: any) =>
									!selectedOrderIds.includes(order.id),
							)
							.map((order: any) => {
								return (
									<Button
										key={order.id}
										type="button"
										className="px-2 py-1 h-auto border rounded-md flex items-center gap-1 text-sm bg-background hover:bg-muted"
										onClick={() =>
											toggleOrderSelection(order.id)
										}
										variant="outline"
									>
										#{order.order_number || "N/A"}
									</Button>
								);
							})}
					</div>
				</div>
			);
		}

		return (
			<div className="border rounded-md p-3 space-y-2">
				<div className="flex items-center justify-between mb-2">
					<h4 className="text-sm font-medium">Available Orders</h4>
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() => setIsSearchDialogOpen(true)}
					>
						<Plus className="h-4 w-4 mr-1" />
						Add Order
					</Button>
				</div>
				<div className="text-center py-8 text-muted-foreground">
					<ShoppingBag className="mx-auto h-12 w-12 mb-4 opacity-40" />
					<p>No orders available for this customer</p>
					<p className="text-xs mt-2">
						Use the "Add Order" button to search across all
						customers
					</p>
				</div>
			</div>
		);
	};

	// Render selected orders section
	const renderSelectedOrders = () => {
		if (!selectedOrderIds.length) {
			return null;
		}

		return (
			<div className="border rounded-md p-3 h-full flex flex-col">
				<h4 className="text-sm font-medium mb-3">Selected Orders</h4>

				<ScrollArea className="flex-1">
					<div className="space-y-3 pr-2">
						{selectedOrderIds.map((orderId) => {
							// Get order from either manually selected or customer orders
							const order =
								selectedOrdersData[orderId] ||
								ordersData?.items?.find(
									(o: any) => o.id === orderId,
								);
							const hasLineItems =
								!!orderLineItems[orderId]?.length;
							const isExpanded = expandedOrders[orderId];

							return (
								<div
									key={orderId}
									className="border rounded-md overflow-hidden"
								>
									<div className="flex items-center justify-between px-3 py-2 bg-primary/10">
										<span className="font-medium text-sm">
											Order #
											{order?.order_number || "N/A"}
											{order?.customer_order_number && (
												<span className="text-sm text-muted-foreground ml-2">
													Ref:{" "}
													{
														order.customer_order_number
													}
												</span>
											)}
										</span>

										<div className="flex items-center gap-1">
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0 text-muted-foreground hover:text-destructive"
												onClick={(e) => {
													e.preventDefault();
													e.stopPropagation();
													toggleOrderSelection(
														orderId,
													);
												}}
												title="Remove order"
											>
												<X className="h-4 w-4" />
											</Button>
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0"
												onClick={(e) => {
													e.preventDefault();
													toggleOrderExpansion(
														orderId,
														e,
													);
												}}
												title={
													isExpanded
														? "Collapse"
														: "Expand"
												}
											>
												{isExpanded ? (
													<ChevronDown className="h-4 w-4" />
												) : (
													<ChevronRight className="h-4 w-4" />
												)}
											</Button>
										</div>
									</div>

									{isExpanded && hasLineItems && (
										<div className="border-t">
											<div className="text-xs font-medium bg-muted px-2 py-1.5 grid grid-cols-8 gap-1">
												<div className="flex items-center justify-center w-8">
													<span className="sr-only">
														Select
													</span>
												</div>
												<div className="col-span-2">
													Description
												</div>
												<div className="text-right">
													Quantity
												</div>
												<div className="text-right">
													Unit Price
												</div>
												<div className="text-right">
													VAT%
												</div>
												<div className="text-right">
													Total Net
												</div>
												<div className="text-right">
													Remaining
												</div>
											</div>

											{(() => {
												// Use an IIFE to maintain scope safety and avoid iteration issues
												const items =
													orderLineItems[orderId] ||
													[];
												// All line items are now revenue-only (removed customer type filter)
												const allItems = items;

												// Get the order data to access lineItemsStatus
												const orderData =
													ordersData?.items?.find(
														(item) =>
															item.id === orderId,
													);

												return allItems.length === 0 ? (
													<div className="p-3 text-center text-muted-foreground text-sm">
														No line items found for
														this order
													</div>
												) : (
													<div className="max-h-[200px] overflow-y-auto">
														{allItems.map(
															(item, idx) => {
																// Find the invoice status for this line item
																const itemStatus =
																	orderData?.lineItemsStatus?.find(
																		(
																			status,
																		) =>
																			status.id ===
																			item.id,
																	);

																// Check if this specific line item should be editable in edit mode
																const isEditableInEditMode =
																	isEditMode &&
																	editableLineItemIds?.has(
																		item.id,
																	);

																const isFullyInvoiced =
																	isEditableInEditMode
																		? false // This line item is part of the credit note being edited
																		: itemStatus?.isFullyInvoiced ||
																			false;

																return (
																	<div
																		key={
																			idx
																		}
																		className={cn(
																			"grid grid-cols-8 gap-1 text-sm py-1.5 px-2 even:bg-muted/20",
																			isFullyInvoiced &&
																				"bg-muted/40",
																		)}
																	>
																		<div className="flex items-center justify-center w-8">
																			<Checkbox
																				checked={
																					!!selectedLineItems[
																						`${orderId}:${item.id || idx}`
																					]
																				}
																				onCheckedChange={() =>
																					toggleLineItemSelection(
																						orderId,
																						item.id,
																						idx,
																					)
																				}
																				disabled={
																					isFullyInvoiced
																				}
																				aria-label="Select line item"
																				className="h-4 w-4 border border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
																			/>
																		</div>
																		<div
																			className="col-span-2 truncate"
																			title={
																				item.description ||
																				"No description"
																			}
																		>
																			{item.description ||
																				"No description"}
																			{isFullyInvoiced && (
																				<span className="ml-2 text-xs text-muted-foreground rounded-sm bg-muted px-1">
																					Fully
																					Invoiced
																				</span>
																			)}
																		</div>
																		<div className="text-right whitespace-nowrap">
																			{item.quantity?.toLocaleString(
																				"de-DE",
																			) ||
																				0}{" "}
																			{item.unit ||
																				""}
																		</div>
																		<div className="text-right whitespace-nowrap">
																			{new Intl.NumberFormat(
																				"de-DE",
																				{
																					style: "currency",
																					currency:
																						item.currency ||
																						"EUR",
																				},
																			).format(
																				item.unitPrice ||
																					0,
																			)}
																		</div>
																		<div className="text-right whitespace-nowrap">
																			{isFullyInvoiced ? (
																				<span className="text-muted-foreground">
																					{item.vatRate ||
																						0}
																					%
																				</span>
																			) : (
																				<>
																					<input
																						type="number"
																						value={
																							item.vatRate ||
																							0
																						}
																						onChange={(
																							e,
																						) => {
																							const newVatRate =
																								Number.parseFloat(
																									e
																										.target
																										.value,
																								) ||
																								0;

																							// Update the item's VAT rate in orderLineItems
																							const updatedItems =
																								[
																									...(orderLineItems[
																										orderId
																									] ||
																										[]),
																								];
																							// All line items are now revenue-only (removed customer type filter)
																							const targetItem =
																								updatedItems[
																									idx
																								];

																							if (
																								targetItem
																							) {
																								// Find the original item in the full list and update it
																								const originalIndex =
																									updatedItems.findIndex(
																										(
																											originalItem,
																										) =>
																											originalItem.id ===
																											targetItem.id,
																									);
																								if (
																									originalIndex !==
																									-1
																								) {
																									updatedItems[
																										originalIndex
																									] =
																										{
																											...updatedItems[
																												originalIndex
																											],
																											vatRate:
																												newVatRate,
																										};

																									// Update the orderLineItems state
																									onOrderLineItemsChange(
																										{
																											...orderLineItems,
																											[orderId]:
																												updatedItems,
																										},
																									);
																								}
																							}
																						}}
																						className="w-12 text-right text-xs border border-input rounded px-1 py-0.5 bg-background hover:bg-muted focus:bg-background focus:outline-none focus:ring-1 focus:ring-ring"
																						min="0"
																						max="100"
																						step="0.1"
																						disabled={
																							isFullyInvoiced
																						}
																					/>
																					%
																				</>
																			)}
																		</div>
																		<div className="text-right whitespace-nowrap font-medium">
																			{new Intl.NumberFormat(
																				"de-DE",
																				{
																					style: "currency",
																					currency:
																						item.currency ||
																						"EUR",
																				},
																			).format(
																				item.totalPrice ||
																					0,
																			)}
																		</div>
																		<div className="text-right whitespace-nowrap font-medium">
																			{new Intl.NumberFormat(
																				"de-DE",
																				{
																					style: "currency",
																					currency:
																						item.currency ||
																						"EUR",
																				},
																			).format(
																				isEditableInEditMode
																					? item.totalPrice ||
																							0 // For editable items, show full amount
																					: itemStatus?.remainingAmount ||
																							0,
																			)}
																		</div>
																	</div>
																);
															},
														)}
													</div>
												);
											})()}
										</div>
									)}
								</div>
							);
						})}
					</div>
				</ScrollArea>
			</div>
		);
	};

	return (
		<>
			<div className="space-y-4">
				{/* Available Orders */}
				{customerId && renderAvailableOrders()}

				{/* Selected Orders */}
				{customerId && selectedOrderIds.length > 0 && (
					<div className="flex-1 min-h-0">
						{renderSelectedOrders()}
					</div>
				)}
			</div>

			{/* Order Search Dialog */}
			<OrderSearchDialog
				open={isSearchDialogOpen}
				onOpenChange={setIsSearchDialogOpen}
				selectedOrderIds={selectedOrderIds}
				onOrderSelect={toggleOrderSelection}
				customerId={customerId}
			/>
		</>
	);
}
