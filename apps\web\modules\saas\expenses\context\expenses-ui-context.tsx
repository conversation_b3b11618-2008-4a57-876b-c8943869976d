"use client";

import { useExpenseMutations } from "@saas/expenses/hooks/use-expenses";

// Type inference from API client
type ExpenseResponse = Awaited<
	ReturnType<typeof import("@saas/expenses/lib/api").fetchExpenses>
>["items"][0];
import React, {
	createContext,
	useContext,
	useState,
	useRef,
	type ReactNode,
} from "react";

// Define types similar to invoice UI context
interface ExpensesUIContextValue {
	// State
	deleteExpense: ExpenseResponse | null;
	selectedExpense: ExpenseResponse | null;
	isDeleteDialogOpen: boolean;
	isViewSheetOpen: boolean;
	focusedDeleteButton: "cancel" | "confirm";
	cancelRef: React.RefObject<HTMLButtonElement | null>;
	confirmRef: React.RefObject<HTMLButtonElement | null>;
	isCreateDialogOpen: boolean;
	isEditDialogOpen: boolean;

	// Actions
	handleDeleteExpense: (expense: ExpenseResponse) => void;
	handleViewExpense: (expense: ExpenseResponse) => void;
	handleEditExpense: (expense: ExpenseResponse) => void;
	handleCancelDelete: () => void;
	handleConfirmDelete: () => Promise<void>;
	handleSetDeleteButtonFocus: (button: "cancel" | "confirm") => void;
	setSelectedExpense: (expense: ExpenseResponse | null) => void;
	closeAllDialogs: () => void;

	// Dialog management
	setDeleteDialogOpen: (open: boolean) => void;
	setViewSheetOpen: (open: boolean) => void;
	setCreateDialogOpen: (open: boolean) => void;
	setEditDialogOpen: (open: boolean) => void;
}

const ExpensesUIContext = createContext<ExpensesUIContextValue | undefined>(
	undefined,
);

export function ExpensesUIProvider({
	children,
	onExpenseDeleted,
}: {
	children: ReactNode;
	onExpenseDeleted?: () => void;
}) {
	// State management
	const [deleteExpense, setDeleteExpense] = useState<ExpenseResponse | null>(
		null,
	);
	const [selectedExpense, setSelectedExpense] =
		useState<ExpenseResponse | null>(null);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isViewSheetOpen, setIsViewSheetOpen] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [focusedDeleteButton, setFocusedDeleteButton] = useState<
		"cancel" | "confirm"
	>("cancel");

	// Refs for focus management
	const cancelRef = useRef<HTMLButtonElement | null>(null);
	const confirmRef = useRef<HTMLButtonElement | null>(null);

	// Mutations
	const { deleteExpense: deleteExpenseMutation } = useExpenseMutations({
		onSuccess: () => {
			onExpenseDeleted?.();
		},
	});

	// Action handlers
	const handleDeleteExpense = (expense: ExpenseResponse) => {
		setDeleteExpense(expense);
		setIsDeleteDialogOpen(true);
	};

	const handleViewExpense = (expense: ExpenseResponse) => {
		setSelectedExpense(expense);
		setIsViewSheetOpen(true);
	};

	const handleEditExpense = (expense: ExpenseResponse) => {
		setSelectedExpense(expense);
		setIsEditDialogOpen(true);
	};

	const handleCancelDelete = () => {
		setDeleteExpense(null);
		setIsDeleteDialogOpen(false);
	};

	const handleConfirmDelete = async () => {
		if (deleteExpense) {
			await deleteExpenseMutation(deleteExpense.id);
			setDeleteExpense(null);
			setIsDeleteDialogOpen(false);
		}
	};

	const handleSetDeleteButtonFocus = (button: "cancel" | "confirm") => {
		setFocusedDeleteButton(button);
	};

	const closeAllDialogs = () => {
		setIsDeleteDialogOpen(false);
		setIsViewSheetOpen(false);
		setIsCreateDialogOpen(false);
		setIsEditDialogOpen(false);
		setDeleteExpense(null);
		setSelectedExpense(null);
	};

	const value: ExpensesUIContextValue = {
		// State
		deleteExpense,
		selectedExpense,
		isDeleteDialogOpen,
		isViewSheetOpen,
		focusedDeleteButton,
		cancelRef,
		confirmRef,
		isCreateDialogOpen,
		isEditDialogOpen,

		// Actions
		handleDeleteExpense,
		handleViewExpense,
		handleEditExpense,
		handleCancelDelete,
		handleConfirmDelete,
		handleSetDeleteButtonFocus,
		setSelectedExpense,
		closeAllDialogs,

		// Dialog management
		setDeleteDialogOpen: setIsDeleteDialogOpen,
		setViewSheetOpen: setIsViewSheetOpen,
		setCreateDialogOpen: setIsCreateDialogOpen,
		setEditDialogOpen: setIsEditDialogOpen,
	};

	return (
		<ExpensesUIContext.Provider value={value}>
			{children}
		</ExpensesUIContext.Provider>
	);
}

export function useExpensesUI() {
	const context = useContext(ExpensesUIContext);
	if (context === undefined) {
		throw new Error(
			"useExpensesUI must be used within an ExpensesUIProvider",
		);
	}
	return context;
}
