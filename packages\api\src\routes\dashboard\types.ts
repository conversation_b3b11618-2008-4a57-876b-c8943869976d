import { z } from "zod";

// Pagination parameters schema
export const paginationParamsSchema = z.object({
	page: z.coerce.number().int().positive().optional().default(1),
	limit: z.coerce.number().int().positive().optional().default(50),
});

// Dashboard search parameters schema
export const dashboardParamsSchema = paginationParamsSchema.extend({
	organizationId: z.string(),
});

// Finance data parameters schema
export const financeDataParamsSchema = z.object({
	organizationId: z.string(),
	timeRange: z.enum(["week", "month", "year"]).optional().default("month"),
	startDate: z.coerce.date().optional(),
	endDate: z.coerce.date().optional(),
});

// Types
export type PaginationParams = z.infer<typeof paginationParamsSchema>;
export type GetVehiclesToursParams = z.infer<typeof dashboardParamsSchema>;
export type GetAvailableVehiclesParams = z.infer<typeof dashboardParamsSchema>;
export type GetAvailableOrdersParams = z.infer<typeof dashboardParamsSchema>;

// Stop information with just the essentials
export type StopInfo = {
	time_type: string | null;
	datetime_start: Date | null;
	datetime_end: Date | null;
	// Address info
	city: string | null;
	zipCode: string | null;
	street: string | null;
	country: string | null;
};

// Streamlined tour information with only first and last stops
export type SimpleTourInfo = {
	id: string;
	tourNumber: string | null;
	firstStop: StopInfo | null;
	lastStop: StopInfo | null;
};

// Work hours type
export type WorkHours = {
	start: string; // Format: "HH:MM"
	end: string; // Format: "HH:MM"
};

// Frontend Types - streamlined
export type VehicleWithTours = {
	id: string;
	licensePlate: string | null;
	// Owner info (Counterparty)
	owner: {
		id: string;
		nameLine1: string;
	} | null;
	// Work hours (hardcoded)
	workHours: WorkHours;
	tours: SimpleTourInfo[]; // Simplified to a single array for all tours
};

// Response type with pagination
export type VehiclesToursResponse = {
	items: VehicleWithTours[];
	total: number;
	page: number;
	totalPages: number;
};

// Available vehicle type for the available vehicles endpoint
export type AvailableVehicle = {
	id: string;
	licensePlate: string | null;
	// Owner info (Counterparty)
	owner: {
		id: string;
		nameLine1: string;
	} | null;
	// Last stop of the current/latest tour (can be null if no tours)
	lastStop: StopInfo | null;
};

// Response type for available vehicles with pagination
export type AvailableVehiclesResponse = {
	items: AvailableVehicle[];
	total: number;
	page: number;
	totalPages: number;
};

// Available order type for the available orders endpoint
export type AvailableOrder = {
	id: string;
	order_number: string | null;
	// Customer info (Counterparty)
	customer: {
		id: string;
		nameLine1: string | null;
	} | null;
	// All stops of the order
	stops: StopInfo[];
	// Order creation date for sorting
	createdAt: Date;
};

// Response type for available orders with pagination
export type AvailableOrdersResponse = {
	items: AvailableOrder[];
	total: number;
	page: number;
	totalPages: number;
};

// Finance Dashboard Types
export interface FinanceDataParams {
	userId: string;
	organizationId: string;
	timeRange: "week" | "month" | "year";
	startDate?: Date;
	endDate?: Date;
}

export interface RevenueDataPoint {
	date: string;
	projected: number;
	confirmed: number;
	average: number;
}

export interface RevenueResponse {
	data: RevenueDataPoint[];
	totalProjected: number;
	totalConfirmed: number;
	averageUserRevenue: number;
	activeUserCount: number;
	userPerformance: {
		totalRevenue: number;
		percentOfAverage: number;
	};
	orderMetrics: {
		totalOrders: number;
		revenuePerOrder: number;
	};
}
