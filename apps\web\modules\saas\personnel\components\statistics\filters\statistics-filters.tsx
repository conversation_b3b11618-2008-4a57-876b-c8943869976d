"use client";

import type { AbsenceType } from "@repo/api/src/routes/personnel-absence/types";
import { Button } from "@ui/components/button";
import { Calendar } from "@ui/components/calendar";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Separator } from "@ui/components/separator";
import { format, subMonths } from "date-fns";
import { CalendarIcon, Filter, X } from "lucide-react";
import { useState } from "react";
import type { DateRange } from "react-day-picker";
import type { StatisticsFilterParams } from "../utils/statistics-types";

// Predefined date range options
const DATE_RANGE_PRESETS = [
	{ label: "Last 30 days", days: 30 },
	{ label: "Last 90 days", days: 90 },
	{ label: "Last 6 months", days: 180 },
	{ label: "Last 12 months", days: 365 },
	{ label: "Year to date", value: "ytd" },
];

// Absence types with friendly names
const ABSENCE_TYPES: { value: AbsenceType; label: string }[] = [
	{ value: "HOLIDAY", label: "Holiday" },
	{ value: "SICK_LEAVE", label: "Sick Leave" },
	{ value: "PERSONAL_LEAVE", label: "Personal Leave" },
	{ value: "MATERNITY_LEAVE", label: "Maternity Leave" },
	{ value: "PATERNITY_LEAVE", label: "Paternity Leave" },
	{ value: "PARENTAL_LEAVE", label: "Parental Leave" },
	{ value: "UNPAID_LEAVE", label: "Unpaid Leave" },
	{ value: "OTHER", label: "Other" },
];

interface StatisticsFiltersProps {
	filters: StatisticsFilterParams;
	departments: Array<{ id: string; name: string }>;
	onDateRangeChange: (startDate: Date, endDate: Date) => void;
	onDepartmentIdsChange: (departmentIds: string[]) => void;
	onAbsenceTypesChange: (types: AbsenceType[]) => void;
	onReset: () => void;
}

export function StatisticsFilters({
	filters,
	departments = [],
	onDateRangeChange,
	onDepartmentIdsChange,
	onAbsenceTypesChange,
	onReset,
}: StatisticsFiltersProps) {
	// Local state to track filter dialogs
	const [datePickerOpen, setDatePickerOpen] = useState(false);
	const [departmentPickerOpen, setDepartmentPickerOpen] = useState(false);
	const [typePickerOpen, setTypePickerOpen] = useState(false);

	// Local state for date range selection using DateRange type
	const [dateRange, setDateRange] = useState<DateRange | undefined>(
		filters.startDate && filters.endDate
			? {
					from: filters.startDate,
					to: filters.endDate,
				}
			: {
					from: subMonths(new Date(), 12),
					to: new Date(),
				},
	);

	// Calculate active filter count
	const activeFilterCount =
		(filters.departmentIds?.length || 0) +
		(filters.absenceTypes?.length || 0) +
		(filters.startDate && filters.endDate ? 1 : 0);

	// Format date range for display
	const formatDateRange = (): string => {
		if (!filters.startDate || !filters.endDate) {
			return "All time";
		}
		return `${format(filters.startDate, "MMM d, yyyy")} - ${format(filters.endDate, "MMM d, yyyy")}`;
	};

	// Apply the selected date range
	const applyDateRange = () => {
		if (dateRange?.from && dateRange?.to) {
			onDateRangeChange(dateRange.from, dateRange.to);
			setDatePickerOpen(false);
		}
	};

	// Apply a preset date range
	const applyPresetRange = (days: number) => {
		if (days) {
			const end = new Date();
			const start = subMonths(end, days / 30);

			setDateRange({ from: start, to: end });
			onDateRangeChange(start, end);
			setDatePickerOpen(false);
		}
	};

	// Reset all filters
	const handleReset = () => {
		onReset();
	};

	return (
		<div className="p-4 border rounded-lg bg-card">
			<div className="flex flex-wrap items-center justify-between gap-2 mb-2">
				<h3 className="text-sm font-medium">Filters</h3>
				{activeFilterCount > 0 && (
					<Button
						variant="ghost"
						size="sm"
						onClick={handleReset}
						className="h-8 text-xs"
					>
						<X className="h-3 w-3 mr-1" /> Clear all filters
					</Button>
				)}
			</div>

			<div className="flex flex-wrap gap-2">
				{/* Date Range Filter */}
				<Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							size="sm"
							className="h-8 border-dashed"
						>
							<CalendarIcon className="h-3.5 w-3.5 mr-1.5" />
							<span>{formatDateRange()}</span>
							{filters.startDate && filters.endDate && (
								<div className="ml-1 px-1 rounded-sm flex items-center bg-secondary/50">
									<X
										className="h-3 w-3"
										onClick={(e) => {
											e.stopPropagation();
											onDateRangeChange(
												undefined as unknown as Date,
												undefined as unknown as Date,
											);
										}}
									/>
								</div>
							)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-auto p-0" align="start">
						<div className="flex">
							<div className="border-r p-2">
								<div className="px-1 py-2 font-medium text-sm">
									Presets
								</div>
								{DATE_RANGE_PRESETS.map((preset, index) => (
									<Button
										key={`preset-${preset.label}-${index}`}
										variant="ghost"
										size="sm"
										className="w-full justify-start font-normal mb-1"
										onClick={() =>
											applyPresetRange(preset.days ?? 30)
										}
									>
										{preset.label}
									</Button>
								))}
							</div>
							<div className="p-2">
								<div className="px-1 py-2 font-medium text-sm">
									Custom range
								</div>
								<Calendar
									mode="range"
									selected={dateRange}
									onSelect={setDateRange}
									initialFocus
									numberOfMonths={2}
									defaultMonth={dateRange?.from}
								/>
								<div className="flex justify-end mt-2">
									<Button size="sm" onClick={applyDateRange}>
										Apply
									</Button>
								</div>
							</div>
						</div>
					</PopoverContent>
				</Popover>

				{/* Department Filter */}
				<Popover
					open={departmentPickerOpen}
					onOpenChange={setDepartmentPickerOpen}
				>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							size="sm"
							className="h-8 border-dashed"
						>
							<Filter className="h-3.5 w-3.5 mr-1.5" />
							<span>
								{filters.departmentIds &&
								filters.departmentIds.length > 0
									? `${filters.departmentIds.length} Departments`
									: "All Departments"}
							</span>
							{filters.departmentIds &&
								filters.departmentIds.length > 0 && (
									<div className="ml-1 px-1 rounded-sm flex items-center bg-secondary/50">
										<X
											className="h-3 w-3"
											onClick={(e) => {
												e.stopPropagation();
												onDepartmentIdsChange([]);
											}}
										/>
									</div>
								)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-[200px]" align="start">
						<div className="space-y-2">
							<div className="font-medium text-sm">
								Filter by Department
							</div>
							<Separator />
							<div className="py-2">
								{departments.map((dept, index) => (
									<div
										key={`dept-${dept.id}-${index}`}
										className="flex items-center space-x-2 mb-1"
									>
										<input
											type="checkbox"
											id={`dept-${dept.id}-${index}`}
											className="rounded border-input h-4 w-4"
											checked={
												filters.departmentIds?.includes(
													dept.id,
												) || false
											}
											onChange={(e) => {
												const newDeptIds = e.target
													.checked
													? [
															...(filters.departmentIds ||
																[]),
															dept.id,
														]
													: (
															filters.departmentIds ||
															[]
														).filter(
															(id) =>
																id !== dept.id,
														);
												onDepartmentIdsChange(
													newDeptIds,
												);
											}}
										/>
										<label
											htmlFor={`dept-${dept.id}-${index}`}
											className="text-sm flex-1 cursor-pointer"
										>
											{dept.name}
										</label>
									</div>
								))}
							</div>
							<Separator />
							<div className="flex justify-between">
								<Button
									variant="ghost"
									size="sm"
									onClick={() => onDepartmentIdsChange([])}
								>
									Clear
								</Button>
								<Button
									size="sm"
									onClick={() =>
										setDepartmentPickerOpen(false)
									}
								>
									Apply
								</Button>
							</div>
						</div>
					</PopoverContent>
				</Popover>

				{/* Absence Type Filter */}
				<Popover open={typePickerOpen} onOpenChange={setTypePickerOpen}>
					<PopoverTrigger asChild>
						<Button
							variant="outline"
							size="sm"
							className="h-8 border-dashed"
						>
							<Filter className="h-3.5 w-3.5 mr-1.5" />
							<span>
								{filters.absenceTypes &&
								filters.absenceTypes.length > 0
									? `${filters.absenceTypes.length} Absence Types`
									: "All Absence Types"}
							</span>
							{filters.absenceTypes &&
								filters.absenceTypes.length > 0 && (
									<div className="ml-1 px-1 rounded-sm flex items-center bg-secondary/50">
										<X
											className="h-3 w-3"
											onClick={(e) => {
												e.stopPropagation();
												onAbsenceTypesChange([]);
											}}
										/>
									</div>
								)}
						</Button>
					</PopoverTrigger>
					<PopoverContent className="w-[200px]" align="start">
						<div className="space-y-2">
							<div className="font-medium text-sm">
								Filter by Absence Type
							</div>
							<Separator />
							<div className="max-h-[200px] overflow-auto pr-2">
								{ABSENCE_TYPES.map((type, index) => (
									<div
										key={`type-${type.value}-${index}`}
										className="flex items-center space-x-2 mb-1"
									>
										<input
											type="checkbox"
											id={`type-${type.value}-${index}`}
											className="rounded border-input h-4 w-4"
											checked={
												filters.absenceTypes?.includes(
													type.value,
												) || false
											}
											onChange={(e) => {
												const newTypes = e.target
													.checked
													? [
															...(filters.absenceTypes ||
																[]),
															type.value,
														]
													: (
															filters.absenceTypes ||
															[]
														).filter(
															(t) =>
																t !==
																type.value,
														);
												onAbsenceTypesChange(newTypes);
											}}
										/>
										<label
											htmlFor={`type-${type.value}-${index}`}
											className="text-sm flex-1 cursor-pointer"
										>
											{type.label}
										</label>
									</div>
								))}
							</div>
							<Separator />
							<div className="flex justify-between">
								<Button
									variant="ghost"
									size="sm"
									onClick={() => onAbsenceTypesChange([])}
								>
									Clear
								</Button>
								<Button
									size="sm"
									onClick={() => setTypePickerOpen(false)}
								>
									Apply
								</Button>
							</div>
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}
