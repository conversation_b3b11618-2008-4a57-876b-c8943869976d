"use client";

import {
	type InitialConfigType,
	LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import {
	type EditorState,
	ParagraphNode,
	type SerializedEditorState,
	TextNode,
} from "lexical";

import { LinkNode } from "@lexical/link";
import { AutoLinkNode } from "@lexical/link";
import { ListItemNode, ListNode } from "@lexical/list";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { Plugins } from "@ui/components/blocks/editor-comment/plugins";
import { EmojiNode } from "@ui/components/editor/nodes/emoji-node";
import { MentionNode } from "@ui/components/editor/nodes/mention-node";
import { editorTheme } from "@ui/components/editor/themes/editor-theme";
import { TooltipProvider } from "@ui/components/tooltip";

const editorConfig: InitialConfigType = {
	namespace: "CommentEditor",
	theme: editorTheme,
	nodes: [
		ListNode,
		ListItemNode,
		HeadingNode,
		ParagraphNode,
		TextNode,
		QuoteNode,
		LinkNode,
		AutoLinkNode,
		MentionNode,
		EmojiNode,
	],
	onError: (error: Error) => {
		console.error(error);
	},
};

export function CommentEditor({
	placeholder = "Add your comment...",
	editorState,
	editorSerializedState,
	onChange,
	onSerializedChange,
	onSubmit,
	submitButtonText = "Comment",
	onCancel,
	isLoading = false,
	compact = true,
	hasContent = false,
	readOnly = false,
}: {
	placeholder?: string;
	editorState?: EditorState;
	editorSerializedState?: SerializedEditorState;
	onChange?: (editorState: EditorState) => void;
	onSerializedChange?: (editorSerializedState: SerializedEditorState) => void;
	onSubmit?: () => void;
	submitButtonText?: string;
	onCancel?: () => void;
	isLoading?: boolean;
	compact?: boolean; // If true, uses minimal toolbar
	hasContent?: boolean;
	readOnly?: boolean; // If true, renders in read-only mode
}) {
	return (
		<div
			className={`overflow-hidden ${readOnly ? "" : "rounded-lg border bg-background"}`}
		>
			<LexicalComposer
				initialConfig={{
					...editorConfig,
					editable: !readOnly,
					...(editorState ? { editorState } : {}),
					...(editorSerializedState
						? { editorState: JSON.stringify(editorSerializedState) }
						: {}),
				}}
			>
				<TooltipProvider>
					<Plugins
						placeholder={placeholder}
						compact={compact}
						onSubmit={onSubmit}
						submitButtonText={submitButtonText}
						onCancel={onCancel}
						isLoading={isLoading}
						hasContent={hasContent}
						readOnly={readOnly}
					/>

					{!readOnly && (
						<OnChangePlugin
							ignoreSelectionChange={true}
							onChange={(editorState) => {
								onChange?.(editorState);
								onSerializedChange?.(editorState.toJSON());
							}}
						/>
					)}
				</TooltipProvider>
			</LexicalComposer>
		</div>
	);
}
