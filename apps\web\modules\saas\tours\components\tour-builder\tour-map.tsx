"use client";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import { MapIcon, TruckIcon } from "lucide-react";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useRef, useState } from "react";
import "mapbox-gl/dist/mapbox-gl.css";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
// Assuming a debounce utility exists or will be created e.g. in @shared/utils/debounce
// For now, let's use a simple inline debounce for demonstration if no utility is immediately available
// A proper utility is recommended for real use.

// Simple debounce utility (inline for now, move to shared utils later)
function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number) {
	let timeout: ReturnType<typeof setTimeout> | null = null;

	const debounced = (...args: Parameters<F>) => {
		if (timeout !== null) {
			clearTimeout(timeout);
			timeout = null;
		}
		timeout = setTimeout(() => func(...args), waitFor);
	};

	return debounced as (...args: Parameters<F>) => ReturnType<F>;
}

// Custom style for map popups
const popupStyles = `
.mapboxgl-popup-content {
	background-color: #fff; 
	border-radius: 6px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
	padding: 10px;
	color: #000;
}
.mapboxgl-popup-content strong {
	color: #000;
	font-weight: 600;
	display: block;
	margin-bottom: 4px;
}
.mapboxgl-popup-content p {
	color: #333;
	margin: 4px 0;
	font-size: 14px;
}`;

// Create a custom HTML marker element for vehicles with license plate - pill shape with triangle pointer
const createVehicleMarkerElement = (licensePlate: string) => {
	// Create a new div element
	const el = document.createElement("div");

	// Apply styles for overall container
	el.className = "flex flex-col items-center";
	el.style.position = "relative";

	// Create pill shape for license plate
	const pill = document.createElement("div");
	pill.className = "rounded-full bg-blue-500 shadow-md px-3 py-1 mb-1";

	// Create text element for license plate
	const text = document.createElement("div");
	text.className = "font-bold text-white text-xs whitespace-nowrap";
	text.textContent = licensePlate.substring(0, 8); // Limit to 8 chars

	// Create triangle pointer
	const triangle = document.createElement("div");
	triangle.style.width = "0";
	triangle.style.height = "0";
	triangle.style.borderLeft = "6px solid transparent";
	triangle.style.borderRight = "6px solid transparent";
	triangle.style.borderTop = "6px solid #3b82f6"; // Same blue as pill
	triangle.style.marginTop = "-1px"; // Overlap slightly with pill

	// Assemble the elements
	pill.appendChild(text);
	el.appendChild(pill);
	el.appendChild(triangle);

	return el;
};

export function TourMap() {
	const {
		stops,
		newTourStops,
		vehicleLocations,
		isLoadingVehicleLocations,
		showVehicleLocations,
		setShowVehicleLocations,
		setMapBounds, // from TourBuilderContext
	} = useTourBuilder();

	const mapContainer = useRef<HTMLDivElement>(null);
	const map = useRef<mapboxgl.Map | null>(null);
	const [mapLoaded, setMapLoaded] = useState(false);
	const [styleLoaded, setStyleLoaded] = useState(false);
	const [mapError, setMapError] = useState<string | null>(null);
	// const [routeCoordinates, setRouteCoordinates] = useState<[number, number][]>([]); // Unused, can be removed
	const [isLoadingRoute, setIsLoadingRoute] = useState(false); // Keep if used by getDirectionsRoute
	const [mapHeight, setMapHeight] = useState(300);
	const [isDragging, setIsDragging] = useState(false);
	const minHeight = 200;
	const maxHeight = 800;

	// Add refs to track auto-fit behavior
	const shouldAutoFitBounds = useRef(true);
	const previousStopsLength = useRef(0);
	const hasInitiallyFittedBounds = useRef(false);

	const noStops = stops.length === 0 && newTourStops.length === 0;
	const shouldShowVehicleLocations = noStops && showVehicleLocations;

	// Function to get bounds and update context
	const updateBoundsInContext = useCallback(() => {
		if (map.current && mapLoaded && map.current.isStyleLoaded()) {
			const bounds = map.current.getBounds();
			if (bounds) {
				const newBounds = {
					minLat: bounds.getSouth(),
					maxLat: bounds.getNorth(),
					minLng: bounds.getWest(),
					maxLng: bounds.getEast(),
				};
				console.log(
					"TourMap: Debounced update - setting map bounds in context:",
					newBounds,
					"Zoom:",
					map.current.getZoom(),
				);
				setMapBounds(newBounds);
			}
		} else {
			console.log(
				"TourMap: Debounced update - map not ready, skipping setMapBounds.",
			);
		}
	}, [mapLoaded, setMapBounds]); // map.current is stable (ref)

	// Create the debounced version of the update function
	// eslint-disable-next-line react-hooks/exhaustive-deps
	const debouncedUpdateBounds = useCallback(
		debounce(updateBoundsInContext, 1500), // Increased delay to reduce API calls
		[updateBoundsInContext], // updateBoundsInContext is stable due to its own useCallback
	);

	// Function to geocode addresses - stable with useCallback
	const geocodeAddress = useCallback(async (street: string, city: string) => {
		try {
			const query = encodeURIComponent(`${street}, ${city}`);
			const response = await fetch(
				`https://api.mapbox.com/geocoding/v5/mapbox.places/${query}.json?access_token=${process.env.NEXT_PUBLIC_MAPBOX_API_KEY}`,
			);

			if (!response.ok) {
				throw new Error("Geocoding request failed");
			}

			const data = await response.json();

			if (data.features && data.features.length > 0) {
				const [longitude, latitude] = data.features[0].center;
				return { longitude, latitude };
			}
			return null;
		} catch (error) {
			console.error("Geocoding error:", error);
			return null;
		}
	}, []);

	// Function to get route between coordinates using Mapbox Directions API - stable with useCallback
	const getDirectionsRoute = useCallback(
		async (coordinates: [number, number][]) => {
			if (coordinates.length < 2) {
				return null;
			}
			try {
				const coordString = coordinates
					.map((coord) => coord.join(","))
					.join(";");
				const url = `https://api.mapbox.com/directions/v5/mapbox/driving/${coordString}?alternatives=false&geometries=geojson&overview=full&steps=false&access_token=${process.env.NEXT_PUBLIC_MAPBOX_API_KEY}`;
				const response = await fetch(url);

				if (!response.ok) {
					const errorData = await response.text();
					console.error(
						`Directions request failed: ${response.status} ${errorData}`,
					);
					throw new Error(
						`Directions request failed: ${response.status} ${errorData}`,
					);
				}
				const data = await response.json();
				if (data.routes && data.routes.length > 0) {
					return data.routes[0].geometry;
				}
				return null;
			} catch (error) {
				console.error("Error getting directions route:", error);
				return null;
			}
		},
		[],
	);

	// Handle resize during drag
	const handleResize = useCallback(
		(e: MouseEvent) => {
			if (!isDragging) {
				return;
			}
			const container = mapContainer.current;
			if (!container) {
				return;
			}
			const rect = container.getBoundingClientRect();
			const newHeight = Math.max(
				minHeight,
				Math.min(maxHeight, e.clientY - rect.top),
			);
			setMapHeight(newHeight);
		},
		[isDragging, minHeight, maxHeight],
	);

	// Effect to handle map.resize() when mapHeight changes
	useEffect(() => {
		if (map.current && mapLoaded && map.current.isStyleLoaded()) {
			console.log("TourMap: Resizing map due to mapHeight change.");
			map.current.resize();
			// The 'resize' event on map.current will trigger debouncedUpdateBounds
		}
	}, [mapHeight, mapLoaded]); // map.current is implicitly covered by mapLoaded

	const handleResizeStart = (e: React.MouseEvent) => {
		e.preventDefault();
		setIsDragging(true);
	};

	const handleResizeEnd = useCallback(() => {
		setIsDragging(false);
		console.log("TourMap: Resize dragging ended.");
		// The map.resize() in the mapHeight effect will have caused Mapbox 'resize' event
		// which is handled by debouncedUpdateBounds. No direct call needed here.
	}, [setIsDragging]);

	// Set up global event listeners for resize (mouse)
	useEffect(() => {
		if (isDragging) {
			const currentHandleResize = (event: MouseEvent) =>
				handleResize(event);
			const currentHandleResizeEnd = () => handleResizeEnd();
			window.addEventListener("mousemove", currentHandleResize);
			window.addEventListener("mouseup", currentHandleResizeEnd);
			return () => {
				window.removeEventListener("mousemove", currentHandleResize);
				window.removeEventListener("mouseup", currentHandleResizeEnd);
			};
		}
	}, [isDragging, handleResize, handleResizeEnd]);

	// Set up global event listeners for resize (touch)
	useEffect(() => {
		const touchResize = (event: TouchEvent) => {
			if (isDragging) {
				handleResize(event.touches[0] as unknown as MouseEvent);
			}
		};
		const touchResizeEnd = () => {
			if (isDragging) {
				handleResizeEnd();
			}
		};
		if (isDragging) {
			window.addEventListener("touchmove", touchResize);
			window.addEventListener("touchend", touchResizeEnd);
		}
		return () => {
			window.removeEventListener("touchmove", touchResize);
			window.removeEventListener("touchend", touchResizeEnd);
		};
	}, [isDragging, handleResize, handleResizeEnd]);

	// Initialize the map
	useEffect(() => {
		if (!mapContainer.current || map.current) {
			return;
		}
		const token = process.env.NEXT_PUBLIC_MAPBOX_API_KEY || "";

		// Declare handler variable outside try block for cleanup access
		let debouncedHandleBoundsUpdate: ReturnType<typeof debounce> | null =
			null;

		try {
			mapboxgl.accessToken = token;
			map.current = new mapboxgl.Map({
				container: mapContainer.current,
				style: process.env.NEXT_PUBLIC_MAPBOX_STYLE_KEY,
				center: [10.5, 48.5],
				zoom: 4,
				attributionControl: false,
				logoPosition: "top-left",
			});

			map.current.addControl(
				new mapboxgl.NavigationControl(),
				"bottom-right",
			);

			const styleSheet = document.createElement("style");
			styleSheet.textContent = `
				.mapboxgl-ctrl-logo { display: none !important; }
				.mapboxgl-ctrl-attrib { display: none !important; }
				${popupStyles}
			`;
			document.head.appendChild(styleSheet);

			// Define event handlers inside the effect to avoid dependency issues
			const handleBoundsUpdate = () => {
				if (map.current?.isStyleLoaded()) {
					const bounds = map.current.getBounds();
					if (bounds) {
						const newBounds = {
							minLat: bounds.getSouth(),
							maxLat: bounds.getNorth(),
							minLng: bounds.getWest(),
							maxLng: bounds.getEast(),
						};
						console.log(
							"TourMap: Update - setting map bounds in context:",
							newBounds,
							"Zoom:",
							map.current.getZoom(),
						);
						setMapBounds(newBounds);
					}
				} else {
					console.log(
						"TourMap: Update - map not ready, skipping setMapBounds.",
					);
				}
			};

			// Create debounced version of the handler
			debouncedHandleBoundsUpdate = debounce(handleBoundsUpdate, 400);

			map.current.on("moveend", debouncedHandleBoundsUpdate);
			map.current.on("zoomend", debouncedHandleBoundsUpdate);
			map.current.on("resize", debouncedHandleBoundsUpdate);

			map.current.on("style.load", () => {
				console.log("Map style loaded");
				setStyleLoaded(true);
			});

			map.current.on("load", () => {
				console.log("Map fully loaded");
				if (!map.current || !map.current.isStyleLoaded()) {
					return;
				}
				try {
					map.current.addSource("route", {
						type: "geojson",
						data: {
							type: "Feature",
							properties: {},
							geometry: { type: "LineString", coordinates: [] },
						},
					});
					map.current.addLayer({
						id: "route-line",
						type: "line",
						source: "route",
						layout: { "line-join": "round", "line-cap": "round" },
						paint: {
							"line-color": "#10b981",
							"line-width": 4,
							"line-opacity": 0.8,
						},
					});
					setMapLoaded(true);
					// Perform initial bounds update
					handleBoundsUpdate();
				} catch (err) {
					console.error("Error setting up map sources/layers:", err);
					setMapError("Failed to setup map layers.");
				}
			});

			let styleErrorCount = 0;
			map.current.on("error", (e) => {
				console.error("Map error:", e.error);
				if (
					e.error?.message &&
					(e.error.message.includes("expression") ||
						e.error.message.includes("style")) &&
					styleErrorCount < 3
				) {
					styleErrorCount++;
					if (styleErrorCount === 3) {
						try {
							if (map.current?.isStyleLoaded()) {
								map.current.setStyle(
									"mapbox://styles/mapbox/streets-v11",
								);
							}
						} catch (styleError) {
							console.error(
								"Error switching map style:",
								styleError,
							);
						}
					}
				}
			});
		} catch (error) {
			console.error("Failed to initialize map:", error);
			setMapError("Failed to initialize map");
		}

		const mapInstance = map.current;
		return () => {
			if (mapInstance && debouncedHandleBoundsUpdate) {
				mapInstance.off("moveend", debouncedHandleBoundsUpdate);
				mapInstance.off("zoomend", debouncedHandleBoundsUpdate);
				mapInstance.off("resize", debouncedHandleBoundsUpdate);
				try {
					if (mapInstance.isStyleLoaded()) {
						if (mapInstance.getLayer("route-line")) {
							mapInstance.removeLayer("route-line");
						}
						if (mapInstance.getSource("route")) {
							mapInstance.removeSource("route");
						}
					}
				} catch (e) {
					console.warn(
						"Error removing map sources/layers during cleanup:",
						e,
					);
				}
				mapInstance.remove();
			}
			map.current = null;
			setStyleLoaded(false);
		};
	}, []); // Empty dependency array - only initialize once

	// Keep track of previous vehicle locations to avoid unnecessary redraws
	const previousVehicleLocationsRef = useRef<string>("");

	// Function to display vehicle locations on the map
	const displayVehicleLocations = useCallback(async () => {
		if (
			!map.current ||
			!mapLoaded ||
			!styleLoaded ||
			!vehicleLocations ||
			vehicleLocations.length === 0
		) {
			console.log("TourMap: displayVehicleLocations - not ready", {
				mapLoaded,
				styleLoaded,
				vehicleLocationsLength: vehicleLocations?.length || 0,
			});
			return;
		}

		// Check if vehicle locations have actually changed
		const currentVehicleLocationsString = JSON.stringify(vehicleLocations);

		// Check if we have any existing vehicle markers on the map
		const existingVehicleMarkers =
			document.querySelectorAll(".mapboxgl-marker");
		const hasExistingMarkers = existingVehicleMarkers.length > 0;

		if (
			previousVehicleLocationsRef.current ===
				currentVehicleLocationsString &&
			hasExistingMarkers
		) {
			console.log(
				"TourMap: Vehicle locations unchanged and markers exist, skipping redraw",
			);
			return;
		}
		previousVehicleLocationsRef.current = currentVehicleLocationsString;

		// Clear existing vehicle markers and routes before adding new ones
		document
			.querySelectorAll(".mapboxgl-marker")
			.forEach((markerNode) => markerNode.remove());

		// Clear existing vehicle route layers and sources
		const style = map.current.getStyle();
		if (style?.layers) {
			style.layers.forEach((layer) => {
				if (
					layer.id.startsWith("vehicle-route-") &&
					map.current?.getLayer(layer.id)
				) {
					map.current.removeLayer(layer.id);
				}
			});
		}
		if (style?.sources) {
			Object.keys(style.sources).forEach((sourceId) => {
				if (
					sourceId.startsWith("vehicle-route-") &&
					map.current?.getSource(sourceId)
				) {
					map.current.removeSource(sourceId);
				}
			});
		}

		const bounds = new mapboxgl.LngLatBounds();

		const processVehicles = async () => {
			for (let index = 0; index < vehicleLocations.length; index++) {
				const vehicle = vehicleLocations[index];
				if (vehicle.location?.coordinates && map.current) {
					const [longitude, latitude] = vehicle.location.coordinates;
					const markerElement = createVehicleMarkerElement(
						vehicle.licensePlate,
					);
					const marker = new mapboxgl.Marker({
						element: markerElement,
					})
						.setLngLat([longitude, latitude])
						.addTo(map.current);
					const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(
						`<strong>Vehicle: ${vehicle.licensePlate}</strong><p>${vehicle.location.address}</p>${
							vehicle.tourData
								? `<p>${vehicle.tourData.isCurrentTour ? "Current" : "Last"} tour: ${vehicle.tourData.tourNumber || "Unnamed tour"}</p>`
								: ""
						}`,
					);
					marker.setPopup(popup);
					bounds.extend([longitude, latitude]);

					if (
						vehicle.tourData?.stops &&
						vehicle.tourData.stops.length > 1 &&
						map.current
					) {
						const stopsWithCoordinates =
							vehicle.tourData.stops.filter(
								(stop: {
									coordinates: [number, number] | null;
								}) => stop.coordinates !== null,
							);
						if (stopsWithCoordinates.length >= 2) {
							try {
								const coordinates = stopsWithCoordinates.map(
									(stop: {
										coordinates: [number, number] | null;
									}) => stop.coordinates!,
								);
								const routeGeometry =
									await getDirectionsRoute(coordinates);
								if (
									routeGeometry?.coordinates?.length > 0 &&
									map.current
								) {
									const sourceId = `vehicle-route-${index}`;
									const layerId = `${sourceId}-layer`;
									try {
										if (map.current.getLayer(layerId)) {
											map.current.removeLayer(layerId);
										}
										if (map.current.getSource(sourceId)) {
											map.current.removeSource(sourceId);
										}
									} catch (cleanupErr) {
										console.error(
											"Cleanup error vehicle route:",
											cleanupErr,
										);
									}
									try {
										map.current.addSource(sourceId, {
											type: "geojson",
											data: {
												type: "Feature",
												properties: {},
												geometry: {
													type: "LineString",
													coordinates:
														routeGeometry.coordinates,
												},
											},
										});
										map.current.addLayer({
											id: layerId,
											type: "line",
											source: sourceId,
											paint: {
												"line-color": vehicle.tourData
													.isCurrentTour
													? "#10b981"
													: "#ef4444",
												"line-width": 6,
												"line-opacity": 1.0,
											},
										});
									} catch (err) {
										console.error(
											"Add vehicle route error:",
											err,
										);
									}
								}
							} catch (err) {
								console.error("Get vehicle route error:", err);
							}
						}
						vehicle.tourData.stops.forEach(
							(
								stop: {
									coordinates: [number, number] | null;
									address: string;
									stopType: string;
								},
								stopIndex: number,
							) => {
								if (stop.coordinates && map.current) {
									if (
										stopIndex ===
										vehicle.tourData!.stops.length - 1
									) {
										return;
									}
									let markerColor = "#6366f1";
									if (stop.stopType === "loading") {
										markerColor = "#10b981";
									} else if (stop.stopType === "unloading") {
										markerColor = "#ef4444";
									} else if (stop.stopType === "stopover") {
										markerColor = "#f59e0b";
									}
									const stopMarker = new mapboxgl.Marker({
										color: markerColor,
										scale: 0.7,
									})
										.setLngLat(stop.coordinates)
										.addTo(map.current);
									const stopPopup = new mapboxgl.Popup({
										offset: 25,
									}).setHTML(
										`<strong>${stopIndex + 1}. ${stop.stopType.charAt(0).toUpperCase() + stop.stopType.slice(1)} Stop</strong><p>${stop.address}</p>`,
									);
									stopMarker.setPopup(stopPopup);
									bounds.extend(stop.coordinates);
								}
							},
						);
					}
				}
			}
			// Only fit bounds on initial load for vehicle locations
			if (
				map.current &&
				!bounds.isEmpty() &&
				!hasInitiallyFittedBounds.current
			) {
				console.log(
					"TourMap: Initial fit bounds for vehicle locations",
				);
				map.current.fitBounds(bounds, { padding: 50, maxZoom: 15 });
				hasInitiallyFittedBounds.current = true;
			}
		};
		processVehicles().catch((err) =>
			console.error("Error processing vehicles for display:", err),
		);
	}, [vehicleLocations, mapLoaded, styleLoaded, getDirectionsRoute]);

	// Reset bounds fitting when stops change to allow initial fitting for edited tours
	useEffect(() => {
		const totalStops = stops.length + newTourStops.length;
		if (totalStops > 0 && hasInitiallyFittedBounds.current === false) {
			// Reset to allow fitting bounds when tour data is loaded
			console.log(
				"TourMap: Ready to fit bounds for tour with",
				totalStops,
				"stops",
			);
		}
	}, [stops.length, newTourStops.length]);

	// Update map with stops or vehicle locations when they change and map is loaded
	useEffect(() => {
		console.log("TourMap: Main effect running", {
			mapLoaded,
			stops: stops.length,
			newTourStops: newTourStops.length,
			shouldShowVehicleLocations,
		});

		if (!map.current || !mapLoaded || !styleLoaded) {
			console.log("TourMap: Map not ready, returning", {
				mapLoaded,
				styleLoaded,
			});
			return;
		}

		// Only clear markers and vehicle routes if we're NOT showing vehicle locations
		// This prevents flickering when moving the map while vehicle locations are displayed
		if (!shouldShowVehicleLocations) {
			// Remove existing markers
			document
				.querySelectorAll(".mapboxgl-marker")
				.forEach((markerNode) => markerNode.remove());

			// Clear existing vehicle route layers and sources
			const style = map.current.getStyle();
			if (style?.layers) {
				style.layers.forEach((layer) => {
					if (
						layer.id.startsWith("vehicle-route-") &&
						map.current?.getLayer(layer.id)
					) {
						map.current.removeLayer(layer.id);
					}
				});
			}
			if (style?.sources) {
				Object.keys(style.sources).forEach((sourceId) => {
					if (
						sourceId.startsWith("vehicle-route-") &&
						map.current?.getSource(sourceId)
					) {
						map.current.removeSource(sourceId);
					}
				});
			}

			// Reset the vehicle locations reference so they can be redrawn when toggled back on
			previousVehicleLocationsRef.current = "";
		}

		if (shouldShowVehicleLocations) {
			// Display vehicle locations without clearing existing routes
			console.log("TourMap: Showing vehicle locations, returning early");
			displayVehicleLocations();
			return;
		}

		const allStops = [...stops, ...newTourStops].sort(
			(a, b) => (a.position || 0) - (b.position || 0),
		);

		console.log("TourMap: Processing tour stops", {
			allStops: allStops.length,
			stops: stops.map((s) => ({
				id: s.id,
				position: s.position,
				street: s.street,
				city: s.city,
				longitude: s.longitude,
				latitude: s.latitude,
				hasCoordinates: !!(s.longitude && s.latitude),
				hasAddress: !!(s.street && s.city),
			})),
		});

		// Also log the raw stop data to see the full structure
		console.log("DEBUG: Raw stops data:", stops.slice(0, 2));

		if (allStops.length === 0) {
			// Clear existing tour route if no stops
			const routeSource = map.current.getSource("route") as
				| mapboxgl.GeoJSONSource
				| undefined;
			if (routeSource) {
				routeSource.setData({
					type: "Feature",
					properties: {},
					geometry: { type: "LineString", coordinates: [] },
				});
			}
			return;
		}

		const geocodePromises: Promise<{
			longitude: number;
			latitude: number;
		} | null>[] = [];
		const bounds = new mapboxgl.LngLatBounds();

		allStops.forEach((stop, index) => {
			const processStop = (
				coords: { longitude: number; latitude: number } | null,
			) => {
				if (coords && map.current) {
					// map.current checked
					let markerColor = "#6366f1";
					if (stop.stopType === "loading") {
						markerColor = "#10b981";
					} else if (stop.stopType === "unloading") {
						markerColor = "#ef4444";
					} else if (stop.stopType === "stopover") {
						markerColor = "#f59e0b";
					}

					const marker = new mapboxgl.Marker({ color: markerColor })
						.setLngLat([coords.longitude, coords.latitude])
						.addTo(map.current);
					const popup = new mapboxgl.Popup({ offset: 25 }).setHTML(
						`<strong>${index + 1}. ${stop.stopType.charAt(0).toUpperCase() + stop.stopType.slice(1)} Stop</strong>
						<p>${stop.street || ""}, ${stop.city || ""}</p>
						<p>Position: ${stop.position || index}</p>`,
					);
					marker.setPopup(popup);
					bounds.extend([coords.longitude, coords.latitude]);
				}
			};

			if (stop.longitude && stop.latitude) {
				// Use existing coordinates if available
				const coords = {
					longitude: stop.longitude,
					latitude: stop.latitude,
				};
				processStop(coords);
				geocodePromises.push(Promise.resolve(coords));
			} else if (stop.street && stop.city) {
				const geocodePromise = async () => {
					const coords = await geocodeAddress(
						stop.street!,
						stop.city!,
					);
					processStop(coords);
					return coords;
				};
				geocodePromises.push(geocodePromise());
			}
		});

		Promise.all(geocodePromises)
			.then(async (results) => {
				if (!map.current) {
					console.log("DEBUG: Map instance not available");
					return; // Guard before further map ops
				}

				const isStyleReady = map.current.isStyleLoaded();
				console.log("DEBUG: Map style check", {
					isStyleLoaded: isStyleReady,
					mapLoaded,
					styleLoaded,
				});

				// Use our styleLoaded state as backup if Mapbox's isStyleLoaded is unreliable
				if (!isStyleReady && !styleLoaded) {
					console.log(
						"DEBUG: Map style not ready for route processing",
					);
					return;
				}

				console.log("DEBUG: Geocoding results:", results);
				const validCoordinates = results
					.filter(Boolean)
					.map(
						(coords) =>
							[coords!.longitude, coords!.latitude] as [
								number,
								number,
							],
					);
				console.log(
					"DEBUG: Valid coordinates for route:",
					validCoordinates,
				);

				// setRouteCoordinates(validCoordinates); // Unused state

				if (validCoordinates.length > 1) {
					setIsLoadingRoute(true);
					console.log(
						"DEBUG: About to fetch route for coordinates:",
						validCoordinates,
					);
					try {
						const routeGeometry =
							await getDirectionsRoute(validCoordinates);
						console.log(
							"DEBUG: Route geometry received:",
							routeGeometry,
						);
						if (
							routeGeometry &&
							map.current &&
							(map.current.isStyleLoaded() || styleLoaded)
						) {
							const routeSource = map.current.getSource(
								"route",
							) as mapboxgl.GeoJSONSource | undefined;
							console.log(
								"DEBUG: Route source found:",
								!!routeSource,
							);
							if (routeSource) {
								console.log(
									"DEBUG: Setting route data on source",
								);
								routeSource.setData({
									type: "Feature",
									properties: {},
									geometry: routeGeometry,
								});
								console.log(
									"DEBUG: Route data set successfully",
								);
							}
						} else {
							console.log(
								"DEBUG: Route geometry or map not ready",
								{
									hasGeometry: !!routeGeometry,
									mapReady: !!map.current,
									styleLoaded: map.current?.isStyleLoaded(),
								},
							);
						}
					} catch (error) {
						console.error(
							"Error fetching or setting directions route:",
							error,
						);
					} finally {
						setIsLoadingRoute(false);
					}
				} else {
					console.log(
						"DEBUG: Not enough valid coordinates for route:",
						validCoordinates.length,
					);
					// Clear route if less than 2 valid coordinates
					const routeSource = map.current.getSource("route") as
						| mapboxgl.GeoJSONSource
						| undefined;
					if (routeSource) {
						routeSource.setData({
							type: "Feature",
							properties: {},
							geometry: { type: "LineString", coordinates: [] },
						});
					}
				}

				if (!bounds.isEmpty() && map.current) {
					// Fit bounds on initial load or when stops change significantly
					const currentStopsLength = allStops.length;
					const shouldFitBounds =
						currentStopsLength !== previousStopsLength.current ||
						!hasInitiallyFittedBounds.current;

					if (shouldFitBounds && currentStopsLength > 0) {
						console.log("TourMap: Fitting bounds for stops", {
							stopsLength: currentStopsLength,
							previousLength: previousStopsLength.current,
							hasInitiallyFitted:
								hasInitiallyFittedBounds.current,
						});
						map.current.fitBounds(bounds, {
							padding: 50,
							maxZoom: 15,
						});
						previousStopsLength.current = currentStopsLength;
						hasInitiallyFittedBounds.current = true;
					}
				}
			})
			.catch((error) =>
				console.error("Error processing geocoded stops:", error),
			);
	}, [
		stops,
		newTourStops,
		mapLoaded,
		styleLoaded,
		shouldShowVehicleLocations,
		// Functions are now stable with useCallback, so they won't cause unnecessary re-renders
		geocodeAddress,
		getDirectionsRoute,
	]);

	// Separate effect to handle vehicle location updates
	useEffect(() => {
		if (shouldShowVehicleLocations && mapLoaded && styleLoaded) {
			console.log(
				"TourMap: Separate vehicle effect - calling displayVehicleLocations",
			);
			displayVehicleLocations();
		}
	}, [
		vehicleLocations,
		shouldShowVehicleLocations,
		mapLoaded,
		styleLoaded,
		displayVehicleLocations,
	]);

	return (
		<div
			className="w-full mb-4 relative"
			style={{ height: `${mapHeight}px` }}
		>
			<div className="absolute top-0 left-0 z-10 p-2 bg-background/60 backdrop-blur-sm rounded-br-md flex items-center">
				<MapIcon className="h-5 w-5 mr-2" />
				<h2 className="text-lg font-medium">Tour Map</h2>
			</div>

			{/* Vehicle locations toggle */}
			{noStops && (
				<div className="absolute top-2 right-2 z-10 p-2 bg-background/80 backdrop-blur-sm rounded-md">
					<div className="flex items-center">
						<Switch
							checked={showVehicleLocations}
							onCheckedChange={setShowVehicleLocations}
							id="show-vehicles"
						/>
						<Label
							htmlFor="show-vehicles"
							className="ml-2 text-sm flex items-center"
						>
							<TruckIcon className="h-4 w-4 mr-1" />
							Show vehicle locations
						</Label>
					</div>
				</div>
			)}

			{/* Filter by map bounds toggle - Assuming useTourBuilder provides filterStopsByMap and setFilterStopsByMap */}
			<div className="absolute top-14 right-2 z-10 p-2 bg-background/80 backdrop-blur-sm rounded-md">
				<TooltipProvider>
					<Tooltip>
						<TooltipTrigger asChild>
							<div className="flex items-center">
								<Switch
									checked={useTourBuilder().filterStopsByMap}
									onCheckedChange={
										useTourBuilder().setFilterStopsByMap
									}
									id="filter-by-map"
								/>
								<Label
									htmlFor="filter-by-map"
									className="ml-2 text-sm flex items-center"
								>
									<MapIcon className="h-4 w-4 mr-1" />
									Filter by map view
								</Label>
							</div>
						</TooltipTrigger>
						<TooltipContent>
							<p className="text-xs max-w-56">
								When enabled, only stops within the current map
								view will be shown in the stops panel
							</p>
						</TooltipContent>
					</Tooltip>
				</TooltipProvider>
			</div>

			{isLoadingRoute && (
				<div className="absolute top-26 right-2 z-10 p-2 bg-background/80 backdrop-blur-sm rounded-md text-sm">
					Calculating route...
				</div>
			)}

			{/* Loading vehicle locations indicator */}
			{shouldShowVehicleLocations && isLoadingVehicleLocations && (
				<div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
					<div className="p-3 bg-background/90 backdrop-blur-sm rounded-md text-sm shadow-lg border">
						Loading vehicle locations...
					</div>
				</div>
			)}

			{mapError ? (
				<div className="h-full w-full flex flex-col items-center justify-center text-center px-4 bg-muted">
					<MapIcon
						className="h-24 w-24 mb-4 text-red-500 opacity-20"
						strokeWidth={1}
					/>
					<p className="text-lg font-medium text-red-500">
						Could not load map
					</p>
					<p className="text-sm text-muted-foreground">
						Please check your Mapbox API key or network connection.
					</p>
				</div>
			) : (
				<div ref={mapContainer} className="w-full h-full" />
			)}

			{/* Resize handle */}
			<div
				className="absolute bottom-0 left-0 right-0 flex justify-center items-center z-10"
				onMouseDown={handleResizeStart}
				onTouchStart={(e) => {
					e.preventDefault(); // Prevent page scroll
					setIsDragging(true);
				}}
			>
				<div className="w-10 h-5 flex flex-col justify-center items-center bg-background rounded-t-md shadow-md cursor-ns-resize hover:bg-gray-100 transition-colors">
					<div className="w-4 h-0.5 bg-gray-400 rounded-full mb-1" />
					<div className="w-4 h-0.5 bg-gray-400 rounded-full" />
				</div>
			</div>
		</div>
	);
}
