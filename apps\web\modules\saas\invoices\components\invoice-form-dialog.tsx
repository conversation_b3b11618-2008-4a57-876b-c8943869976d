"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import type { LineItemFormValues } from "@repo/api/src/routes/line-items/types";
import { useInvoiceMutations } from "@saas/invoices/hooks/use-invoice";
import { ContactSelector } from "@saas/organizations/components/ContactSelector";
import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	usePaymentTerms,
	useVatTerms,
} from "@saas/organizations/hooks/use-config";
import {
	type LineItem,
	LineItemsTable,
} from "@saas/shared/components/line-items/line-items-table";
import { Button } from "@ui/components/button";
import { CurrencySelect } from "@ui/components/currency-select";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { InputWithDate } from "@ui/components/input-with-date";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import { Loader2, PlusCircle, ShoppingBag } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { OrderSelector } from "./order-selector";

const formSchema = z.object({
	customerId: z.string().min(1, { message: "Customer is required" }),
	contactId: z.string().optional(),
	billingCounterpartyId: z.string().optional(),
	invoiceDate: z.date(),
	dueDate: z.date(),
	mode: z.enum(["manual", "order"]).default("manual"),
	orderIds: z.array(z.string()).optional(),
	reference: z.string().optional(),
	vatTermId: z.string().optional(),
	paymentTermId: z.string().optional(),
	comment: z.string().optional(),
	internalComment: z.string().optional(),
	currency: z.string().default("EUR"),
});

type FormValues = z.infer<typeof formSchema>;

export interface InvoiceFormDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSuccess?: () => void;
	initialCustomerId?: string;
	defaultMode?: "manual" | "order";
	orderIds?: string[];
}

export function InvoiceFormDialog({
	open,
	onOpenChange,
	onSuccess,
	initialCustomerId,
	defaultMode = "manual",
	orderIds = [],
}: InvoiceFormDialogProps) {
	const t = useTranslations();
	const { activeOrganization } = useActiveOrganization();
	const {
		createInvoice,
		generateInvoicePreview,
		isLoading,
		isGeneratingPreview,
	} = useInvoiceMutations({ onSuccess });
	const [mode, setMode] = useState<"manual" | "order">(defaultMode);
	const [lineItems, setLineItems] = useState<LineItem[]>([]);
	const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
	const [selectedContact, setSelectedContact] = useState<any>(null);
	const [defaultVatRate, setDefaultVatRate] = useState<number>(0);

	// Billing counterparty state management
	const [selectedBillingCounterparty, setSelectedBillingCounterparty] =
		useState<any>(null);

	const { data: vatTerms, isLoading: isLoadingVatTerms } = useVatTerms();
	const { data: paymentTerms, isLoading: isLoadingPaymentTerms } =
		usePaymentTerms();

	// Add states for invoice preview
	const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
	const [invoicePreviewPdfData, setInvoicePreviewPdfData] = useState<
		string | undefined
	>(undefined);
	const [submittedValues, setSubmittedValues] = useState<FormValues | null>(
		null,
	);

	// States for the OrderSelector component
	const [selectedLineItems, setSelectedLineItems] = useState<
		Record<string, boolean>
	>({});
	const [orderLineItems, setOrderLineItems] = useState<Record<string, any[]>>(
		{},
	);
	const [expandedOrders, setExpandedOrders] = useState<
		Record<string, boolean>
	>({});
	const [lineItemProportions, setLineItemProportions] = useState<
		Record<string, number>
	>({});
	const [calculatedTotals, setCalculatedTotals] = useState({
		gross: 0,
		net: 0,
		vat: 0,
		rate: 0,
		vatBreakdown: [] as Array<{
			rate: number;
			net: number;
			vat: number;
		}>,
	});

	// Add loading state tracking
	const [isCustomerLoaded, setIsCustomerLoaded] = useState(false);
	const [isOrderDataLoaded, setIsOrderDataLoaded] = useState(false);
	const [shouldPreselect, setShouldPreselect] = useState(false);
	const [hasPreselectCompleted, setHasPreselectCompleted] = useState(false);

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			customerId: initialCustomerId || "",
			invoiceDate: new Date(),
			dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default due date is 30 days from now
			vatTermId: "none",
			paymentTermId: "",
			currency: "EUR",
			contactId: "",
			billingCounterpartyId: "",
			mode: defaultMode,
			orderIds: orderIds,
		},
	});

	// Handle customer selection and update VAT/payment terms
	const handleSelectedCustomerChange = (customer: any) => {
		setSelectedCustomer(customer);
		// Mark customer as loaded when we get customer data
		setIsCustomerLoaded(!!customer);

		// Reset contact when customer changes
		form.setValue("contactId", "");
		setSelectedContact(null);

		// Reset billing counterparty when customer changes
		setSelectedBillingCounterparty(null);
		form.setValue("billingCounterpartyId", "");

		// Only reset orderIds when customer actually changes, not during initial load
		// Check if this is a real customer change vs initial loading
		const currentCustomerId = form.getValues("customerId");
		const isInitialLoad =
			!selectedCustomer && customer && currentCustomerId === customer.id;

		if (mode === "order" && !isInitialLoad) {
			form.setValue("orderIds", []);
		}

		// If customer is null (cleared), reset all form fields to defaults
		if (!customer) {
			form.setValue("vatTermId", "none");
			form.setValue("paymentTermId", "");
			form.setValue("currency", "EUR");
			setDefaultVatRate(0);
			// VAT rate will be automatically updated via currentVatRate prop in OrderSelector
			setIsCustomerLoaded(false);
			return;
		}

		let newVatRate = 0;

		if (customer?.financialProfile) {
			// If customer has a financial profile with VAT term, set it
			if (customer.financialProfile?.vatTerm) {
				form.setValue(
					"vatTermId",
					customer.financialProfile?.vatTerm?.id,
				);
				// Set the default VAT rate from the customer's VAT term
				newVatRate = Number.parseFloat(
					customer.financialProfile.vatTerm.rate || "0",
				);
				setDefaultVatRate(newVatRate);
			} else if (vatTerms?.items?.length) {
				// Otherwise select the default VAT term from the list
				const defaultVatTerm = vatTerms.items.find(
					(term) => term.isDefault,
				);
				if (defaultVatTerm) {
					form.setValue("vatTermId", defaultVatTerm.id);
					newVatRate = Number.parseFloat(defaultVatTerm.rate || "0");
					setDefaultVatRate(newVatRate);
				} else {
					form.setValue("vatTermId", "none");
					setDefaultVatRate(0);
				}
			} else {
				form.setValue("vatTermId", "none");
				setDefaultVatRate(0);
			}

			// If customer has a financial profile with payment term, set it
			if (customer.financialProfile.paymentTerm) {
				form.setValue(
					"paymentTermId",
					customer.financialProfile.paymentTerm.id,
				);

				// Update due date based on the payment term
				const daysToPayment =
					customer.financialProfile.paymentTerm.daysToPayment || 30;
				const invoiceDate = form.getValues("invoiceDate");
				if (invoiceDate) {
					const dueDate = new Date(invoiceDate);
					dueDate.setDate(dueDate.getDate() + daysToPayment);
					form.setValue("dueDate", dueDate);
				}
			} else if (paymentTerms?.items?.length) {
				// Otherwise select the default payment term from the list
				const defaultPaymentTerm = paymentTerms.items.find(
					(term) => term.isDefault,
				);
				if (defaultPaymentTerm) {
					form.setValue("paymentTermId", defaultPaymentTerm.id);
				}
			}

			// Set currency if available
			if (customer.financialProfile.currency) {
				form.setValue("currency", customer.financialProfile.currency);
			}
		} else {
			form.setValue("vatTermId", "none");
			form.setValue("paymentTermId", "");
			setDefaultVatRate(0);
		}

		// VAT rate will be automatically updated via currentVatRate prop in OrderSelector
	};

	// Handle billing counterparty selection - memoized to prevent stale closures
	const handleBillingCounterpartySelect = useCallback(
		(
			counterparty: any,
			addresses?: {
				primary: any[];
			},
		) => {
			// If counterparty is null/cleared, revert to customer's payment terms
			if (!counterparty) {
				setSelectedBillingCounterparty(null);

				// Revert to customer's payment terms if available
				if (selectedCustomer?.financialProfile?.paymentTerm) {
					form.setValue(
						"paymentTermId",
						selectedCustomer.financialProfile.paymentTerm.id,
					);

					// Update due date based on customer's payment term
					const daysToPayment =
						selectedCustomer.financialProfile.paymentTerm
							.daysToPayment || 30;
					const invoiceDate = form.getValues("invoiceDate");
					if (invoiceDate) {
						const dueDate = new Date(invoiceDate);
						dueDate.setDate(dueDate.getDate() + daysToPayment);
						form.setValue("dueDate", dueDate);
					}
				} else if (paymentTerms?.items?.length) {
					// Fall back to default payment term
					const defaultPaymentTerm = paymentTerms.items.find(
						(term) => term.isDefault,
					);
					if (defaultPaymentTerm) {
						form.setValue("paymentTermId", defaultPaymentTerm.id);
					} else {
						form.setValue("paymentTermId", "");
					}
				}

				// Revert VAT terms to customer's
				let revertedVatRate = 0;
				if (selectedCustomer?.financialProfile?.vatTerm) {
					form.setValue(
						"vatTermId",
						selectedCustomer.financialProfile.vatTerm.id,
					);
					revertedVatRate = Number.parseFloat(
						selectedCustomer.financialProfile.vatTerm.rate || "0",
					);
					setDefaultVatRate(revertedVatRate);
				} else if (vatTerms?.items?.length) {
					const defaultVatTerm = vatTerms.items.find(
						(term) => term.isDefault,
					);
					if (defaultVatTerm) {
						form.setValue("vatTermId", defaultVatTerm.id);
						revertedVatRate = Number.parseFloat(
							defaultVatTerm.rate || "0",
						);
						setDefaultVatRate(revertedVatRate);
					} else {
						form.setValue("vatTermId", "none");
						setDefaultVatRate(0);
					}
				}

				// VAT rate will be automatically updated via currentVatRate prop in OrderSelector

				// Revert currency to customer's
				if (selectedCustomer?.financialProfile?.currency) {
					form.setValue(
						"currency",
						selectedCustomer.financialProfile.currency,
					);
				} else {
					form.setValue("currency", "EUR");
				}

				return;
			}

			// Store the selected billing counterparty data for later use
			setSelectedBillingCounterparty({
				...counterparty,
				addresses,
			});

			// Update payment terms based on billing counterparty (priority over customer)
			if (counterparty?.financialProfile?.paymentTerm) {
				form.setValue(
					"paymentTermId",
					counterparty.financialProfile.paymentTerm.id,
				);

				// Update due date based on the billing counterparty's payment term
				const daysToPayment =
					counterparty.financialProfile.paymentTerm.daysToPayment ||
					30;
				const invoiceDate = form.getValues("invoiceDate");
				if (invoiceDate) {
					const dueDate = new Date(invoiceDate);
					dueDate.setDate(dueDate.getDate() + daysToPayment);
					form.setValue("dueDate", dueDate);
				}
			} else if (paymentTerms?.items?.length) {
				// If billing counterparty has no payment term, fall back to default or keep customer's
				// Only reset if no customer payment term was already set
				const currentPaymentTermId = form.getValues("paymentTermId");
				if (!currentPaymentTermId) {
					const defaultPaymentTerm = paymentTerms.items.find(
						(term) => term.isDefault,
					);
					if (defaultPaymentTerm) {
						form.setValue("paymentTermId", defaultPaymentTerm.id);
					}
				}
			}

			// Update VAT terms based on billing counterparty (priority over customer)
			if (counterparty?.financialProfile?.vatTerm) {
				form.setValue(
					"vatTermId",
					counterparty.financialProfile.vatTerm.id,
				);
				// Set the default VAT rate from the billing counterparty's VAT term
				const vatRate = Number.parseFloat(
					counterparty.financialProfile.vatTerm.rate || "0",
				);
				setDefaultVatRate(vatRate);
				// VAT rate will be automatically updated via currentVatRate prop in OrderSelector
			}

			// Update currency based on billing counterparty (priority over customer)
			if (counterparty?.financialProfile?.currency) {
				form.setValue(
					"currency",
					counterparty.financialProfile.currency,
				);
			}
		},
		[form, selectedCustomer, paymentTerms, vatTerms, setDefaultVatRate],
	);

	// Initialize orderLineItems and selectedLineItems with any pre-selected orderIds
	useEffect(() => {
		if (orderIds.length > 0 && open) {
			// Reset order data loaded state
			setIsOrderDataLoaded(false);

			// Don't fetch order details here - let OrderSelector handle it
			// Just mark that we have the order IDs ready
			setIsOrderDataLoaded(true);
		}
	}, [open, orderIds]);

	// Get current VAT rate from selected VAT term
	const currentVatRate = useMemo(() => {
		const currentVatTermId = form.getValues("vatTermId");
		if (currentVatTermId !== "none") {
			const selectedVatTerm = vatTerms?.items?.find(
				(term) => term.id === currentVatTermId,
			);
			return Number.parseFloat(selectedVatTerm?.rate || "0");
		}
		return defaultVatRate; // Use default rate from customer if no term selected
	}, [form.watch("vatTermId"), vatTerms?.items, defaultVatRate]);

	// Calculate due date based on payment term and invoice date
	useEffect(() => {
		const paymentTermId = form.watch("paymentTermId");
		const invoiceDate = form.watch("invoiceDate");

		if (paymentTermId && invoiceDate) {
			const paymentTerm = paymentTerms?.items?.find(
				(term) => term.id === paymentTermId,
			);
			if (paymentTerm) {
				const dueDate = new Date(invoiceDate);
				dueDate.setDate(dueDate.getDate() + paymentTerm.daysToPayment);
				form.setValue("dueDate", dueDate);
			}
		}
	}, [
		form.watch("paymentTermId"),
		form.watch("invoiceDate"),
		paymentTerms?.items,
	]);

	// Calculate totals
	const { subtotal, vatAmount, total, vatRate } = useMemo(() => {
		// For "order" mode, use the calculated totals from OrderSelector
		if (mode === "order") {
			return {
				subtotal: calculatedTotals.net,
				vatAmount: calculatedTotals.vat,
				total: calculatedTotals.gross,
				vatRate: calculatedTotals.rate,
			};
		}

		// For "manual" mode, calculate from lineItems as before
		const subtotalValue = lineItems.reduce(
			(sum, item) => sum + (item.totalPrice || 0),
			0,
		);

		// Calculate VAT amount and total
		const vatAmountValue = (subtotalValue * currentVatRate) / 100;
		const totalValue = subtotalValue + vatAmountValue;

		return {
			subtotal: subtotalValue,
			vatAmount: vatAmountValue,
			total: totalValue,
			vatRate: currentVatRate,
		};
	}, [lineItems, currentVatRate, mode, calculatedTotals]);

	// Group line items by VAT rate to calculate VAT amounts per rate
	const vatBreakdown = useMemo(() => {
		const breakdown: Record<
			string,
			{ rate: number; amount: number; subtotal: number }
		> = {};

		lineItems.forEach((item) => {
			// Use item's individual vatRate if available, otherwise use form's current rate
			const itemVatRate =
				item.vatRate !== null ? item.vatRate : currentVatRate;
			const rateKey = itemVatRate.toString();

			if (!breakdown[rateKey]) {
				breakdown[rateKey] = {
					rate: itemVatRate || 0,
					amount: 0,
					subtotal: 0,
				};
			}

			breakdown[rateKey].subtotal += item.totalPrice || 0;
			breakdown[rateKey].amount =
				breakdown[rateKey].subtotal * ((itemVatRate || 0) / 100);
		});

		return Object.values(breakdown).sort((a, b) => a.rate - b.rate);
	}, [lineItems, currentVatRate]);

	const onSubmit = async (values: FormValues) => {
		if (values.mode === "manual" && lineItems.length === 0) {
			form.setError("root", {
				type: "manual",
				message: "At least one line item is required",
			});
			return;
		}

		if (
			values.mode === "order" &&
			(!values.orderIds || values.orderIds.length === 0)
		) {
			form.setError("orderIds", {
				type: "manual",
				message: "At least one order must be selected",
			});
			return;
		}

		// Additional validation for line items in order mode
		if (values.mode === "order") {
			// Check if any line items are selected
			const hasSelectedLineItems = Object.values(selectedLineItems).some(
				(isSelected) => isSelected === true,
			);

			if (!hasSelectedLineItems) {
				form.setError("orderIds", {
					type: "manual",
					message: "At least one line item must be selected",
				});
				return;
			}
		}

		try {
			// Store the submitted values for later use
			setSubmittedValues(values);

			// Convert the date objects to ISO strings to avoid timezone issues
			const invoiceDate = values.invoiceDate
				? values.invoiceDate.toISOString()
				: undefined;
			const dueDate = values.dueDate
				? values.dueDate.toISOString()
				: undefined;

			// Calculate VAT amount for the API call
			const vatAmount = subtotal * (vatRate / 100);

			// Format contact information if a contact is selected
			let contactName = "";
			let contactEmail = "";

			if (selectedContact) {
				// Format contact name with salutation if available
				const salutationText = selectedContact.salutation
					? `${selectedContact.salutation}. `
					: "";
				contactName = `${salutationText}${selectedContact.firstName} ${selectedContact.lastName}`;
				contactEmail = selectedContact.email || "";
			}

			// Common payload for both modes
			const payload: any = {
				organizationId: activeOrganization?.id,
				customerId: values.customerId,
				contactId: values.contactId || undefined,
				billingCounterpartyId:
					values.billingCounterpartyId || undefined,
				contact_name: contactName || undefined,
				contact_email: contactEmail || undefined,
				invoice_date: invoiceDate,
				due_date: dueDate,
				comment: values.comment || undefined,
				internal_comment: values.internalComment || undefined,
			};

			// Add mode-specific fields
			if (values.mode === "manual") {
				// Manual invoice specific fields
				payload.tax_rate = vatRate;
				payload.tax_amount = vatAmount;
				payload.total_amount = subtotal + vatAmount;
				payload.vatTermId =
					values.vatTermId !== "none" ? values.vatTermId : undefined;
				payload.paymentTermId = values.paymentTermId || undefined;
				payload.currency = values.currency;
				payload.subtotal = subtotal;
				payload.lineItems = lineItems.map((item) => ({
					description: item.description,
					// Removed type field - all line items are now revenue-only
					quantity: Number(item.quantity),
					unit: item.unit,
					unitPrice: Number(item.unitPrice),
					totalPrice: Number(item.totalPrice),
					currency: values.currency,
					notes: item.notes,
					vatRate: item.vatRate ?? vatRate,
				}));
			} else {
				// Order-based invoice specific fields using order_allocations based on selected line items
				const orderAllocations = (values.orderIds || []).map(
					(orderId) => {
						// Get the line items for this order
						const orderItems = orderLineItems[orderId] || [];

						// Create the line items array for this order
						const lineItems = orderItems
							.filter((item, index) => {
								// Only include selected line items (all line items are now revenue-only)
								const itemKey = `${orderId}:${item.id || index}`;
								return selectedLineItems[itemKey];
							})
							.map((item, index) => {
								// Get the item key for finding proportion
								const itemKey = `${orderId}:${item.id || index}`;
								// Calculate proportion (default to 100% if not specified)
								const proportion =
									lineItemProportions[itemKey] !== undefined
										? lineItemProportions[itemKey] / 100
										: 1;

								// Apply proportion to quantity and price
								return {
									orderLineItemId: item.id,
									description: item.description,
									quantity: item.quantity * proportion,
									unit: item.unit,
									unitPrice: item.unitPrice,
									totalPrice: item.totalPrice * proportion,
									vatRate: item.vatRate,
									currency: item.currency || values.currency,
								};
							});

						return {
							orderId,
							lineItems,
						};
					},
				);

				// Add the order_allocations to the payload
				payload.order_allocations = orderAllocations;
			}

			// Generate invoice preview instead of creating immediately
			const previewResult = await generateInvoicePreview(payload);

			// Store the PDF preview data and open the preview dialog
			if (previewResult.pdfBase64) {
				setInvoicePreviewPdfData(previewResult.pdfBase64);
				setIsPreviewDialogOpen(true);
			}
		} catch (error) {
			console.error("Failed to generate invoice preview:", error);
		}
	};

	// Handle final creation after preview confirmation
	const handleCreateInvoice = async () => {
		if (!submittedValues) {
			return;
		}

		try {
			// Convert the date objects to ISO strings to avoid timezone issues
			const invoiceDate = submittedValues.invoiceDate
				? submittedValues.invoiceDate.toISOString()
				: undefined;
			const dueDate = submittedValues.dueDate
				? submittedValues.dueDate.toISOString()
				: undefined;

			// Calculate VAT amount for the API call
			const vatAmount = subtotal * (vatRate / 100);

			// Format contact information if a contact is selected
			let contactName = "";
			let contactEmail = "";

			if (selectedContact) {
				// Format contact name with salutation if available
				const salutationText = selectedContact.salutation
					? `${selectedContact.salutation}. `
					: "";
				contactName = `${salutationText}${selectedContact.firstName} ${selectedContact.lastName}`;
				contactEmail = selectedContact.email || "";
			}

			// Common payload for both modes
			const payload: any = {
				organizationId: activeOrganization?.id,
				customerId: submittedValues.customerId,
				contactId: submittedValues.contactId || undefined,
				billingCounterpartyId:
					submittedValues.billingCounterpartyId || undefined,
				contact_name: contactName || undefined,
				contact_email: contactEmail || undefined,
				invoice_date: invoiceDate,
				due_date: dueDate,
				comment: submittedValues.comment || undefined,
				internal_comment: submittedValues.internalComment || undefined,
			};

			// Add mode-specific fields
			if (submittedValues.mode === "manual") {
				// Manual invoice specific fields
				payload.tax_rate = vatRate;
				payload.tax_amount = vatAmount;
				payload.total_amount = subtotal + vatAmount;
				payload.vatTermId =
					submittedValues.vatTermId !== "none"
						? submittedValues.vatTermId
						: undefined;
				payload.paymentTermId =
					submittedValues.paymentTermId || undefined;
				payload.currency = submittedValues.currency;
				payload.subtotal = subtotal;
				payload.lineItems = lineItems.map((item) => ({
					description: item.description,
					// Removed type field - all line items are now revenue-only
					quantity: Number(item.quantity),
					unit: item.unit,
					unitPrice: Number(item.unitPrice),
					totalPrice: Number(item.totalPrice),
					currency: submittedValues.currency,
					notes: item.notes,
					vatRate: item.vatRate ?? vatRate,
				}));
			} else {
				// Order-based invoice specific fields using order_allocations based on selected line items
				const orderAllocations = (submittedValues.orderIds || []).map(
					(orderId) => {
						// Get the line items for this order
						const orderItems = orderLineItems[orderId] || [];

						// Create the line items array for this order
						const lineItems = orderItems
							.filter((item, index) => {
								// Only include selected line items (all line items are now revenue-only)
								const itemKey = `${orderId}:${item.id || index}`;
								return selectedLineItems[itemKey];
							})
							.map((item, index) => {
								// Get the item key for finding proportion
								const itemKey = `${orderId}:${item.id || index}`;
								// Calculate proportion (default to 100% if not specified)
								const proportion =
									lineItemProportions[itemKey] !== undefined
										? lineItemProportions[itemKey] / 100
										: 1;

								// Apply proportion to quantity and price
								return {
									orderLineItemId: item.id,
									description: item.description,
									quantity: item.quantity * proportion,
									unit: item.unit,
									unitPrice: item.unitPrice,
									totalPrice: item.totalPrice * proportion,
									vatRate: item.vatRate,
									currency:
										item.currency ||
										submittedValues.currency,
								};
							});

						return {
							orderId,
							lineItems,
						};
					},
				);

				// Add the order_allocations to the payload
				payload.order_allocations = orderAllocations;
			}

			await createInvoice(payload);

			// Reset form and close dialogs
			form.reset();
			setLineItems([]);
			setIsPreviewDialogOpen(false);
			setInvoicePreviewPdfData(undefined);
			setSubmittedValues(null);
			onOpenChange(false);
		} catch (error) {
			console.error("Failed to create invoice:", error);
		}
	};

	// Handle selected contact change
	const handleSelectedContactChange = (contact: any) => {
		setSelectedContact(contact);
	};

	const handleAddLineItem = async (
		item: LineItemFormValues & { _references?: string[] },
	) => {
		// Add a temporary ID for client-side operations and use the form's currency
		const newItem = {
			...item,
			id: `temp-${Date.now()}`,
			currency: form.getValues("currency"),
		} as LineItem;
		setLineItems((prev) => [...prev, newItem]);
	};

	const handleUpdateLineItem = async (
		item: LineItemFormValues & { id: string; _references?: string[] },
	) => {
		if (!item.id) {
			return;
		}

		// Ensure the currency matches the form currency
		const updatedItem = {
			...item,
			currency: form.getValues("currency"),
		} as LineItem;

		setLineItems((prev) =>
			prev.map((li) => (li.id === item.id ? updatedItem : li)),
		);
	};

	const handleDeleteLineItem = async (itemId: string) => {
		setLineItems((prev) => prev.filter((item) => item.id !== itemId));
	};

	// Update all line items when currency changes
	const handleCurrencyChange = (currency: string) => {
		form.setValue("currency", currency);

		// Update all existing line items with the new currency
		if (lineItems.length > 0) {
			setLineItems(
				lineItems.map((item) => ({
					...item,
					currency,
				})),
			);
		}
	};

	return (
		<>
			<Dialog
				open={open}
				onOpenChange={(isOpen) => {
					if (!isOpen) {
						// Reset form and states when dialog is closed
						form.reset();
						setLineItems([]);
						setSelectedCustomer(null);
						setSelectedContact(null);
						setSelectedBillingCounterparty(null);
						setInvoicePreviewPdfData(undefined);
						setSubmittedValues(null);
						// Reset loading states
						setIsCustomerLoaded(false);
						setIsOrderDataLoaded(false);
						setShouldPreselect(false);
						setHasPreselectCompleted(false);
					}
					onOpenChange(isOpen);
				}}
			>
				<DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
					<DialogHeader>
						<DialogTitle>
							{t("app.invoices.createInvoice")}
						</DialogTitle>
						<DialogDescription>
							{t("app.invoices.createInvoiceDescription")}
						</DialogDescription>
					</DialogHeader>

					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(onSubmit)}
							className="space-y-6"
						>
							{/* Mode Switcher */}
							<div className="flex space-x-4">
								<Button
									type="button"
									variant={
										mode === "manual"
											? "primary"
											: "outline"
									}
									onClick={() => {
										setMode("manual");
										form.setValue("mode", "manual");
									}}
								>
									<PlusCircle className="h-4 w-4 mr-2" />
									Manual Invoice
								</Button>
								<Button
									type="button"
									variant={
										mode === "order" ? "primary" : "outline"
									}
									onClick={() => {
										setMode("order");
										form.setValue("mode", "order");
									}}
								>
									<ShoppingBag className="h-4 w-4 mr-2" />
									From Orders
								</Button>
							</div>

							<div className="grid grid-cols-2 gap-4">
								{/* Customer - replaced with CounterpartySelector */}
								<FormField
									control={form.control}
									name="customerId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("app.invoices.customer")}
											</FormLabel>
											<FormControl>
												<CounterpartySelector
													name="customerId"
													value={field.value}
													onChange={(value) => {
														field.onChange(value);
														// Reset orderIds when customer changes
														if (mode === "order") {
															form.setValue(
																"orderIds",
																[],
															);
														}
													}}
													type="customer"
													placeholder={t(
														"app.invoices.selectCustomer",
													)}
													onSelectedCounterpartyChange={
														handleSelectedCustomerChange
													}
													allowClear={true}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Contact Person Selector */}
								<FormField
									control={form.control}
									name="contactId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Contact Person
											</FormLabel>
											<FormControl>
												<ContactSelector
													name="contactId"
													value={field.value}
													onChange={field.onChange}
													counterpartyId={form.getValues(
														"customerId",
													)}
													placeholder="Select contact person"
													onSelectedContactChange={
														handleSelectedContactChange
													}
													allowClear={true}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Invoice Date */}
								<FormField
									control={form.control}
									name="invoiceDate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("app.invoices.invoiceDate")}
											</FormLabel>
											<FormControl>
												<InputWithDate
													inputProps={{
														value: field.value
															? format(
																	field.value,
																	"dd.MM.yyyy",
																)
															: undefined,
														name: field.name,
													}}
													onDateChange={(date) => {
														if (
															date instanceof Date
														) {
															field.onChange(
																date,
															);
														}
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Payment Term selector */}
								<FormField
									control={form.control}
									name="paymentTermId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Payment Terms</FormLabel>
											<Select
												disabled={isLoadingPaymentTerms}
												onValueChange={field.onChange}
												value={field.value || ""}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select payment term" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{paymentTerms?.items?.map(
														(term) => (
															<SelectItem
																key={term.id}
																value={term.id}
															>
																{term.name} (
																{
																	term.daysToPayment
																}{" "}
																days )
																{term.isDefault &&
																	" - Default"}
															</SelectItem>
														),
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Due Date */}
								<FormField
									control={form.control}
									name="dueDate"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("app.invoices.dueDate")}
											</FormLabel>
											<FormControl>
												<InputWithDate
													inputProps={{
														value: field.value
															? format(
																	field.value,
																	"dd.MM.yyyy",
																)
															: undefined,
														name: field.name,
													}}
													onDateChange={(date) => {
														if (
															date instanceof Date
														) {
															field.onChange(
																date,
															);
														}
													}}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								{/* Only show Currency in manual mode */}
								{mode === "manual" && (
									<FormField
										control={form.control}
										name="currency"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													{t("common.currency")}
												</FormLabel>
												<FormControl>
													<CurrencySelect
														name="currency"
														value={field.value}
														onValueChange={(
															value,
														) => {
															handleCurrencyChange(
																value,
															);
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}

								{/* VAT Term - hidden input to keep the form working */}
								<input
									type="hidden"
									{...form.register("vatTermId")}
								/>

								{/* Mode - hidden input */}
								<input
									type="hidden"
									{...form.register("mode")}
								/>
							</div>

							{/* Billing Counterparty Selector - Full width below customer and contact */}
							<div className="w-full">
								<FormField
									control={form.control}
									name="billingCounterpartyId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Billing Contact (Optional)
											</FormLabel>
											<FormControl>
												<CounterpartySelector
													{...field}
													name="billingCounterpartyId"
													placeholder="Select billing counterparty..."
													allowClear={true}
													includeAddresses={true}
													showSameGroupAs={
														selectedCustomer?.id
													}
													tooltip="Select a different counterparty for billing if different from the customer"
													onSelectedCounterpartyChange={
														handleBillingCounterpartySelect
													}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Mode-specific content */}
							{mode === "manual" ? (
								/* Manual invoice fields - Line Items */
								<div className="space-y-2">
									<h3 className="text-lg font-medium">
										{t("app.invoices.lineItems")}
									</h3>
									<LineItemsTable
										items={lineItems}
										onCreateItem={handleAddLineItem}
										onUpdateItem={handleUpdateLineItem}
										onDeleteItem={handleDeleteLineItem}
										hideTypeColumn={true}
										hideCurrencyColumn={true}
										hideTypeTotals={true}
										defaultVat={currentVatRate}
									/>
									{form.formState.errors.root && (
										<p className="text-sm font-medium text-destructive mt-2">
											{form.formState.errors.root.message}
										</p>
									)}

									{/* Totals */}
									{lineItems.length > 0 && (
										<div className="mt-4 border-t pt-4">
											<div className="flex justify-end text-sm">
												<div className="w-1/3">
													<div className="flex justify-between py-1">
														<span>
															{t(
																"app.invoices.subtotal",
															)}
															:
														</span>
														<span className="font-medium">
															{subtotal.toFixed(
																2,
															)}{" "}
															{form.getValues(
																"currency",
															)}
														</span>
													</div>

													{/* Display each VAT rate separately */}
													{vatBreakdown.map(
														(vatItem, index) => (
															<div
																key={index}
																className="flex justify-between py-1"
															>
																<span>
																	{t(
																		"app.invoices.vat",
																	)}{" "}
																	(
																	{
																		vatItem.rate
																	}
																	%):
																</span>
																<span className="font-medium">
																	{vatItem.amount.toFixed(
																		2,
																	)}{" "}
																	{form.getValues(
																		"currency",
																	)}
																</span>
															</div>
														),
													)}

													<div className="flex justify-between border-t pt-2 font-semibold">
														<span>
															{t(
																"app.invoices.total",
															)}
															:
														</span>
														<span>
															{total.toFixed(2)}{" "}
															{form.getValues(
																"currency",
															)}
														</span>
													</div>
												</div>
											</div>
										</div>
									)}
								</div>
							) : (
								/* Order selection section */
								<div className="col-span-2 space-y-4">
									<FormField
										control={form.control}
										name="orderIds"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Select Orders to Invoice
												</FormLabel>
												<FormControl>
													<OrderSelector
														customerId={form.watch(
															"customerId",
														)}
														selectedOrderIds={
															field.value || []
														}
														onOrderSelectionChange={
															field.onChange
														}
														selectedLineItems={
															selectedLineItems
														}
														onLineItemSelectionChange={
															setSelectedLineItems
														}
														orderLineItems={
															orderLineItems
														}
														onOrderLineItemsChange={
															setOrderLineItems
														}
														expandedOrders={
															expandedOrders
														}
														onExpandedOrdersChange={
															setExpandedOrders
														}
														onTotalsChange={(
															totals,
														) => {
															setCalculatedTotals(
																totals,
															);
														}}
														lineItemProportions={
															lineItemProportions
														}
														onLineItemProportionsChange={
															setLineItemProportions
														}
														defaultVat={
															currentVatRate
														}
														currentVatRate={
															currentVatRate
														}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Totals Summary */}
									{calculatedTotals.net > 0 && (
										<div className="border border-border rounded-lg p-4 bg-muted/20">
											<h4 className="text-sm font-medium mb-3">
												Invoice Totals
											</h4>
											<div className="space-y-2 text-sm">
												<div className="flex justify-between">
													<span>Total Net:</span>
													<span className="font-medium">
														{new Intl.NumberFormat(
															"de-DE",
															{
																style: "currency",
																currency:
																	form.watch(
																		"currency",
																	) || "EUR",
															},
														).format(
															calculatedTotals.net,
														)}
													</span>
												</div>

												{/* VAT Breakdown - show each rate separately */}
												{calculatedTotals.vatBreakdown.map(
													(vatGroup, index) => (
														<div
															key={index}
															className="flex justify-between"
														>
															<span>
																VAT (
																{vatGroup.rate.toFixed(
																	1,
																)}
																%):
															</span>
															<span className="font-medium">
																{new Intl.NumberFormat(
																	"de-DE",
																	{
																		style: "currency",
																		currency:
																			form.watch(
																				"currency",
																			) ||
																			"EUR",
																	},
																).format(
																	vatGroup.vat,
																)}
															</span>
														</div>
													),
												)}

												<hr className="border-border" />
												<div className="flex justify-between text-base font-semibold">
													<span>Total Gross:</span>
													<span>
														{new Intl.NumberFormat(
															"de-DE",
															{
																style: "currency",
																currency:
																	form.watch(
																		"currency",
																	) || "EUR",
															},
														).format(
															calculatedTotals.gross,
														)}
													</span>
												</div>
											</div>
										</div>
									)}
								</div>
							)}

							{/* Public Comment */}
							<FormField
								control={form.control}
								name="comment"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("app.invoices.publicComment")}
										</FormLabel>
										<FormControl>
											<Textarea
												placeholder={t(
													"app.invoices.publicCommentPlaceholder",
												)}
												className="min-h-20"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Internal Comment */}
							<FormField
								control={form.control}
								name="internalComment"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("app.invoices.internalComment")}
										</FormLabel>
										<FormControl>
											<Textarea
												placeholder={t(
													"app.invoices.internalCommentPlaceholder",
												)}
												className="min-h-20"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<DialogFooter>
								<Button
									type="button"
									variant="outline"
									onClick={() => onOpenChange(false)}
								>
									{t("app.invoices.cancel")}
								</Button>
								<Button
									type="submit"
									disabled={isLoading || isGeneratingPreview}
								>
									{isGeneratingPreview ? (
										<>
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
											{t(
												"app.invoices.generatingPreview",
											)}
										</>
									) : (
										t("app.invoices.previewInvoice")
									)}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</DialogContent>
			</Dialog>

			{/* Invoice Preview Dialog */}
			<Dialog
				open={isPreviewDialogOpen}
				onOpenChange={(open) => {
					if (!open) {
						setInvoicePreviewPdfData(undefined);
					}
					setIsPreviewDialogOpen(open);
				}}
			>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>Invoice Preview</DialogTitle>
					</DialogHeader>
					<div className="h-[80vh]">
						{invoicePreviewPdfData ? (
							<iframe
								src={`data:application/pdf;base64,${invoicePreviewPdfData}`}
								width="100%"
								height="100%"
								title="Invoice PDF Preview"
							/>
						) : (
							<div className="flex items-center justify-center h-full">
								<Loader2 className="h-8 w-8 text-muted-foreground animate-spin" />
								<p className="ml-2">Loading preview...</p>
							</div>
						)}
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => {
								setIsPreviewDialogOpen(false);
								// Don't clear preview data so it can be shown again if needed
							}}
						>
							Back to Form
						</Button>
						<Button
							onClick={handleCreateInvoice}
							disabled={isLoading}
						>
							{isLoading ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								</>
							) : (
								t("app.invoices.createInvoice")
							)}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
