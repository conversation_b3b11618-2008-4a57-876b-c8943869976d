import type {
	CreateDepartmentInput,
	UpdateDepartmentInput,
} from "@repo/api/src/routes/settings/department/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface ListDepartmentsParams {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
}

// Query keys for React Query
export const departmentKeys = {
	all: ["departments"] as const,
	lists: () => [...departmentKeys.all, "list"] as const,
	list: (params: ListDepartmentsParams) =>
		[...departmentKeys.lists(), params] as const,
	details: () => [...departmentKeys.all, "detail"] as const,
	detail: (organizationId: string, id: string) =>
		[...departmentKeys.details(), organizationId, id] as const,
};

// API functions
export const fetchDepartments = async (params: ListDepartmentsParams) => {
	const response = await apiClient.settings.department.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch departments");
	}

	return response.json();
};

// React Query Hooks
export const useDepartmentsQuery = (
	params: ListDepartmentsParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: departmentKeys.list(params),
		queryFn: () => fetchDepartments(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

// Mutation Hooks
export const useCreateDepartmentMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateDepartmentInput) => {
			const response = await apiClient.settings.department.$post({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to create department");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Department created successfully");
			queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create department",
			);
		},
	});
};

export const useUpdateDepartmentMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			id,
			data,
		}: {
			id: string;
			data: UpdateDepartmentInput;
		}) => {
			const response = await apiClient.settings.department[":id"].$put({
				param: { id },
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to update department");
			}

			return response.json();
		},
		onSuccess: (_, { id }) => {
			toast.success("Department updated successfully");
			queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
			queryClient.invalidateQueries({
				queryKey: departmentKeys.detail(organizationId, id),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update department",
			);
		},
	});
};

export const useDeleteDepartmentMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.settings.department[":id"].$delete(
				{
					param: { id },
					query: { organizationId },
				},
			);

			if (!response.ok) {
				throw new Error("Failed to delete department");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Department deleted successfully");
			queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete department",
			);
		},
	});
};
