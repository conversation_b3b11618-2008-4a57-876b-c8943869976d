import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { useVehicleView } from "@saas/vehicles/context/vehicle-view-context";
import type { useVehicleById } from "@saas/vehicles/hooks/use-vehicles";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { CurrencySelect } from "@ui/components/currency-select";
import { Input } from "@ui/components/input";
import PriceDisplay from "@ui/components/price-display";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface DetailsPanelProps {
	vehicle: ReturnType<typeof useVehicleById>["data"];
}

export function DetailsPanel({ vehicle }: DetailsPanelProps) {
	const t = useTranslations();
	const { isEditMode, fieldChanges, updateField } = useVehicleView();

	// Helper to get the current value (from fieldChanges if editing, or vehicle otherwise)
	// with proper type handling for different field types
	const getValue = (fieldName: string, defaultValue = "") => {
		const value =
			isEditMode && fieldName in fieldChanges
				? fieldChanges[fieldName as keyof typeof fieldChanges]
				: vehicle
					? vehicle[fieldName as keyof typeof vehicle]
					: null;

		// Handle different value types appropriately
		if (value === null || value === undefined) {
			return defaultValue;
		}

		// Convert to appropriate type based on field name
		if (typeof value === "boolean") {
			return value.toString(); // Convert boolean to string
		}

		// Handle numbers specifically to avoid "0" being treated as falsy
		if (typeof value === "number") {
			return value.toString(); // Convert number to string for input value
		}

		// Default case: return as string
		return value.toString();
	};

	// Helper to get a numeric value for number inputs
	const getNumericValue = (fieldName: string): string => {
		const value =
			isEditMode && fieldName in fieldChanges
				? fieldChanges[fieldName as keyof typeof fieldChanges]
				: vehicle
					? vehicle[fieldName as keyof typeof vehicle]
					: null;

		if (value === null || value === undefined || value === "") {
			return "";
		}

		return value.toString();
	};

	// Handle date inputs
	const handleDateChange = (fieldName: string, dateString: string) => {
		try {
			if (!dateString || dateString.trim() === "") {
				updateField(fieldName, null);
				return;
			}

			// Try to create a valid date
			const dateParts = dateString.split(".");
			if (dateParts.length === 3) {
				const day = Number.parseInt(dateParts[0]);
				const month = Number.parseInt(dateParts[1]) - 1; // JS months are 0-based
				const year = Number.parseInt(dateParts[2]);

				const date = new Date(year, month, day);
				if (!Number.isNaN(date.getTime())) {
					updateField(fieldName, date.toISOString());
					return;
				}
			}

			// If date parsing failed, try direct Date construction
			const date = new Date(dateString);
			if (!Number.isNaN(date.getTime())) {
				updateField(fieldName, date.toISOString());
			}
		} catch (error) {
			console.error(`Error parsing date: ${dateString}`, error);
		}
	};

	// Format date for display
	const formatDate = (date: string | null | undefined) => {
		if (!date) {
			return "-";
		}
		try {
			return format(new Date(date), "dd.MM.yyyy");
		} catch (error) {
			return "-";
		}
	};

	// Date input component
	const DateInput = ({
		fieldName,
		value,
	}: { fieldName: string; value: string | null | undefined }) => {
		const [localValue, setLocalValue] = useState(
			value ? formatDate(value) : "",
		);

		return (
			<Input
				type="text"
				placeholder="DD.MM.YYYY"
				value={localValue}
				onChange={(e) => {
					const newValue = e.target.value;
					setLocalValue(newValue);
					handleDateChange(fieldName, newValue);
				}}
			/>
		);
	};

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>{t("app.vehicle.detailsPanel.title")}</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.licensePlate")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											placeholder="License Plate"
											value={getValue("licensePlate")}
											onChange={(e) =>
												updateField(
													"licensePlate",
													e.target.value,
												)
											}
										/>
									</div>
								) : (
									<p>{vehicle?.licensePlate || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.vehicle.attributes.isActiveInDispatch",
									)}
								</h4>
								{isEditMode ? (
									<div className="flex items-center space-x-2">
										<Switch
											checked={
												fieldChanges?.isActiveInDispatch !==
												undefined
													? !!fieldChanges.isActiveInDispatch
													: !!vehicle?.isActiveInDispatch
											}
											onCheckedChange={(checked) =>
												updateField(
													"isActiveInDispatch",
													checked,
												)
											}
										/>
										<span>
											{fieldChanges?.isActiveInDispatch !==
											undefined
												? fieldChanges.isActiveInDispatch
													? t(
															"app.vehicle.isActiveInDispatch.active",
														)
													: t(
															"app.vehicle.isActiveInDispatch.inactive",
														)
												: vehicle?.isActiveInDispatch
													? t(
															"app.vehicle.isActiveInDispatch.active",
														)
													: t(
															"app.vehicle.isActiveInDispatch.inactive",
														)}
										</span>
									</div>
								) : (
									<Badge
										className={`${vehicle?.isActiveInDispatch ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-red-100 text-red-800 hover:bg-red-100"}`}
									>
										{vehicle?.isActiveInDispatch
											? t(
													"app.vehicle.isActiveInDispatch.active",
												)
											: t(
													"app.vehicle.isActiveInDispatch.inactive",
												)}
									</Badge>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.manufacturer")}
								</h4>
								{isEditMode ? (
									<Input
										placeholder="Manufacturer"
										value={getValue("manufacturer")}
										onChange={(e) =>
											updateField(
												"manufacturer",
												e.target.value,
											)
										}
									/>
								) : (
									<p>{vehicle?.manufacturer || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.owner")}
								</h4>
								{isEditMode ? (
									<CounterpartySelector
										name="owner"
										placeholder="Select owner"
										value={getValue("ownerId")}
										onChange={(value) =>
											updateField("ownerId", value)
										}
										type="carrier"
										allowClear
									/>
								) : (
									<p>{vehicle?.owner?.nameLine1 || "-"}</p>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.vehicle.detailsPanel.specifications")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.chassisNumber")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											placeholder="Chassis number"
											value={getValue("chassisNumber")}
											onChange={(e) =>
												updateField(
													"chassisNumber",
													e.target.value,
												)
											}
										/>
									</div>
								) : (
									<p>{vehicle?.chassisNumber || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.vehicle.attributes.yearOfManufacture",
									)}
								</h4>
								{isEditMode ? (
									<Input
										type="number"
										placeholder="Year of manufacture"
										value={getNumericValue(
											"yearOfManufacture",
										)}
										onChange={(e) => {
											const value =
												e.target.value !== ""
													? Number.parseInt(
															e.target.value,
														)
													: null;
											updateField(
												"yearOfManufacture",
												value,
											);
										}}
									/>
								) : (
									<p>{vehicle?.yearOfManufacture || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.usageDuration")}
								</h4>
								{isEditMode ? (
									<Input
										placeholder="Usage duration"
										value={getValue("usageDuration")}
										onChange={(e) =>
											updateField(
												"usageDuration",
												e.target.value,
											)
										}
									/>
								) : (
									<p>{vehicle?.usageDuration || "-"}</p>
								)}
							</div>
						</div>
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.mileage")}
								</h4>
								{isEditMode ? (
									<Input
										placeholder="Mileage"
										value={getValue("mileage")}
										onChange={(e) =>
											updateField(
												"mileage",
												e.target.value,
											)
										}
									/>
								) : (
									<p>{vehicle?.kilometers || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.vehicle.attributes.mileageAtPurchase",
									)}
								</h4>
								{isEditMode ? (
									<Input
										placeholder="Mileage at purchase"
										value={getValue("mileageAtPurchase")}
										onChange={(e) =>
											updateField(
												"mileageAtPurchase",
												e.target.value,
											)
										}
									/>
								) : (
									<p>
										{vehicle?.kilometersAtPurchase || "-"}
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Physical Dimensions & Capacity Section */}
					<div className="mt-6 pt-6 border-t">
						<h3 className="text-lg font-medium mb-4">
							Physical Dimensions & Capacity
						</h3>
						<div className="grid grid-cols-2 gap-6">
							<div className="space-y-4">
								<div>
									<h4 className="text-sm font-medium text-muted-foreground">
										Length (meters)
									</h4>
									{isEditMode ? (
										<Input
											type="number"
											step="0.01"
											min="0"
											placeholder="0.00"
											value={getNumericValue("length")}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseFloat(
																e.target.value,
															)
														: null;
												updateField("length", value);
											}}
										/>
									) : (
										<p>
											{vehicle?.length
												? `${vehicle.length} m`
												: "-"}
										</p>
									)}
								</div>
								<div>
									<h4 className="text-sm font-medium text-muted-foreground">
										Width (meters)
									</h4>
									{isEditMode ? (
										<Input
											type="number"
											step="0.01"
											min="0"
											placeholder="0.00"
											value={getNumericValue("width")}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseFloat(
																e.target.value,
															)
														: null;
												updateField("width", value);
											}}
										/>
									) : (
										<p>
											{vehicle?.width
												? `${vehicle.width} m`
												: "-"}
										</p>
									)}
								</div>
								<div>
									<h4 className="text-sm font-medium text-muted-foreground">
										Height (meters)
									</h4>
									{isEditMode ? (
										<Input
											type="number"
											step="0.01"
											min="0"
											placeholder="0.00"
											value={getNumericValue("height")}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseFloat(
																e.target.value,
															)
														: null;
												updateField("height", value);
											}}
										/>
									) : (
										<p>
											{vehicle?.height
												? `${vehicle.height} m`
												: "-"}
										</p>
									)}
								</div>
							</div>
							<div className="space-y-4">
								<div>
									<h4 className="text-sm font-medium text-muted-foreground">
										Loading Weight (kg)
									</h4>
									{isEditMode ? (
										<Input
											type="number"
											step="0.01"
											min="0"
											placeholder="0.00"
											value={getNumericValue(
												"loadingWeight",
											)}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseFloat(
																e.target.value,
															)
														: null;
												updateField(
													"loadingWeight",
													value,
												);
											}}
										/>
									) : (
										<p>
											{vehicle?.loadingWeight
												? `${vehicle.loadingWeight} kg`
												: "-"}
										</p>
									)}
								</div>
								<div>
									<h4 className="text-sm font-medium text-muted-foreground">
										Euro Pallets Capacity
									</h4>
									{isEditMode ? (
										<Input
											type="number"
											min="0"
											step="1"
											placeholder="0"
											value={getNumericValue(
												"euroPalletsAmount",
											)}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseInt(
																e.target.value,
																10,
															)
														: null;
												updateField(
													"euroPalletsAmount",
													value,
												);
											}}
										/>
									) : (
										<p>
											{vehicle?.euroPalletsAmount
												? `${vehicle.euroPalletsAmount} pallets`
												: "-"}
										</p>
									)}
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>
						{t("app.vehicle.detailsPanel.ownership_info")}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.financingType")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											placeholder="Financing type"
											value={getValue("financingType")}
											onChange={(e) =>
												updateField(
													"financingType",
													e.target.value,
												)
											}
										/>
									</div>
								) : (
									<p>{vehicle?.financingType || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.purchasePrice")}
								</h4>
								{isEditMode ? (
									<div className="grid grid-cols-2 gap-2">
										<Input
											type="number"
											placeholder="Purchase price"
											value={getNumericValue(
												"purchasePrice",
											)}
											onChange={(e) => {
												const value =
													e.target.value !== ""
														? Number.parseFloat(
																e.target.value,
															)
														: null;
												updateField(
													"purchasePrice",
													value,
												);
											}}
										/>
										<CurrencySelect
											name="currency"
											value={getValue("currency")}
											onValueChange={(value) =>
												updateField("currency", value)
											}
											currencies="custom"
										/>
									</div>
								) : (
									<div>
										{vehicle?.purchasePrice != null ? (
											<PriceDisplay
												purchasePrice={
													vehicle.purchasePrice
												}
												currencyType={
													vehicle.currency ?? ""
												}
											/>
										) : (
											"-"
										)}
									</div>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.purchaseDate")}
								</h4>
								{isEditMode ? (
									<DateInput
										fieldName="purchaseDate"
										value={
											getValue(
												"purchaseDate",
												undefined,
											) as string | null
										}
									/>
								) : (
									<p>
										{vehicle?.purchaseDate
											? formatDate(vehicle.purchaseDate)
											: "-"}
									</p>
								)}
							</div>
						</div>
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t("app.vehicle.attributes.residualValue")}
								</h4>
								{isEditMode ? (
									<Input
										placeholder="Residual value"
										value={getValue("residualValue")}
										onChange={(e) =>
											updateField(
												"residualValue",
												e.target.value,
											)
										}
									/>
								) : (
									<p>{vehicle?.residualValue || "-"}</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.vehicle.attributes.registrationDate",
									)}
								</h4>
								{isEditMode ? (
									<DateInput
										fieldName="registrationDate"
										value={
											getValue(
												"registrationDate",
												undefined,
											) as string | null
										}
									/>
								) : (
									<p>
										{vehicle?.registrationDate
											? formatDate(
													vehicle.registrationDate,
												)
											: "-"}
									</p>
								)}
							</div>
							<div>
								<h4 className="text-sm font-medium text-muted-foreground">
									{t(
										"app.vehicle.attributes.deregistrationDate",
									)}
								</h4>
								{isEditMode ? (
									<DateInput
										fieldName="deregistrationDate"
										value={
											getValue(
												"deregistrationDate",
												undefined,
											) as string | null
										}
									/>
								) : (
									<p>
										{vehicle?.deregistrationDate
											? formatDate(
													vehicle.deregistrationDate,
												)
											: "-"}
									</p>
								)}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{(vehicle?.notes || isEditMode) && (
				<Card>
					<CardHeader>
						<CardTitle>
							{t("app.vehicle.attributes.notes")}
						</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditMode ? (
							<Textarea
								placeholder="Notes"
								rows={4}
								value={getValue("notes")}
								onChange={(e) =>
									updateField("notes", e.target.value)
								}
							/>
						) : (
							<p className="whitespace-pre-wrap">
								{vehicle?.notes || "-"}
							</p>
						)}
					</CardContent>
				</Card>
			)}
		</div>
	);
}
