"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Skeleton } from "@ui/components/skeleton";
import { formatDistanceToNow } from "date-fns";
import { MessageCircle, Pin, Plus, Users } from "lucide-react";
import React, { useState } from "react";
import { useThreads } from "../hooks/use-threads";
import { StatusBadge } from "./StatusBadge";
import { ThreadCreateForm } from "./ThreadCreateForm";
import { ThreadViewSheet } from "./ThreadViewSheet";

interface ThreadManagementSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	entityType: string;
	entityId: string;
	entityTitle?: string;
}

function ThreadListSkeleton() {
	return (
		<div className="space-y-3">
			{Array.from({ length: 5 }).map((_, i) => (
				<div key={i} className="p-3 border rounded-lg">
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Skeleton className="h-4 w-16" />
							<Skeleton className="h-4 w-20" />
						</div>
						<Skeleton className="h-4 w-3/4" />
						<div className="flex items-center gap-4 text-xs">
							<Skeleton className="h-3 w-16" />
							<Skeleton className="h-3 w-12" />
						</div>
					</div>
				</div>
			))}
		</div>
	);
}

function ThreadListItem({
	thread,
	isSelected,
	onClick,
}: {
	thread: any;
	isSelected: boolean;
	onClick: () => void;
}) {
	return (
		<Card
			className={`cursor-pointer transition-colors hover:bg-muted/50 ${
				isSelected ? "bg-muted border-primary" : ""
			}`}
			onClick={onClick}
		>
			<CardContent className="p-3">
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<StatusBadge status={thread.status} />
						{thread.isPinned && (
							<Badge
								status="warning"
								className="text-xs px-1 py-0"
							>
								<Pin className="w-3 h-3" />
							</Badge>
						)}
					</div>

					<h4 className="font-medium text-sm line-clamp-2 leading-tight text-foreground">
						{thread.title}
					</h4>

					{thread.description && (
						<p className="text-xs text-muted-foreground line-clamp-2">
							{thread.description}
						</p>
					)}

					<div className="flex items-center gap-4 text-xs text-muted-foreground">
						<div className="flex items-center gap-1">
							<MessageCircle className="w-3 h-3" />
							{thread.commentCount || 0}
						</div>
						<div className="flex items-center gap-1">
							<Users className="w-3 h-3" />
							{/* Participant count would come from API */}1
						</div>
						<span>
							{formatDistanceToNow(new Date(thread.updatedAt), {
								addSuffix: true,
							})}
						</span>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

export function ThreadManagementSheet({
	open,
	onOpenChange,
	entityType,
	entityId,
	entityTitle,
}: ThreadManagementSheetProps) {
	const [selectedThreadId, setSelectedThreadId] = useState<string | null>(
		null,
	);
	const [showCreateForm, setShowCreateForm] = useState(false);

	const {
		data: threadsData,
		isLoading,
		error,
		entityType: filterEntityType,
		setEntityType,
		entityId: filterEntityId,
		setEntityId,
	} = useThreads();

	// Set filters when component mounts or props change
	React.useEffect(() => {
		if (open && entityType && entityId) {
			setEntityType(entityType);
			setEntityId(entityId);
		}
	}, [open, entityType, entityId, setEntityType, setEntityId]);

	const threads = threadsData?.items || [];

	// Auto-select first thread if none selected
	React.useEffect(() => {
		if (threads.length > 0 && !selectedThreadId) {
			setSelectedThreadId(threads[0].id);
		}
	}, [threads, selectedThreadId]);

	const handleCreateSuccess = () => {
		setShowCreateForm(false);
		// The new thread will appear in the list automatically via React Query
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent
				side="right"
				className="w-full max-w-none p-0 overflow-hidden"
				style={{ width: "80vw", maxWidth: "none" }}
			>
				<SheetHeader className="px-6 py-4 border-b">
					<div className="flex items-center gap-2">
						<MessageCircle className="h-5 w-5 text-muted-foreground" />
						<SheetTitle className="text-lg text-foreground">
							Threads
						</SheetTitle>
					</div>
					<SheetDescription className="text-muted-foreground">
						{entityType}{" "}
						{entityTitle ? `#${entityTitle}` : `#${entityId}`} •{" "}
						{threads.length} threads
					</SheetDescription>
				</SheetHeader>

				<div className="flex h-[calc(100vh-100px)]">
					{/* Left Panel - Thread List */}
					<div className="w-2/5 border-r bg-muted/20">
						<div className="p-4 border-b bg-background">
							<div className="flex items-center justify-between">
								<h3 className="font-medium text-foreground">
									All Threads
								</h3>
								<Button
									size="sm"
									onClick={() => setShowCreateForm(true)}
								>
									<Plus className="w-4 h-4 mr-1" />
									New
								</Button>
							</div>
						</div>

						<ScrollArea className="h-[calc(100vh-180px)]">
							<div className="p-4 space-y-3">
								{error ? (
									<div className="text-center py-8">
										<div className="text-muted-foreground text-sm">
											Failed to load threads
										</div>
									</div>
								) : isLoading ? (
									<ThreadListSkeleton />
								) : threads.length === 0 ? (
									<div className="text-center py-8">
										<MessageCircle className="h-12 w-12 mx-auto text-muted-foreground/40 mb-4" />
										<div className="text-muted-foreground text-sm">
											No threads yet
										</div>
										<Button
											size="sm"
											className="mt-2"
											onClick={() =>
												setShowCreateForm(true)
											}
										>
											Create First Thread
										</Button>
									</div>
								) : (
									threads.map((thread) => (
										<ThreadListItem
											key={thread.id}
											thread={thread}
											isSelected={
												selectedThreadId === thread.id
											}
											onClick={() =>
												setSelectedThreadId(thread.id)
											}
										/>
									))
								)}
							</div>
						</ScrollArea>
					</div>

					{/* Right Panel - Thread Detail */}
					<div className="flex-1 flex flex-col">
						{showCreateForm ? (
							<div className="p-6">
								<ThreadCreateForm
									entityType={entityType}
									entityId={entityId}
									onCancel={() => setShowCreateForm(false)}
									onSuccess={handleCreateSuccess}
								/>
							</div>
						) : selectedThreadId ? (
							<ThreadViewSheet
								threadId={selectedThreadId}
								open={true}
								onOpenChange={() => {}} // Don't close on sheet changes
								embedded={true} // New prop to indicate embedded mode
								entityTitle={entityTitle}
							/>
						) : (
							<div className="flex-1 flex items-center justify-center text-muted-foreground">
								<div className="text-center">
									<MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-40" />
									<p>Select a thread to view details</p>
								</div>
							</div>
						)}
					</div>
				</div>
			</SheetContent>
		</Sheet>
	);
}
