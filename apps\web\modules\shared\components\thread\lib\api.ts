import type {
	ListCommentsInput,
	ListThreadsInput,
} from "@repo/api/src/routes/threads/types";
import { apiClient } from "@shared/lib/api-client";

// Thread API Functions
export const fetchThreads = async (params: ListThreadsInput) => {
	const response = await apiClient.threads.$get({
		query: {
			organizationId: params.organizationId,
			entityType: params.entityType,
			entityId: params.entityId,
			status: params.status,
			search: params.search,
			isPinned: params.isPinned?.toString(),
			authorId: params.authorId,
			unread: params.unread?.toString(),
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch threads");
	}

	return response.json();
};

export const fetchThreadById = async (organizationId: string, id: string) => {
	const response = await apiClient.threads[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch thread details");
	}

	return response.json();
};

// Comment API Functions
export const fetchComments = async (params: ListCommentsInput) => {
	const response = await apiClient.threads[":id"].comments.$get({
		param: { id: params.threadId },
		query: {
			parentId: params.parentId,
			isPinned: params.isPinned?.toString(),
			status: params.status,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch comments");
	}

	return response.json();
};

// Members autocomplete API function
export const fetchMembersAutocomplete = async (params: {
	organizationId: string;
	q: string;
	limit?: number;
}) => {
	const response = await apiClient.organizations.members.autocomplete.$get({
		query: {
			organizationId: params.organizationId,
			q: params.q,
			limit: params.limit?.toString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch member suggestions");
	}

	return response.json();
};

// Query Keys
export const threadKeys = {
	all: ["threads"] as const,
	list: (params: ListThreadsInput) =>
		[...threadKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...threadKeys.all, "detail", organizationId, id] as const,
};

export const commentKeys = {
	all: ["comments"] as const,
	list: (params: ListCommentsInput) =>
		[...commentKeys.all, "list", params] as const,
};

export const memberKeys = {
	all: ["members"] as const,
	autocomplete: (params: {
		organizationId: string;
		q: string;
		limit?: number;
	}) => [...memberKeys.all, "autocomplete", params] as const,
};
