import { useExpensesQuery } from "@saas/expenses/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";

export function useOrderAllocatedExpenses(orderId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useExpensesQuery({
		organizationId: activeOrganization?.id || "",
		orderId: orderId, // Filter expenses allocated to this order
		limit: 100,
		sortBy: "expense_date",
		sortDirection: "desc",
	});
}

// Transform the expense data to show allocated amounts per line item
export function transformExpenseAllocations(expenses: any[], orderId: string) {
	const allocatedLineItems: Array<{
		id: string;
		expenseId: string;
		lineItemId: string;
		supplier: string;
		supplierInvoiceNumber?: string;
		expenseDate: Date;
		description: string;
		totalLineItemPrice: number;
		allocatedAmount: number;
		allocationMethod: "fixed" | "percentage";
		allocationValue: number;
		currency: string;
		category?: string;
		supplierDocumentUrl?: string;
	}> = [];

	for (const expense of expenses) {
		for (const lineItem of expense.lineItems) {
			// Find allocations for this order
			for (const allocation of lineItem.allocations || []) {
				if (allocation.type === "order") {
					// Find the specific entity allocation for this order
					const orderEntity = allocation.entities?.find(
						(entity: any) =>
							entity.entityId === orderId &&
							entity.entityType === "order",
					);

					if (orderEntity) {
						allocatedLineItems.push({
							id: `${lineItem.id}-${allocation.id}`,
							expenseId: expense.id,
							lineItemId: lineItem.id,
							supplier:
								expense.supplier?.nameLine1 ||
								"Unknown Supplier",
							supplierInvoiceNumber:
								expense.supplier_invoice_number,
							expenseDate: new Date(expense.expense_date),
							description: lineItem.description,
							totalLineItemPrice: lineItem.totalPrice || 0,
							allocatedAmount: orderEntity.allocatedAmount || 0,
							allocationMethod: allocation.method,
							allocationValue: allocation.value,
							currency: expense.currency || "EUR",
							category: lineItem.category?.name,
							supplierDocumentUrl: expense.supplier_document_url,
						});
					}
				}
			}
		}
	}

	return allocatedLineItems;
}
