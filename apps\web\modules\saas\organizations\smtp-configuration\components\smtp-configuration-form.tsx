"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@ui/components/collapsible";
import React from "react";

import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import Microsoft from "@ui/components/icons/microsoft";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { ChevronDown, Loader2, TestTube } from "lucide-react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
	useOAuth2Authentication,
	useSmtpConfiguration,
	useSmtpConfigurationCreation,
	useSmtpConfigurationUpdate,
	useSmtpConnectionTest,
} from "../../hooks/use-smtp-configuration";

// Validation schema for SMTP configuration
const smtpConfigurationSchema = z
	.object({
		// Core SMTP Settings
		host: z.string().min(1, "SMTP host is required"),
		port: z.coerce
			.number()
			.min(1)
			.max(65535, "Port must be between 1 and 65535"),
		security: z.enum(["none", "starttls", "ssl"]).default("starttls"),

		// Authentication - username is always required, password optional for OAuth2
		useOAuth2: z.boolean().default(false),
		username: z.string().min(1, "Username is required"),
		password: z.string().optional(),
		refreshToken: z.string().optional(),
		accessToken: z.string().optional(),
		tokenExpires: z.date().optional(),

		// Email Settings
		fromEmail: z.string().email("Please enter a valid email address"),
		fromName: z.string().optional(),

		// Advanced TLS Settings
		rejectUnauthorized: z.boolean().default(true),

		// Certificate Settings
		customCaCert: z.string().optional(),
		clientCert: z.string().optional(),
		clientKey: z.string().optional(),
		servername: z.string().optional(),
	})
	.refine(
		(data) => {
			// For OAuth2: require access token (username is always required)
			if (data.useOAuth2) {
				return data.accessToken && data.accessToken.length > 0;
			}
			// For password auth: require password (username is always required)
			return data.password && data.password.length > 0;
		},
		{
			message:
				"Either complete OAuth2 authentication or provide password",
			path: ["password"],
		},
	);

type SmtpConfigurationFormValues = z.infer<typeof smtpConfigurationSchema>;

interface SmtpConfigurationFormProps {
	onCancel?: () => void;
	onSuccess?: () => void;
}

export function SmtpConfigurationForm({
	onCancel,
	onSuccess,
}: SmtpConfigurationFormProps) {
	const { data: existingConfig } = useSmtpConfiguration();
	const { createSmtpConfiguration, isSubmitting: isCreating } =
		useSmtpConfigurationCreation();
	const { updateSmtpConfiguration, isSubmitting: isUpdating } =
		useSmtpConfigurationUpdate();
	const { testSmtpConnection, isTestingConnection: isTestingFormConnection } =
		useSmtpConnectionTest();
	const { initiateOAuth2Flow, isAuthenticating } = useOAuth2Authentication();

	const isEditing = !!existingConfig;
	const isSubmitting = isCreating || isUpdating;

	const form = useForm<SmtpConfigurationFormValues>({
		resolver: zodResolver(smtpConfigurationSchema),
		defaultValues: existingConfig
			? {
					host: existingConfig.host,
					port: existingConfig.port,
					security: (existingConfig.secure
						? "ssl"
						: existingConfig.requireTLS
							? "starttls"
							: "none") as "none" | "starttls" | "ssl",
					username: existingConfig.username,
					password: "", // Don't populate password for security
					fromEmail: existingConfig.fromEmail,
					fromName: existingConfig.fromName || "",
					rejectUnauthorized: existingConfig.rejectUnauthorized,
					customCaCert: existingConfig.customCaCert || "",
					clientCert: existingConfig.clientCert || "",
					clientKey: "", // Don't populate for security
					servername: existingConfig.servername || "",
					// OAuth2 fields
					useOAuth2: existingConfig.useOAuth2 || false,
					refreshToken: "", // Don't populate for security
					accessToken: "", // Don't populate for security
					tokenExpires: existingConfig.tokenExpires
						? new Date(existingConfig.tokenExpires)
						: undefined,
				}
			: {
					host: "",
					port: 587,
					security: "starttls" as const,
					username: "",
					password: "",
					fromEmail: "",
					fromName: "",
					rejectUnauthorized: true,
					customCaCert: "",
					clientCert: "",
					clientKey: "",
					servername: "",
					// OAuth2 fields
					useOAuth2: false,
					refreshToken: "",
					accessToken: "",
					tokenExpires: undefined,
				},
	});

	const onSubmit = async (values: SmtpConfigurationFormValues) => {
		// Convert security enum to backend format
		const backendValues = {
			...values,
			secure: values.security === "ssl",
			requireTLS: values.security === "starttls",
			ignoreTLS: false, // Always false with our new approach
		};

		// Remove the security field as it's not in the backend schema
		const { security, ...submitValues } = backendValues;

		if (isEditing) {
			await updateSmtpConfiguration(submitValues);
		} else {
			await createSmtpConfiguration(submitValues);
		}

		if (!isEditing) {
			form.reset();
		}

		// Call success callback if provided
		onSuccess?.();
	};

	const onTestConnection = async () => {
		const values = form.getValues();
		const isValid = await form.trigger();

		if (!isValid) {
			return;
		}

		// Convert security enum to backend format for testing
		const testValues = {
			...values,
			secure: values.security === "ssl",
			requireTLS: values.security === "starttls",
			ignoreTLS: false,
			sendTestEmail: false, // Just test connection, don't send email
		};

		// Remove the security field as it's not in the backend schema
		const { security, ...submitValues } = testValues;

		testSmtpConnection(submitValues);
	};

	const handleOAuth2Flow = async (provider: "microsoft") => {
		try {
			const tokens = await initiateOAuth2Flow(provider);

			// Update form with OAuth2 tokens
			if (tokens.refreshToken) {
				form.setValue("refreshToken", tokens.refreshToken);
			}
			form.setValue("accessToken", tokens.accessToken);
			if (tokens.tokenExpires) {
				form.setValue("tokenExpires", new Date(tokens.tokenExpires));
			}

			// Set username to authenticated user's email from OAuth2 response
			if (tokens.userEmail) {
				form.setValue("username", tokens.userEmail);
			}

			// Prefill Microsoft Exchange SMTP settings
			if (provider === "microsoft") {
				form.setValue("host", "smtp.office365.com");
				form.setValue("port", 587);
				form.setValue("security", "starttls"); // Use STARTTLS for Microsoft Exchange
				form.setValue("rejectUnauthorized", true); // Verify certificates

				// Set fromEmail to authenticated user's email if not already set
				if (tokens.userEmail && !form.getValues("fromEmail")) {
					form.setValue("fromEmail", tokens.userEmail);
				}
			}

			form.setValue("password", ""); // Clear password since we're using OAuth2
		} catch (error) {
			// Error is already handled by the mutation
			console.error("OAuth2 flow failed:", error);
		}
	};

	// Watch for authentication method changes and clear opposing fields
	const useOAuth2 = form.watch("useOAuth2");

	// Clear OAuth2 fields when switching to password auth
	React.useEffect(() => {
		if (!useOAuth2) {
			form.setValue("refreshToken", "");
			form.setValue("accessToken", "");
			form.setValue("tokenExpires", undefined);
		}
		// Note: We don't clear username when switching to OAuth2 anymore
		// because username is always required (it will be auto-populated for OAuth2)
	}, [useOAuth2, form]);

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>
					{isEditing
						? "Update SMTP Configuration"
						: "Add SMTP Configuration"}
				</CardTitle>
				<CardDescription>
					{isEditing
						? "Update your organization's SMTP settings for sending emails."
						: "Configure your organization's SMTP settings for sending emails. We'll test the connection before saving."}
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Form {...form}>
					<form
						id="smtp-configuration-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						{/* Basic SMTP Settings */}
						<div className="space-y-4">
							<h4 className="text-sm font-medium">
								Basic Settings
							</h4>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="host"
									render={({ field }) => (
										<FormItem>
											<FormLabel>SMTP Host</FormLabel>
											<FormControl>
												<Input
													placeholder="smtp.gmail.com"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												Your SMTP server hostname
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="port"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Port</FormLabel>
											<FormControl>
												<Input
													type="number"
													placeholder="587"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												SMTP port (usually 587 or 465)
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="security"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Security</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value}
											disabled={isSubmitting}
										>
											<FormControl>
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value="none">
													None - No encryption
												</SelectItem>
												<SelectItem value="starttls">
													STARTTLS - Upgrade to TLS
													(recommended)
												</SelectItem>
												<SelectItem value="ssl">
													SSL/TLS - Direct SSL
													connection
												</SelectItem>
											</SelectContent>
										</Select>
										<FormDescription>
											Choose encryption method. STARTTLS
											is recommended for most providers.
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Authentication Method Switcher */}
							<div className="space-y-4">
								<FormField
									control={form.control}
									name="useOAuth2"
									render={({ field }) => (
										<FormItem className="space-y-3">
											<FormLabel>
												Authentication Method
											</FormLabel>
											<FormControl>
												<div className="flex space-x-6">
													<div className="flex items-center space-x-2">
														<input
															type="radio"
															id="password-auth"
															name="auth-method"
															checked={
																!field.value
															}
															onChange={() =>
																field.onChange(
																	false,
																)
															}
															className="h-4 w-4"
															disabled={
																isSubmitting
															}
														/>
														<label
															htmlFor="password-auth"
															className="text-sm font-medium"
														>
															Username & Password
														</label>
													</div>
													<div className="flex items-center space-x-2">
														<input
															type="radio"
															id="oauth2-auth"
															name="auth-method"
															checked={
																field.value
															}
															onChange={() =>
																field.onChange(
																	true,
																)
															}
															className="h-4 w-4"
															disabled={
																isSubmitting
															}
														/>
														<label
															htmlFor="oauth2-auth"
															className="text-sm font-medium"
														>
															OAuth2 (Microsoft
															Exchange)
														</label>
													</div>
												</div>
											</FormControl>
											<FormDescription>
												Choose between traditional
												username/password authentication
												or modern OAuth2 for enhanced
												security.
											</FormDescription>
										</FormItem>
									)}
								/>

								{/* Username & Password Fields */}
								{!form.watch("useOAuth2") && (
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-muted/20">
										<FormField
											control={form.control}
											name="username"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Username
													</FormLabel>
													<FormControl>
														<Input
															placeholder="<EMAIL>"
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														SMTP username (usually
														your email)
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="password"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Password
													</FormLabel>
													<FormControl>
														<Input
															type="password"
															placeholder={
																isEditing
																	? "Leave empty to keep current password"
																	: "Your SMTP password or app password"
															}
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														{isEditing
															? "Leave empty to keep current password"
															: "Your SMTP password or app password"}
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								)}

								{/* OAuth2 Authentication */}
								{form.watch("useOAuth2") && (
									<div className="space-y-4">
										{/* Microsoft Exchange Connection Card */}
										<Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800">
											<CardContent className="p-6">
												<div className="flex items-center gap-4 mb-4">
													<div className="flex-shrink-0">
														<div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex items-center justify-center">
															<Microsoft className="w-8 h-8" />
														</div>
													</div>
													<div className="flex-1">
														<h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
															Microsoft Exchange
														</h3>
														<p className="text-sm text-gray-600 dark:text-gray-400">
															Secure OAuth2
															authentication
														</p>
													</div>
												</div>

												<p className="text-sm text-gray-700 dark:text-gray-300 mb-4">
													Connect to Microsoft
													Exchange using OAuth2 for
													enhanced security. No
													password storage required -
													tokens are managed
													automatically.
												</p>

												<Button
													type="button"
													onClick={() =>
														handleOAuth2Flow(
															"microsoft",
														)
													}
													disabled={
														isSubmitting ||
														isAuthenticating
													}
													className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white"
												>
													{isAuthenticating ? (
														<Loader2 className="h-4 w-4 mr-2 animate-spin" />
													) : (
														<Microsoft className="h-4 w-4 mr-2" />
													)}
													{isAuthenticating
														? "Connecting..."
														: "Connect to Microsoft Exchange"}
												</Button>
											</CardContent>
										</Card>

										{/* Show authenticated user email */}
										{form.watch("username") &&
											form.watch("accessToken") && (
												<Card className="bg-green-50 dark:bg-green-950/50 border-green-200 dark:border-green-800">
													<CardContent className="p-4">
														<div className="flex items-center gap-2 mb-2">
															<div className="w-2 h-2 bg-green-500 rounded-full" />
															<span className="text-sm font-medium text-green-800 dark:text-green-200">
																Successfully
																Connected
															</span>
														</div>
														<FormField
															control={
																form.control
															}
															name="username"
															render={({
																field,
															}) => (
																<FormItem>
																	<FormLabel className="text-green-700 dark:text-green-300">
																		Authenticated
																		Email
																	</FormLabel>
																	<FormControl>
																		<Input
																			{...field}
																			disabled={
																				true
																			}
																			className="bg-white dark:bg-gray-900 border-green-300 dark:border-green-700"
																		/>
																	</FormControl>
																	<FormDescription className="text-green-600 dark:text-green-400">
																		This
																		email
																		will be
																		used for
																		SMTP
																		authentication
																		and
																		sending
																	</FormDescription>
																	<FormMessage />
																</FormItem>
															)}
														/>
													</CardContent>
												</Card>
											)}
									</div>
								)}
							</div>
						</div>

						{/* Email Settings */}
						<div className="space-y-4">
							<h4 className="text-sm font-medium">
								Email Settings
							</h4>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="fromEmail"
									render={({ field }) => (
										<FormItem>
											<FormLabel>From Email</FormLabel>
											<FormControl>
												<Input
													placeholder="<EMAIL>"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												Email address to send from
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="fromName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												From Name (Optional)
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Your Company"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												Display name for sent emails
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Advanced Settings */}
						<Collapsible>
							<CollapsibleTrigger className="flex items-center space-x-2 text-sm font-medium hover:underline">
								<ChevronDown className="h-4 w-4" />
								<span>Advanced Settings</span>
							</CollapsibleTrigger>
							<CollapsibleContent className="space-y-4 mt-4">
								{/* Certificate Verification */}
								<div className="space-y-4">
									<h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
										Certificate Settings
									</h5>

									<FormField
										control={form.control}
										name="rejectUnauthorized"
										render={({ field }) => (
											<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
												<div className="space-y-0.5">
													<FormLabel>
														Verify Certificates
													</FormLabel>
													<FormDescription className="text-xs">
														Validate TLS
														certificates
														(recommended)
													</FormDescription>
												</div>
												<FormControl>
													<Switch
														checked={field.value}
														onCheckedChange={
															field.onChange
														}
														disabled={isSubmitting}
													/>
												</FormControl>
											</FormItem>
										)}
									/>
								</div>

								{/* Certificate Settings */}
								<div className="space-y-4">
									<h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
										Custom Certificates
									</h5>

									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<FormField
											control={form.control}
											name="servername"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Server Name
													</FormLabel>
													<FormControl>
														<Input
															placeholder="mail.example.com"
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														TLS servername for
														IP-based connections
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>

										<div />

										<FormField
											control={form.control}
											name="customCaCert"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Custom CA Certificate
													</FormLabel>
													<FormControl>
														<Textarea
															placeholder="-----BEGIN CERTIFICATE-----..."
															className="min-h-[100px] font-mono text-xs"
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														PEM-encoded CA
														certificate chain
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="clientCert"
											render={({ field }) => (
												<FormItem>
													<FormLabel>
														Client Certificate
													</FormLabel>
													<FormControl>
														<Textarea
															placeholder="-----BEGIN CERTIFICATE-----..."
															className="min-h-[100px] font-mono text-xs"
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														PEM-encoded client
														certificate
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="clientKey"
											render={({ field }) => (
												<FormItem className="md:col-span-2">
													<FormLabel>
														Client Private Key
													</FormLabel>
													<FormControl>
														<Textarea
															placeholder={
																isEditing
																	? "Leave empty to keep current key"
																	: "-----BEGIN PRIVATE KEY-----..."
															}
															className="min-h-[100px] font-mono text-xs"
															{...field}
															disabled={
																isSubmitting
															}
														/>
													</FormControl>
													<FormDescription>
														{isEditing
															? "Leave empty to keep current private key"
															: "PEM-encoded client private key"}
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>
							</CollapsibleContent>
						</Collapsible>
					</form>
				</Form>
			</CardContent>
			<CardFooter className="flex gap-2">
				{onCancel && (
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
				)}
				<Button
					type="button"
					variant="outline"
					onClick={onTestConnection}
					disabled={isSubmitting || isTestingFormConnection}
				>
					{isTestingFormConnection && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					<TestTube className="mr-2 h-4 w-4" />
					Test Connection
				</Button>
				<Button
					type="submit"
					form="smtp-configuration-form"
					disabled={isSubmitting || isTestingFormConnection}
				>
					{isSubmitting && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					{isEditing ? "Update Configuration" : "Save Configuration"}
				</Button>
			</CardFooter>
		</Card>
	);
}
