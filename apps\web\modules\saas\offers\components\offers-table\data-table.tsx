"use client";

import { useOffersUI } from "@saas/offers/context/offers-ui-context";
import type { Offer } from "@saas/offers/lib/types";
import { DataTable } from "@saas/shared/components/data-table";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { useOffers } from "../../hooks/use-offer";
import { useColumns } from "./columns";

// Delete dialog component
function DeleteOfferDialog() {
	const t = useTranslations();
	const {
		deleteOffer,
		isDeleteDialogOpen,
		handleCancelDelete,
		handleConfirmDelete,
		focusedDeleteButton,
		handleSetDeleteButtonFocus,
		cancelRef,
		confirmRef,
		declineReason,
		setDeclineReason,
	} = useOffersUI();

	// Focus management effect
	useEffect(() => {
		if (!isDeleteDialogOpen) {
			return;
		}

		if (focusedDeleteButton === "cancel") {
			cancelRef.current?.focus();
		} else {
			confirmRef.current?.focus();
		}
	}, [focusedDeleteButton, isDeleteDialogOpen, cancelRef, confirmRef]);

	if (!deleteOffer) {
		return null;
	}

	return (
		<AlertDialog
			open={isDeleteDialogOpen}
			onOpenChange={(open: boolean) => {
				if (!open) {
					handleCancelDelete();
				}
			}}
		>
			<AlertDialogContent data-shortcuts-scope="offers-delete-dialog">
				<AlertDialogHeader>
					<AlertDialogTitle>Cancel Offer</AlertDialogTitle>
					<AlertDialogDescription asChild>
						<div className="space-y-2 text-sm text-muted-foreground">
							Are you sure you want to cancel this offer? This
							action cannot be undone.
						</div>
					</AlertDialogDescription>
					<div className="mt-2 font-medium text-foreground">
						{deleteOffer.order?.order_number || deleteOffer.orderId}{" "}
						- {deleteOffer.status || "Unknown"}
						<span className="block text-sm text-muted-foreground">
							{deleteOffer.customer?.nameLine1 ||
								"Unknown customer"}
						</span>
					</div>
				</AlertDialogHeader>

				<div className="my-4">
					<label
						htmlFor="decline-reason"
						className="block text-sm font-medium mb-2"
					>
						Decline Reason (optional)
					</label>
					<textarea
						id="decline-reason"
						className="w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
						placeholder="Enter a reason for cancelling this offer..."
						rows={3}
						value={declineReason || ""}
						onChange={(e) =>
							setDeclineReason(e.target.value || null)
						}
					/>
				</div>

				<AlertDialogFooter>
					<div className="flex gap-2">
						<AlertDialogCancel
							ref={cancelRef}
							onClick={handleCancelDelete}
							onFocus={() => handleSetDeleteButtonFocus("cancel")}
						>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							ref={confirmRef}
							className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
							onClick={handleConfirmDelete}
							onFocus={() =>
								handleSetDeleteButtonFocus("confirm")
							}
						>
							Confirm
						</AlertDialogAction>
					</div>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
}

// Table component with the delete dialog
export function OffersDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
		status,
		setStatus,
		dateRange,
		setDateRange,
	} = useOffers();
	const { setSelectedOffer } = useOffersUI();
	const columns = useColumns();
	const offers = (data?.items ?? []) as Offer[];
	const t = useTranslations();

	return (
		<>
			<DataTable
				columns={columns}
				data={offers}
				defaultColumnVisibility={{
					id: false,
					createdAt: false,
					accepted_at: false,
					declined_at: false,
				}}
				onSearch={setSearch}
				searchValue={search}
				searchPlaceholder="Search offers..."
				pagination={{
					page,
					setPage,
					pageSize,
					setPageSize,
					totalPages: data?.totalPages ?? 1,
					total: data?.total ?? 0,
				}}
				isLoading={isLoading}
				sorting={sorting}
				onSortingChange={setSorting}
				manualSorting={true}
				shortcutsScope="offers-shortcuts"
			/>
			<DeleteOfferDialog />
		</>
	);
}
