import type {
	Address as <PERSON><PERSON><PERSON><PERSON><PERSON>,
	AddressUsage as DbAddressUsage,
} from "@repo/database";
import { Building, MapPin, PackageCheck, Truck } from "lucide-react";

// API response types - extends DB types with related data
interface AddressUsageWithAddress extends DbAddressUsage {
	address: DbAddress;
	loadingConfig?: any;
	unloadingConfig?: any;
}

// Simplified address type for component usage
interface SimpleAddress extends DbAddress {
	isDefault?: boolean;
	type: "PRIMARY" | "LOADING" | "UNLOADING";
}

/**
 * Select the best primary address based on simplified priority:
 * 1. PRIMARY + isDefault
 * 2. PRIMARY (any)
 */
export function selectBestPrimaryAddress(
	addresses: AddressUsageWithAddress[],
): AddressUsageWithAddress | null {
	if (addresses.length === 0) return null;

	// Find PRIMARY + isDefault
	let address = addresses.find(
		(addr) => addr.type === "PRIMARY" && addr.isDefault,
	);
	if (address) return address;

	// Find any PRIMARY
	address = addresses.find((addr) => addr.type === "PRIMARY");
	if (address) return address;

	// Return first available if none match our criteria
	return addresses[0] || null;
}

/**
 * Format address for display in a consistent way
 * Can handle both direct address and address usage objects
 */
export function formatAddressForDisplay(
	address:
		| DbAddress
		| AddressUsageWithAddress
		| SimpleAddress
		| { address: DbAddress },
): string {
	// Handle AddressUsage or any object with nested address
	let addressData: any;

	if ("address" in address && typeof address.address === "object") {
		addressData = address.address;
	} else {
		addressData = address;
	}

	const parts = [];
	if (addressData.street) parts.push(addressData.street);
	if (addressData.zipCode && addressData.city) {
		parts.push(`${addressData.zipCode} ${addressData.city}`);
	} else if (addressData.city) {
		parts.push(addressData.city);
	}
	if (addressData.country) parts.push(addressData.country);

	return parts.join(", ") || "Address";
}

/**
 * Get address type icon component
 */
export function getAddressTypeIcon(type: string) {
	switch (type) {
		case "PRIMARY":
			return Building;
		case "LOADING":
			return Truck;
		case "UNLOADING":
			return PackageCheck;
		default:
			return MapPin;
	}
}

/**
 * Get address type badge class with dark mode support
 */
export function getAddressTypeBadgeClass(type: string) {
	switch (type) {
		case "PRIMARY":
			return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
		case "LOADING":
			return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
		case "UNLOADING":
			return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
		default:
			return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
	}
}

/**
 * Get address type display properties (for backward compatibility)
 * @deprecated Use getAddressTypeIcon and getAddressTypeBadgeClass instead
 */
export function getAddressTypeInfo(type: string) {
	switch (type) {
		case "PRIMARY":
			return {
				icon: "Building",
				badgeClass:
					"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
				label: "Primary",
			};
		case "LOADING":
			return {
				icon: "Truck",
				badgeClass:
					"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
				label: "Loading",
			};
		case "UNLOADING":
			return {
				icon: "PackageCheck",
				badgeClass:
					"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
				label: "Unloading",
			};
		default:
			return {
				icon: "MapPin",
				badgeClass:
					"bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
				label: "Address",
			};
	}
}

export type { DbAddress as Address, AddressUsageWithAddress, SimpleAddress };
