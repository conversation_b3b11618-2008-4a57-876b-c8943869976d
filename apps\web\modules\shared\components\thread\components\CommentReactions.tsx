"use client";

import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";

interface Reaction {
	emoji: string;
	count: number;
	hasReacted: boolean;
	users?: Array<{ id: string; name: string }>;
}

interface CommentReactionsProps {
	reactions: Reaction[];
	onReactionClick: (emoji: string, hasReacted: boolean) => void;
	disabled?: boolean;
}

export function CommentReactions({
	reactions,
	onReactionClick,
	disabled = false,
}: CommentReactionsProps) {
	if (!reactions || reactions.length === 0) {
		return null;
	}

	return (
		<div className="flex flex-wrap gap-1 mt-2">
			{reactions.map((reaction) => (
				<Button
					key={reaction.emoji}
					variant="outline"
					size="sm"
					className={cn(
						"h-6 px-2 py-0 text-xs rounded-full border transition-colors",
						reaction.hasReacted
							? "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
							: "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100",
					)}
					onClick={() =>
						onReactionClick(reaction.emoji, reaction.hasReacted)
					}
					disabled={disabled}
				>
					<span className="mr-1">{reaction.emoji}</span>
					<span className="font-medium">{reaction.count}</span>
				</Button>
			))}
		</div>
	);
}
