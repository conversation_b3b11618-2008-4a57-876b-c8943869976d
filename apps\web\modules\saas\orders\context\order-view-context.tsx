"use client";
import { useOrderLineItems } from "@saas/orders/hooks/use-line-items";
import { useOrderById } from "@saas/orders/hooks/use-orders";
import { useUpdateOrderMutation } from "@saas/orders/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { useRouter } from "@shared/hooks/router";
import { useTranslations } from "next-intl";
import { useParams, usePathname } from "next/navigation";
import React, {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useLayoutEffect,
	useMemo,
	useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";

// Use useLayoutEffect on client, useEffect on server
const useIsomorphicLayoutEffect =
	typeof window !== "undefined" ? useLayoutEffect : useEffect;

// Define extended order type that includes lineItems
type OrderWithLineItems = ReturnType<typeof useOrderById>["data"];

// Extract the LineItem type from the response
type LineItemResponse = NonNullable<
	ReturnType<typeof useOrderLineItems>["data"]
>["items"][number];

// Get the line item object from inside the response
type LineItem = NonNullable<LineItemResponse>;

interface OrderViewContextProps {
	orderId: string;
	activeTab: string;
	setActiveTab: (tab: string) => void;
	isLoading: boolean;
	order: OrderWithLineItems;
	refreshOrder: () => void;
	lineItems: LineItem[] | null;
	lineItemsLoading: boolean;
	// Edit mode related
	isEditMode: boolean;
	toggleEditMode: () => void;
	fieldChanges: Partial<OrderWithLineItems>;
	updateField: (fieldName: string, value: any) => void;
	saveChanges: () => Promise<void>;
	cancelEdit: () => void;
	isSaving: boolean;
}

const OrderViewContext = createContext<OrderViewContextProps | undefined>(
	undefined,
);

export function OrderViewProvider({
	children,
}: {
	children: React.ReactNode;
}) {
	const t = useTranslations();
	// Start with all useContext calls in the same order
	const params = useParams<{ orderId: string; organizationSlug: string }>();
	const pathname = usePathname();
	const router = useRouter();
	const orderId = params.orderId as string;
	const { activeOrganization } = useActiveOrganization();
	const { registerScopeWithShortcuts, activeScopes, enableScope } =
		useShortcuts();

	// Define tabs in order for arrow navigation
	const tabs = ["details", "items", "stops", "damages", "activity"];

	// Group all useState calls together
	const [activeTab, setActiveTab] = useState<string>("details");
	const [isEditMode, setIsEditMode] = useState<boolean>(false);
	const [fieldChanges, setFieldChanges] = useState<
		Partial<OrderWithLineItems>
	>({});

	// Fetch the specific order by ID using the hook
	const { data: order, isLoading, refetch } = useOrderById(orderId);

	// Fetch line items for the order using the new hook
	const { data: lineItemsData, isLoading: lineItemsLoading } =
		useOrderLineItems(orderId);

	// Get the update mutation
	const updateOrderMutation = useUpdateOrderMutation(
		activeOrganization?.id || "",
	);
	const isSaving = updateOrderMutation.isPending;

	// Keep all useCallback hooks together in a consistent order
	// Update URL when tab changes - client side only
	const updateTab = useCallback(
		(tab: string) => {
			setActiveTab(tab);
			// This only runs on the client
			window.history.pushState(null, "", `${pathname}#${tab}`);
		},
		[pathname],
	);

	// Edit mode related functions - Keep all useCallback together
	const toggleEditMode = useCallback(() => {
		// Only allow editing in the details tab
		if (activeTab !== "details") {
			return;
		}

		setIsEditMode((prev) => {
			// If we're exiting edit mode, reset changes
			if (prev) {
				setFieldChanges({});
			}
			return !prev;
		});
	}, [activeTab]);

	// Update a specific field in the change tracker
	const updateField = useCallback(
		(fieldName: string, value: any) => {
			// Only allow updates in the details tab
			if (activeTab !== "details") {
				return;
			}

			setFieldChanges((prev) => ({
				...prev,
				[fieldName]: value,
			}));
		},
		[activeTab],
	);

	// Submit all changes
	const saveChanges = useCallback(async () => {
		// Only allow saving in the details tab
		if (activeTab !== "details") {
			return;
		}

		try {
			if (!orderId || !activeOrganization?.id) {
				throw new Error("Missing order ID or organization ID");
			}

			// Submit changes to the API
			await updateOrderMutation.mutateAsync({
				id: orderId,
				...fieldChanges,
			} as any);

			// Reset edit state
			setIsEditMode(false);
			setFieldChanges({});

			// Refresh the data
			await refetch();
		} catch (error) {
			console.error("Error saving changes:", error);
			// Handle error (show toast, etc.)
		}
	}, [
		activeTab,
		fieldChanges,
		orderId,
		activeOrganization?.id,
		updateOrderMutation,
		refetch,
	]);

	// Cancel editing and discard changes
	const cancelEdit = useCallback(() => {
		// Only allow canceling in the details tab
		if (activeTab !== "details") {
			return;
		}

		setIsEditMode(false);
		setFieldChanges({});
	}, [activeTab]);

	// Register shortcuts for the order view - update to re-register on tab changes
	useEffect(() => {
		// Always ensure we have the order-view scope registered
		const cleanup = registerScopeWithShortcuts("order-view");

		// When tab changes, make sure the scope is enabled
		enableScope("order-view");

		// Important to clean up when the component unmounts
		return cleanup;
	}, [registerScopeWithShortcuts, enableScope, activeTab]);

	// Use hotkeys for navigating tabs (ArrowRight and ArrowLeft)
	useHotkeys(
		"arrowright",
		(e) => {
			const currentIndex = tabs.indexOf(activeTab);
			if (currentIndex !== -1) {
				const nextIndex = (currentIndex + 1) % tabs.length;
				updateTab(tabs[nextIndex]);
			}
			e.preventDefault();
		},
		{
			enabled: activeScopes.includes("order-view"),
			enableOnFormTags: false,
		},
		[activeTab, tabs, updateTab, activeScopes],
	);

	useHotkeys(
		"arrowleft",
		(e) => {
			const currentIndex = tabs.indexOf(activeTab);
			if (currentIndex !== -1) {
				const prevIndex =
					(currentIndex - 1 + tabs.length) % tabs.length;
				updateTab(tabs[prevIndex]);
			}
			e.preventDefault();
		},
		{
			enabled: activeScopes.includes("order-view"),
			enableOnFormTags: false,
		},
		[activeTab, tabs, updateTab, activeScopes],
	);

	// Toggle edit mode with Ctrl+E (only on details tab)
	useHotkeys(
		"ctrl+e",
		(e) => {
			if (activeTab === "details") {
				toggleEditMode();
				e.preventDefault();
			}
		},
		{
			enabled:
				activeScopes.includes("order-view") && activeTab === "details",
			enableOnFormTags: true,
		},
		[toggleEditMode, activeTab, activeScopes],
	);

	// Save changes with Ctrl+S (only on details tab and in edit mode)
	useHotkeys(
		"ctrl+s",
		(e) => {
			if (activeTab === "details" && isEditMode && !isSaving) {
				saveChanges();
				e.preventDefault();
			}
		},
		{
			enabled:
				activeScopes.includes("order-view") &&
				activeTab === "details" &&
				isEditMode,
			enableOnFormTags: true,
		},
		[saveChanges, activeTab, isEditMode, isSaving, activeScopes],
	);

	// Cancel edit with Escape (only on details tab and in edit mode)
	useHotkeys(
		"escape",
		(e) => {
			// Check if we're focused in an input field
			const activeElement = document.activeElement;
			const isInputField =
				activeElement instanceof HTMLInputElement ||
				activeElement instanceof HTMLTextAreaElement ||
				activeElement instanceof HTMLSelectElement;

			if (activeTab === "details" && isEditMode) {
				// Always cancel edit mode when Escape is pressed
				cancelEdit();
				e.preventDefault();
			} else if (!isEditMode) {
				// If not in edit mode, navigate back to order list
				const organizationSlug = params.organizationSlug as string;
				router.push(`/app/${organizationSlug}/orders`);
				e.preventDefault();
			}
		},
		{
			enabled: activeScopes.includes("order-view"),
			// We want this to work even when in form fields
			enableOnFormTags: true,
		},
		[
			cancelEdit,
			activeTab,
			isEditMode,
			activeScopes,
			router,
			params.organizationSlug,
		],
	);

	// Sync with URL hash on initial load - client side only
	useIsomorphicLayoutEffect(() => {
		const hash = window.location.hash.replace("#", "");
		if (hash && tabs.includes(hash)) {
			setActiveTab(hash);
		}
	}, [tabs]);

	// Create the final context value object after all hooks
	const contextValue = useMemo(
		() => ({
			orderId,
			activeTab,
			setActiveTab: updateTab,
			isLoading,
			order,
			refreshOrder: refetch,
			// Get the line item objects
			lineItems: (lineItemsData?.items as LineItem[]) || null,
			lineItemsLoading,
			isEditMode,
			toggleEditMode,
			fieldChanges,
			updateField,
			saveChanges,
			cancelEdit,
			isSaving,
		}),
		[
			orderId,
			activeTab,
			updateTab,
			isLoading,
			order,
			refetch,
			lineItemsData,
			lineItemsLoading,
			isEditMode,
			toggleEditMode,
			fieldChanges,
			updateField,
			saveChanges,
			cancelEdit,
			isSaving,
		],
	);

	return (
		<OrderViewContext.Provider value={contextValue}>
			{children}
		</OrderViewContext.Provider>
	);
}

export function useOrderView() {
	const context = useContext(OrderViewContext);
	if (!context) {
		throw new Error("useOrderView must be used within a OrderViewProvider");
	}
	return context;
}
