"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useVehiclesQuery } from "@saas/vehicles/lib/api";
import { useDebounce } from "@shared/hooks/use-debounce";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { Car, Check, ChevronDown, HelpCircle, Truck, X } from "lucide-react";
import { useEffect, useId, useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

// Define vehicle type
export type VehicleType = "TRUCK" | "TRAILER" | "";

// Custom Trailer SVG Icon
function TrailerIcon({ className }: { className?: string }) {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
			strokeLinejoin="round"
			className={className}
			aria-label="Trailer icon"
			role="img"
		>
			<title>Trailer</title>
			<rect x="4" y="5" width="16" height="10" rx="1" />
			<line x1="4" y1="15" x2="4" y2="17" />
			<line x1="20" y1="15" x2="20" y2="17" />
			<circle cx="8" cy="17" r="2" />
			<circle cx="16" cy="17" r="2" />
			<line x1="10" y1="5" x2="14" y2="5" />
		</svg>
	);
}

// Define vehicle structure based on actual API response
interface Vehicle {
	id: string;
	vehicleType: VehicleType;
	attachedTo?: {
		id: string;
		licensePlate: string | null;
		manufacturer: string | null;
		model?: string | null;
		licensePlateNumber?: string | null;
		vehicleIdentificationNumber?: string | null;
	} | null;
	licensePlate?: string | null;
	manufacturer?: string | null;
	model?: string | null;
	vehicleIdentificationNumber?: string | null;
	isActiveInDispatch?: boolean;

	// Physical Dimensions & Capacity
	length?: number | null;
	width?: number | null;
	height?: number | null;
	loadingWeight?: number | null;
	euroPalletsAmount?: number | null;
}

// Query keys for vehicles
const vehicleKeys = {
	all: ["vehicles"] as const,
	list: (params: any) => [...vehicleKeys.all, "list", params] as const,
};

export interface VehicleSelectorProps {
	value?: string;
	onChange?: (value: string) => void;
	onDataFetched?: (data: any) => void;
	name: string;
	label?: string;
	tooltip?: string;
	placeholder?: string;
	type?: VehicleType;
	disabled?: boolean;
	error?: string;
	className?: string;
	isLoading?: boolean;
	allowClear?: boolean;
	showInactive?: boolean;
}

/**
 * A reusable Vehicle selector component
 */
export function VehicleSelector({
	value,
	onChange,
	onDataFetched,
	name,
	label,
	tooltip,
	placeholder = "Select vehicle",
	type = "",
	disabled = false,
	error,
	className,
	isLoading: externalIsLoading = false,
	allowClear = false,
	showInactive = false,
}: VehicleSelectorProps): JSX.Element {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id || "";
	const [open, setOpen] = useState(false);
	const [search, setSearch] = useState("");
	const debouncedSearch = useDebounce(search, 300);
	const buttonRef = useRef<HTMLButtonElement>(null);
	const searchInputRef = useRef<HTMLInputElement>(null);
	const dropdownContentRef = useRef<HTMLDivElement>(null);
	const shouldFocusRef = useRef(false);
	const uniqueId = useId();
	const searchInputId = `vehicle-search-${uniqueId}`;

	// Reset search when closing the dropdown
	useEffect(() => {
		if (!open) {
			setSearch("");
		} else {
			// Flag that we should focus when the dropdown opens
			shouldFocusRef.current = true;
		}
	}, [open]);

	// Fetch vehicles with search and filtering
	const { data, isLoading: isLoadingData } = useVehiclesQuery({
		organizationId,
		search: debouncedSearch,
		limit: 50,
		isActiveInDispatch: showInactive ? undefined : true,
	});

	// Filter vehicles by type client-side
	const filteredVehicles: Vehicle[] = type
		? data?.items?.filter(
				(vehicle: Vehicle) => vehicle.vehicleType === type,
			) || []
		: data?.items || [];

	// Pass the fetched data to parent if needed
	useEffect(() => {
		if (data && onDataFetched) {
			onDataFetched(data);
		}
	}, [data, onDataFetched]);

	// Multi-approach focus strategy
	useEffect(() => {
		if (!open || !shouldFocusRef.current) {
			return;
		}

		// Schedule multiple focus attempts with increasing delays
		const attempts = [10, 50, 100, 250, 500];

		const focusAttempts = attempts.map((delay) =>
			setTimeout(() => {
				if (searchInputRef.current) {
					searchInputRef.current.focus();
					// Also try to select any existing text
					searchInputRef.current.select();
				} else {
					// As a fallback, try to focus by ID
					const inputEl = document.getElementById(searchInputId);
					inputEl?.focus();
				}
			}, delay),
		);

		shouldFocusRef.current = false;

		return () => {
			// Clean up all timeouts
			focusAttempts.forEach(clearTimeout);
		};
	}, [open, searchInputId]);

	// Find the selected vehicle
	const selectedVehicle = filteredVehicles.find((item) => item.id === value);

	// Combined loading state
	const isLoading = externalIsLoading || isLoadingData;

	// Helper to handle clearing selection
	const handleClearSelection = (e: React.MouseEvent) => {
		e.stopPropagation();
		onChange?.("");
	};

	// Helper to get vehicle icon
	const getVehicleIcon = (vehicle: Vehicle) => {
		if (vehicle.vehicleType === "TRUCK") {
			return <Truck className="h-4 w-4 mr-2" />;
		}
		if (vehicle.vehicleType === "TRAILER") {
			return <TrailerIcon className="h-4 w-4 mr-2" />;
		}
		return <Car className="h-4 w-4 mr-2" />;
	};

	// Handle keyboard navigation
	const handleKeyDown = (e: React.KeyboardEvent) => {
		// Close the dropdown menu when pressing Escape
		if (e.key === "Escape") {
			e.preventDefault();
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle search input
	const handleSearchInputChange = (
		e: React.ChangeEvent<HTMLInputElement>,
	) => {
		setSearch(e.target.value);
	};

	// Prevent dropdown from stealing focus from search input
	const handleSearchInputKeyDown = (
		e: React.KeyboardEvent<HTMLInputElement>,
	) => {
		// Prevent propagation to stop the dropdown from handling these events
		e.stopPropagation();

		// Still close on Escape
		if (e.key === "Escape") {
			setOpen(false);
			buttonRef.current?.focus();
		}
	};

	// Handle dropdown trigger click, to set focus flag
	const handleTriggerClick = () => {
		shouldFocusRef.current = true;
	};

	// Handle wheel events in the dropdown content to ensure scrolling works
	const handleWheel = (e: React.WheelEvent) => {
		// Don't block the wheel event, let it propagate naturally
		e.stopPropagation();
	};

	// Render the selected vehicle card when we have a value and the data is loaded
	if (value && selectedVehicle && !isLoading) {
		return (
			<div className={cn("space-y-2", className)}>
				<div className="relative">
					<Card className="w-full">
						<CardContent className="p-4">
							<div className="flex flex-col items-start">
								<div className="flex justify-between w-full items-center">
									<div className="flex items-center font-medium text-base">
										{getVehicleIcon(selectedVehicle)}
										{selectedVehicle.licensePlate ||
											"No registration"}
									</div>
									{allowClear && (
										<button
											type="button"
											className="h-6 w-6 rounded-md inline-flex items-center justify-center text-muted-foreground hover:bg-accent hover:text-accent-foreground"
											onClick={handleClearSelection}
											aria-label="Clear selection"
										>
											<X className="h-4 w-4" />
										</button>
									)}
								</div>
								{selectedVehicle.manufacturer && (
									<div className="mt-1 text-sm text-muted-foreground">
										{selectedVehicle.manufacturer}{" "}
										{selectedVehicle.model}
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				</div>

				{error && (
					<p className="text-sm font-medium text-destructive">
						{error}
					</p>
				)}
			</div>
		);
	}

	// Otherwise, render the dropdown selector
	return (
		<div className={cn("space-y-2", className)}>
			<DropdownMenu open={open} onOpenChange={setOpen} modal={false}>
				<DropdownMenuTrigger asChild disabled={disabled || isLoading}>
					<Button
						ref={buttonRef}
						variant="outline"
						className={cn(
							"w-full justify-between p-4 h-auto text-left",
							!selectedVehicle && "text-muted-foreground",
						)}
						onClick={handleTriggerClick}
					>
						{isLoading ? (
							<div>Loading...</div>
						) : (
							<div className="flex justify-between w-full items-center">
								<span>{placeholder}</span>
								<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
							</div>
						)}
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					ref={dropdownContentRef}
					className="p-0 max-h-[300px] overflow-auto"
					align="start"
					sideOffset={4}
					style={{
						width: "var(--radix-dropdown-menu-trigger-width)",
					}}
					role="menu"
					aria-orientation="vertical"
					aria-label="Vehicles list"
					onKeyDown={handleKeyDown}
					onWheel={handleWheel}
				>
					<div className="p-2 border-b">
						<Input
							id={searchInputId}
							ref={searchInputRef}
							placeholder="Search vehicles..."
							value={search}
							onChange={handleSearchInputChange}
							onKeyDown={handleSearchInputKeyDown}
							onClick={(e) => e.stopPropagation()}
							className="h-8"
							autoFocus
							// Force autofocus
							onFocus={(e) => e.currentTarget.select()}
						/>
					</div>
					<div className="overflow-y-auto max-h-[250px]">
						{!filteredVehicles.length ? (
							<div className="py-6 text-center">
								<p className="text-sm text-muted-foreground">
									No vehicles found.
								</p>
							</div>
						) : (
							<>
								{filteredVehicles.map((vehicle) => (
									<DropdownMenuItem
										key={vehicle.id}
										className={cn(
											"flex flex-col w-full items-start text-left px-3 py-2 cursor-pointer",
											value === vehicle.id
												? "bg-accent/5 text-accent-foreground"
												: "",
											"focus:bg-accent/10 focus:text-accent-foreground/90",
										)}
										onSelect={() => {
											onChange?.(vehicle.id);
											setOpen(false); // Close dropdown after selection
										}}
										role="menuitemradio"
										aria-checked={value === vehicle.id}
									>
										<div className="flex w-full items-center justify-between">
											<div className="flex items-center font-medium">
												{getVehicleIcon(vehicle)}
												{vehicle.licensePlate ||
													"No registration"}
											</div>
											<div className="flex items-center gap-2">
												<Badge
													status={
														vehicle.vehicleType ===
														"TRUCK"
															? "info"
															: "success"
													}
													className="text-xs capitalize"
												>
													{vehicle.vehicleType}
												</Badge>
												{value === vehicle.id && (
													<Check className="h-4 w-4 text-primary" />
												)}
											</div>
										</div>
										<div className="mt-1 text-xs text-muted-foreground">
											{vehicle.manufacturer}{" "}
											{vehicle.model}
											{vehicle.vehicleIdentificationNumber && (
												<span className="ml-2">
													VIN:{" "}
													{
														vehicle.vehicleIdentificationNumber
													}
												</span>
											)}
										</div>
									</DropdownMenuItem>
								))}
							</>
						)}
					</div>
				</DropdownMenuContent>
			</DropdownMenu>

			{error && (
				<p className="text-sm font-medium text-destructive">{error}</p>
			)}
		</div>
	);
}

/**
 * A form-connected version of the Vehicle selector for use with react-hook-form
 */
export function FormVehicleSelector({
	name,
	label,
	tooltip,
	placeholder = "Select vehicle",
	type = "",
	disabled = false,
	className,
	isLoading = false,
	allowClear = false,
	showInactive = false,
}: Omit<VehicleSelectorProps, "onChange" | "value" | "error">): JSX.Element {
	const form = useFormContext();

	if (!form) {
		throw new Error(
			"FormVehicleSelector must be used within a FormProvider",
		);
	}

	return (
		<FormField
			control={form.control}
			name={name}
			render={({ field }) => (
				<FormItem className={className}>
					{label && (
						<FormLabel
							htmlFor={name}
							className="flex items-center gap-1"
						>
							{label}
							{tooltip && (
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<HelpCircle className="h-3.5 w-3.5 text-muted-foreground cursor-help" />
										</TooltipTrigger>
										<TooltipContent>
											<p>{tooltip}</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							)}
						</FormLabel>
					)}
					<FormControl>
						<VehicleSelector
							onChange={field.onChange}
							value={field.value}
							name={name}
							placeholder={placeholder}
							type={type}
							disabled={disabled}
							isLoading={isLoading}
							allowClear={allowClear}
							showInactive={showInactive}
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
