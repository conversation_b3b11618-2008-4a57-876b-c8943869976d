"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import Microsoft from "@ui/components/icons/microsoft";
import { Input } from "@ui/components/input";
import { Loader2, TestTube } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
	useMemberConnectionTest,
	useMemberEmailConfiguration,
	useMemberEmailConfigurationCreation,
	useMemberEmailConfigurationUpdate,
	useMemberOAuth2Authentication,
} from "../../hooks/use-member-email-configuration";
import { useSmtpConfiguration } from "../../hooks/use-smtp-configuration";

// Simplified validation schema for member email configuration
const memberEmailConfigurationSchema = z
	.object({
		// Personal email identity
		fromEmail: z.string().email("Please enter a valid email address"),
		fromName: z.string().optional(),

		// Authentication credentials
		useOAuth2: z.boolean().default(false),
		username: z.string().min(1, "Username is required"),
		password: z.string().optional(),

		// OAuth2 fields
		refreshToken: z.string().optional(),
		accessToken: z.string().optional(),
		tokenExpires: z.date().optional(),
	})
	.refine(
		(data) => {
			// For OAuth2: require access token
			if (data.useOAuth2) {
				return data.accessToken && data.accessToken.length > 0;
			}
			// For password auth: require password
			return data.password && data.password.length > 0;
		},
		{
			message:
				"Either complete OAuth2 authentication or provide password",
			path: ["password"],
		},
	);

type MemberEmailConfigurationFormValues = z.infer<
	typeof memberEmailConfigurationSchema
>;

interface MemberEmailConfigurationFormProps {
	onCancel?: () => void;
	onSuccess?: () => void;
}

export function MemberEmailConfigurationForm({
	onCancel,
	onSuccess,
}: MemberEmailConfigurationFormProps) {
	const { data: orgSmtpConfig } = useSmtpConfiguration();
	const { data: existingConfig } = useMemberEmailConfiguration();
	const { createMemberEmailConfiguration, isSubmitting: isCreating } =
		useMemberEmailConfigurationCreation();
	const { updateMemberEmailConfiguration, isSubmitting: isUpdating } =
		useMemberEmailConfigurationUpdate();
	const { testMemberConnection, isTestingConnection } =
		useMemberConnectionTest();
	const { initiateMemberOAuth2Flow, isAuthenticating } =
		useMemberOAuth2Authentication();

	const isEditing = !!existingConfig;
	const isSubmitting = isCreating || isUpdating;

	// Check if organization uses OAuth2
	const orgUsesOAuth2 = orgSmtpConfig?.useOAuth2 || false;

	const form = useForm<MemberEmailConfigurationFormValues>({
		resolver: zodResolver(memberEmailConfigurationSchema),
		defaultValues: existingConfig
			? {
					fromEmail: existingConfig.fromEmail,
					fromName: existingConfig.fromName || "",
					username: existingConfig.username || "",
					password: "", // Don't populate password for security
					useOAuth2: false, // Will be determined by organization settings
					refreshToken: "", // Don't populate for security
					accessToken: "", // Don't populate for security
					tokenExpires: existingConfig.tokenExpires
						? new Date(existingConfig.tokenExpires)
						: undefined,
				}
			: {
					fromEmail: "",
					fromName: "",
					username: "",
					password: "",
					useOAuth2: orgUsesOAuth2,
					refreshToken: "",
					accessToken: "",
					tokenExpires: undefined,
				},
	});

	const onSubmit = async (values: MemberEmailConfigurationFormValues) => {
		if (isEditing) {
			await updateMemberEmailConfiguration(values);
		} else {
			await createMemberEmailConfiguration(values);
		}

		if (!isEditing) {
			form.reset();
		}

		onSuccess?.();
	};

	const onTestConnection = async () => {
		const values = form.getValues();
		const isValid = await form.trigger();

		if (!isValid) {
			return;
		}

		testMemberConnection({
			...values,
		});
	};

	const handleOAuth2Flow = async () => {
		try {
			const result = await initiateMemberOAuth2Flow("microsoft");

			// Update form with OAuth2 tokens
			if (result.userEmail) {
				form.setValue("username", result.userEmail);

				// Set fromEmail to authenticated user's email if not already set
				if (!form.getValues("fromEmail")) {
					form.setValue("fromEmail", result.userEmail);
				}
			}
			form.setValue("accessToken", result.accessToken);
			form.setValue("refreshToken", result.refreshToken);
			if (result.tokenExpires) {
				form.setValue("tokenExpires", new Date(result.tokenExpires));
			}
			form.setValue("useOAuth2", true);
		} catch (error) {
			console.error("OAuth2 flow failed:", error);
		}
	};

	// If organization doesn't have SMTP config, show message
	if (!orgSmtpConfig) {
		return (
			<Card className="w-full">
				<CardHeader>
					<CardTitle>Member Email Configuration</CardTitle>
					<CardDescription>
						Your organization needs to configure SMTP settings first
						before you can set up your personal email configuration.
					</CardDescription>
				</CardHeader>
				<CardContent>
					<p className="text-sm text-muted-foreground">
						Please ask your organization administrator to configure
						the organization's SMTP settings.
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>
					{isEditing
						? "Update My Email Configuration"
						: "Set Up My Email Configuration"}
				</CardTitle>
				<CardDescription>
					Configure your personal email credentials. You'll inherit
					the organization's SMTP server settings.
				</CardDescription>
			</CardHeader>
			<CardContent>
				{/* Organization SMTP Settings (Read-only) */}
				<div className="mb-6 p-4 bg-muted/50 rounded-lg">
					<h4 className="text-sm font-medium mb-3">
						Inherited Organization Settings
					</h4>
					<div className="grid grid-cols-2 gap-4 text-sm">
						<div>
							<span className="text-muted-foreground">
								SMTP Host:
							</span>
							<span className="ml-2">{orgSmtpConfig.host}</span>
						</div>
						<div>
							<span className="text-muted-foreground">Port:</span>
							<span className="ml-2">{orgSmtpConfig.port}</span>
						</div>
						<div>
							<span className="text-muted-foreground">
								Security:
							</span>
							<span className="ml-2">
								{orgSmtpConfig.secure
									? "SSL/TLS"
									: orgSmtpConfig.requireTLS
										? "STARTTLS"
										: "None"}
							</span>
						</div>
						<div>
							<span className="text-muted-foreground">
								Auth Method:
							</span>
							<span className="ml-2">
								{orgSmtpConfig.useOAuth2
									? "OAuth2"
									: "Password"}
							</span>
						</div>
					</div>
				</div>

				<Form {...form}>
					<form
						id="member-email-configuration-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						{/* Personal Email Settings */}
						<div className="space-y-4">
							<h4 className="text-sm font-medium">
								Personal Email Settings
							</h4>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="fromEmail"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												My Email Address
											</FormLabel>
											<FormControl>
												<Input
													placeholder="<EMAIL>"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												Email address to send from
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="fromName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												Display Name (Optional)
											</FormLabel>
											<FormControl>
												<Input
													placeholder="Your Name"
													{...field}
													disabled={isSubmitting}
												/>
											</FormControl>
											<FormDescription>
												Name shown in sent emails
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Authentication Credentials */}
						<div className="space-y-4">
							<h4 className="text-sm font-medium">
								Authentication Credentials
							</h4>

							{orgUsesOAuth2 ? (
								// OAuth2 Authentication (inherited from org)
								<div className="space-y-4">
									<Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800">
										<CardContent className="p-6">
											<div className="flex items-center gap-4 mb-4">
												<div className="flex-shrink-0">
													<div className="w-12 h-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm flex items-center justify-center">
														<Microsoft className="w-8 h-8" />
													</div>
												</div>
												<div className="flex-1">
													<h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
														Microsoft Exchange
													</h3>
													<p className="text-sm text-gray-600 dark:text-gray-400">
														Authenticate with your
														Microsoft account
													</p>
												</div>
											</div>

											<p className="text-sm text-gray-700 dark:text-gray-300 mb-4">
												Connect using your Microsoft
												account credentials. This will
												use the same OAuth2 setup as
												your organization.
											</p>

											<Button
												type="button"
												onClick={handleOAuth2Flow}
												disabled={
													isSubmitting ||
													isAuthenticating
												}
												className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white"
											>
												{isAuthenticating && (
													<Loader2 className="mr-2 h-4 w-4 animate-spin" />
												)}
												<Microsoft className="h-4 w-4 mr-2" />
												Connect My Microsoft Account
											</Button>
										</CardContent>
									</Card>

									{/* Show authenticated user email */}
									{form.watch("username") &&
										form.watch("accessToken") && (
											<Card className="bg-green-50 dark:bg-green-950/50 border-green-200 dark:border-green-800">
												<CardContent className="p-4">
													<div className="flex items-center gap-2 mb-2">
														<div className="w-2 h-2 bg-green-500 rounded-full" />
														<span className="text-sm font-medium text-green-800 dark:text-green-200">
															Successfully
															Connected
														</span>
													</div>
													<FormField
														control={form.control}
														name="username"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-green-700 dark:text-green-300">
																	Authenticated
																	Email
																</FormLabel>
																<FormControl>
																	<Input
																		{...field}
																		disabled={
																			true
																		}
																		className="bg-white dark:bg-gray-900 border-green-300 dark:border-green-700"
																	/>
																</FormControl>
																<FormDescription className="text-green-600 dark:text-green-400">
																	This email
																	will be used
																	for SMTP
																	authentication
																</FormDescription>
																<FormMessage />
															</FormItem>
														)}
													/>
												</CardContent>
											</Card>
										)}
								</div>
							) : (
								// Username & Password Authentication
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg bg-muted/20">
									<FormField
										control={form.control}
										name="username"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Username</FormLabel>
												<FormControl>
													<Input
														placeholder="<EMAIL>"
														{...field}
														disabled={isSubmitting}
													/>
												</FormControl>
												<FormDescription>
													Your SMTP username (usually
													your email)
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Password</FormLabel>
												<FormControl>
													<Input
														type="password"
														placeholder={
															isEditing
																? "Leave empty to keep current password"
																: "Your SMTP password or app password"
														}
														{...field}
														disabled={isSubmitting}
													/>
												</FormControl>
												<FormDescription>
													{isEditing
														? "Leave empty to keep current password"
														: "Your SMTP password or app password"}
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							)}
						</div>
					</form>
				</Form>
			</CardContent>
			<CardFooter className="flex gap-2">
				{onCancel && (
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
						disabled={isSubmitting || isAuthenticating}
					>
						Cancel
					</Button>
				)}
				<Button
					type="button"
					variant="outline"
					onClick={onTestConnection}
					disabled={
						isSubmitting || isTestingConnection || isAuthenticating
					}
				>
					{isTestingConnection && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					<TestTube className="mr-2 h-4 w-4" />
					Test Connection
				</Button>
				<Button
					type="submit"
					form="member-email-configuration-form"
					disabled={
						isSubmitting || isTestingConnection || isAuthenticating
					}
				>
					{isSubmitting && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					{isEditing
						? "Update My Configuration"
						: "Save My Configuration"}
				</Button>
			</CardFooter>
		</Card>
	);
}
