import { useInvoiceEmailPreview } from "@saas/invoices/lib/api";
import { useOfferEmailPreview } from "@saas/offers/lib/api";
import { usePreviewOrderConfirmationEmail } from "@saas/orders/lib/api";
import { useTransportOrderOperations } from "@saas/tours/hooks/use-tours";
import { useMemo } from "react";
import { isEmailPreview } from "../lib/email-preview-config";
import type {
	EmailPreviewData,
	UseUniversalEmailPreviewConfig,
	UseUniversalEmailPreviewReturn,
} from "../types/email-preview";

/**
 * Helper function to safely convert API responses to EmailPreviewData
 */
function normalizeEmailPreviewData(data: any): EmailPreviewData | null {
	if (!data) {
		return null;
	}
	if (isEmailPreview(data)) {
		return data;
	}
	return null;
}

/**
 * Universal hook for email previews that abstracts different entity-specific hooks
 */
export function useUniversalEmailPreview({
	entityType,
	entityId,
	organizationId,
	recipientEmail,
	enabled = true,
}: UseUniversalEmailPreviewConfig): UseUniversalEmailPreviewReturn {
	// Invoice email preview
	const invoicePreview = useInvoiceEmailPreview(
		organizationId,
		entityId,
		recipientEmail,
		{ enabled: enabled && entityType === "invoice" },
	);

	// Offer email preview
	const offerPreview = useOfferEmailPreview(
		organizationId,
		entityId,
		recipientEmail,
		{ enabled: enabled && entityType === "offer" },
	);

	// Order confirmation email preview
	const orderPreview = usePreviewOrderConfirmationEmail(
		organizationId,
		entityId,
		recipientEmail,
		{ enabled: enabled && entityType === "order-confirmation" },
	);

	// Transport order email preview (uses different pattern)
	const transportOrderOperations = useTransportOrderOperations(
		entityType === "transport-order" ? entityId : undefined,
		enabled && entityType === "transport-order",
	);

	// Return the appropriate hook result based on entity type
	return useMemo(() => {
		switch (entityType) {
			case "invoice":
				return {
					data: enabled
						? normalizeEmailPreviewData(invoicePreview.data)
						: null,
					isLoading: enabled ? invoicePreview.isLoading : false,
					error: enabled ? invoicePreview.error : null,
					refetch: invoicePreview.refetch,
				};

			case "offer":
				return {
					data: enabled
						? normalizeEmailPreviewData(offerPreview.data)
						: null,
					isLoading: enabled ? offerPreview.isLoading : false,
					error: enabled ? offerPreview.error : null,
					refetch: offerPreview.refetch,
				};

			case "order-confirmation":
				return {
					data: enabled
						? normalizeEmailPreviewData(orderPreview.data)
						: null,
					isLoading: enabled ? orderPreview.isLoading : false,
					error: enabled ? orderPreview.error : null,
					refetch: orderPreview.refetch,
				};

			case "transport-order":
				return {
					data: enabled
						? normalizeEmailPreviewData(
								transportOrderOperations.emailPreview,
							)
						: null,
					isLoading: enabled
						? transportOrderOperations.isLoadingEmailPreview
						: false,
					error: null, // Transport order hook doesn't expose error
					refetch: transportOrderOperations.refetchEmailPreview,
				};

			default:
				return {
					data: null,
					isLoading: false,
					error: new Error(`Unsupported entity type: ${entityType}`),
					refetch: () => Promise.resolve(),
				};
		}
	}, [
		entityType,
		enabled,
		invoicePreview.data,
		invoicePreview.isLoading,
		invoicePreview.error,
		invoicePreview.refetch,
		offerPreview.data,
		offerPreview.isLoading,
		offerPreview.error,
		offerPreview.refetch,
		orderPreview.data,
		orderPreview.isLoading,
		orderPreview.error,
		orderPreview.refetch,
		transportOrderOperations.emailPreview,
		transportOrderOperations.isLoadingEmailPreview,
		transportOrderOperations.refetchEmailPreview,
	]);
}
