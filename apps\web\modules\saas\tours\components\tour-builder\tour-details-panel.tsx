"use client";
import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { VehicleSelector } from "@saas/shared/components/VehicleSelector";
import { TransportOrderPanel } from "@saas/tours/components/tour-builder/transport-order-panel";
import { useTourBuilder } from "@saas/tours/context/tours-planner-context";
import { useVehiclesQuery } from "@saas/vehicles/lib/api";
import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { ScrollArea } from "@ui/components/scroll-area";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib";
import { format, parseISO } from "date-fns";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { TourDocumentsPanel } from "./tour-documents-panel";
import { TourExpensesPanel } from "./tour-expenses-panel";
import { TruckDisplay } from "./truck-display";

export function TourDetailsPanel() {
	const t = useTranslations();
	const { activeOrganization } = useActiveOrganization();
	const {
		tourData,
		updateTourData,
		selectVehicle,
		isSaving,
		mode,
		tourId,
		stops,
		newTourStops,
		vehicleDimensions,
		calculateCargoFill,
		getCurrentLoadingMeters,
	} = useTourBuilder();
	const [selectedVehicleData, setSelectedVehicleData] = useState<any>(null);
	const [selectedTrailerData, setSelectedTrailerData] = useState<any>(null);

	const isEditMode = mode === "edit" && !!tourId;

	// Get status badge variant and label
	const getStatusBadge = () => {
		const status = tourData.status || "draft";

		if (status === "confirmed" || status === "completed") {
			return {
				className: "bg-green-100 text-green-800 hover:bg-green-100",
				label: status.charAt(0).toUpperCase() + status.slice(1),
			};
		}

		if (status === "cancelled") {
			return {
				className: "bg-red-100 text-red-800 hover:bg-red-100",
				label: "Cancelled",
			};
		}

		return {
			className: "bg-gray-100 text-gray-800 hover:bg-gray-100",
			label: "Draft",
		};
	};

	const statusBadge = getStatusBadge();

	const { data: vehiclesData } = useVehiclesQuery({
		organizationId: activeOrganization?.id || "",
		limit: 100,
	});

	// Set organization ID when it becomes available
	useEffect(() => {
		if (activeOrganization?.id && !tourData.organizationId) {
			updateTourData({ organizationId: activeOrganization.id });
		}
	}, [activeOrganization?.id, tourData.organizationId, updateTourData]);

	// Auto-update dates based on first and last stops
	useEffect(() => {
		// Combine regular stops and new tour stops
		const allStops = [...stops, ...newTourStops].sort(
			(a, b) => (a.position || 0) - (b.position || 0),
		);

		if (allStops.length > 0) {
			const firstStop = allStops[0];
			const lastStop = allStops[allStops.length - 1];

			// Set start date from first stop - prefer datetime_start, fallback to datetime_end
			if (firstStop.datetime_start) {
				const startDate =
					firstStop.datetime_start instanceof Date
						? firstStop.datetime_start
						: parseISO(firstStop.datetime_start as string);
				updateTourData({ startDate: startDate.toISOString() });
			} else if (firstStop.datetime_end) {
				const startDate =
					firstStop.datetime_end instanceof Date
						? firstStop.datetime_end
						: parseISO(firstStop.datetime_end as string);
				updateTourData({ startDate: startDate.toISOString() });
			}

			// Set end date from last stop (prefer datetime_end if available, otherwise use datetime_start)
			if (lastStop.datetime_end) {
				const endDate =
					lastStop.datetime_end instanceof Date
						? lastStop.datetime_end
						: parseISO(lastStop.datetime_end as string);
				updateTourData({ endDate: endDate.toISOString() });
			} else if (lastStop.datetime_start) {
				const endDate =
					lastStop.datetime_start instanceof Date
						? lastStop.datetime_start
						: parseISO(lastStop.datetime_start as string);
				updateTourData({ endDate: endDate.toISOString() });
			}
		}
	}, [stops, newTourStops, updateTourData]);

	// This function is kept for reference but will no longer be used directly
	const handleDateChange = (
		field: "startDate" | "endDate",
		value: Date | string | null,
	) => {
		if (value) {
			updateTourData({ [field]: value });
		} else {
			updateTourData({ [field]: null });
		}
	};

	const handleCarrierChange = (carrierId: string) => {
		updateTourData({ carrierId: carrierId || null });
	};

	const handleVehicleChange = async (vehicleId: string) => {
		// Use the centralized function from the context
		await selectVehicle(vehicleId, activeOrganization?.id || "");

		// Update local state for UI purposes
		if (vehicleId && vehiclesData?.items) {
			const selectedVehicle = vehiclesData.items.find(
				(v) => v.id === vehicleId,
			);
			setSelectedVehicleData(selectedVehicle);

			// Update trailer data for local UI state
			const attachedTrailer = vehiclesData.items.find(
				(item) =>
					item.vehicleType === "TRAILER" &&
					item.attachedToId === vehicleId,
			);
			if (attachedTrailer) {
				setSelectedTrailerData(attachedTrailer);
			}
		} else {
			setSelectedVehicleData(null);
		}
	};

	const handleTrailerChange = (trailerId: string) => {
		updateTourData({ trailerVehicleId: trailerId || null });
		if (trailerId && vehiclesData?.items) {
			const selectedTrailer = vehiclesData.items.find(
				(v) => v.id === trailerId,
			);
			setSelectedTrailerData(selectedTrailer);

			// Auto-fill trailer license plate if trailer is selected
			if (selectedTrailer?.licensePlate) {
				updateTourData({
					trailerLicensePlate: selectedTrailer.licensePlate,
				});
			}
		} else {
			// Clear license plate if no trailer is selected
			setSelectedTrailerData(null);
			updateTourData({ trailerLicensePlate: "" });
		}
	};

	const handleLicensePlateChange = (
		field: "truckLicensePlate" | "trailerLicensePlate",
		value: string,
	) => {
		updateTourData({ [field]: value });
	};

	return (
		<Card className="h-full flex flex-col min-h-0">
			<CardHeader className="shrink-0">
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<CardTitle>Tour Details</CardTitle>
						{isEditMode && (
							<Badge className={cn(statusBadge.className)}>
								{statusBadge.label}
							</Badge>
						)}
					</div>
					{(isEditMode && tourData.tourNumber) ||
					tourData.startDate ||
					tourData.endDate ? (
						<div className="flex items-center gap-4 text-sm text-muted-foreground">
							{isEditMode && tourData.tourNumber && (
								<span>#{tourData.tourNumber}</span>
							)}
							{(tourData.startDate || tourData.endDate) && (
								<span>
									{tourData.startDate
										? format(
												new Date(tourData.startDate),
												"dd.MM.yyyy",
											)
										: "__.__.____"}
									{" - "}
									{tourData.endDate
										? format(
												new Date(tourData.endDate),
												"dd.MM.yyyy",
											)
										: "__.__.____"}
								</span>
							)}
						</div>
					) : null}
				</div>
			</CardHeader>
			<CardContent className="p-0 flex-1 min-h-0">
				<ScrollArea className="h-full">
					<div className="space-y-4 px-6">
						{/* Hidden date inputs for form functionality */}
						<div className="hidden">
							<input
								type="hidden"
								name="startDate"
								value={tourData.startDate || ""}
							/>
							<input
								type="hidden"
								name="endDate"
								value={tourData.endDate || ""}
							/>
						</div>
						<div className="space-y-2">
							<label
								htmlFor="carrierId"
								className="text-sm font-medium"
							>
								Carrier
							</label>
							<CounterpartySelector
								name="carrierId"
								value={tourData.carrierId || ""}
								onChange={handleCarrierChange}
								placeholder="Select a carrier"
								type="carrier"
								disabled={isSaving}
								allowClear={true}
							/>
						</div>
						<div className="flex gap-4">
							<div className="space-y-2 flex-1">
								<label
									htmlFor="vehicleId"
									className="text-sm font-medium"
								>
									Truck
								</label>
								<VehicleSelector
									name="vehicleId"
									value={tourData.vehicleId || ""}
									onChange={handleVehicleChange}
									placeholder="Select a truck"
									type="TRUCK"
									disabled={isSaving}
									allowClear={true}
									onDataFetched={setSelectedVehicleData}
								/>
								{!tourData.vehicleId && (
									<Input
										id="truckLicensePlate"
										placeholder="License plate number"
										className="mt-2"
										value={tourData.truckLicensePlate || ""}
										onChange={(e) =>
											handleLicensePlateChange(
												"truckLicensePlate",
												e.target.value,
											)
										}
										disabled={isSaving}
									/>
								)}
							</div>
							<div className="space-y-2 flex-1">
								<label
									htmlFor="trailerVehicleId"
									className="text-sm font-medium"
								>
									Trailer
								</label>
								<VehicleSelector
									name="trailerVehicleId"
									value={tourData.trailerVehicleId || ""}
									onChange={handleTrailerChange}
									placeholder="Select a trailer"
									type="TRAILER"
									disabled={isSaving}
									allowClear={true}
									onDataFetched={setSelectedTrailerData}
								/>
								{!tourData.trailerVehicleId && (
									<Input
										id="trailerLicensePlate"
										placeholder="License plate number"
										className="mt-2"
										value={
											tourData.trailerLicensePlate || ""
										}
										onChange={(e) =>
											handleLicensePlateChange(
												"trailerLicensePlate",
												e.target.value,
											)
										}
										disabled={isSaving}
									/>
								)}
							</div>
						</div>
						<TruckDisplay
							length={vehicleDimensions.length || undefined}
							height={vehicleDimensions.height || undefined}
							cargoFill={calculateCargoFill()}
							currentLoadingMeters={getCurrentLoadingMeters()}
							maxLoadingMeters={
								vehicleDimensions.length || undefined
							}
						/>
						{tourId && <Separator className="my-4" />}
						{tourId && <TransportOrderPanel tourId={tourId} />}
						{!tourData.carrierId && (
							<p className="text-xs text-muted-foreground mt-2">
								Select a carrier to enable transport order
								features
							</p>
						)}
						{tourId && <Separator className="my-4" />}
						{tourId && <TourExpensesPanel tourId={tourId} />}
						{tourId && <Separator className="my-4" />}
						{tourId && <TourDocumentsPanel tourId={tourId} />}
						<div className="h-4" /> {/* Bottom padding space */}
					</div>
				</ScrollArea>
			</CardContent>
		</Card>
	);
}
