import type {
	AdminEditInvoiceLineItemsInput,
	CreateInvoiceInput,
	InvoiceStatus,
} from "@repo/api/src/routes/invoices/types";
import { apiClient } from "@shared/lib/api-client";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

type FetchInvoicesParams = {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
	status?: InvoiceStatus;
	orderId?: string;
	customerId?: string;
	startDate?: Date;
	endDate?: Date;
};

export const invoiceKeys = {
	all: ["invoices"] as const,
	list: (params: FetchInvoicesParams) =>
		[...invoiceKeys.all, "list", params] as const,
	detail: (organizationId?: string, id?: string) =>
		[...invoiceKeys.all, "detail", organizationId, id] as const,
};

export const fetchInvoices = async (params: FetchInvoicesParams) => {
	const response = await apiClient.invoices.$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
			status: params.status,
			orderId: params.orderId,
			customerId: params.customerId,
			startDate: params.startDate?.toISOString(),
			endDate: params.endDate?.toISOString(),
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch invoices list");
	}

	return response.json();
};

export const useInvoicesQuery = (params: FetchInvoicesParams) => {
	return useQuery({
		queryKey: invoiceKeys.list(params),
		queryFn: () => fetchInvoices(params),
		placeholderData: keepPreviousData,
		enabled: params.organizationId !== "", // Only run query when we have a valid organizationId
	});
};

export const fetchInvoiceById = async (organizationId: string, id: string) => {
	const response = await apiClient.invoices[":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch invoice details");
	}

	return response.json();
};

export const useInvoiceByIdQuery = (organizationId?: string, id?: string) => {
	return useQuery({
		queryKey: invoiceKeys.detail(organizationId, id),
		queryFn: () => {
			if (!organizationId || !id) {
				return null;
			}
			return fetchInvoiceById(organizationId, id);
		},
		enabled: !!organizationId && !!id,
	});
};

// Create invoice mutation
export const useCreateInvoiceMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (data: CreateInvoiceInput) => {
			const response = await apiClient.invoices.$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create invoice");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice created successfully");
		},
		onError: (error) => {
			toast.error("Failed to create invoice");
			console.error("Create invoice error:", error);
		},
	});
};

// Mark invoice as paid mutation
export interface MarkPaidParams {
	id: string;
	paymentDate?: Date;
}

export const useMarkInvoicePaidMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (params: string | MarkPaidParams) => {
			const id = typeof params === "string" ? params : params.id;
			const paymentDate =
				typeof params === "string" ? undefined : params.paymentDate;

			const response = await apiClient.invoices[":id"]["mark-paid"].$put({
				param: { id },
				query: { organizationId },
				json: paymentDate ? { paymentDate: paymentDate } : undefined,
			});

			if (!response.ok) {
				throw new Error("Failed to mark invoice as paid");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice marked as paid successfully");
		},
		onError: (error) => {
			toast.error("Failed to mark invoice as paid");
			console.error("Mark invoice paid error:", error);
		},
	});
};

// Cancel invoice mutation
export interface CancelInvoiceParams {
	id: string;
	createCancellationInvoice?: boolean;
}

export const useCancelInvoiceMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async (params: string | CancelInvoiceParams) => {
			// Handle both string and object parameter
			const id = typeof params === "string" ? params : params.id;
			const createCancellationInvoice =
				typeof params === "object"
					? params.createCancellationInvoice
					: false;

			const response = await apiClient.invoices[":id"].cancel.$put({
				param: { id },
				query: { organizationId },
				json: { createCancellationInvoice },
			});

			if (!response.ok) {
				throw new Error("Failed to cancel invoice");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice cancelled successfully");
		},
		onError: (error) => {
			toast.error("Failed to cancel invoice");
			console.error("Cancel invoice error:", error);
		},
	});
};

// Send invoice email mutation
export interface SendInvoiceEmailParams {
	id: string;
	recipientEmail?: string;
	ccEmails?: string[];
	bccEmails?: string[];
	documentIds?: string[]; // Add support for document attachments
	customSubject?: string;
	documentFilenames?: Record<string, string>; // Custom filenames for email attachments
}

export const useSendInvoiceEmailMutation = (organizationId: string) => {
	return useMutation({
		mutationFn: async ({
			id,
			recipientEmail,
			ccEmails,
			bccEmails,
			documentIds,
			customSubject,
			documentFilenames,
		}: SendInvoiceEmailParams) => {
			const response = await apiClient.invoices[":id"][
				"send-email"
			].$post({
				param: { id },
				query: { organizationId },
				json: {
					recipientEmail,
					ccEmails,
					bccEmails,
					documentIds,
					customSubject,
					documentFilenames,
				},
			});

			if (!response.ok) {
				// Try to parse error message from response, but handle parsing errors gracefully
				const errorData = await response.json().catch(() => null);
				// Extract error message from any potential response format
				const errorMessage = "Failed to send invoice email";

				console.log(errorData);

				throw new Error(errorMessage);
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice email sent successfully");
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to send invoice email",
			);
			console.error("Send invoice email error:", error);
		},
	});
};

// Email preview query
export const useInvoiceEmailPreview = (
	organizationId: string,
	invoiceId: string,
	recipientEmail?: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: [
			...invoiceKeys.detail(invoiceId),
			"email-preview",
			recipientEmail,
		],
		queryFn: async () => {
			if (!invoiceId) {
				throw new Error("Invoice ID is required");
			}

			const response = await apiClient.invoices[":id"][
				"email-preview"
			].$get({
				param: { id: invoiceId },
				query: {
					organizationId,
					...(recipientEmail ? { recipientEmail } : {}),
				},
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => null);
				let errorMessage = "Failed to fetch email preview";

				// Check if the error response has the expected structure
				if (errorData && typeof errorData === "object") {
					if (
						"error" in errorData &&
						typeof errorData.error === "object" &&
						errorData.error &&
						"message" in errorData.error
					) {
						errorMessage = errorData.error.message as string;
					}
				}

				throw new Error(errorMessage);
			}

			return response.json();
		},
		enabled:
			options?.enabled !== undefined
				? options.enabled
				: !!invoiceId && !!organizationId,
	});
};

// Type for the PDF preview API response
export interface InvoicePDFPreviewResponse {
	success: boolean;
	message: string;
	pdfBase64?: string; // Optional because it might fail
	error?: { message: string; details?: string }; // For structured errors from backend
}

// Generate Invoice PDF Preview
export const generateInvoicePDFPreview = async (
	data: any,
): Promise<InvoicePDFPreviewResponse> => {
	const response = await apiClient["invoice-previews"].generate.$post({
		json: data,
	});

	if (!response.ok) {
		let errorPayload: any;
		try {
			errorPayload = await response.json();
		} catch (e) {
			// If response is not JSON or fails to parse, use default error
			throw new Error(
				"Failed to generate invoice PDF preview. Server returned an invalid error format.",
			);
		}
		// Assuming error payload might be { error: { message: string } } or { message: string }
		const errorMessage =
			errorPayload?.error?.message ||
			errorPayload?.message ||
			"Failed to generate invoice PDF preview";
		throw new Error(errorMessage);
	}

	// If response.ok is true, we expect the success structure.
	return response.json() as Promise<InvoicePDFPreviewResponse>;
};

// Get PDF for an existing invoice
export const getInvoicePdf = async (
	invoiceId: string,
	organizationId: string,
): Promise<InvoicePDFPreviewResponse> => {
	if (!organizationId) {
		throw new Error("Organization ID is required");
	}

	const response = await apiClient.invoices[":id"].pdf.$get({
		param: { id: invoiceId },
		query: { organizationId },
	});

	if (!response.ok) {
		let errorPayload: any;
		try {
			errorPayload = await response.json();
		} catch (e) {
			throw new Error(
				"Failed to get invoice PDF. Server returned an invalid error format.",
			);
		}
		const errorMessage =
			errorPayload?.error?.message ||
			errorPayload?.message ||
			"Failed to get invoice PDF";
		throw new Error(errorMessage);
	}

	return response.json() as Promise<InvoicePDFPreviewResponse>;
};

// Admin edit invoice line items mutation
export const useAdminEditInvoiceLineItemsMutation = (
	organizationId: string,
) => {
	return useMutation({
		mutationFn: async ({
			id,
			lineItemsToUpdate,
		}: {
			id: string;
			lineItemsToUpdate: AdminEditInvoiceLineItemsInput["lineItemsToUpdate"];
		}) => {
			const response = await apiClient.invoices[":id"].$put({
				param: { id },
				query: { organizationId },
				json: { lineItemsToUpdate },
			});

			if (!response.ok) {
				// Try to parse error message from response
				let errorMessage = "Failed to update invoice line items";

				try {
					const errorData = await response.json();
					// Type assertion as Record<string, any> for flexible property access
					const errorObj = errorData as Record<string, any>;
					if (errorObj.error?.message) {
						errorMessage = errorObj.error.message;
					} else if (errorObj.message) {
						errorMessage = errorObj.message;
					}
				} catch (e) {
					// If JSON parsing fails, use default error message
				}

				throw new Error(errorMessage);
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Invoice line items updated successfully");
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update invoice line items",
			);
			console.error("Admin edit invoice line items error:", error);
		},
	});
};
