import type {
	AbsenceType,
	CreatePersonnelAbsenceInput,
} from "@repo/api/src/routes/personnel-absence/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useDebounce } from "@shared/hooks/use-debounce";
import type { SortingState } from "@tanstack/react-table";
import { useCallback, useState } from "react";
import {
	useAbsenceByIdQuery,
	useAbsenceStatisticsQuery,
	useAbsenceTableQuery,
	useAbsencesQuery,
	useCreateAbsenceMutation,
	useDeleteAbsenceMutation,
	useRemoveAbsenceDocumentMutation,
	useUpdateAbsenceMutation,
	useUploadAbsenceDocumentMutation,
} from "../lib/api-absence";

export function usePersonnelAbsences(
	personnelIdProp?: string,
	options?: { enabled?: boolean },
) {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [type, setType] = useState<AbsenceType | undefined>();
	const [departmentId, setDepartmentId] = useState<string | undefined>();
	const [startDate, setStartDate] = useState<Date | undefined>();
	const [endDate, setEndDate] = useState<Date | undefined>();

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = useCallback((size: number) => {
		setPageSize(size);
		setPage(1);
	}, []);

	const isQueryEnabled =
		!!activeOrganization?.id &&
		(options?.enabled !== undefined
			? !!personnelIdProp && options.enabled
			: false);

	const query = useAbsencesQuery(
		{
			organizationId: activeOrganization?.id ?? "",
			search: debouncedSearch,
			type,
			departmentId,
			startDate,
			endDate,
			page,
			limit: pageSize,
			sortBy: sorting[0]?.id,
			sortDirection: sorting[0]?.desc ? "desc" : "asc",
			personnelId: personnelIdProp,
		},
		{ enabled: isQueryEnabled },
	);

	const deleteMutation = useDeleteAbsenceMutation(
		activeOrganization?.id ?? "",
	);

	const deleteAbsence = useCallback(
		async (id: string) => {
			if (activeOrganization?.id) {
				try {
					await deleteMutation.mutateAsync(id);
				} catch (error) {
					console.error("Delete absence error:", error);
				}
			}
		},
		[activeOrganization?.id, deleteMutation],
	);

	const stableSetSearch = useCallback(
		(value: string) => setSearch(value),
		[],
	);
	const stableSetPage = useCallback((value: number) => setPage(value), []);
	const stableSetSorting = useCallback(
		(value: SortingState) => setSorting(value),
		[],
	);
	const stableSetType = useCallback(
		(value: AbsenceType | undefined) => setType(value),
		[],
	);
	const stableSetDepartmentId = useCallback(
		(value: string | undefined) => setDepartmentId(value),
		[],
	);
	const stableSetStartDate = useCallback(
		(value: Date | undefined) => setStartDate(value),
		[],
	);
	const stableSetEndDate = useCallback(
		(value: Date | undefined) => setEndDate(value),
		[],
	);

	return {
		data: query.data,
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		error: query.error,
		search,
		setSearch: stableSetSearch,
		type,
		setType: stableSetType,
		departmentId,
		setDepartmentId: stableSetDepartmentId,
		startDate,
		setStartDate: stableSetStartDate,
		endDate,
		setEndDate: stableSetEndDate,
		page,
		setPage: stableSetPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting: stableSetSorting,
		personnelId: personnelIdProp,
		refetch: query.refetch,
		deleteAbsence,
	};
}

export function usePersonnelAbsenceById(absenceId: string) {
	const { activeOrganization } = useActiveOrganization();

	return useAbsenceByIdQuery(activeOrganization?.id ?? "", absenceId);
}

export function usePersonnelAbsenceMutations(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const createMutation = useCreateAbsenceMutation(orgId);
	const updateMutation = useUpdateAbsenceMutation(orgId);
	const deleteMutation = useDeleteAbsenceMutation(orgId);

	const createWithCallback = async (
		data: Omit<CreatePersonnelAbsenceInput, "organizationId"> | FormData,
	) => {
		// Handle both regular data objects and FormData
		if (data instanceof FormData) {
			console.log("FormData received:", data);

			// Extract the JSON data and file
			const jsonData = data.get("data");
			const documentFile = data.get("documentFile") as File;

			console.log("Extracted file:", documentFile);
			console.log("Extracted JSON data:", jsonData);

			if (jsonData && typeof jsonData === "string") {
				try {
					// Parse the JSON data
					const parsedData = JSON.parse(jsonData);
					console.log("Parsed form data:", parsedData);

					// Create a proper object with documentFile that useCreateAbsenceMutation expects
					const properData = {
						...parsedData,
						documentFile,
						organizationId: orgId,
					};

					console.log(
						"Final data structure for mutation:",
						properData,
					);

					// Pass as a normal object, not FormData
					const result = await createMutation.mutateAsync(
						properData as any,
					);
					if (options?.onSuccess) {
						options.onSuccess();
					}
					return result;
				} catch (error) {
					console.error("Error processing FormData:", error);
					throw error;
				}
			}
		} else {
			// For regular data objects, add organizationId directly
			const result = await createMutation.mutateAsync({
				...(data as Omit<
					CreatePersonnelAbsenceInput,
					"organizationId"
				>),
				organizationId: orgId,
			});
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		}
	};

	const updateWithCallback = async ({
		id,
		data,
	}: {
		id: string;
		data: any;
	}) => {
		const result = await updateMutation.mutateAsync({ id, data });
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	const deleteWithCallback = async (id: string) => {
		const result = await deleteMutation.mutateAsync(id);
		if (options?.onSuccess) {
			options.onSuccess();
		}
		return result;
	};

	return {
		createAbsence: createWithCallback,
		updateAbsence: updateWithCallback,
		deleteAbsence: deleteWithCallback,
		isLoading:
			createMutation.isPending ||
			updateMutation.isPending ||
			deleteMutation.isPending,
	};
}

export function usePersonnelAbsenceTable() {
	const { activeOrganization } = useActiveOrganization();
	const [search, setSearch] = useState("");
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [sorting, setSorting] = useState<SortingState>([]);
	const [departmentId, setDepartmentId] = useState<string | undefined>();
	const currentYear = new Date().getFullYear();

	const debouncedSearch = useDebounce(search, 300);

	const handlePageSizeChange = (size: number) => {
		setPageSize(size);
		setPage(1);
	};

	const query = useAbsenceTableQuery(
		activeOrganization?.id ?? "",
		currentYear,
		departmentId,
		page,
		pageSize,
		debouncedSearch,
		sorting[0]?.id,
		sorting[0]?.desc ? "desc" : "asc",
	);

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		search,
		setSearch,
		departmentId,
		setDepartmentId,
		page,
		setPage,
		pageSize,
		setPageSize: handlePageSizeChange,
		sorting,
		setSorting,
		refetch: query.refetch,
	};
}

export function usePersonnelAbsenceStatistics() {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const query = useAbsenceStatisticsQuery(orgId);

	return {
		data: query.data,
		isLoading: query.isLoading,
		error: query.error,
		refetch: query.refetch,
	};
}

export function usePersonnelAbsenceDocuments(options?: {
	onSuccess?: () => void;
}) {
	const { activeOrganization } = useActiveOrganization();
	const orgId = activeOrganization?.id ?? "";

	const uploadMutation = useUploadAbsenceDocumentMutation(orgId);
	const removeMutation = useRemoveAbsenceDocumentMutation(orgId);

	const uploadDocument = useCallback(
		async ({ absenceId, file }: { absenceId: string; file: File }) => {
			const result = await uploadMutation.mutateAsync({
				absenceId,
				file,
			});
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		},
		[uploadMutation, options],
	);

	const removeDocument = useCallback(
		async (absenceId: string) => {
			const result = await removeMutation.mutateAsync(absenceId);
			if (options?.onSuccess) {
				options.onSuccess();
			}
			return result;
		},
		[removeMutation, options],
	);

	return {
		uploadDocument,
		removeDocument,
		isLoading: uploadMutation.isPending || removeMutation.isPending,
	};
}
