"use client";

import type { ExpenseLineItemInput } from "@repo/api/src/routes/costs/types";
import {
	calculateAllocationStatus,
	createExpenseCustomActions,
	expenseArrayToLineItems,
	lineItemToExpenseInput,
} from "@saas/expenses/lib/expense-line-item-adapters";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useExpenseCategoriesQuery } from "@saas/organizations/lib/api-expense-categories";
import { LineItemsTable } from "@saas/shared/components/line-items/line-items-table";
import { ExpenseCategorySelector } from "@ui/components/expense-category-selector";
import React from "react";
import type { ExpenseLineItemWithAllocations } from "./expense-line-item-table";

interface ExpenseLineItemsWrapperProps {
	items: ExpenseLineItemWithAllocations[];
	onCreateItem: (item: ExpenseLineItemInput) => void;
	onUpdateItem: (item: ExpenseLineItemInput & { id: string }) => void;
	onDeleteItem: (itemId: string) => void;
	onAllocateItem: (itemId: string) => void;
	currency?: string;
}

export function ExpenseLineItemsWrapper({
	items,
	onCreateItem,
	onUpdateItem,
	onDeleteItem,
	onAllocateItem,
	currency = "EUR",
}: ExpenseLineItemsWrapperProps) {
	const { activeOrganization } = useActiveOrganization();

	// Fetch categories for name lookup
	const { data: categoriesData } = useExpenseCategoriesQuery({
		organizationId: activeOrganization?.id || "",
		page: 1,
		limit: 100,
	});

	// Helper function to get category name
	const getCategoryName = (categoryId: string) => {
		if (!categoryId) return "-";
		const category = categoriesData?.items?.find(
			(cat) => cat.id === categoryId,
		);
		return category?.name || "-";
	};

	// Convert expense items to shared LineItem format
	const lineItems = expenseArrayToLineItems(items, getCategoryName);

	// Handle create item - convert from shared format back to expense format
	const handleCreateItem = async (item: any) => {
		const expenseInput = lineItemToExpenseInput(item);
		await onCreateItem(expenseInput);
	};

	// Handle update item - convert from shared format back to expense format
	const handleUpdateItem = async (item: any) => {
		const expenseInput = lineItemToExpenseInput(item);
		await onUpdateItem({ ...expenseInput, id: item.id });
	};

	// Handle custom actions (like Allocate button)
	const handleCustomAction = (itemId: string, action: string) => {
		if (action === "allocate") {
			onAllocateItem(itemId);
		}
	};

	// Create custom actions for expenses
	const customActions = createExpenseCustomActions();

	return (
		<LineItemsTable
			items={lineItems}
			onCreateItem={handleCreateItem}
			onUpdateItem={handleUpdateItem}
			onDeleteItem={onDeleteItem}
			// Expense-specific features
			showCategoryColumn={true}
			categorySelector={ExpenseCategorySelector}
			showAllocationColumn={true}
			allocationStatusRenderer={calculateAllocationStatus}
			showOcrIndicators={true}
			showVatAmountColumn={true}
			customActions={customActions}
			onCustomAction={handleCustomAction}
			// Fixed currency for expenses
			fixedCurrency={currency}
			hideCurrencyColumn={false} // Show currency but make it fixed
			// Hide features not needed for expenses
			hideInvoiceStatusColumn={true}
			hideTypeTotals={true}
			// Show currency totals for expense summary
			showCurrencyTotals={true}
			// Default VAT rate for new items
			defaultVat={0}
		/>
	);
}
