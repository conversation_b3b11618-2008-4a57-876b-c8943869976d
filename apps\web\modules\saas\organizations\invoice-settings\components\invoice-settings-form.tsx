"use client";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { invoiceConfigurationBaseSchema } from "@repo/api/src/routes/settings/invoice-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { useInvoiceSettings } from "@saas/organizations/hooks/use-invoice-settings";
import { useUpdateInvoiceConfigurationMutation } from "@saas/organizations/lib/api-invoice-settings";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { AlertTriangle, Crown, InfoIcon, Loader2, Zap } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import type { z } from "zod";

// Form values derived from the base schema, omitting fields managed by the backend
type InvoiceSettingsFormValues = Omit<
	z.infer<typeof invoiceConfigurationBaseSchema>,
	"lastDate"
>;

// Function to check if a string consists only of valid placeholders
function containsOnlyValidPlaceholders(format: string): boolean {
	// Remove all valid placeholders from the string
	const cleanedFormat = format
		.replace(/\{YYYY\}/g, "")
		.replace(/\{MM\}/g, "")
		.replace(/\{SEQ\}/g, "");

	// If anything remains, the string contains invalid characters
	return cleanedFormat.length === 0;
}

// Function to generate a sample invoice number based on the current settings
function generateExampleInvoiceNumber(
	prefix: string | undefined,
	leadingZeros: number,
	format: string,
): string {
	// Current date components
	const now = new Date();
	const year = now.getFullYear().toString();
	const month = (now.getMonth() + 1).toString().padStart(2, "0");
	const shortYear = year.substring(2);

	// Example sequential number with leading zeros
	const seq = "1".padStart(leadingZeros || 4, "0");

	// Replace placeholders in the format string
	let result = format
		.replace(/\{YYYY\}/g, year)
		.replace(/\{YY\}/g, shortYear)
		.replace(/\{MM\}/g, month)
		.replace(/\{SEQ\}/g, seq);

	// Simple prefix logic - prepend prefix if it exists
	if (prefix) {
		result = prefix + result;
	}

	return result;
}

// ZUGFeRD Settings Card Component
function ZugferdSettingsCard() {
	const { data, isLoading } = useInvoiceSettings();
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";
	const updateMutation =
		useUpdateInvoiceConfigurationMutation(organizationId);

	const handleZugferdToggle = async (enabled: boolean) => {
		if (!data || !organizationId) {
			return;
		}

		try {
			await updateMutation.mutateAsync({
				prefix: data.prefix ?? "",
				leadingZeros: data.leadingZeros,
				numberFormat: data.numberFormat,
				counterReset: data.counterReset as
					| "YEARLY"
					| "MONTHLY"
					| "NEVER",
				lastNumber: data.lastNumber ?? 0,
				zugferdEnabled: enabled,
			});
		} catch (error) {
			console.error("Failed to update ZUGFeRD setting:", error);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full">
				<CardContent className="pt-6">
					<div className="flex items-center justify-center py-4">
						<Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/30">
							<Crown className="h-5 w-5 text-amber-600 dark:text-amber-400" />
						</div>
						<div>
							<CardTitle className="flex items-center gap-2">
								ZUGFeRD Digital Invoicing
								<span className="rounded-full bg-amber-600 px-2 py-0.5 text-xs font-medium text-white dark:bg-amber-500">
									Premium
								</span>
							</CardTitle>
							<CardDescription>
								Generate ZUGFeRD-compliant electronic invoices
								with embedded XML data
							</CardDescription>
						</div>
					</div>
					<Button
						onClick={() =>
							handleZugferdToggle(!data?.zugferdEnabled)
						}
						disabled={updateMutation.isPending}
						variant={data?.zugferdEnabled ? "primary" : "outline"}
						className={
							data?.zugferdEnabled
								? "bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600"
								: "border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-950/20"
						}
					>
						{updateMutation.isPending && (
							<Loader2 className="mr-2 h-4 w-4 animate-spin" />
						)}
						{data?.zugferdEnabled ? "Enabled" : "Enable"}
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<Alert>
					<Zap className="h-4 w-4 text-amber-600 dark:text-amber-400" />
					<AlertDescription>
						<div className="space-y-2">
							<p className="font-medium">Benefits of ZUGFeRD:</p>
							<ul className="text-sm text-muted-foreground space-y-1">
								<li>
									• Automated invoice processing for customers
								</li>
								<li>
									• Faster payment cycles and reduced errors
								</li>
								<li>• EU-wide standard compliance</li>
								<li>• Enhanced digital workflow integration</li>
							</ul>
						</div>
					</AlertDescription>
				</Alert>
			</CardContent>
		</Card>
	);
}

export function InvoiceSettingsForm() {
	return (
		<div className="space-y-6">
			<ZugferdSettingsCard />
			<InvoiceNumberConfigurationCard />
		</div>
	);
}

// Export ZugferdSettingsCard for standalone use
export { ZugferdSettingsCard };

function InvoiceNumberConfigurationCard() {
	const { data, isLoading, updateInvoiceSettings, isUpdating } =
		useInvoiceSettings();
	const [showValidationAlert, setShowValidationAlert] = useState(false);

	// Create extended form schema with additional validation
	const formSchema = invoiceConfigurationBaseSchema.extend({
		// Add stricter validation for the number format
		numberFormat: invoiceConfigurationBaseSchema.shape.numberFormat.refine(
			containsOnlyValidPlaceholders,
			"Format must consist only of valid placeholders: {YYYY}, {MM}, and {SEQ}",
		),
	});

	const form = useForm<InvoiceSettingsFormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			prefix: "",
			leadingZeros: 4,
			numberFormat: "{YYYY}{MM}{SEQ}",
			counterReset: "YEARLY",
			lastNumber: 0,
		},
		mode: "onBlur",
		reValidateMode: "onChange",
	});

	// Watch form values for preview
	const watchedValues = useWatch({
		control: form.control,
		defaultValue: form.getValues(),
	});

	// Generate example invoice number based on current form values
	const exampleInvoiceNumber = useMemo(() => {
		return generateExampleInvoiceNumber(
			watchedValues.prefix,
			watchedValues.leadingZeros || 4,
			watchedValues.numberFormat || "{YYYY}{MM}{SEQ}",
		);
	}, [
		watchedValues.prefix,
		watchedValues.leadingZeros,
		watchedValues.numberFormat,
	]);

	// Update form values when data is loaded
	useEffect(() => {
		if (data) {
			form.reset({
				prefix: data.prefix ?? "",
				leadingZeros: data.leadingZeros,
				numberFormat: data.numberFormat,
				counterReset: data.counterReset as
					| "YEARLY"
					| "MONTHLY"
					| "NEVER",
				lastNumber: data.lastNumber ?? 0,
			});

			// Trigger validation after loading data to ensure prefilled values are validated
			setTimeout(() => {
				form.trigger();
			}, 0);
		}
	}, [data, form]);

	const onSubmit = async (values: InvoiceSettingsFormValues) => {
		// Check if form is valid before submitting
		if (!form.formState.isValid) {
			// Form contains validation errors, show them to the user
			// by triggering validation display
			form.trigger();
			setShowValidationAlert(true);
			return; // Prevent submission
		}

		// Hide validation alert when submitting valid form
		setShowValidationAlert(false);

		// Only proceed if validation passes
		try {
			// Pass form values to the update function
			// Backend will handle lastNumber and lastDate fields
			await updateInvoiceSettings(values);
		} catch (error) {
			// API error handling is already done in the mutation hook
			console.error("Failed to update invoice settings", error);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full">
				<CardContent className="pt-6">
					<div className="flex items-center justify-center py-8">
						<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle>Invoice Number Configuration</CardTitle>
				<CardDescription>
					Configure how invoice numbers are generated for your
					organization.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<Alert className="mb-6">
					<InfoIcon className="h-4 w-4" />
					<AlertDescription>
						<span className="font-medium">
							Example invoice number:
						</span>{" "}
						{exampleInvoiceNumber}
					</AlertDescription>
				</Alert>

				{showValidationAlert && (
					<Alert className="mb-6 border-destructive" variant="error">
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							Please correct the validation errors before saving.
						</AlertDescription>
					</Alert>
				)}

				<Form {...form}>
					<form
						id="invoice-settings-form"
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="prefix"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Invoice Prefix</FormLabel>
									<FormControl>
										<Input
											placeholder="INV"
											{...field}
											value={field.value || ""}
											disabled={isUpdating}
										/>
									</FormControl>
									<FormDescription>
										Optional prefix for invoice numbers
										(e.g., INV, FACT). Will be automatically
										added before the number format.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="leadingZeros"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Leading Zeros</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											max={10}
											{...field}
											disabled={isUpdating}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("leadingZeros");
											}}
											className={
												form.formState.errors
													.leadingZeros
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Number of leading zeros in the
										sequential part (e.g., 4 = "0001")
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="numberFormat"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Number Format</FormLabel>
									<FormControl>
										<Input
											placeholder="{YYYY}{MM}{SEQ}"
											{...field}
											onChange={(e) => {
												field.onChange(e.target.value);
												// Validate immediately after changing
												form.trigger("numberFormat");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors
													.numberFormat
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										Format template using placeholders:
										{"{YYYY}"} - Year (4 digits),
										{"{YY}"} - Year (2 digits),
										{"{MM}"} - Month,
										{"{SEQ}"} - Sequential number
										(required). Only these placeholders can
										be used, no other characters are
										allowed.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="counterReset"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Counter Reset</FormLabel>
									<Select
										onValueChange={field.onChange}
										defaultValue={field.value}
										disabled={isUpdating}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select when to reset the counter" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="YEARLY">
												Yearly
											</SelectItem>
											<SelectItem value="MONTHLY">
												Monthly
											</SelectItem>
											<SelectItem value="NEVER">
												Never
											</SelectItem>
										</SelectContent>
									</Select>
									<FormDescription>
										When to reset the sequential number
										counter
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="lastNumber"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Current Sequence Number
									</FormLabel>
									<FormControl>
										<Input
											type="number"
											min={0}
											value={field.value || 0}
											onChange={(e) => {
												field.onChange(
													e.target.valueAsNumber || 0,
												);
												form.trigger("lastNumber");
											}}
											disabled={isUpdating}
											className={
												form.formState.errors.lastNumber
													? "border-destructive"
													: ""
											}
										/>
									</FormControl>
									<FormDescription>
										The current sequence number. Set this
										when migrating from another system to
										avoid duplicate invoice numbers.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</form>
				</Form>
			</CardContent>
			<CardFooter>
				<Button
					type="submit"
					form="invoice-settings-form"
					disabled={isUpdating}
				>
					{isUpdating && (
						<Loader2 className="mr-2 h-4 w-4 animate-spin" />
					)}
					Save Configuration
				</Button>
			</CardFooter>
		</Card>
	);
}
