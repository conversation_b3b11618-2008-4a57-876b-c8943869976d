import type {
	CreateExpenseCategoryInput,
	UpdateExpenseCategoryInput,
} from "@repo/api/src/routes/settings/expense-categories/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface ListExpenseCategoriesParams {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
}

// Query keys for React Query
export const expenseCategoryKeys = {
	all: ["expense-categories"] as const,
	lists: () => [...expenseCategoryKeys.all, "list"] as const,
	list: (params: ListExpenseCategoriesParams) =>
		[...expenseCategoryKeys.lists(), params] as const,
	details: () => [...expenseCategoryKeys.all, "detail"] as const,
	detail: (organizationId: string, id: string) =>
		[...expenseCategoryKeys.details(), organizationId, id] as const,
};

// API functions
export const fetchExpenseCategories = async (
	params: ListExpenseCategoriesParams,
) => {
	const response = await apiClient.settings["expense-categories"].$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch expense categories");
	}

	return response.json();
};

export const useExpenseCategoriesQuery = (
	params: ListExpenseCategoriesParams,
) => {
	return useQuery({
		queryKey: expenseCategoryKeys.list(params),
		queryFn: () => fetchExpenseCategories(params),
		placeholderData: keepPreviousData,
		enabled: !!params.organizationId,
	});
};

// Create expense category mutation
export const useCreateExpenseCategoryMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<CreateExpenseCategoryInput, "organizationId">,
		) => {
			const response = await apiClient.settings[
				"expense-categories"
			].$post({
				json: {
					...data,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create expense category");
			}

			return response.json();
		},
		onSuccess: () => {
			// Invalidate and refetch expense categories
			queryClient.invalidateQueries({
				queryKey: expenseCategoryKeys.lists(),
			});
			toast.success("Expense category created successfully");
		},
		onError: (error) => {
			toast.error("Failed to create expense category");
			console.error("Create expense category error:", error);
		},
	});
};

// Update expense category mutation
export const useUpdateExpenseCategoryMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			id,
			data,
		}: {
			id: string;
			data: Omit<UpdateExpenseCategoryInput, "id" | "organizationId">;
		}) => {
			const response = await apiClient.settings["expense-categories"][
				":id"
			].$put({
				param: { id },
				json: {
					...data,
					id,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to update expense category");
			}

			return response.json();
		},
		onSuccess: () => {
			// Invalidate and refetch expense categories
			queryClient.invalidateQueries({
				queryKey: expenseCategoryKeys.lists(),
			});
			toast.success("Expense category updated successfully");
		},
		onError: (error) => {
			toast.error("Failed to update expense category");
			console.error("Update expense category error:", error);
		},
	});
};

// Delete expense category mutation
export const useDeleteExpenseCategoryMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.settings["expense-categories"][
				":id"
			].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete expense category");
			}

			return response.json();
		},
		onSuccess: () => {
			// Invalidate and refetch expense categories
			queryClient.invalidateQueries({
				queryKey: expenseCategoryKeys.lists(),
			});
			toast.success("Expense category deleted successfully");
		},
		onError: (error) => {
			toast.error("Failed to delete expense category");
			console.error("Delete expense category error:", error);
		},
	});
};
