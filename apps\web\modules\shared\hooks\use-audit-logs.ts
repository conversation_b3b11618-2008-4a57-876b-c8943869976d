import type {
	ActionType,
	AuditLog,
	AuditLogResponse,
	EntityType,
} from "@repo/api/src/routes/audit/types";
import {} from "@repo/database";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { useCallback, useState } from "react";

// Re-export the types for convenience
export type { AuditLog, AuditLogResponse, EntityType, ActionType };

/**
 * Query key for audit logs to enable proper React Query caching
 */
export const auditLogsQueryKey = (
	organizationId?: string,
	entityType?: string | null,
	entityId?: string | null,
	userId?: string,
	from?: string,
	to?: string,
	action?: string,
	page?: number,
	limit?: number,
) =>
	[
		"audit-logs",
		organizationId ?? "",
		entityType ?? "",
		entityId ?? "",
		userId ?? "",
		from ?? "",
		to ?? "",
		action ?? "",
		page ?? 1,
		limit ?? 10,
	] as const;

/**
 * Hook for fetching audit logs with filters
 */
export function useAuditLogs({
	organizationId,
	entityType,
	entityTypes,
	entityId,
	userId,
	from,
	to,
	action,
	page = 1,
	limit = 10,
	enabled = true,
}: {
	organizationId: string;
	entityType?: EntityType | string | null;
	entityTypes?: (EntityType | string)[];
	entityId?: string | null;
	userId?: string;
	from?: string;
	to?: string;
	action?: ActionType | string;
	page?: number;
	limit?: number;
	enabled?: boolean;
}) {
	const queryKey = auditLogsQueryKey(
		organizationId,
		entityType ?? (entityTypes ? "multiple types" : ""),
		entityId,
		userId,
		from,
		to,
		action,
		page,
		limit,
	);

	return useQuery({
		queryKey,
		queryFn: async () => {
			// Create query parameters for the API call
			// Use a simple object with proper types required by the Hono client
			const query: {
				organizationId: string;
				entityType?: string;
				entityTypes?: string[];
				entityId?: string;
				userId?: string;
				from?: string;
				to?: string;
				action?: string;
				page?: string;
				limit?: string;
				sortBy: string;
				sortDirection: "asc" | "desc";
			} = {
				organizationId,
				sortBy: "createdAt",
				sortDirection: "desc",
			};

			if (entityType) {
				query.entityType = entityType;
			}
			if (entityTypes) {
				query.entityTypes = entityTypes;
			}
			if (entityId) {
				query.entityId = entityId;
			}
			if (userId) {
				query.userId = userId;
			}
			if (from) {
				query.from = from;
			}
			if (to) {
				query.to = to;
			}
			if (action) {
				query.action = action;
			}
			if (page) {
				query.page = String(page);
			}
			if (limit) {
				query.limit = String(limit);
			}

			try {
				// Call the API using the Hono client
				const response = await apiClient.audit.logs.$get({ query });

				// Parse the response as JSON
				const data = await response.json();

				// Type check to ensure we got a valid response
				if (
					"items" in data &&
					"total" in data &&
					"page" in data &&
					"totalPages" in data
				) {
					return data as AuditLogResponse;
				}

				// If we got an error response, throw it
				if ("error" in data) {
					throw new Error(data.error);
				}

				// Otherwise, return a default structure
				return {
					items: [],
					total: 0,
					page: 1,
					totalPages: 1,
				} as AuditLogResponse;
			} catch (error) {
				console.error("Error fetching audit logs:", error);
				throw error;
			}
		},
		enabled,
		refetchOnWindowFocus: true,
		staleTime: 60 * 1000, // Consider data stale after 1 minute
		gcTime: 5 * 60 * 1000, // Keep inactive query in cache for 5 minutes
		refetchOnMount: true,
		refetchOnReconnect: true,
	});
}

/**
 * Hook for fetching entity-specific audit logs
 */
export function useEntityAuditLogs(
	organizationId: string | undefined,
	entityType: EntityType | string | null | undefined,
	entityId: string | null | undefined,
	options?: {
		entityTypes?: (EntityType | string)[];
		enabled?: boolean;
		page?: number;
		limit?: number;
	},
) {
	const [page, setPage] = useState(options?.page || 1);
	const [limit] = useState(options?.limit || 10);

	const passedEnabled = options?.enabled !== false;
	const hasEssentialParams = !!(
		organizationId &&
		(entityType || options?.entityTypes || entityId)
	);
	const finalEnabled = passedEnabled && hasEssentialParams;

	const { data, isLoading, refetch } = useAuditLogs({
		organizationId: organizationId || "",
		entityType: options?.entityTypes ? undefined : entityType,
		entityTypes: options?.entityTypes,
		entityId,
		page,
		limit,
		enabled: finalEnabled,
	});

	// Always define pagination logic, but it will operate on potentially undefined data
	const totalPages = data?.totalPages || 1;
	const hasNextPage = finalEnabled && data ? page < data.totalPages : false;
	const hasPreviousPage = finalEnabled && data ? page > 1 : false;

	const goToNextPage = useCallback(() => {
		if (hasNextPage) {
			setPage((prevPage) => prevPage + 1);
		}
	}, [hasNextPage]); // Dependency on hasNextPage which depends on data and finalEnabled

	const goToPreviousPage = useCallback(() => {
		if (hasPreviousPage) {
			setPage((prevPage) => prevPage - 1);
		}
	}, [hasPreviousPage]); // Dependency on hasPreviousPage

	if (!finalEnabled) {
		return {
			auditLogs: undefined,
			isLoading: false, // Not loading if not enabled
			refetch: () =>
				Promise.resolve() as Promise<AuditLogResponse | undefined>,
			page,
			setPage,
			hasNextPage: false,
			hasPreviousPage: false,
			goToNextPage, // Return the memoized function even if not enabled
			goToPreviousPage, // Return the memoized function even if not enabled
			total: 0,
			totalPages: 1, // Or 0, depending on desired default
		};
	}

	return {
		auditLogs: data?.items,
		total: data?.total || 0,
		totalPages,
		isLoading: isLoading || (finalEnabled && !data), // isLoading from useQuery, or if enabled and data hasn't loaded yet
		refetch,
		page,
		setPage,
		hasNextPage,
		hasPreviousPage,
		goToNextPage,
		goToPreviousPage,
	};
}
