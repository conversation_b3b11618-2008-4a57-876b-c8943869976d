import type {
	CreateCounterpartyGroupInput,
	UpdateCounterpartyGroupInput,
} from "@repo/api/src/routes/counterparty-groups/types";
import { apiClient } from "@shared/lib/api-client";
import {
	keepPreviousData,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

// Types for API requests
export interface ListCounterpartyGroupsParams {
	organizationId: string;
	search?: string;
	page?: number;
	limit?: number;
	sortBy?: string;
	sortDirection?: "asc" | "desc";
}

export interface ListGroupMembersParams {
	organizationId: string;
	groupId: string;
	search?: string;
	page?: number;
	limit?: number;
}

// Query keys for React Query
export const counterpartyGroupKeys = {
	all: ["counterparty-groups"] as const,
	lists: () => [...counterpartyGroupKeys.all, "list"] as const,
	list: (params: ListCounterpartyGroupsParams) =>
		[...counterpartyGroupKeys.lists(), params] as const,
	details: () => [...counterpartyGroupKeys.all, "detail"] as const,
	detail: (organizationId: string, id: string) =>
		[...counterpartyGroupKeys.details(), organizationId, id] as const,
	members: () => [...counterpartyGroupKeys.all, "members"] as const,
	membersList: (params: ListGroupMembersParams) =>
		[...counterpartyGroupKeys.members(), params] as const,
};

// API functions
export const fetchCounterpartyGroups = async (
	params: ListCounterpartyGroupsParams,
) => {
	const response = await apiClient["counterparty-groups"].$get({
		query: {
			organizationId: params.organizationId,
			search: params.search,
			page: params.page?.toString(),
			limit: params.limit?.toString(),
			sortBy: params.sortBy,
			sortDirection: params.sortDirection,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparty groups");
	}

	return response.json();
};

export const fetchCounterpartyGroupById = async (
	organizationId: string,
	id: string,
) => {
	const response = await apiClient["counterparty-groups"][":id"].$get({
		param: { id },
		query: { organizationId },
	});

	if (!response.ok) {
		throw new Error("Failed to fetch counterparty group");
	}

	return response.json();
};

// Temporarily disabled due to TypeScript issues with nested routes
// Will implement once type issues are resolved
export const fetchGroupMembers = async (params: ListGroupMembersParams) => {
	// TODO: Implement once API client types support nested routes properly
	throw new Error("Group members API not yet implemented");
};

// React Query Hooks
export const useCounterpartyGroupsQuery = (
	params: ListCounterpartyGroupsParams,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined
			? options.enabled
			: !!params.organizationId;

	return useQuery({
		queryKey: counterpartyGroupKeys.list(params),
		queryFn: () => fetchCounterpartyGroups(params),
		placeholderData: keepPreviousData,
		enabled: isEnabled && !!params.organizationId,
	});
};

export const useCounterpartyGroupQuery = (
	organizationId: string,
	id: string,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: counterpartyGroupKeys.detail(organizationId, id),
		queryFn: () => fetchCounterpartyGroupById(organizationId, id),
		enabled: options?.enabled !== false && !!organizationId && !!id,
	});
};

export const useGroupMembersQuery = (
	params: ListGroupMembersParams,
	options?: { enabled?: boolean },
) => {
	return useQuery({
		queryKey: counterpartyGroupKeys.membersList(params),
		queryFn: () => fetchGroupMembers(params),
		placeholderData: keepPreviousData,
		enabled:
			options?.enabled !== false &&
			!!params.organizationId &&
			!!params.groupId,
	});
};

// Mutation Hooks
export const useCreateCounterpartyGroupMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (data: CreateCounterpartyGroupInput) => {
			const response = await apiClient["counterparty-groups"].$post({
				json: { ...data, organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to create counterparty group");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Counterparty group created successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to create counterparty group",
			);
		},
	});
};

export const useUpdateCounterpartyGroupMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			id,
			data,
		}: {
			id: string;
			data: UpdateCounterpartyGroupInput;
		}) => {
			const response = await apiClient["counterparty-groups"][":id"].$put(
				{
					param: { id },
					json: { ...data, organizationId },
				} as any,
			);

			if (!response.ok) {
				throw new Error("Failed to update counterparty group");
			}

			return response.json();
		},
		onSuccess: (_, { id }) => {
			toast.success("Counterparty group updated successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.detail(organizationId, id),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update counterparty group",
			);
		},
	});
};

export const useDeleteCounterpartyGroupMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient["counterparty-groups"][
				":id"
			].$delete({
				param: { id },
				query: { organizationId },
			});

			if (!response.ok) {
				throw new Error("Failed to delete counterparty group");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Counterparty group deleted successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.lists(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to delete counterparty group",
			);
		},
	});
};

// Temporarily disabled due to TypeScript issues with nested routes
export const useAddCounterpartyToGroupMutation = (organizationId: string) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			groupId,
			counterpartyId,
		}: {
			groupId: string;
			counterpartyId: string;
		}) => {
			// TODO: Implement once API client types support nested routes properly
			throw new Error(
				"Add counterparty to group API not yet implemented",
			);
		},
		onSuccess: (_, { groupId }) => {
			toast.success("Counterparty added to group successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.detail(organizationId, groupId),
			});
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.members(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to add counterparty to group",
			);
		},
	});
};

export const useRemoveCounterpartyFromGroupMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async ({
			groupId,
			counterpartyId,
		}: {
			groupId: string;
			counterpartyId: string;
		}) => {
			// TODO: Implement once API client types support nested routes properly
			throw new Error(
				"Remove counterparty from group API not yet implemented",
			);
		},
		onSuccess: (_, { groupId }) => {
			toast.success("Counterparty removed from group successfully");
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.detail(organizationId, groupId),
			});
			queryClient.invalidateQueries({
				queryKey: counterpartyGroupKeys.members(),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to remove counterparty from group",
			);
		},
	});
};
