"use client";

import type { CreditNote } from "@repo/database";
import { useShortcuts } from "@saas/shared/components/shortcuts/context/shortcuts";
import { DELETE_DIALOG_SHORTCUTS } from "@saas/shared/components/shortcuts/registry/shortcut-registry";
import { useTranslations } from "next-intl";
import {
	type ReactNode,
	createContext,
	useCallback,
	useContext,
	useRef,
	useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { toast } from "sonner";
import {
	useCustomerCreditNoteMutations,
	useCustomerCreditNotes,
} from "../hooks/use-customer-credit-notes";

interface CreditNotesUIContextValue {
	// State
	deleteCreditNote: CreditNote | null;
	selectedCreditNote: CreditNote | null;
	editCreditNoteId: string | null;
	isDeleteDialogOpen: boolean;
	isViewSheetOpen: boolean;
	isEditDialogOpen: boolean;
	focusedDeleteButton: "cancel" | "confirm";
	cancelRef: React.RefObject<HTMLButtonElement | null>;
	confirmRef: React.RefObject<HTMLButtonElement | null>;
	isViewPdfDialogOpen: boolean;
	viewCreditNoteId: string | null;
	viewCreditNotePdfData: string | null;
	isLoadingPdf: boolean;
	isCreateDialogOpen: boolean;

	// Actions
	handleDeleteCreditNote: (creditNote: CreditNote) => void;
	handleViewCreditNote: (creditNote: CreditNote) => void;
	handleEditCreditNote: (creditNoteId: string) => void;
	handleCancelDelete: () => void;
	handleConfirmDelete: () => Promise<void>;
	handleSetDeleteButtonFocus: (button: "cancel" | "confirm") => void;
	setSelectedCreditNote: (creditNote: CreditNote | null) => void;
	closeAllDialogs: () => void;
	handleViewPdf: (creditNote: CreditNote) => Promise<void>;

	// Dialog management
	setDeleteDialogOpen: (open: boolean) => void;
	setViewSheetOpen: (open: boolean) => void;
	setCreateDialogOpen: (open: boolean) => void;
	setEditDialogOpen: (open: boolean) => void;
	setViewPdfDialogOpen: (open: boolean) => void;
}

const CreditNotesUIContext = createContext<CreditNotesUIContextValue | null>(
	null,
);

export function CreditNotesUIProvider({
	children,
	onCreditNoteDeleted,
}: {
	children: ReactNode;
	onCreditNoteDeleted?: () => void;
}) {
	const t = useTranslations();
	const { deleteCreditNote: deleteCreditNoteMutation } =
		useCustomerCreditNoteMutations({
			onSuccess: onCreditNoteDeleted,
		});
	const { creditNotes } = useCustomerCreditNotes();
	const {
		enableScope,
		disableScope,
		addShortcuts,
		removeShortcuts,
		activeScopes,
	} = useShortcuts();

	// State
	const [deleteCreditNote, setDeleteCreditNote] = useState<CreditNote | null>(
		null,
	);
	const [selectedCreditNote, setSelectedCreditNote] =
		useState<CreditNote | null>(null);
	const [editCreditNoteId, setEditCreditNoteId] = useState<string | null>(
		null,
	);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [isViewSheetOpen, setIsViewSheetOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const [focusedDeleteButton, setFocusedDeleteButton] = useState<
		"cancel" | "confirm"
	>("cancel");

	// PDF viewer states
	const [isViewPdfDialogOpen, setIsViewPdfDialogOpen] = useState(false);
	const [viewCreditNoteId, setViewCreditNoteId] = useState<string | null>(
		null,
	);
	const [viewCreditNotePdfData, setViewCreditNotePdfData] = useState<
		string | null
	>(null);
	const [isLoadingPdf, setIsLoadingPdf] = useState(false);

	// Refs for focus management
	const cancelRef = useRef<HTMLButtonElement>(null);
	const confirmRef = useRef<HTMLButtonElement>(null);

	// Actions
	const handleViewCreditNote = useCallback((creditNote: CreditNote) => {
		setSelectedCreditNote(creditNote);
		setIsViewSheetOpen(true);
	}, []);

	const handleEditCreditNote = useCallback((creditNoteId: string) => {
		setEditCreditNoteId(creditNoteId);
		setIsEditDialogOpen(true);
	}, []);

	const handleDeleteCreditNote = useCallback(
		(creditNote: CreditNote) => {
			setDeleteCreditNote(creditNote);
			setFocusedDeleteButton("cancel");
			setIsDeleteDialogOpen(true);

			// When delete dialog opens, disable table scope and enable dialog scope
			disableScope("credit-notes-shortcuts");
			enableScope("credit-notes-delete-dialog");

			// Register shortcuts for the delete dialog
			addShortcuts(DELETE_DIALOG_SHORTCUTS);

			return () => {
				removeShortcuts(DELETE_DIALOG_SHORTCUTS.map((s) => s.id));
				disableScope("credit-notes-delete-dialog");
				enableScope("credit-notes-shortcuts");
			};
		},
		[addShortcuts, disableScope, enableScope, removeShortcuts],
	);

	const handleCancelDelete = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setDeleteCreditNote(null);
		setFocusedDeleteButton("cancel");

		// Re-enable table scope when dialog closes
		disableScope("credit-notes-delete-dialog");
		enableScope("credit-notes-shortcuts");
	}, [disableScope, enableScope]);

	const handleConfirmDelete = useCallback(async () => {
		if (!deleteCreditNote) {
			return;
		}

		try {
			// Delete the credit note
			await deleteCreditNoteMutation(deleteCreditNote.id);
			toast.success("Credit note deleted successfully");

			// Close dialog and reset state
			setIsDeleteDialogOpen(false);
			setDeleteCreditNote(null);
			setFocusedDeleteButton("cancel");

			// Re-enable table scope when dialog closes
			disableScope("credit-notes-delete-dialog");
			enableScope("credit-notes-shortcuts");

			// Notify parent component
			onCreditNoteDeleted?.();
		} catch (error) {
			toast.error("Failed to delete credit note");
		}
	}, [
		deleteCreditNote,
		deleteCreditNoteMutation,
		disableScope,
		enableScope,
		onCreditNoteDeleted,
	]);

	const handleSetDeleteButtonFocus = useCallback(
		(button: "cancel" | "confirm") => {
			setFocusedDeleteButton(button);
		},
		[],
	);

	const handleViewPdf = useCallback(async (creditNote: CreditNote) => {
		setIsLoadingPdf(true);
		setViewCreditNoteId(creditNote.id);
		setIsViewPdfDialogOpen(true);

		try {
			// TODO: Implement PDF retrieval for credit notes
			const pdfBase64 = ""; // This should be replaced with actual PDF data
			setViewCreditNotePdfData(pdfBase64);
		} catch (error) {
			console.error("Failed to load credit note PDF:", error);
			toast.error("Failed to load credit note PDF");
		} finally {
			setIsLoadingPdf(false);
		}
	}, []);

	const closeAllDialogs = useCallback(() => {
		setIsDeleteDialogOpen(false);
		setIsViewSheetOpen(false);
		setIsCreateDialogOpen(false);
		setIsEditDialogOpen(false);
		setIsViewPdfDialogOpen(false);
		setDeleteCreditNote(null);
		setSelectedCreditNote(null);
		setEditCreditNoteId(null);
		setViewCreditNoteId(null);
		setViewCreditNotePdfData(null);
		setFocusedDeleteButton("cancel");
	}, []);

	// Add keyboard handlers for arrow keys in delete dialog
	useHotkeys(
		"left",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("cancel");
			cancelRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("credit-notes-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"right",
		(e) => {
			e.preventDefault();
			setFocusedDeleteButton("confirm");
			confirmRef.current?.focus();
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("credit-notes-delete-dialog"),
			preventDefault: true,
		},
	);

	// Add keyboard handlers for enter/esc in delete dialog
	useHotkeys(
		"enter",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				if (focusedDeleteButton === "cancel") {
					handleCancelDelete();
				} else {
					void handleConfirmDelete();
				}
			}
		},
		{
			enabled:
				isDeleteDialogOpen &&
				activeScopes.includes("credit-notes-delete-dialog"),
			preventDefault: true,
		},
	);

	useHotkeys(
		"esc",
		(e) => {
			e.preventDefault();
			if (isDeleteDialogOpen) {
				handleCancelDelete();
			}
			if (isViewSheetOpen) {
				setIsViewSheetOpen(false);
				setSelectedCreditNote(null);
			}
		},
		{
			enabled:
				(isDeleteDialogOpen &&
					activeScopes.includes("credit-notes-delete-dialog")) ||
				isViewSheetOpen,
			preventDefault: true,
		},
	);

	// Add keyboard handler for Ctrl+D to trigger delete
	useHotkeys(
		"ctrl+d",
		(e) => {
			e.preventDefault();
			const highlightedRow = document.querySelector(
				"[data-row-id].bg-muted",
			);
			if (!isDeleteDialogOpen && highlightedRow) {
				const rowId = highlightedRow.getAttribute("data-row-id");
				const creditNote = creditNotes.find(
					(p: { id: string }) => p.id === rowId,
				);
				if (creditNote) {
					// Convert to proper CreditNote type
					const typedCreditNote = {
						...creditNote,
						createdAt: new Date(creditNote.createdAt),
						credit_note_date: new Date(creditNote.credit_note_date),
					} as unknown as CreditNote;
					handleDeleteCreditNote(typedCreditNote);
				}
			}
		},
		{
			enabled: activeScopes.includes("credit-notes-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	// Add keyboard handler for Ctrl+N to create new credit note
	useHotkeys(
		"ctrl+n",
		(e) => {
			e.preventDefault();
			if (activeScopes.includes("credit-notes-shortcuts")) {
				setIsCreateDialogOpen(true);
			}
		},
		{
			enabled: activeScopes.includes("credit-notes-shortcuts"),
			preventDefault: true,
			enableOnFormTags: true,
		},
	);

	return (
		<CreditNotesUIContext.Provider
			value={{
				// State
				deleteCreditNote,
				selectedCreditNote,
				editCreditNoteId,
				isDeleteDialogOpen,
				isViewSheetOpen,
				isEditDialogOpen,
				focusedDeleteButton,
				cancelRef,
				confirmRef,
				isViewPdfDialogOpen,
				viewCreditNoteId,
				viewCreditNotePdfData,
				isLoadingPdf,
				isCreateDialogOpen,

				// Actions
				handleDeleteCreditNote,
				handleViewCreditNote,
				handleEditCreditNote,
				handleCancelDelete,
				handleConfirmDelete,
				handleSetDeleteButtonFocus,
				setSelectedCreditNote,
				closeAllDialogs,
				handleViewPdf,

				// Dialog management
				setDeleteDialogOpen: setIsDeleteDialogOpen,
				setViewSheetOpen: setIsViewSheetOpen,
				setCreateDialogOpen: setIsCreateDialogOpen,
				setEditDialogOpen: setIsEditDialogOpen,
				setViewPdfDialogOpen: setIsViewPdfDialogOpen,
			}}
		>
			{children}
		</CreditNotesUIContext.Provider>
	);
}

export function useCreditNotesUI() {
	const context = useContext(CreditNotesUIContext);
	if (!context) {
		throw new Error(
			"useCreditNotesUI must be used within a CreditNotesUIProvider",
		);
	}
	return context;
}
