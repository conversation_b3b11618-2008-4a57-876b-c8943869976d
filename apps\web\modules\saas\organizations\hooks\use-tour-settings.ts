import type { UpdateTourConfigurationInput } from "@repo/api/src/routes/settings/tour-settings/types";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
	fetchTourConfiguration,
	useTourConfigurationQuery,
	useUpdateTourConfigurationMutation,
} from "@saas/organizations/lib/api-tour-settings";
import {} from "../lib/api-invoice-settings";

// Type for frontend form values (excludes backend-managed dates)
export type TourSettingsFormValues = Omit<
	UpdateTourConfigurationInput,
	"organizationId"
>;

// Main hook for tour configuration
export function useTourSettings() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	// Query for tour configuration
	const query = useTourConfigurationQuery(organizationId, {
		enabled: !!organizationId,
	});

	// Mutation for updating tour configuration
	const updateMutation = useUpdateTourConfigurationMutation(organizationId);

	// Wrapped update function
	const updateTourSettings = async (data: TourSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values for lastDate from the query data
		const currentValues = query.data;

		return await updateMutation.mutateAsync(data);
	};

	return {
		data: query.data,
		isLoading: query.isLoading,
		isError: query.isError,
		error: query.error,
		refetch: query.refetch,
		updateTourSettings,
		isUpdating: updateMutation.isPending,
	};
}

// Hook for accessing raw fetch function
export function useTourSettingsRaw() {
	const { activeOrganization } = useActiveOrganization();
	const organizationId = activeOrganization?.id ?? "";

	const updateMutation = useUpdateTourConfigurationMutation(organizationId);

	const fetchSettings = async () => {
		if (!organizationId) {
			throw new Error("No active organization");
		}
		return await fetchTourConfiguration(organizationId);
	};

	const updateSettings = async (data: TourSettingsFormValues) => {
		if (!organizationId) {
			throw new Error("No active organization");
		}

		// Get current values to preserve lastDate
		const currentValues = await fetchTourConfiguration(organizationId);

		return await updateMutation.mutateAsync(data);
	};

	return {
		fetchSettings,
		updateSettings,
		isUpdating: updateMutation.isPending,
	};
}
