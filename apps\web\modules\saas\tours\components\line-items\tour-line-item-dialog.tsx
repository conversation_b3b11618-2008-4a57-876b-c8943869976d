"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import type { LineItemFormValues } from "@repo/api/src/routes/line-items/types";
import type { LineItem } from "@saas/shared/components/line-items/line-items-table";
import { But<PERSON> } from "@ui/components/button";
import { CurrencySelect } from "@ui/components/currency-select";
import {
	<PERSON><PERSON>,
	Dialog<PERSON>ontent,
	Dialog<PERSON>ooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { TourLineItemAllocations } from "./tour-line-item-allocations";

// Import the Allocation type from the TourLineItemAllocations component
import type { Allocation } from "./tour-line-item-allocations";

// Type definition for a tour line item allocation - extend from Allocation type
interface TourLineItemAllocation extends Allocation {
	// Additional fields that might be in the API but not in the UI component
	allocatedQuantity?: number;
	allocatedAmount?: number;
}

interface TourLineItemDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	initialData: LineItem | null;
	tourId: string;
	onSave: (
		data: LineItemFormValues & { allocations?: TourLineItemAllocation[] },
	) => Promise<void>;
}

// Schema for validating the form
const formSchema = z.object({
	description: z.string().min(1, "Description is required"),
	quantity: z.coerce.number().min(0, "Quantity must be a positive number"),
	unit: z.string().optional(),
	unitPrice: z.coerce.number().min(0, "Unit price must be a positive number"),
	totalPrice: z.coerce
		.number()
		.min(0, "Total price must be a positive number"),
	currency: z.string().min(1, "Currency is required"),
	notes: z.string().optional(),
	vatRate: z.coerce.number().min(0).default(0),
});

export function TourLineItemDialog({
	open,
	onOpenChange,
	initialData,
	tourId,
	onSave,
}: TourLineItemDialogProps) {
	const t = useTranslations();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [allocations, setAllocations] = useState<TourLineItemAllocation[]>(
		[],
	);

	// Form initialization with default values
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			description: "",
			quantity: 0,
			unit: "",
			unitPrice: 0,
			totalPrice: 0,
			currency: "EUR",
			notes: "",
			vatRate: 0,
		},
	});

	// Update form when initialData changes
	useEffect(() => {
		if (initialData) {
			form.reset({
				description: initialData.description || "",
				quantity: initialData.quantity || 0,
				unit: initialData.unit || "",
				unitPrice: initialData.unitPrice || 0,
				totalPrice: initialData.totalPrice || 0,
				currency: initialData.currency || "EUR",
				notes: initialData.notes || "",
				vatRate: initialData.vatRate || 0,
			});

			// Initialize allocations if they exist
			if (initialData.tourLineItemAllocations) {
				setAllocations(
					initialData.tourLineItemAllocations.map(
						(allocation: any) => ({
							orderId: allocation.orderId,
							// Default to percentage if not defined
							allocationType:
								allocation.allocationType || "percentage",
							percentageValue: allocation.percentageValue,
							fixedValue: allocation.fixedValue,
							// Keep additional fields if they exist
							allocatedQuantity: allocation.allocatedQuantity,
							allocatedAmount: allocation.allocatedAmount,
						}),
					),
				);
			} else {
				setAllocations([]);
			}
		} else {
			form.reset({
				description: "",
				quantity: 0,
				unit: "",
				unitPrice: 0,
				totalPrice: 0,
				currency: "EUR",
				notes: "",
				vatRate: 0,
			});
			setAllocations([]);
		}
	}, [initialData, form]);

	// Auto-calculate total price when quantity or unit price changes
	useEffect(() => {
		const subscription = form.watch((value, { name }) => {
			if (
				(name === "quantity" || name === "unitPrice") &&
				value.quantity &&
				value.unitPrice
			) {
				const total = Number(value.quantity) * Number(value.unitPrice);
				form.setValue("totalPrice", total);
			}
		});

		return () => subscription.unsubscribe();
	}, [form]);

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		if (isSubmitting) return;

		setIsSubmitting(true);
		try {
			// Get allocations that have valid orderIds
			const validAllocations = allocations.filter(
				(allocation) => allocation.orderId,
			);

			// Combine form values with tour ID and allocations
			const data = {
				...values,
				tourId,
				// Removed hardcoded type - all line items are now revenue-only
				allocations,
			};

			await onSave(data);
		} catch (error) {
			console.error("Error submitting form:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[700px]">
				<DialogHeader>
					<DialogTitle>
						{initialData ? "Edit Tour Cost" : "Add Tour Cost"}
					</DialogTitle>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4"
					>
						<FormField
							control={form.control}
							name="description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Description</FormLabel>
									<FormControl>
										<Input
											placeholder="Tour transport cost"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="quantity"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Quantity</FormLabel>
										<FormControl>
											<Input
												type="number"
												min={0}
												step={0.01}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="unit"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Unit</FormLabel>
										<FormControl>
											<Input
												placeholder="km"
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="unitPrice"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Unit Price</FormLabel>
										<FormControl>
											<Input
												type="number"
												min={0}
												step={0.01}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="totalPrice"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Total Price</FormLabel>
										<FormControl>
											<Input
												type="number"
												min={0}
												step={0.01}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="currency"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Currency</FormLabel>
										<FormControl>
											<CurrencySelect
												name={field.name}
												value={field.value}
												onValueChange={field.onChange}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="vatRate"
								render={({ field }) => (
									<FormItem>
										<FormLabel>VAT %</FormLabel>
										<FormControl>
											<Input
												type="number"
												min={0}
												step={0.01}
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="notes"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Notes</FormLabel>
									<FormControl>
										<Textarea
											placeholder="Additional notes or details"
											className="h-20"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Allocations section */}
						<div className="space-y-2">
							<h3 className="text-sm font-medium">
								Order Allocations
							</h3>
							<TourLineItemAllocations
								tourId={tourId}
								allocations={allocations}
								onAllocationsChange={setAllocations}
								lineItemTotalPrice={form.watch("totalPrice")}
							/>
						</div>

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={isSubmitting}>
								{isSubmitting ? "Saving..." : "Save"}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
