"use client";

import { useExpenses } from "@saas/expenses/hooks/use-expenses";
import { DataTable } from "@saas/shared/components/data-table";
import { useColumns } from "./columns";

export function ExpensesDataTable() {
	const {
		data,
		isLoading,
		search,
		setSearch,
		page,
		setPage,
		pageSize,
		setPageSize,
		sorting,
		setSorting,
	} = useExpenses();
	const columns = useColumns();

	const expenses = data?.items ?? [];

	return (
		<DataTable
			columns={columns}
			data={expenses}
			onSearch={setSearch}
			searchValue={search}
			searchPlaceholder="Search expenses..."
			pagination={{
				page,
				setPage,
				pageSize,
				setPageSize,
				totalPages:
					Math.ceil((data?.items?.length ?? 0) / pageSize) || 1,
				total: data?.items?.length ?? 0,
			}}
			isLoading={isLoading}
			sorting={sorting}
			onSortingChange={setSorting}
			manualSorting={true}
			shortcutsScope="expenses-shortcuts"
		/>
	);
}
