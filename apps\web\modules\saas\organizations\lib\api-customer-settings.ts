import type { UpdateCustomerConfigurationInput } from "@repo/api/src/routes/settings/contact-settings/types";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Query keys for React Query
export const customerConfigurationKeys = {
	all: ["customer-configuration"] as const,
	details: () => [...customerConfigurationKeys.all, "detail"] as const,
	detail: (organizationId: string) =>
		[...customerConfigurationKeys.details(), organizationId] as const,
};

// API functions
export const fetchCustomerConfiguration = async (organizationId: string) => {
	const response = await apiClient.settings["customer-settings"].$get({
		query: {
			organizationId,
		},
	});

	if (!response.ok) {
		throw new Error("Failed to fetch customer configuration");
	}

	return response.json();
};

// React Query Hooks
export const useCustomerConfigurationQuery = (
	organizationId: string,
	options?: { enabled?: boolean },
) => {
	const isEnabled =
		options?.enabled !== undefined ? options.enabled : !!organizationId;

	return useQuery({
		queryKey: customerConfigurationKeys.detail(organizationId),
		queryFn: () => fetchCustomerConfiguration(organizationId),
		enabled: isEnabled && !!organizationId,
	});
};

// Mutation Hooks
export const useUpdateCustomerConfigurationMutation = (
	organizationId: string,
) => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (
			data: Omit<UpdateCustomerConfigurationInput, "organizationId">,
		) => {
			const response = await apiClient.settings["customer-settings"].$put(
				{
					json: { ...data, organizationId },
				},
			);

			if (!response.ok) {
				throw new Error("Failed to update customer configuration");
			}

			return response.json();
		},
		onSuccess: () => {
			toast.success("Customer configuration updated successfully");
			queryClient.invalidateQueries({
				queryKey: customerConfigurationKeys.detail(organizationId),
			});
		},
		onError: (error) => {
			toast.error(
				error instanceof Error
					? error.message
					: "Failed to update customer configuration",
			);
		},
	});
};
