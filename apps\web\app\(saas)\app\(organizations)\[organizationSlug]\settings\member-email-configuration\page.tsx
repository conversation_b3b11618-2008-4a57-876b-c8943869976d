import { MemberEmailConfigurationManager } from "@saas/organizations/member-email-configuration/components/member-email-configuration-manager";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Separator } from "@ui/components/separator";

import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import React from "react";

export const metadata: Metadata = {
	title: "My Email Configuration | Settings",
	description: "Configure your personal email settings for sending emails",
};

export default async function MemberEmailConfigurationPage() {
	const t = await getTranslations();

	return (
		<div className="space-y-6">
			<div>
				<h3 className="text-lg font-medium">My Email Configuration</h3>
				<p className="text-sm text-muted-foreground">
					Configure your personal email credentials for sending emails
					from your account
				</p>
			</div>
			<Separator />

			<div className="grid gap-6">
				<div className="col-span-1">
					<MemberEmailConfigurationManager />
				</div>
			</div>

			<Card>
				<CardHeader>
					<CardTitle>About Personal Email Configuration</CardTitle>
					<CardDescription>
						How personal email settings work
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<p>
						Personal email configuration allows you to use your own
						email credentials while leveraging your organization's
						SMTP server settings. This gives you the ability to send
						emails from your personal email address within the
						platform.
					</p>

					<div className="space-y-2">
						<h4 className="font-medium">How it works:</h4>
						<ol className="list-decimal pl-5 space-y-1">
							<li>
								Your organization provides the SMTP server
								configuration
							</li>
							<li>
								You configure your personal email credentials
								(username/password or OAuth2)
							</li>
							<li>
								Test your personal configuration to verify it
								works
							</li>
							<li>
								Activate your configuration to start sending
								emails from your account
							</li>
						</ol>
					</div>

					<div className="space-y-2">
						<h4 className="font-medium">Benefits:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								Send emails from your personal email address
							</li>
							<li>
								Maintain your email identity in communications
							</li>
							<li>
								Fallback to organization settings when not
								configured
							</li>
							<li>
								Support for both password and OAuth2
								authentication
							</li>
						</ul>
					</div>

					<div className="space-y-2">
						<h4 className="font-medium">Requirements:</h4>
						<ul className="list-disc pl-5 space-y-1">
							<li>
								Your organization must have SMTP configuration
								set up
							</li>
							<li>
								Your email credentials must work with the
								organization's SMTP server
							</li>
							<li>
								For OAuth2: Your email provider must support the
								organization's OAuth2 setup
							</li>
						</ul>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
