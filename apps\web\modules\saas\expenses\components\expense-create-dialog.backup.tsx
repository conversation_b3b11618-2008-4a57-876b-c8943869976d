"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import type {
	CreateExpenseInput,
	ExpenseLineItemInput,
	UpdateExpenseInput,
} from "@repo/api/src/routes/costs/types";

import { CounterpartySelector } from "@saas/organizations/components/CounterpartySelector";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { InputWithDate } from "@ui/components/input-with-date";
import { Label } from "@ui/components/label";
import { MultiEntitySelector } from "@ui/components/multi-entity-selector";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { format } from "date-fns";
import { Calendar, Crown, Plus, Trash2 } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";
import { useInvoiceOCRMutation } from "../lib/api-ocr";
import type { ExpenseLineItemWithAllocations } from "../types/expense-line-items";
import { ExpenseLineItemsWrapper } from "./line-items/expense-line-items-wrapper";

// Updated allocation interface to match backend schema
interface AllocationData {
	id?: string; // Include ID for existing allocations
	type: "general" | "order" | "vehicle" | "personnel";
	method: "fixed" | "percentage";
	value: number;
	selectAll: boolean;
	allocation_start?: Date;
	allocation_end?: Date;
	notes?: string;
	// For frontend display - derived from entities
	entityIds: string[];
	// Store the actual entities for backend submission
	entities?: Array<{
		id?: string;
		entityId: string;
		entityType: string;
		allocatedAmount: number;
	}>;
}

// Infer expense response type from API instead of hardcoding
type ExpenseAPIResponse = Awaited<
	ReturnType<typeof import("@saas/expenses/lib/api").fetchExpenses>
>["items"][0];

type ExpenseLineItemFromAPI = ExpenseAPIResponse["lineItems"][0];

// Conversion function: Backend normalized format → Frontend AllocationData format
function convertBackendAllocationsToFrontend(
	backendAllocations: any[],
): AllocationData[] {
	if (!backendAllocations || backendAllocations.length === 0) {
		return [];
	}

	return backendAllocations.map((allocation) => ({
		id: allocation.id,
		type: allocation.type,
		method: allocation.method,
		value: allocation.value,
		selectAll: allocation.selectAll,
		allocation_start: allocation.allocation_start
			? new Date(allocation.allocation_start)
			: undefined,
		allocation_end: allocation.allocation_end
			? new Date(allocation.allocation_end)
			: undefined,
		notes: allocation.notes,
		// Convert entities array to entityIds array for frontend (always array)
		entityIds:
			allocation.entities?.map((entity: any) => entity.entityId) || [],
		// Store original entities for reference
		entities: allocation.entities || [],
	}));
}

// Form schema
const expenseFormSchema = z.object({
	supplierCounterpartyId: z.string().min(1, "Supplier is required"),
	supplier_invoice_number: z.string().optional(),
	expense_date: z.string().min(1, "Expense date is required"),
	due_date: z.string().optional(),
	paid_date: z.string().optional(),
	hasServicePeriod: z.boolean().default(false),
	service_period_start: z.string().optional(),
	service_period_end: z.string().optional(),
	description: z.string().optional(),
	notes: z.string().optional(),
	totalAmount: z.number().min(0.01, "Total amount must be greater than 0"),
	totalVat: z.number().min(0, "VAT amount must be non-negative").optional(),
	totalGross: z.number().min(0.01, "Gross amount must be greater than 0"),
	currency: z.string().min(1, "Currency is required"),
	status: z.enum(["open", "paid", "overdue"]).default("open"),
	isRecurring: z.boolean().default(false),
	recurringStart: z.string().optional(),
	recurringEnd: z.string().optional(),
});

type ExpenseFormValues = z.infer<typeof expenseFormSchema>;

// Multiple Allocation Form Component
interface MultipleAllocationFormProps {
	lineItem:
		| (ExpenseLineItemInput & {
				id: string;
				allocations?: AllocationData[]; // Use proper AllocationData type
		  })
		| null;
	currency: string;
	onSave: (allocations: AllocationData[]) => void;
	onCancel: () => void;
}

function MultipleAllocationForm({
	lineItem,
	currency,
	onSave,
	onCancel,
}: MultipleAllocationFormProps) {
	const [allocations, setAllocations] = useState<AllocationData[]>([]);

	// Initialize allocations when lineItem changes
	useEffect(() => {
		if (lineItem?.allocations) {
			// Ensure all saved allocations have proper structure
			const normalizedAllocations = lineItem.allocations.map((alloc) => ({
				...alloc,
				entityIds: alloc.entityIds || [], // Ensure entityIds is always an array
			}));
			setAllocations(normalizedAllocations);
		} else {
			setAllocations([]);
		}
	}, [lineItem]);

	const formatCurrency = (value: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency,
		}).format(value);
	};

	const addAllocation = () => {
		const newAllocation: AllocationData = {
			type: "general",
			method: "fixed",
			value: 0,
			entityIds: [],
			selectAll: false,
		};
		setAllocations([...allocations, newAllocation]);
	};

	const removeAllocation = (index: number) => {
		setAllocations(allocations.filter((_, i) => i !== index));
	};

	const updateAllocation = (
		index: number,
		field: keyof AllocationData,
		value: any,
	) => {
		const updated = [...allocations];
		updated[index] = { ...updated[index], [field]: value };
		setAllocations(updated);
	};

	const getTotalAllocated = () => {
		if (!lineItem) {
			return 0;
		}
		return allocations.reduce((sum, allocation) => {
			if (allocation.method === "fixed") {
				return sum + allocation.value;
			}
			return sum + ((lineItem.totalPrice || 0) * allocation.value) / 100;
		}, 0);
	};

	const getRemainingAmount = () => {
		if (!lineItem) {
			return 0;
		}
		return (lineItem.totalPrice || 0) - getTotalAllocated();
	};

	const handleSave = () => {
		// Validate that non-general allocations have entity selections
		const invalidAllocations = allocations.filter(
			(allocation) =>
				allocation.type !== "general" &&
				!allocation.selectAll &&
				allocation.entityIds.length === 0,
		);

		if (invalidAllocations.length > 0) {
			toast.error(
				`Please select entities for all ${invalidAllocations[0].type} allocations`,
			);
			return;
		}

		// Validate that allocation amounts are valid
		const invalidAmounts = allocations.filter(
			(allocation) => allocation.value <= 0,
		);

		if (invalidAmounts.length > 0) {
			toast.error("All allocation amounts must be greater than 0");
			return;
		}

		onSave(allocations);
	};

	if (!lineItem) {
		return null;
	}

	return (
		<div className="flex flex-col h-full">
			{/* Fixed Header: Allocation Summary */}
			<div className="border rounded-lg p-4 bg-muted/50 mb-4 flex-shrink-0">
				<h4 className="font-medium mb-2">Allocation Summary</h4>
				<div className="grid grid-cols-3 gap-4 text-sm">
					<div>
						<span className="text-muted-foreground">
							Total Amount:
						</span>
						<div className="font-medium">
							{formatCurrency(lineItem.totalPrice || 0)}
						</div>
					</div>
					<div>
						<span className="text-muted-foreground">
							Allocated:
						</span>
						<div className="font-medium">
							{formatCurrency(getTotalAllocated())}
						</div>
					</div>
					<div>
						<span className="text-muted-foreground">
							Remaining:
						</span>
						<div
							className={`font-medium ${getRemainingAmount() < 0 ? "text-red-600" : "text-green-600"}`}
						>
							{formatCurrency(getRemainingAmount())}
						</div>
					</div>
				</div>
			</div>

			{/* Scrollable Content: Allocations List */}
			<div className="flex-1 overflow-y-auto space-y-3 mb-4">
				<div className="flex items-center justify-between">
					<h4 className="font-medium">
						Allocations ({allocations.length})
					</h4>
					<Button onClick={addAllocation} size="sm" variant="outline">
						<Plus className="h-4 w-4 mr-2" />
						Add Allocation
					</Button>
				</div>

				{allocations.length === 0 && (
					<div className="border rounded-lg p-8 text-center text-muted-foreground">
						<p>No allocations added yet</p>
						<p className="text-sm mt-1">
							Click "Add Allocation" to start
						</p>
					</div>
				)}

				{allocations.map((allocation, index) => (
					<div
						key={index}
						className="border rounded-lg p-4 space-y-3"
					>
						<div className="flex items-center justify-between">
							<span className="font-medium">
								Allocation {index + 1}
							</span>
							<Button
								onClick={() => removeAllocation(index)}
								size="sm"
								variant="ghost"
								className="text-red-600"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>

						<div className="grid grid-cols-2 gap-3">
							{/* Allocation Type */}
							<div className="space-y-2">
								<Label>Type</Label>
								<Select
									value={allocation.type}
									onValueChange={(
										value:
											| "general"
											| "order"
											| "vehicle"
											| "personnel",
									) => updateAllocation(index, "type", value)}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="general">
											General
										</SelectItem>
										<SelectItem value="order">
											Order
										</SelectItem>
										<SelectItem value="vehicle">
											Vehicle
										</SelectItem>
										<SelectItem value="personnel">
											Personnel
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Allocation Method */}
							<div className="space-y-2">
								<Label>Method</Label>
								<Select
									value={allocation.method}
									onValueChange={(
										value: "fixed" | "percentage",
									) =>
										updateAllocation(index, "method", value)
									}
								>
									<SelectTrigger>
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="fixed">
											Fixed Amount
										</SelectItem>
										<SelectItem value="percentage">
											Percentage
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>

						{/* Amount/Percentage Input */}
						<div className="space-y-2">
							<Label>
								{allocation.method === "fixed"
									? "Amount"
									: "Percentage"}
							</Label>
							<Input
								type="number"
								step="0.01"
								min="0"
								max={
									allocation.method === "percentage"
										? "100"
										: undefined
								}
								value={allocation.value || ""}
								onChange={(e) =>
									updateAllocation(
										index,
										"value",
										Number.parseFloat(e.target.value) || 0,
									)
								}
								onFocus={(e) => {
									// Select all text when focusing, so typing replaces the value
									e.target.select();
								}}
								placeholder={
									allocation.method === "fixed" ? "0.00" : "0"
								}
							/>
							{allocation.method === "percentage" && (
								<div className="text-sm text-muted-foreground">
									={" "}
									{formatCurrency(
										((lineItem.totalPrice || 0) *
											allocation.value) /
											100,
									)}
								</div>
							)}
						</div>

						{/* Entity Selection */}
						{allocation.type !== "general" && (
							<div className="space-y-2">
								<Label>Select {allocation.type}s</Label>
								<MultiEntitySelector
									entityType={
										allocation.type as
											| "order"
											| "vehicle"
											| "personnel"
									}
									selectedIds={allocation.entityIds}
									onChange={(ids) =>
										updateAllocation(
											index,
											"entityIds",
											ids,
										)
									}
									selectAll={allocation.selectAll}
									onSelectAllChange={(selectAll) => {
										// Single state update to handle both selectAll and entityIds
										const updated = [...allocations];
										updated[index] = {
											...updated[index],
											selectAll,
											entityIds: selectAll
												? []
												: updated[index].entityIds,
										};
										setAllocations(updated);
									}}
									placeholder={`Search and select ${allocation.type}s...`}
									maxHeight="150px"
								/>
							</div>
						)}

						{/* Notes */}
						<div className="space-y-2">
							<Label>Notes (Optional)</Label>
							<Input
								value={allocation.notes || ""}
								onChange={(e) =>
									updateAllocation(
										index,
										"notes",
										e.target.value,
									)
								}
								placeholder="Additional notes for this allocation"
							/>
						</div>

						{/* Timeframe Fields */}
						<div className="grid grid-cols-2 gap-3">
							<div className="space-y-2">
								<Label className="flex items-center gap-2">
									<Calendar className="h-4 w-4" />
									Start Date (Optional)
								</Label>
								<Input
									type="date"
									value={
										allocation.allocation_start
											? allocation.allocation_start
													.toISOString()
													.split("T")[0]
											: ""
									}
									onChange={(e) =>
										updateAllocation(
											index,
											"allocation_start",
											e.target.value
												? new Date(e.target.value)
												: undefined,
										)
									}
								/>
							</div>
							<div className="space-y-2">
								<Label className="flex items-center gap-2">
									<Calendar className="h-4 w-4" />
									End Date (Optional)
								</Label>
								<Input
									type="date"
									value={
										allocation.allocation_end
											? allocation.allocation_end
													.toISOString()
													.split("T")[0]
											: ""
									}
									onChange={(e) =>
										updateAllocation(
											index,
											"allocation_end",
											e.target.value
												? new Date(e.target.value)
												: undefined,
										)
									}
								/>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Fixed Footer: Action Buttons */}
			<div className="flex justify-end gap-2 pt-4 border-t bg-background flex-shrink-0">
				<Button onClick={onCancel} variant="outline">
					Cancel
				</Button>
				<Button
					onClick={handleSave}
					disabled={
						allocations.length === 0 ||
						allocations.some(
							(allocation) =>
								allocation.value <= 0 ||
								(allocation.type !== "general" &&
									!allocation.selectAll &&
									allocation.entityIds.length === 0),
						)
					}
				>
					Save Allocations ({allocations.length})
				</Button>
			</div>
		</div>
	);
}

export interface ExpenseCreateDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: CreateExpenseInput | UpdateExpenseInput) => Promise<void>;
	expense?: ExpenseAPIResponse;
	mode?: "create" | "edit";
	// New prop for preselecting carrier as supplier
	preselectedCarrierId?: string;
}

export function ExpenseCreateDialog({
	isOpen,
	onClose,
	onSubmit,
	expense,
	mode = "create",
	preselectedCarrierId,
}: ExpenseCreateDialogProps) {
	const { activeOrganization } = useActiveOrganization();

	// Initialize OCR mutation
	const ocrMutation = useInvoiceOCRMutation(
		activeOrganization?.id || "",
		(response: any) => {
			const actualData = response.data; // Unwrap the extra layer
			const prefilledFields = new Set<string>();

			// Prefill supplier counterparty
			if (actualData?.prefillData?.supplierCounterpartyId) {
				form.setValue(
					"supplierCounterpartyId",
					actualData.prefillData.supplierCounterpartyId,
				);
				prefilledFields.add("supplierCounterpartyId");
			}

			// Prefill expense date
			if (actualData?.prefillData?.expense_date) {
				form.setValue(
					"expense_date",
					actualData.prefillData.expense_date,
				);
				prefilledFields.add("expense_date");
			}

			// Prefill supplier invoice number
			if (actualData?.prefillData?.supplier_invoice_number) {
				form.setValue(
					"supplier_invoice_number",
					actualData.prefillData.supplier_invoice_number,
				);
				prefilledFields.add("supplier_invoice_number");
			}

			// Prefill description
			if (actualData?.prefillData?.description) {
				form.setValue(
					"description",
					actualData.prefillData.description,
				);
				prefilledFields.add("description");
			}

			// Prefill currency
			if (actualData?.prefillData?.currency) {
				form.setValue("currency", actualData.prefillData.currency);
				prefilledFields.add("currency");
			}

			// Prefill line items
			if (
				actualData?.prefillData?.lineItems &&
				actualData.prefillData.lineItems.length > 0
			) {
				const ocrLineItems: ExpenseLineItemWithAllocations[] =
					actualData.prefillData.lineItems.map(
						(item: any, index: number) => ({
							id: `ocr-${Date.now()}-${index}`,
							description: item.description || "",
							quantity: item.quantity || 1,
							unit: item.unit || "",
							unitPrice: item.unitPrice || 0,
							totalPrice: item.totalPrice || 0,
							vatRate: item.vatRate || 0,
							vatAmount: item.vatAmount || 0,
							categoryId: item.categoryId || null,
							notes: "",
							allocatedAmount: 0,
							remainingAmount: item.totalPrice || 0,
							isFullyAllocated: false,
							allocationCount:
								item.suggestedAllocations?.length || 0,
							allocations: [],
							createdAt: new Date().toISOString(),
							updatedAt: new Date().toISOString(),
							expenseId: "",
							savedAllocations: item.suggestedAllocations || [],
							isOcrGenerated: item.isOcrGenerated || true,
							isOcrCategorized:
								item.isOcrCategorized || !!item.categoryId,
							isOcrAllocated: item.isOcrAllocated || false,
						}),
					);

				setLineItems(ocrLineItems);
				prefilledFields.add("lineItems");

				// Check if any line items have allocations and add to prefilled fields
				const hasAllocations = ocrLineItems.some(
					(item) => item.isOcrAllocated,
				);
				if (hasAllocations) {
					prefilledFields.add("allocations");
				}

				// Calculate totals from line items immediately (synchronous)
				const total = ocrLineItems.reduce(
					(sum, item) => sum + (item.totalPrice || 0),
					0,
				);
				const totalVat = ocrLineItems.reduce(
					(sum, item) => sum + (item.vatAmount || 0),
					0,
				);
				const totalGross = total + totalVat;
				form.setValue("totalAmount", total);
				form.setValue("totalVat", totalVat);
				form.setValue("totalGross", totalGross);
			}

			// Update the prefilled fields state for visual indicators
			setOcrPrefilledFields(prefilledFields);
		},
	);

	// Convert expense.lineItems to the expected format - with proper allocation conversion
	const initialLineItems: ExpenseLineItemWithAllocations[] =
		expense?.lineItems?.map(
			(item) =>
				({
					...item,
					allocationCount: item.allocations?.length || 0,
					// Convert backend allocations to frontend format
					savedAllocations: convertBackendAllocationsToFrontend(
						item.allocations || [],
					),
				}) as ExpenseLineItemWithAllocations,
		) || [];

	const [isSubmitting, setIsSubmitting] = useState(false);
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [removeDocument, setRemoveDocument] = useState(false);
	const [lineItems, setLineItems] =
		useState<ExpenseLineItemWithAllocations[]>(initialLineItems);

	// Document preview state
	const [documentPreviewUrl, setDocumentPreviewUrl] = useState<string | null>(
		null,
	);

	// Simplified allocation panel state - just track open/close and selected item
	const [allocationPanelOpen, setAllocationPanelOpen] = useState(false);
	const [selectedLineItem, setSelectedLineItem] = useState<
		(ExpenseLineItemInput & { id: string }) | null
	>(null);

	// Track which fields were prefilled by OCR for visual indication
	const [ocrPrefilledFields, setOcrPrefilledFields] = useState<Set<string>>(
		new Set(),
	);

	const form = useForm<ExpenseFormValues>({
		resolver: zodResolver(expenseFormSchema),
		defaultValues: {
			supplierCounterpartyId:
				expense?.supplierId || preselectedCarrierId || "",
			supplier_invoice_number: expense?.supplier_invoice_number || "",
			expense_date: expense?.expense_date
				? new Date(expense.expense_date).toISOString().split("T")[0]
				: new Date().toISOString().split("T")[0],
			due_date: expense?.due_date
				? new Date(expense.due_date).toISOString().split("T")[0]
				: "",
			paid_date: expense?.paid_date
				? new Date(expense.paid_date).toISOString().split("T")[0]
				: "",
			hasServicePeriod: !!(
				expense?.service_period_start || expense?.service_period_end
			),
			service_period_start: expense?.service_period_start
				? new Date(expense.service_period_start)
						.toISOString()
						.split("T")[0]
				: "",
			service_period_end: expense?.service_period_end
				? new Date(expense.service_period_end)
						.toISOString()
						.split("T")[0]
				: "",
			description: expense?.description || "",
			notes: expense?.notes || "",
			totalAmount: expense?.totalAmount || 0,
			totalVat: expense?.totalVat || 0,
			totalGross: expense?.totalGross || 0,
			currency: expense?.currency || "EUR",
			status: (expense?.status as "open" | "paid" | "overdue") || "open",
			isRecurring: expense?.isRecurring || false,
			recurringStart: expense?.recurringStart
				? new Date(expense.recurringStart).toISOString().split("T")[0]
				: "",
			recurringEnd: expense?.recurringEnd
				? new Date(expense.recurringEnd).toISOString().split("T")[0]
				: "",
		},
	});

	// Auto-calculate total amount from line items
	const calculateTotalFromLineItems = () => {
		const total = lineItems.reduce(
			(sum, item) => sum + (item.totalPrice || 0),
			0,
		);
		const totalVat = lineItems.reduce(
			(sum, item) => sum + (item.vatAmount || 0),
			0,
		);
		const totalGross = total + totalVat;
		form.setValue("totalAmount", total);
		form.setValue("totalVat", totalVat);
		form.setValue("totalGross", totalGross);

		return total;
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			// Validate file type (PDF and images)
			if (
				file.type !== "application/pdf" &&
				!file.type.startsWith("image/")
			) {
				toast.error("Only PDF files and images are allowed");
				return;
			}

			// Validate file size (max 10MB)
			if (file.size > 10 * 1024 * 1024) {
				toast.error("File size must be less than 10MB");
				return;
			}

			setSelectedFile(file);
			setRemoveDocument(false);

			// Create preview URL for the document
			if (file.type === "application/pdf") {
				const objectUrl = URL.createObjectURL(file);
				setDocumentPreviewUrl(objectUrl);
			}
			// For images, create an object URL
			else if (file.type.startsWith("image/")) {
				const objectUrl = URL.createObjectURL(file);
				setDocumentPreviewUrl(objectUrl);
			}
		} else {
			setDocumentPreviewUrl(null);
		}
	};

	const handleRemoveDocument = () => {
		setSelectedFile(null);
		setRemoveDocument(true);
		// Clean up preview URL
		if (documentPreviewUrl) {
			URL.revokeObjectURL(documentPreviewUrl);
			setDocumentPreviewUrl(null);
		}
	};

	// Clean up object URLs when component unmounts or dialog closes
	useEffect(() => {
		return () => {
			if (documentPreviewUrl) {
				URL.revokeObjectURL(documentPreviewUrl);
			}
		};
	}, [documentPreviewUrl]);

	// Reset state when dialog closes
	useEffect(() => {
		if (!isOpen) {
			setSelectedFile(null);
			setRemoveDocument(false);
			setLineItems([]);
			setOcrPrefilledFields(new Set()); // Clear OCR prefilled indicators
			if (documentPreviewUrl) {
				URL.revokeObjectURL(documentPreviewUrl);
				setDocumentPreviewUrl(null);
			}

			// Reset form to default values when closing (especially important for create mode)
			if (mode === "create") {
				form.reset({
					supplierCounterpartyId: preselectedCarrierId || "",
					supplier_invoice_number: "",
					expense_date: new Date().toISOString().split("T")[0],
					due_date: "",
					paid_date: "",
					hasServicePeriod: false,
					service_period_start: "",
					service_period_end: "",
					description: "",
					notes: "",
					totalAmount: 0,
					totalVat: 0,
					totalGross: 0,
					currency: "EUR",
					status: "open",
					isRecurring: false,
					recurringStart: "",
					recurringEnd: "",
				});
			}
		}
	}, [isOpen, documentPreviewUrl, mode, form]);

	// Initialize line items when expense changes or dialog opens for the first time
	useEffect(() => {
		if (isOpen) {
			setLineItems(initialLineItems);
		}
	}, [isOpen, expense?.id]); // Only depend on isOpen and expense ID, not the entire initialLineItems array

	// Handle preselected carrier when dialog opens in create mode
	useEffect(() => {
		if (isOpen && mode === "create" && preselectedCarrierId && !expense) {
			form.setValue("supplierCounterpartyId", preselectedCarrierId);
		}
	}, [isOpen, mode, preselectedCarrierId, expense, form]);

	// Reset form when expense data changes (for edit mode)
	useEffect(() => {
		if (isOpen && expense && mode === "edit") {
			form.reset({
				supplierCounterpartyId: expense?.supplierId || "",
				supplier_invoice_number: expense?.supplier_invoice_number || "",
				expense_date: expense?.expense_date
					? new Date(expense.expense_date).toISOString().split("T")[0]
					: new Date().toISOString().split("T")[0],
				due_date: expense?.due_date
					? new Date(expense.due_date).toISOString().split("T")[0]
					: "",
				paid_date: expense?.paid_date
					? new Date(expense.paid_date).toISOString().split("T")[0]
					: "",
				hasServicePeriod: !!(
					expense?.service_period_start || expense?.service_period_end
				),
				service_period_start: expense?.service_period_start
					? new Date(expense.service_period_start)
							.toISOString()
							.split("T")[0]
					: "",
				service_period_end: expense?.service_period_end
					? new Date(expense.service_period_end)
							.toISOString()
							.split("T")[0]
					: "",
				description: expense?.description || "",
				notes: expense?.notes || "",
				totalAmount: expense?.totalAmount || 0,
				totalVat: expense?.totalVat || 0,
				totalGross: expense?.totalGross || 0,
				currency: expense?.currency || "EUR",
				status:
					(expense?.status as "open" | "paid" | "overdue") || "open",
				isRecurring: expense?.isRecurring || false,
				recurringStart: expense?.recurringStart
					? new Date(expense.recurringStart)
							.toISOString()
							.split("T")[0]
					: "",
				recurringEnd: expense?.recurringEnd
					? new Date(expense.recurringEnd).toISOString().split("T")[0]
					: "",
			});
		}
	}, [isOpen, expense, mode, form]);

	// Auto-calculate totals whenever line items change
	useEffect(() => {
		if (lineItems.length > 0) {
			const total = lineItems.reduce(
				(sum, item) => sum + (item.totalPrice || 0),
				0,
			);
			const totalVat = lineItems.reduce(
				(sum, item) => sum + (item.vatAmount || 0),
				0,
			);
			const totalGross = total + totalVat;
			form.setValue("totalAmount", total);
			form.setValue("totalVat", totalVat);
			form.setValue("totalGross", totalGross);
		}
	}, [lineItems, form]);

	// Line item handlers
	const handleCreateLineItem = (item: ExpenseLineItemInput) => {
		const newItem = {
			...item,
			id: `temp-${Date.now()}`, // Temporary ID for new items
			allocatedAmount: 0,
			remainingAmount: item.totalPrice || 0,
			isFullyAllocated: false,
			allocationCount: 0,
			// New normalized schema structure
			allocations: [],
			category: null,
			vatAmount: item.vatAmount || null,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
			expenseId: "",
			savedAllocations: [],
		} as ExpenseLineItemWithAllocations;

		const updatedLineItems = [...lineItems, newItem];
		setLineItems(updatedLineItems);
	};

	const handleUpdateLineItem = (
		item: ExpenseLineItemInput & { id: string },
	) => {
		const updatedLineItems = lineItems.map((li) =>
			li.id === item.id
				? {
						...li,
						...item,
						vatAmount: item.vatAmount || li.vatAmount,
						remainingAmount:
							(item.totalPrice || 0) - (li.allocatedAmount || 0),
					}
				: li,
		);
		setLineItems(updatedLineItems as ExpenseLineItemWithAllocations[]);
	};

	const handleDeleteLineItem = (itemId: string) => {
		const updatedLineItems = lineItems.filter((li) => li.id !== itemId);
		setLineItems(updatedLineItems);
	};

	// Simplified allocation handlers
	const handleOpenAllocationPanel = (itemId: string) => {
		const item = lineItems.find((li) => li.id === itemId);
		if (item?.id) {
			setSelectedLineItem({
				id: item.id,
				description: item.description,
				totalPrice: item.totalPrice || 0,
				quantity: item.quantity ?? undefined,
				unit: item.unit ?? undefined,
				unitPrice: item.unitPrice ?? undefined,
				vatRate: item.vatRate ?? undefined,
				categoryId: item.categoryId ?? undefined,
				notes: item.notes ?? undefined,
				allocations: (item.savedAllocations || []).map(
					(alloc): AllocationData => ({
						...alloc,
						entityIds: alloc.entityIds || [], // Ensure entityIds is always an array
					}),
				),
			});
			setAllocationPanelOpen(true);
		}
	};

	const handleCloseAllocationPanel = () => {
		setAllocationPanelOpen(false);
		setSelectedLineItem(null);
	};

	const handleSaveAllocations = async (
		lineItemId: string,
		allocations: AllocationData[],
	) => {
		// Update the line item with new allocations
		setLineItems(
			lineItems.map((item) => {
				if (item.id === lineItemId) {
					let newAllocatedAmount = 0;
					let allocationCount = 0;

					// Calculate total allocated amount and count from allocations
					for (const alloc of allocations) {
						if (alloc.method === "fixed") {
							newAllocatedAmount += alloc.value;
						} else {
							newAllocatedAmount +=
								((item.totalPrice || 0) * alloc.value) / 100;
						}
						allocationCount += alloc.entityIds?.length || 1;
					}

					// For new line items, update with frontend calculation
					const isNewLineItem = item.id?.startsWith("temp-");
					const finalAllocatedAmount = isNewLineItem
						? newAllocatedAmount
						: item.allocatedAmount || 0;

					return {
						...item,
						allocatedAmount: finalAllocatedAmount,
						remainingAmount:
							(item.totalPrice || 0) - finalAllocatedAmount,
						isFullyAllocated:
							finalAllocatedAmount >= (item.totalPrice || 0),
						allocationCount,
						savedAllocations: allocations,
					};
				}
				return item;
			}),
		);

		// Close the allocation panel
		handleCloseAllocationPanel();
	};

	const onFormSubmit = async (values: ExpenseFormValues) => {
		if (!activeOrganization?.id) {
			toast.error("No active organization");
			return;
		}

		if (lineItems.length === 0) {
			toast.error("At least one line item is required");
			return;
		}

		try {
			setIsSubmitting(true);

			const expenseData = {
				organizationId: activeOrganization.id,
				supplierId: values.supplierCounterpartyId,
				supplier_invoice_number:
					values.supplier_invoice_number || undefined,
				expense_date: values.expense_date
					? new Date(values.expense_date)
					: undefined,
				due_date: values.due_date
					? new Date(values.due_date)
					: undefined,
				paid_date: values.paid_date
					? new Date(values.paid_date)
					: undefined,
				service_period_start: values.service_period_start
					? new Date(values.service_period_start)
					: undefined,
				service_period_end: values.service_period_end
					? new Date(values.service_period_end)
					: undefined,
				description: values.description || undefined,
				notes: values.notes || undefined,
				totalAmount: Math.round(values.totalAmount * 100) / 100, // Fix floating point
				totalVat: values.totalVat
					? Math.round(values.totalVat * 100) / 100
					: undefined,
				totalGross: Math.round(values.totalGross * 100) / 100,
				currency: values.currency,
				status: values.status,
				isRecurring: values.isRecurring,
				recurringStart: values.recurringStart
					? new Date(values.recurringStart)
					: undefined,
				recurringEnd: values.recurringEnd
					? new Date(values.recurringEnd)
					: undefined,
				lineItems: lineItems.map((item) => ({
					description: item.description,
					quantity: item.quantity ?? 1,
					unit: item.unit ?? undefined,
					unitPrice: item.unitPrice ?? 0,
					totalPrice: item.totalPrice || 0,
					vatRate: item.vatRate ?? 0,
					vatAmount: item.vatAmount
						? Math.round(item.vatAmount * 100) / 100
						: undefined,
					categoryId: item.categoryId ?? undefined,
					notes: item.notes ?? undefined,
					allocations:
						item.savedAllocations?.map((alloc: AllocationData) => ({
							type: alloc.type,
							method: alloc.method,
							value: alloc.value,
							entityIds: alloc.selectAll ? [] : alloc.entityIds,
							selectAll: alloc.selectAll,
							allocation_start: alloc.allocation_start,
							allocation_end: alloc.allocation_end,
							notes: alloc.notes,
						})) || [],
				})),
			};

			if (mode === "edit" && expense?.id) {
				await onSubmit({
					...expenseData,
					id: expense.id,
					...(selectedFile && { document: selectedFile }),
					...(removeDocument && { removeDocument: true }),
				} as UpdateExpenseInput);
			} else {
				await onSubmit({
					...expenseData,
					...(selectedFile && { document: selectedFile }),
				} as CreateExpenseInput);
			}

			onClose();
			form.reset();
			setLineItems([]);
			setSelectedFile(null);
			setRemoveDocument(false);
		} catch (error) {
			console.error("Submit error:", error);
			toast.error("Failed to submit expense. Please try again.");
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent className="max-w-[90vw] max-h-[85vh] overflow-hidden p-0">
					<DialogHeader className="px-6 pt-6 pb-4">
						<DialogTitle>
							{mode === "edit"
								? "Edit Expense"
								: "Create Expense"}
						</DialogTitle>
						<DialogDescription>
							{mode === "edit"
								? "Update expense details and line items"
								: "Add a new expense with line items and allocations"}
						</DialogDescription>
					</DialogHeader>

					{/* Split Panel Layout */}
					<div className="flex h-[calc(85vh-120px)]">
						{/* LEFT: Document Preview Panel (35% width) */}
						<div className="w-[35%] border-r overflow-hidden flex flex-col">
							<div className="bg-muted p-3 font-medium flex justify-between items-center">
								<span>Document Preview</span>
								<div className="flex gap-2 items-center">
									<Input
										type="file"
										onChange={handleFileChange}
										accept=".pdf,.jpg,.jpeg,.png"
										className="max-w-xs"
									/>
									{selectedFile && (
										<Button
											onClick={() => {
												if (selectedFile) {
													ocrMutation.mutate(
														selectedFile,
													);
												}
											}}
											disabled={ocrMutation.isPending}
											size="sm"
											variant="outline"
											className="border-amber-600 text-amber-600 hover:bg-amber-50 dark:border-amber-400 dark:text-amber-400 dark:hover:bg-amber-950/20"
										>
											<Crown className="h-4 w-4 mr-2 text-amber-600 dark:text-amber-400" />
											{ocrMutation.isPending
												? "Processing..."
												: "Extract Data"}
										</Button>
									)}
								</div>
							</div>
							<div className="flex-1 overflow-hidden">
								{documentPreviewUrl ? (
									selectedFile?.type === "application/pdf" ? (
										<iframe
											src={documentPreviewUrl}
											className="w-full h-full"
											title="Invoice Document Preview"
										/>
									) : (
										<div className="flex items-center justify-center h-full">
											<img
												src={documentPreviewUrl}
												alt="Document Preview"
												className="max-w-full max-h-full object-contain"
											/>
										</div>
									)
								) : expense?.supplier_document_url &&
									mode === "edit" ? (
									/* Show existing document in edit mode */
									<iframe
										src={expense.supplier_document_url}
										className="w-full h-full"
										title="Existing Invoice Document"
									/>
								) : (
									<div className="flex flex-col items-center justify-center h-full text-muted-foreground p-6">
										<div className="text-center">
											<div className="text-lg font-medium mb-2">
												{mode === "edit" &&
												expense?.supplier_document_url
													? "Document Loaded"
													: "No Document"}
											</div>
											<div className="text-sm">
												{mode === "edit" &&
												expense?.supplier_document_url
													? "Existing invoice document"
													: "Upload an invoice document to preview it here"}
											</div>
										</div>
									</div>
								)}
							</div>
						</div>

						{/* RIGHT: Form Content Panel (65% width) */}
						<div className="w-[65%] flex flex-col h-full overflow-hidden">
							{allocationPanelOpen ? (
								/* Allocation Panel View */
								<div className="flex flex-col h-full">
									<div className="flex items-center justify-between p-6 pb-4 flex-shrink-0 border-b">
										<h3 className="text-lg font-semibold">
											Manage Allocations
										</h3>
										<Button
											variant="ghost"
											size="sm"
											onClick={handleCloseAllocationPanel}
										>
											← Back to Form
										</Button>
									</div>

									{/* Create an inline version of AllocationPanel */}
									{selectedLineItem && (
										<div className="flex flex-col flex-1 min-h-0">
											{/* Line Item Info */}
											<div className="border-b p-6 flex-shrink-0">
												<div className="border rounded-lg p-4">
													<h4 className="font-medium mb-2">
														Line Item Details
													</h4>
													<div className="space-y-2 text-sm">
														<div className="flex justify-between">
															<span className="text-muted-foreground">
																Description:
															</span>
															<span className="font-medium">
																{
																	selectedLineItem.description
																}
															</span>
														</div>
														<div className="flex justify-between">
															<span className="text-muted-foreground">
																Total Amount:
															</span>
															<span className="font-medium">
																{new Intl.NumberFormat(
																	"en-US",
																	{
																		style: "currency",
																		currency:
																			form.watch(
																				"currency",
																			),
																	},
																).format(
																	selectedLineItem.totalPrice ||
																		0,
																)}
															</span>
														</div>
													</div>
												</div>
											</div>

											{/* Multiple Allocations Management */}
											<div className="flex-1 min-h-0 p-6">
												<MultipleAllocationForm
													lineItem={
														selectedLineItem as ExpenseLineItemInput & {
															id: string;
															allocations?: AllocationData[];
														}
													}
													currency={form.watch(
														"currency",
													)}
													onSave={(
														allocations: AllocationData[],
													) =>
														handleSaveAllocations(
															selectedLineItem.id,
															allocations,
														)
													}
													onCancel={
														handleCloseAllocationPanel
													}
												/>
											</div>
										</div>
									)}
								</div>
							) : (
								/* Normal Form View */
								<div className="p-6 overflow-y-auto">
									<Form {...form}>
										<form
											onSubmit={form.handleSubmit(
												onFormSubmit,
												() => {
													toast.error(
														"Please fix the form errors before submitting.",
													);
												},
											)}
											className="space-y-6"
										>
											{/* Basic expense fields */}
											<div className="grid grid-cols-2 gap-4">
												<FormField
													control={form.control}
													name="supplierCounterpartyId"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="flex items-center gap-2">
																Supplier *
																{ocrPrefilledFields.has(
																	"supplierCounterpartyId",
																) && (
																	<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
																)}
															</FormLabel>
															<FormControl>
																<CounterpartySelector
																	value={
																		field.value
																	}
																	onChange={
																		field.onChange
																	}
																	name={
																		field.name
																	}
																	placeholder="Select supplier"
																	allowClear={
																		true
																	}
																	className={
																		ocrPrefilledFields.has(
																			"supplierCounterpartyId",
																		)
																			? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
																			: ""
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="supplier_invoice_number"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="flex items-center gap-2">
																Invoice Number
																{ocrPrefilledFields.has(
																	"supplier_invoice_number",
																) && (
																	<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
																)}
															</FormLabel>
															<FormControl>
																<Input
																	{...field}
																	placeholder="Enter supplier invoice number"
																	className={
																		ocrPrefilledFields.has(
																			"supplier_invoice_number",
																		)
																			? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
																			: ""
																	}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>

											{/* Date fields */}
											<div className="grid grid-cols-3 gap-4">
												<FormField
													control={form.control}
													name="expense_date"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="flex items-center gap-2">
																Expense Date *
																{ocrPrefilledFields.has(
																	"expense_date",
																) && (
																	<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
																)}
															</FormLabel>
															<FormControl>
																<InputWithDate
																	inputProps={{
																		value: field.value
																			? format(
																					new Date(
																						field.value,
																					),
																					"dd.MM.yyyy",
																				)
																			: undefined,
																		name: field.name,
																		className:
																			ocrPrefilledFields.has(
																				"expense_date",
																			)
																				? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
																				: "",
																	}}
																	onDateChange={(
																		date,
																	) => {
																		if (
																			date instanceof
																			Date
																		) {
																			field.onChange(
																				date
																					.toISOString()
																					.split(
																						"T",
																					)[0],
																			);
																		} else if (
																			date ===
																			null
																		) {
																			field.onChange(
																				"",
																			);
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="due_date"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Due Date
															</FormLabel>
															<FormControl>
																<InputWithDate
																	inputProps={{
																		value: field.value
																			? format(
																					new Date(
																						field.value,
																					),
																					"dd.MM.yyyy",
																				)
																			: undefined,
																		name: field.name,
																	}}
																	onDateChange={(
																		date,
																	) => {
																		if (
																			date instanceof
																			Date
																		) {
																			field.onChange(
																				date
																					.toISOString()
																					.split(
																						"T",
																					)[0],
																			);
																		} else if (
																			date ===
																			null
																		) {
																			field.onChange(
																				"",
																			);
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="paid_date"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Paid Date
															</FormLabel>
															<FormControl>
																<InputWithDate
																	inputProps={{
																		value: field.value
																			? format(
																					new Date(
																						field.value,
																					),
																					"dd.MM.yyyy",
																				)
																			: undefined,
																		name: field.name,
																	}}
																	onDateChange={(
																		date,
																	) => {
																		if (
																			date instanceof
																			Date
																		) {
																			field.onChange(
																				date
																					.toISOString()
																					.split(
																						"T",
																					)[0],
																			);
																		} else if (
																			date ===
																			null
																		) {
																			field.onChange(
																				"",
																			);
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>

											{/* Status and Currency */}
											<div className="grid grid-cols-2 gap-4">
												<FormField
													control={form.control}
													name="status"
													render={({ field }) => (
														<FormItem>
															<FormLabel>
																Status *
															</FormLabel>
															<FormControl>
																<Select
																	value={
																		field.value
																	}
																	onValueChange={
																		field.onChange
																	}
																>
																	<SelectTrigger>
																		<SelectValue placeholder="Select status" />
																	</SelectTrigger>
																	<SelectContent>
																		<SelectItem value="open">
																			Open
																		</SelectItem>
																		<SelectItem value="paid">
																			Paid
																		</SelectItem>
																		<SelectItem value="overdue">
																			Overdue
																		</SelectItem>
																	</SelectContent>
																</Select>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="currency"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="flex items-center gap-2">
																Currency *
																{ocrPrefilledFields.has(
																	"currency",
																) && (
																	<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
																)}
															</FormLabel>
															<FormControl>
																<Select
																	value={
																		field.value
																	}
																	onValueChange={
																		field.onChange
																	}
																>
																	<SelectTrigger
																		className={
																			ocrPrefilledFields.has(
																				"currency",
																			)
																				? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
																				: ""
																		}
																	>
																		<SelectValue placeholder="Select currency" />
																	</SelectTrigger>
																	<SelectContent>
																		<SelectItem value="EUR">
																			EUR
																		</SelectItem>
																		<SelectItem value="USD">
																			USD
																		</SelectItem>
																		<SelectItem value="GBP">
																			GBP
																		</SelectItem>
																		<SelectItem value="CHF">
																			CHF
																		</SelectItem>
																	</SelectContent>
																</Select>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>

											{/* Description and Notes */}
											<FormField
												control={form.control}
												name="description"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="flex items-center gap-2">
															Description
															{ocrPrefilledFields.has(
																"description",
															) && (
																<Crown className="h-3 w-3 text-amber-600 dark:text-amber-400" />
															)}
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																placeholder="Brief description of the expense"
																className={
																	ocrPrefilledFields.has(
																		"description",
																	)
																		? "border-amber-500 bg-amber-50 dark:bg-amber-950/20"
																		: ""
																}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="notes"
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Notes
														</FormLabel>
														<FormControl>
															<Textarea
																{...field}
																placeholder="Additional notes or comments"
																rows={3}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											{/* Line Items Table */}
											<div className="space-y-4">
												<ExpenseLineItemsWrapper
													items={lineItems}
													onCreateItem={
														handleCreateLineItem
													}
													onUpdateItem={
														handleUpdateLineItem
													}
													onDeleteItem={
														handleDeleteLineItem
													}
													onAllocateItem={
														handleOpenAllocationPanel
													}
												/>
											</div>

											<DialogFooter className="px-0 pt-4">
												<Button
													type="button"
													variant="outline"
													onClick={onClose}
												>
													Cancel
												</Button>
												<Button
													type="submit"
													disabled={
														isSubmitting ||
														lineItems.length === 0
													}
												>
													{isSubmitting
														? mode === "edit"
															? "Updating..."
															: "Creating..."
														: mode === "edit"
															? "Update Expense"
															: "Create Expense"}
												</Button>
											</DialogFooter>
										</form>
									</Form>
								</div>
							)}
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
}
